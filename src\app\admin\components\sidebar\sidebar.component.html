<div class="hidden-lg hidden-md hidden-sm">
  <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
    <div class="container">
       <div class="navbar-header">
         <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#page-sidebar">
           <span class="sr-only">toggle-navigation</span>
           <span class="icon-bar"></span>
           <span class="icon-bar"></span>
           <span class="icon-bar"></span>
         </button>
       </div>
    </div>
  </nav>
</div>


<div id="page-sidebar" [ngClass]="sideBarStatus">
  <span class="toggle-sidebar" (click)="changeSideBarStatus()"><i class="fa fa-angle-left li-icon" aria-hidden="true"></i></span>

<div class="main-menu" style="" >
  <!-- <ul class="list-group collapse in" id="page-sidebar"> -->
  <ul class="list-group collapse in">
    <li class="list-group-item heading"><a [routerLink]="['/']"><img _ngcontent-c3="" class="img-responsive" src="./assets/images/logo/CVeek-logo.svg"><span class="li-name" style="color:#3d7bce;">CVeek CPanel</span></a></li>
    <li *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.faq-list]="display[0]"><a (click)="display[0]=!display[0]"  ><i class="fa fa-question-circle li-icon" title="General FAQ's"></i><span class="li-name">General FAQ's <i class="fa" [class.fa-angle-up]="display[0]" [class.fa-angle-down]="!display[0]" ></i></span> </a></li>
      <ng-container *ngIf="display[0] && role === 'ROLE_ADMIN'" >
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" data-toggle="modal" data-target="#modal" (click)="displayCreateModal()" ><i class="fa fa-plus li-icon" title="Add New FAQ"></i> <span class="li-name">Add New FAQ </span></a></li> -->
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['faq/new']" ><i class="fa fa-plus li-icon" title="Add New FAQ"></i> <span class="li-name">Add New FAQ </span></a></li>
        <li class="list-group-item sub-list" ><a  routerLinkActive="active"   [routerLink]="['faqs']" ><i class="fa fa-cog li-icon" title=" Manage FAQ's"></i> <span class="li-name"> Manage FAQ's</span> </a></li>
      </ng-container>
    <li *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.help-topics]="display[1]"><a (click)="display[1]=!display[1]"  ><i class="fa fa-h-square li-icon" title="Help Center"></i><span class="li-name"> Help Center <i class="fa" [class.fa-angle-up]="display[1]" [class.fa-angle-down]="!display[1]" ></i></span>  </a></li>
      <ng-container  *ngIf="display[1] && role === 'ROLE_ADMIN'" >
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" data-toggle="modal" data-target="#modal" (click)="displayCreateTopicModal()" ><i class="fa fa-plus li-icon" title="Add New Help Topic"></i> <span class="li-name">Add New Help Topic</span></a></li> -->
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"  [routerLink]="['help-topic/new']" ><i class="fa fa-plus li-icon" title="Add New Help Topic"></i> <span class="li-name">Add New Help Topic</span></a></li>
        <li class="list-group-item sub-list" ><a  routerLinkActive="active"   [routerLink]="['help-topics']" ><i class="fa fa-cog li-icon" title="Manage Help Topics"></i><span class="li-name">Manage Help Topics</span>   </a></li>
      </ng-container>
    <li *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.help-tips]="display[2]"><a (click)="display[2]=!display[2]"  ><i class="fa fa-comments li-icon" title="Onsite Help"></i><span class="li-name"> Onsite Help <i class="fa" [class.fa-angle-up]="display[2]" [class.fa-angle-down]="!display[2]" ></i></span> </a></li>
      <ng-container  *ngIf="display[2] &&  role === 'ROLE_ADMIN'" >
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" data-toggle="modal" data-target="#modal" (click)="displayCreateTipModal()"><i class="fa fa-plus li-icon" title="Add New Help Tip"></i> <span class="li-name">Add New Help Tip</span></a></li> -->
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['help-tip/new']"><i class="fa fa-plus li-icon" title="Add New Help Tip"></i> <span class="li-name">Add New Help Tip</span></a></li>
        <li class="list-group-item sub-list" ><a  routerLinkActive="active"   [routerLink]="['help-tips']"><i class="fa fa-cog li-icon" title="Manage Help Tips"></i><span class="li-name"> Manage Help Tips</span>  </a></li>
      </ng-container>
    <li *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.verification-list]="display[3]"><a (click)="display[3]=!display[3]" ><i class=" fa fa-check-square-o li-icon" title="Values Verification"></i><span class="li-name"> Values Verification <i class="fa" [class.fa-angle-up]="display[3]" [class.fa-angle-down]="!display[3]" ></i> </span>  </a></li>
      <ng-container  *ngIf="display[3] && role === 'ROLE_ADMIN'" >
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"   [routerLink]="['actions-log']" ><i class="fa fa-file-text-o li-icon" title="Actions Log"></i><span class="li-name">  Actions Log </span> </a></li>
        <li class="list-group-item sub-list" ><a  routerLinkActive="active"   [routerLink]="['manage-values-verification']" ><i class="fa fa-cog li-icon" title="Manage transactions"></i><span class="li-name">  Manage transactions </span>  </a></li>
      </ng-container>
      <li *ngIf=" role === 'ROLE_ADMIN'|| role === 'ROLE_CONTACT_ADMIN'" class="list-group-item"  [class.contact-list]="display[4]"><a (click)="display[4]=!display[4]" ><i class=" fa fa-envelope-square li-icon" title=" Manage Messages"></i> <span class="li-name">  Manage Messages <i class="fa" [class.fa-angle-up]="display[4]" [class.fa-angle-down]="!display[4]" ></i> </span> </a></li>
      <ng-container  *ngIf="display[4] && (role === 'ROLE_ADMIN'|| role === 'ROLE_CONTACT_ADMIN')" >
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"   [routerLink]="['messages']"  ><i class="fa fa-envelope li-icon" title="Messages"></i>  <span class="li-name"> Messages </span> </a></li>
        <li class="list-group-item sub-list" ><a  routerLinkActive="active"   [routerLink]="['message-archive']" ><i class="fa fa-download li-icon" title="Message Archive"></i><span class="li-name"> Message Archive </span>  </a></li>
        <li class="list-group-item sub-list" ><a  routerLinkActive="active"   [routerLink]="['message-templates']" ><i class="fa fa-edit li-icon" title="Message Templates"></i> <span class="li-name"> Message Templates </span> </a></li>
      </ng-container>
      <li *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.contact-list]="display[5]"><a (click)="display[5]=!display[5]" ><i class=" fa fa-suitcase li-icon" title=" Manage Messages"></i> <span class="li-name">  Companies <i class="fa" [class.fa-angle-up]="display[5]" [class.fa-angle-down]="!display[5]" ></i> </span> </a></li>
      <ng-container  *ngIf="display[5] && role === 'ROLE_ADMIN'" >
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"   [routerLink]="['company-verification']"  ><i class="fa fa-cog li-icon" title="Manage Company Verification"></i>  <span class="li-name">Manage Companies </span> </a></li>
      </ng-container>
      <!-- <li class="list-group-item"  [class.contact-list]="display[6]"><a (click)="display[6]=!display[6]" ><i class=" fa fa-pencil li-icon" title="Articles"></i> <span class="li-name">  Articles <i class="fa" [class.fa-angle-up]="display[6]" [class.fa-angle-down]="!display[6]" ></i> </span> </a></li>
      <ng-container  *ngIf="display[6]" >
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"   [routerLink]="['articles']"  ><i class="fa fa-copy li-icon" title="Manage articles"></i>  <span class="li-name"> Manage Articles </span> </a></li>
      </ng-container> -->
      <li  *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.contact-list]="display[7]"><a (click)="display[7]=!display[7]" ><i class=" fa fa-pencil li-icon" title="Manage Values"></i> <span class="li-name">  Manage Values <i class="fa" [class.fa-angle-up]="display[7]" [class.fa-angle-down]="!display[7]" ></i> </span> </a></li>
      <ng-container  *ngIf="display[7] && role === 'ROLE_ADMIN'" >
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/JobTitle']" ><i class="fa fa-plus li-icon" title="Add New Job_title"></i> <span class="li-name">Add New Job Title </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/JobTitle']"  ><i class="fa fa-suitcase li-icon" title="Manage Job_titles"></i>  <span class="li-name"> Manage Job Titles </span> </a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/Institution']" ><i class="fa fa-plus li-icon" title="Add New Institution"></i> <span class="li-name">Add New Institution </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/Institution']"  ><i class="fa fa-university li-icon" title="Manage Institutions"></i>  <span class="li-name"> Manage Institutions </span> </a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/EducationField']"   ><i class="fa fa-plus li-icon" title="Add New Education"></i> <span class="li-name">Add New Education </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/EducationField']"  ><i class="fa fa-university li-icon" title="Manage Education"></i>  <span class="li-name"> Manage Education </span> </a></li>
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/Major']"   ><i class="fa fa-plus li-icon" title="Add New Major"></i> <span class="li-name">Add New Major </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/Major']"  ><i class="fa fa-university li-icon" title="Manage Majors"></i>  <span class="li-name"> Manage Majors </span> </a></li> -->
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/Minor']" ><i class="fa fa-plus li-icon" title="Add New Minor"></i> <span class="li-name">Add New Minor </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/Minor']"  ><i class="fa fa-circle li-icon" title="Manage Minors"></i>  <span class="li-name"> Manage Minors </span> </a></li> -->
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/CompanyIndustry']" ><i class="fa fa-plus li-icon" title="Add New Company Industry"></i> <span class="li-name">Add Co-Industry </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/CompanyIndustry']"  ><i class="fa fa-circle li-icon" title="Manage Company Industries"></i>  <span class="li-name"> Manage Co-Industry </span> </a></li> -->
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/Skill']" ><i class="fa fa-plus li-icon" title="Add New Skill"></i> <span class="li-name">Add New Skill </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/Skill']"  ><i class="fa fa-circle li-icon" title="Manage Skills"></i>  <span class="li-name"> Manage Skills </span> </a></li>
        <!-- <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/CompanySpecialty']" ><i class="fa fa-plus li-icon" title="Add New Company Specialty"></i> <span class="li-name">Add Co-Specialty </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/CompanySpecialty']"  ><i class="fa fa-circle li-icon" title="Manage Company Spacialties"></i>  <span class="li-name"> Manage Co-Specialty </span> </a></li> -->
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['new-value/ExperienceField']" ><i class="fa fa-plus li-icon" title="Add New Experience Field"></i> <span class="li-name">Add Exp-Field </span></a></li>
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active" [routerLink]="['manage-values/ExperienceField']"  ><i class="fa fa-circle li-icon" title="Manage Experience Fields"></i>  <span class="li-name"> Manage Exp-Fields </span> </a></li>

      </ng-container>

      <li *ngIf=" role === 'ROLE_ADMIN'" class="list-group-item"  [class.contact-list]="display[8]"><a (click)="display[8]=!display[8]" ><i class=" fa fa-suitcase li-icon" title=" Manage Ads"></i> <span class="li-name">  Manage Ads <i class="fa" [class.fa-angle-up]="display[8]" [class.fa-angle-down]="!display[8]" ></i> </span> </a></li>
      <ng-container  *ngIf="display[8] &&  role === 'ROLE_ADMIN'" >
        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"   [routerLink]="['manage-active-advs']"  ><i class="fa fa-cog li-icon" title="Manage Active Ads"></i>  <span class="li-name">Active Ads </span> </a></li>

        <li  class="list-group-item sub-list"> <a  routerLinkActive="active"   [routerLink]="['manage-ended-advs']"  ><i class="fa fa-cog li-icon" title="Manage Ended Ads"></i>  <span class="li-name">Ended Ads </span> </a></li>
      </ng-container>
  </ul>
 </div>

</div>








