import { VerificationService } from './../../../services/verification.service';
import { FormBuilder, Validators, FormGroup, FormControl } from '@angular/forms';
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy, OnChanges, ViewChild, ElementRef } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { DataMap } from "shared/Models/data_map";

@Component({
  selector: 'app-transaction',
  templateUrl: './transaction.component.html',
  styleUrls: ['./transaction.component.css']
})
export class TransactionComponent implements OnInit, OnDestroy, OnChanges {
  oldTransaction;
  transactionForm: any;
  @Input() previewMode = false;
  @Input() languagesArray;
  @Input() userInput;
  @Input() adminEntry;
  @Input() skillParentArr;
  @Input() skillCatsArr;
  @Input() majorArr;
  @Input() majorParentArr;
  @Output() closeModal = new EventEmitter();
  @Output() openAddParentModal = new EventEmitter();
  @Output() verifyMajorClicked = new EventEmitter();
  @Output() verifyJobTitleClicked = new EventEmitter();
  @Output() openAddCatModal = new EventEmitter();
  @Output() openAddExpFieldModal = new EventEmitter();
  @Input() experienceFields;
  @Input() jobTitles;
  selectedFields;
  majorParentBySelectedMajor: any = [];
  majors: any = [];
  majorParents: any = [];
  // @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: any;
  @ViewChild('googlelocationplaceLocation') googlelocationplaceLocation: any;
  @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: ElementRef;
  private ngUnsubscribe: Subject<any> = new Subject();
  selectedParents = [];
  selectedCat: string;
  selectedParent: string;
  selectedMajor: string;
  data_map = new DataMap();
  constructor(private fb: FormBuilder, private verificationService: VerificationService) {

  }

  ngOnInit() {
    if (!this.previewMode) {
      this.buildEmptyForm();
    }
    // for (let i = 0; i < this.majorArr.length; i++) {
    //   this.majors.push(...this.majorArr[i]);
    // }
    // // console.log('changeMajors', this.majors);
    // for (let i = 0; i < this.majorParentArr.length; i++) {
    //   this.majorParents.push(...this.majorParentArr[i]);
    // }
    // console.log('changeMajors', this.majors);
  }
  /// We need  ngOnChanges in order to update userInout when  call 
  ngOnChanges() {
    if (!this.previewMode) {
      this.buildEmptyForm();
    }
  }


  buildEmptyForm() {
    console.log('buildEmptyForm.userInput', this.userInput);
    if (this.userInput.type === 'UNIVERSITY') {
      this.transactionForm = this.fb.group({
        'verified_entry': this.fb.group({
          'id': [this.userInput.unverified_item_id, Validators.required],
          'verified': [1],
          'location': ['', Validators.required],
          'country': ['', Validators.required],
          'country_code': ['', Validators.required],
          'city': ['', Validators.required],
          // 'street_address': ['', Validators.required],
          'street_address': ['',],
          'url': ['', Validators.required],
          // 'unverified_item_id': [ this.userInput.unverified_item_id, Validators.required],
          'item_type_trans': this.fb.array([])
        })
      });
      this.oldTransaction = {
        'id': this.userInput.id,
        'cv_id': this.userInput.cv_id,
        'unverified_item_id': this.userInput.unverified_item_id,
        'type': this.userInput.type,
        'date': this.userInput.date,
        'url': this.userInput.url,
        'location': this.userInput.country + this.userInput.city,
        'country': this.userInput.country,
        'country_code': this.userInput.country_code,
        'city': this.userInput.city,
        'street_address': this.userInput.street_address,
        'verified': this.userInput.verified,
        'name': this.userInput.name
      };
    } else if (this.userInput.type === 'SKILL') {
      this.transactionForm = this.fb.group({
        'verified_entry': this.fb.group({
          'id': [this.userInput.unverified_item_id, Validators.required],
          'verified': [1],
          'skill_category_id': [this.userInput.skill_category_id, Validators.required],
          'job_titles': [null, Validators.required],
          'item_type_trans': this.fb.array([])
        })
      });
      this.oldTransaction = {
        'id': this.userInput.id,
        'cv_id': this.userInput.cv_id,
        'adv_id': this.userInput.adv_id,
        'unverified_item_id': this.userInput.unverified_item_id,
        'type': this.userInput.type,
        'date': this.userInput.date,
        'name': this.userInput.name,
        'verified': this.userInput.verified
      };
    } else if (this.userInput.type === 'MINOR') {
      this.transactionForm = this.fb.group({
        'verified_entry': this.fb.group({
          'id': [this.userInput.unverified_item_id, Validators.required],
          'verified': [1],
          'major_education_field_id': [Validators.required],
          // 'major_id': [this.userInput.major_id, Validators.required],
        //  'major_parent_id': [],
          // 'major_parent_id': [this.userInput.major_parent_id, Validators.required],
          // 'unverified_item_id': [ this.userInput.unverified_item_id, Validators.required],
          'item_type_trans': this.fb.array([])
        })
      });
      this.oldTransaction = {
        'id': this.userInput.id,
        'cv_id': this.userInput.cv_id,
        'adv_id': this.userInput.adv_id,
        'unverified_item_id': this.userInput.unverified_item_id,
        'type': this.userInput.type,
        'date': this.userInput.date,
        'major_id': this.userInput.major_id,
    //    'major_id': this.userInput.major_id,
        // 'major_parent_id': this.userInput.major_parent_id,
        // 'major_parent': this.userInput.major_parent,
        'major_name': this.userInput.major_name,
        'name': this.userInput.name,
        'verified': this.userInput.verified,
      };

    } else if (this.userInput.type === 'MAJOR') {
      this.transactionForm = this.fb.group({
        'verified_entry': this.fb.group({
          'id': [this.userInput.unverified_item_id, Validators.required],
          'verified': [1],
         // 'major_parent_id': [this.userInput.major_parent_id, Validators.required],
          // 'major_parent'      : [ { 'name': (this.userInput.major_parent || '')}],
          // 'unverified_item_id': [ this.userInput.unverified_item_id, Validators.required],
          'item_type_trans': this.fb.array([])
        })
      });
      this.oldTransaction = {
        'id': this.userInput.id,
        'cv_id': this.userInput.cv_id,
        'adv_id': this.userInput.adv_id,
        'unverified_item_id': this.userInput.unverified_item_id,
        'type': this.userInput.type,
        'date': this.userInput.date,
        'name': this.userInput.name,
      //  'name': this.userInput.major_name,
        // 'parent_id': this.userInput.parent_id,
        'verified': this.userInput.verified,
        'openedFromMinor': this.userInput.openedFromMinor
      };
    } else if (this.userInput.type === 'JOB_TITLE') {
      this.transactionForm = this.fb.group({
        'verified_entry': this.fb.group({
          'id': [this.userInput.unverified_item_id, Validators.required],
          'name': [this.userInput.name, Validators.required],
          'verified': [1],
          'experience_fields': [null, Validators.required],
          'item_type_trans': this.fb.array([])
        })
      });
      this.oldTransaction = {
        'id': this.userInput.id,
        'cv_id': this.userInput.cv_id,
        'adv_id': this.userInput.adv_id,
        'unverified_item_id': this.userInput.unverified_item_id,
        'type': this.userInput.type,
        'date': this.userInput.date,
        'name': this.userInput.name,
        'job_title_id': this.userInput.job_title_id,
        'experience_fields': this.userInput.experience_fields,
        'major_job_title_id': this.userInput.major_job_title_id,
        'verified': this.userInput.verified,
        'openedFromSynonym': this.userInput.openedFromSynonym
      };
    } else if (this.userInput.type === 'JOB_TITLE_SYNONYMS') {
      this.transactionForm = this.fb.group({
        'verified_entry': this.fb.group({
          'id': [this.userInput.unverified_item_id, Validators.required],
          'verified': [1],
          'job_title_id': [null, Validators.required],
          'item_type_trans': this.fb.array([])
        })
      });
      this.oldTransaction = {
        'id': this.userInput.id,
        'cv_id': this.userInput.cv_id,
        'adv_id': this.userInput.adv_id,
        'unverified_item_id': this.userInput.unverified_item_id,
        'type': this.userInput.type,
        'date': this.userInput.date,
        'name': this.userInput.name,
        'job_title_id': this.userInput.job_title_id,
        'job_title_name': this.userInput.job_title_name,
        'verified': this.userInput.verified,
      };
    }
    for (let lang of this.languagesArray) {
      this.fillFormTrans(lang.id);
    }

    if (this.majors.length < 0)
      for (let i = 0; i < this.majorArr.length; i++) {
        this.majors.push(...this.majorArr[i]);
      }
    // console.log('changeMajors', this.majors);
    // if (this.majorParents.length < 0)
    //   for (let i = 0; i < this.majorParentArr.length; i++) {
    //     this.majorParents.push(...this.majorParentArr[i]);
    //   }
    // console.log('changeMajorsParent', this.majorParents);
  }

  ngAfterViewInit() {
    if(this.userInput.type === 'UNIVERSITY'){
      setTimeout(() => {
        this.getLocationPlacefromLocationAutoComplete();
      }, 1)
    }
  }

  private getLocationPlacefromLocationAutoComplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete_location = new google.maps.places.Autocomplete(this.googlelocationplaceLocation.nativeElement,
      {
        types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry','formatted_address']   
      });

    autocomplete_location.addListener('place_changed', (res) => {
      const place_location = autocomplete_location.getPlace();
      console.log(place_location);
      this.getLocationAddress(place_location);
    });
    // this.location_init = true;
  }
  getLocationAddress(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place);
    this.location.setValue(data_location.fullAddress);
    this.country.setValue(data_location.country);
    this.city.setValue(data_location.city);
    this.country_code.setValue(data_location.country_code);
    this.street_address.setValue(data_location.street_address);
  }
  private fillFormTrans(langId) {
    this.item_type_trans.insert(this.item_type_trans.length, this.createFormTransControls(langId));
  }
  private createFormTransControls(langId: number) {
    return new FormGroup({
      'translated_language_id': new FormControl(langId),
      'name': new FormControl('', Validators.required)
    });

  }
  changeLocation(event) {
    // this.foo = e.target.value;
    //console.log('changeLocation', event.target.value);
    if (event.target.value == '') {
     // console.log('changeLocationIf', event.target.value);
      this.location.setValue('');
      this.country.setValue('');
      this.city.setValue('');
      this.country_code.setValue('');
      this.city.setValue('');
      this.street_address.setValue('');
    }
  }

  search(num) {
    if (num === 0) {
      document.getElementById('search-university')
        .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.name + ' university');
    } else if (num === 1) {
      document.getElementById('search-major')
        .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.name + ' major');
    } else if (num === 2) {
      document.getElementById('search-minor')
        .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.name + ' minor');
    } else if (num === 3) {
      document.getElementById('search-skill')
        .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.name + ' skill');
    } else if (num === 4) {
      document.getElementById('search-job-title')
        .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.name + ' job title');
    } else if (num === 5) {
      document.getElementById('search-job-title-synonyms')
        .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.name + ' job title');
    }


  }

  searchCountryCode() {
    document.getElementById('search-country-code')
      .setAttribute('href', 'https://www.google.com/search?q=' + this.userInput.country + ' country code');
  }

  endTransaction() {
  //  console.log('end trans');
    let transToDelete = { 'system_action_log_id': this.userInput.id };
    //console.log('trans to delete', transToDelete);
    this.verificationService.endTransaction(transToDelete).subscribe(res => {
      //console.log('end trans res', res);
      this.closeModal.emit({ 'new': res['data'], 'old': this.oldTransaction , 'endTransaction':true});
    });
  }

  saveTransaction(formValue) {
    //console.log('is form valid?', this.transactionForm.valid);
    //console.log(this.transactionForm.value);
    let count = 0;
    if (this.transactionForm.valid) {
      let dataToSend = this.transactionForm.value;
      for (let i = 0; i < this.languagesArray.length; i++) {
        if (dataToSend.verified_entry.item_type_trans[i].name !== '') {
          count++;
        }
      }
      if (count === this.languagesArray.length) {
        this.verificationService.saveTransaction(dataToSend, this.userInput.id).subscribe(res => {
          // console.log('save transaction res', res);
          if(res['error']){
            alert(res['error']);
          }
          else if(res['check_unverified_entry']){
            this.closeModal.emit({ 'new': res['check_unverified_entry'], 'old': this.oldTransaction ,'endTransaction':false});
          }
        });
        // console.log('true form');
      } else {
        alert('please enter valid values, english translation(name) ' +
          'must be filled at least');
      }

    }

  }

  changeMajorId(event) {
    var major_selected = this.majors.filter(x => x.major_id === this.selectedMajor);
    // var major_selected = this.majorArr.filter(function (item) {
    //   return item.major_id == 5;
    // });
    // console.log('changeMajorIdMajorSelected', major_selected);
    this.majorParentBySelectedMajor = this.majorParents.filter(x => x.value === major_selected[0].parent_id);
    // console.log('changeMajorIdParent', this.majorParentBySelectedMajor);
  }
  AddNewParent() {
    this.openAddParentModal.emit();
  }

  verifyMajor() {
    this.verifyMajorClicked.emit();
  }

  verifyJobTitle() {
    this.verifyJobTitleClicked.emit();
  }

  addNewCategory() {
    this.openAddCatModal.emit();
  }

  addNewField() {
    this.openAddExpFieldModal.emit();
  }



  get id() {
    return this.transactionForm.get('verified_entry.id');
  }
  get location() {
    return this.transactionForm.get('verified_entry.location');
  }
  get country() {
    return this.transactionForm.get('verified_entry.country');
  }

  get country_code() {
    return this.transactionForm.get('verified_entry.country_code');
  }

  get city() {
    return this.transactionForm.get('verified_entry.city');
  }

  get street_address() {
    return this.transactionForm.get('verified_entry.street_address');
  }

  get url() {
    return this.transactionForm.get('verified_entry.url');
  }

  get verified_entry() {
    return this.transactionForm.get('verified_entry');
  }

  get item_type_trans() {
    return this.transactionForm.get('verified_entry.item_type_trans');
  }

  // for skill

  get skill_category_id() {
    return this.transactionForm.get('verified_entry.skill_category_id');
  }

  get skill_category() {
    return this.transactionForm.get('verified_entry.skill_category');
  }

  get job_titles() {
    return this.transactionForm.get('verified_entry.job_titles');
  }

  // for major

  get major_parent_id() {
    return this.transactionForm.get('verified_entry.major_parent_id');

  }

  get major_id() {
    return this.transactionForm.get('verified_entry.major_id');

  }

  get job_title_id() {
    return this.transactionForm.get('verified_entry.job_title_id');

  }

  get experience_fields() {
    return this.transactionForm.get('verified_entry.experience_fields');

  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();

  }

}
