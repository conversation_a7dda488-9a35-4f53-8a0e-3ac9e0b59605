import { LanguageService } from './../../../admin/services/language.service';
import { Language } from 'app/admin/models/language';
import { Question } from './../../../admin/models/question';
import { Category } from './../../../admin/models/category';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FaqService } from '../../services/faq.service';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs/Subject';
import { DomSanitizer, Title, Meta } from '@angular/platform-browser';
import { AccordionModule } from 'primeng/accordion';
import { FieldsetModule } from 'primeng/fieldset';

declare var $: any;
@Component({
  selector: 'app-faq',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.css']
})
export class FaqComponent implements OnInit, OnDestroy {
  role = '';
  categories: Category[][] = [];
  faqsArray: Question[][] = [];
  currentLangId: number;
  filteredCategories: Category[][] = [];
  questions: Question[][][] = [];
  display: boolean[] = [];
  displayFaqs: boolean[] = [];
  displayAnswer: boolean[][][] = [];
  filteredQuestions: Question[][][] = [];
  languagesArray: Language[] = [];
  showLoader = true;
  noResultsAvailable = false;
  noFaqsAvailable = false;
  private ngUnsubscribe: Subject<any> = new Subject();


  constructor(private faqService: FaqService,
    private translate: TranslateService,
    private languageService: LanguageService,
    public sanitizer: DomSanitizer,
    private title: Title,
    private meta:Meta) {
    translate.addLangs(['en', 'ar']);
    translate.setDefaultLang('en');
    const browserLang = translate.getBrowserLang();
  //  title.setTitle('FAQ | CVeek');


    if (localStorage.getItem('defaultLang')) {
      this.currentLangId = +localStorage.getItem('defaultLang');
    } else {
      this.currentLangId = 1;
    }
  }

  ngOnInit() {
    this.translate.setDefaultLang('en');
    this.translate.use("en");
    
    if (localStorage.getItem('role')) {
      this.role = localStorage.getItem('role');

      if(this.role === 'unauth' || this.role === 'ROLE_JOB_SEEKER'){
        this.title.setTitle('CVeek  سيفيك | FAQs For Job Seeker');
        this.meta.updateTag({ name: 'description', content: 'CVeek FAQs for job seeker. Have Questions about using CVeek? Read answers to Frequently Asked Questions to help you get the best experience in creating your resume and applying for jobs.' });
      }
      else if(this.role === 'ROLE_EMPLOYER'){
        this.title.setTitle('CVeek  سيفيك | FAQs For Employer');
        this.meta.updateTag({ name: 'description', content: 'CVeek Questions and answers for employer. CVeek Homepage |FAQs for employer. Have questions about using CVeek? Read answers to frequently asked questions to help you get the best experience in job posting and job application management.' });
      }

      // if (this.role === 'ROLE_EMPLOYER') {
      //   this.applySpecificStyle();
      // }
    }
    this.getLanguages();
    this.getCount();
  }

  getCount() {
    let count = 0;
    if (this.filteredQuestions[this.currentLangId - 1]) {
      for (let i = 0; i < this.filteredQuestions[this.currentLangId - 1].length; i++) {
        count += this.filteredQuestions[this.currentLangId - 1][i].length;
      }
    }

    return count;
  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {

      let temp = res['data'];
      for (let lang of temp) {
        this.languagesArray.push({
          'id': lang.id,
          'name': lang.name
        });

      }

      this.getAllFaqs();
    });

  }
  getAllFaqs() {
    for (let i = 0; i < this.languagesArray.length; i++) {
      this.faqService.getFaqs(i + 1).takeUntil(this.ngUnsubscribe).subscribe(res => {

        let catTemp = res['categories'];
        this.categories[i] = [];
        for (let category of catTemp) {
          this.categories[i].push({
            'id': category.id,
            'transId': category.faq_category_translation[0].id,
            'name': category.faq_category_translation[0].name,
            'langId': category.faq_category_translation[0].translated_languages_id
          });
        }


        if(res['faqs'].length > 0){
          let temp = res['faqs'];
          this.faqsArray[i] = [];
          for (let q of temp) {
            if (q.faq_translation.length !== 0 && q.faq_translation[0].question !== null && q.faq_translation[0].answer !== null) {
              this.faqsArray[i].push({
                'id': q.id,
                'question': q.faq_translation[0].question,
                'answer': q.faq_translation[0].answer,
                'order': q.order,
                'activation': q.active,
                'type': q.faq_type,
                'langId': q.faq_translation[0].translated_languages_id,
                'category': q.faq_category.faq_category_translation[0].name,
                'catId': q.faq_category_id
              });
            }
          }

          // console.log("check comp faq", this.faqsArray);
          // if (this.faqsArray[0][0].type === 'Company Faq') {
          //   this.applySpecificStyle();
          // }

          this.filteredCategories[i] = [];
          for (let m = 0; m < this.categories[i].length; m++) {
            if (this.hasMatchedItemInFaqsArray(this.categories[i][m], i)) {
              this.filteredCategories[i].push(this.categories[i][m]);
              this.display.push(false);
              this.displayFaqs.push(false);
            }
          }


          this.questions[i] = [];
          this.filteredQuestions[i] = [];
          this.displayAnswer[i] = [];
          for (let k = 0; k < this.filteredCategories[i].length; k++) {
            this.questions[i][k] = [];
            this.filteredQuestions[i][k] = [];
            this.displayAnswer[i][k] = [];
            for (let j = 0; j < this.faqsArray[i].length; j++) {
              if (this.faqsArray[i][j].catId === this.filteredCategories[i][k].id) {
                this.questions[i][k].push(this.faqsArray[i][j]);
                this.filteredQuestions[i][k].push(this.faqsArray[i][j]);
                this.displayAnswer[i][k].push(false);
              }
            }
          }

          // checking just english default lang
          if (this.filteredQuestions[0]){
            if (this.filteredQuestions[0].length === 0) {
              this.noFaqsAvailable = true;
            }
            else {
              this.noFaqsAvailable = false;
            }
          }
          this.showLoader = false;
      }

      else{
        this.noFaqsAvailable = true;
        this.showLoader = false;
      }
      });
    }
    this.getCount();
  }


  search(query) {
    for (let i = 0; i < this.questions[this.currentLangId - 1].length; i++) {
      this.filteredQuestions[this.currentLangId - 1][i] = (query) ? this.questions[this.currentLangId - 1][i]
        .filter(q => q.question.toLowerCase().includes(query.toLowerCase())) : this.questions[this.currentLangId - 1][i];
    }

    for (let i = 0; i < this.filteredQuestions[this.currentLangId - 1].length; i++) {
      if (this.filteredQuestions[this.currentLangId - 1][i].length === 0) {

        this.filteredQuestions[this.currentLangId - 1].splice(i, 1);
        i--;


      }
    }
    this.filteredCategories[this.currentLangId - 1] = [];
    for (let c of this.categories[this.currentLangId - 1]) {
      if (this.hasMatchedItem(c)) {
        this.filteredCategories[this.currentLangId - 1].push(c);
      }
    }

    this.getCount();

    // checking just english default lang
    if (this.filteredQuestions[0].length === 0) {
      this.noResultsAvailable = true;
    }
    else {
      this.noResultsAvailable = false;
    }

  }

  hasMatchedItem(cat: Category): boolean {
    let result = false;
    for (let item of this.filteredQuestions[this.currentLangId - 1]) {
      for (let q of item) {
        if (q.catId === cat.id) {
          result = true;
        }
      }
    }
    return result;
  }

  hasMatchedItemInFaqsArray(category: Category, i): boolean {
    let result = false;
    for (let q of this.faqsArray[i]) {
      if (q.catId === category.id) {
        result = true;

      }
    }
    return result;
  }

  // applySpecificStyle() {
  //   $('h3').addClass('company-heading');
  //   $('div.content-col').addClass('company-content-col');
  // }

  toggleAnswer(fId) {

    $('div.panel-body#' + fId).toggleClass('view-answer');
  }


  showAll(i) {
    this.display[i] = true;
  }

  showLess(i) {
    this.display[i] = false;
  }



  changeLang(langId: number) {
    this.translate.use(this.getLangAbbrev(langId));
    this.currentLangId = langId;
    this.getCount();
  }



  getLangAbbrev(langId: number) {
    return this.languageService.getLangAbbrev(langId);
  }
  displayAnswerr(i, j) {
    for (var i1 = 0; i1 < this.displayAnswer[this.currentLangId - 1].length; i1++) {
      for (var i2 = 0; i2 < this.displayAnswer[this.currentLangId - 1][i1].length; i2++) {
        this.displayAnswer[this.currentLangId - 1][i1][i2] = false;
      }
    }
    this.displayAnswer[this.currentLangId - 1][i][j] = !this.displayAnswer[this.currentLangId - 1][i][j];
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
