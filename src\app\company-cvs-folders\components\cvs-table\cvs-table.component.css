.colSelectIc {
    font-size: 1.6rem;
    cursor: pointer;
}

label input[type="checkbox"] {
    display: none;
}

.cust-checkbox {
    font-size: 21px;
    text-align: center;
}

.cust-checkbox .fa {
    color: gold;
}

.cust-checkbox .fa-star-o {
    color: gray;
}

:host ::ng-deep .rec-cvs-table {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 20%), 0 1px 1px 0 rgb(0 0 0 / 14%), 0 2px 1px -1px rgb(0 0 0 / 12%);
    margin-bottom: 15px;
}

:host ::ng-deep .rec-cvs-table .ui-table-thead {
    background-color: #f4f4f4;
}

:host ::ng-deep .rec-cvs-table .ui-table-thead>tr>th,
    :host ::ng-deep .rec-cvs-table .ui-table-tbody>tr>td {
    border: none;
}

:host ::ng-deep .rec-cvs-table .ui-table-thead>tr>th {
    text-align: left;
}

:host ::ng-deep .ui-table-loading-content {
    color: #186ba0;
    font-size: 20px;
    top: 50px;
}

:host ::ng-deep .rec-cvs-table tr {
    cursor: pointer;
}

:host ::ng-deep .rec-cvs-table .ui-sortable-column.ui-state-highlight , :host ::ng-deep .rec-cvs-table .ui-sortable-column.ui-state-highlight .ui-sortable-column-icon{
    background-color: transparent;
    color: #000;
}

:host ::ng-deep .rec-cvs-table .ui-sortable-column:focus {
    box-shadow: none;
    background: #e0e0e0;
}


::ng-deep  .full-preview-page-content{
    margin-top: 0px  !important;
}
    
.filters-tags{
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.filters-tags p{
    border: 1px solid;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    background: #3d7bcead;
    color: #fff;
}

.filters-tags span{
    cursor: pointer;
}

.no-resumes{
    display: flex;
    justify-content: center;
    color: #969895;
    font-size: 1rem;  
}
.actions-col{
    visibility: hidden;
    position: absolute;
    right: 0px;
    white-space: nowrap;
    height: 100%;
    display: flex;
    align-items: center;
    min-width: 120px;
}
:host ::ng-deep .ui-table.ui-table-hoverable-rows .ui-table-tbody > tr.ui-selectable-row:not(.ui-state-highlight):hover .actions-col{
    visibility: visible;
}
.calendar-buttons{
    padding: 7px;
}
.red{
    color:red;
}
.green{
    color:#30A03E;
}

.cv-summary-mobile{
    display: flex;
    align-items: center;
    width:100%;
    min-height: 90px;
}
.cv-summary-mob-content{
    width:calc(100% - 100px);
}
.cv-summary-mob-options{
    width:100px;
    height:100%;
    text-align: right;
}

:host ::ng-deep .ui-table .ui-sortable-column.ui-state-highlight{
    background:#3D7BCE;
    color:#fff;
}
:host ::ng-deep .ui-table .ui-sortable-column.ui-state-highlight .pi-fw{
    color:#fff;
}
:host ::ng-deep .ui-sortable-column .pi-sort-alt{
    display:none;
}

:host ::ng-deep .ui-table .ui-table-tbody > tr.opened-cv{
    /* background:#f9f9f9; */
    background:#ffffff;
}
:host ::ng-deep .ui-table .ui-table-tbody > tr.not-opened-cv{
    background:#ffffff;
    font-weight: bold;
    color:#3D7BCE;
}
:host ::ng-deep .ui-table.ui-table-hoverable-rows .ui-table-tbody > tr.ui-selectable-row:not(.ui-state-highlight):hover {
    box-shadow: inset 0 -1px 0 0 rgba(100,121,143,0.122);
    background-color: #f3f3f3;
}
:host ::ng-deep .ui-table.ui-table-hoverable-rows .ui-table-tbody > tr.not-opened-cv.ui-selectable-row:not(.ui-state-highlight):hover {
    color:#3D7BCE;
}
:host ::ng-deep .ui-table .ui-table-tbody > tr{
    border-bottom: 1px solid #eee;
}
/* .calendar-buttons .btn-no-corner:first-child {
    margin-right:7px;
} */

.drag-preview{
    display:none;
}
.cdk-drag-preview{
    padding:10px 20px;
    background:#3D7BCE;
    color:#fff;
}

.adv-info-btn i{
    font-size: 12px;
    color: #fff;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    background: #80817f;
    height: 20px;
    width: 20px;
    text-align: center;
}
.total-filtered-cvs{
    font-weight:bold;
    padding-left: 18px;
}

.info-div-mob{
    position:absolute;
    top:5px;
    right:15px;
}

/* start styling checkbox */
.select-cv-checkbox-div{
    position:absolute;
    top:5px;
    right:38px;
}
.select-cv-checkbox{
    margin-right: 10px;
}

.table-header-settings{
    display: flex;
    align-items: center;
    text-align:center;
    min-width:71px;
}
.table-header-settings input{
    margin:0 5px 0 0;
}
/* start filter tags style */
.tags-container{
    display:flex;
    margin-top:15px;
    margin-bottom:15px;
    padding-left: 18px;
}
.tags-col-1{
    display: inline-block;
    width: calc(100% - 50px);
}
.tags-col-2{
    display: inline-block;
    width: 50px;
    text-align: right;
}
.single-tag{
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 10px;
}
.selectLabel{
    font-size: 14px;
    font-weight: bold;
}
.tag-label{
    color: #808080; 
    background-color:#f2f2f2; 
    padding:3px; 
    margin-left: 5px !important;
    margin-right:3px;
}
.close2 {
    border:none;
    filter: alpha(opacity=20);
}
.close2:hover{
    color: red;
}
.tag-separator{
    display: inline-block;
    padding: 0 10px;
    margin: 12px 0;
    color: #30457c;
    font-size: 24px;
}
/* End filter tags style */

@media (min-width: 768px) {
    #interviewModal .modal-dialog {
        width: 30%;
    }
}


/* @media screen and (max-width:1200px){
    .actions-col-res{
        width: 120px;
        right:67px !important;
    }
} */
/* @media screen and (max-width:855px){
    .actions-col-res{
        right:130px !important;
    }
} */

@media screen and (max-width:767px){
    /* p-table responsive layout , override orginal breakpoint  */
    :host ::ng-deep .ui-table-responsive .ui-table-thead > tr > th , :host ::ng-deep .ui-table-responsive .ui-table-tfoot > tr > td{
        display: none !important;
    }
    :host ::ng-deep .ui-table-responsive  colgroup {
        display: none !important;
    }
    :host ::ng-deep .ui-table-responsive .ui-table-tbody > tr > td {
        text-align: left;
        display: block;
        border: 0 none;
        width: 100% !important;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
    :host ::ng-deep .ui-table-responsive .ui-table-tbody > tr > td .ui-column-title {
        padding: .4em;
        min-width: 30%;
        display: inline-block;
        margin: -.4em 1em -.4em -.4em;
        font-weight: bold;
        color: #000;
    }
        
    .logo-div{
        float:left;
        width:49px;
        margin-bottom: 4px;
        text-align: left;
    }
    .logo-div img{
        width:40px;
    }
    .content-div{
        float:left;
        width:calc(100% - 51px);
        text-align: left;
    }
    .content-div .comp-name{
        font-weight: bold;
        margin-bottom:4px;
    }
    .content-div .job-title{
        font-size: 1em;
        font-weight: bold;
        color: #30457c;
        margin-bottom:4px;
    }
    .content-div .location-mob{
        margin-bottom:4px;
    }
    .favourite-div{
        position:absolute;
        top:5px;
        right:15px;
    }
    .favourite-div .cust-checkbox{
        font-size: 18px;
    }

    .adv-info-btn i{
        font-size: 12px;
        line-height: 8px;
        height: 17px;
        width: 17px;
    }
    .total-filtered-cvs{
        margin-top:10px;
    }
}

