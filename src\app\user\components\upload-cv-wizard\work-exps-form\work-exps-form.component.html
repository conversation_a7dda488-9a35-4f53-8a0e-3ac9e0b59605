<p-toast [style]="{marginTop: '100px'}">
  <ng-template let-message pTemplate="message">
    <span>{{message.detail}}</span> &nbsp;
  </ng-template>
</p-toast>

<div class="custom-container">
  <div *ngIf="showLoader" class="loader-container">
    <app-pre-loader [show]="showLoader"></app-pre-loader>
  </div>
  <div *ngIf="!showLoader">
    <ng-container *ngFor="let workExpForm of workExpsControls; let i = index">
      <div class="row flex-row justify-content-center align-items-stretch">
        <div class="col-md-1 d-flex align-items-end justify-content-center pl-3"></div>
        <div class="col-md-9 col-sm-12 prev-border add-certification mr-3" style="margin-right: 16px;">
          <div class="workexp-form-container" [ngClass]="{'minimized': isMinimized[i]}">
            <form [formGroup]="workExpForm" class="form-horizontal validate-form" (ngSubmit)="form.valid && saveAndExitStep()"
              (keydown.enter)="$event.preventDefault()">
              <p class="add-certification-p" translate>workExperience.addWorkExperience</p>

              <!-- Company Name -->
              <div formGroupName="company" class="form-group focus-container has-feedback"
                [ngClass]="{'has-val': workExpForm.get('company.name').value, 'has-error': workExpForm.get('company.name')?.invalid && shouldShowValidationErrors(i)}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right"></div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <input type="text" class="form-control" formControlName="name">
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>workExperience.companyName</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="workExpForm.get('company.name')?.invalid && shouldShowValidationErrors(i)"
                    translate>validationMessages.required</span>
                </div>
              </div>
              <!-- Location -->
              <div formGroupName="company" class="form-group focus-container" id="location" [ngClass]="{'has-val': workExpForm.get('company.fullLocation').value,
                            'has-error': shouldShowValidationErrors(i) && (workExpForm.get('company.fullLocation').value == '' ||
                                        workExpForm.get('company.city').errors ||
                                        workExpForm.get('company').hasError('InvalidLocationError'))}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <input type="text" class="form-control" #googlePlace placeholder="" formControlName="fullLocation"
                    (keyup)="LocationClearOnChange($event, i)">
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>workExperience.location</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="(workExpForm.submitted || false) && workExpForm.get('company.fullLocation').value == ''">
                    Required</span>
                  <span class="error-message" *ngIf="(workExpForm.submitted || false) && workExpForm.get('company.city').errors?.required &&
                                                !workExpForm.get('company.fullLocation').errors &&
                                                !workExpForm.get('company').hasError('InvalidLocationError')">
                    City required</span>
                  <span class="error-message"
                    *ngIf="(workExpForm.submitted || false) && workExpForm.get('company').hasError('InvalidLocationError')">
                    {{ workExpForm.get('company').errors?.InvalidLocationError }}</span>
                </div>
              </div>
              <!-- Dates -->
              <div class="form-group focus-container">
                <div class="col-lg-2 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-8 col-md-8 col-sm-6">
                  <div class="row">
                    <!-- From Date -->
                    <div formGroupName="from" class="col-lg-6 margin-bo-mo-10">
                      <div class="row" style="display:flex; align-items:center;">
                        <div class="col-lg-3 col-md-2 col-xs-3 alignment-right">
                          <label class="control-label label-bot-bit" translate>workExperience.from</label>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding"
                          [ngClass]="{'has-error': workExpForm.get('from.year')?.invalid && shouldShowValidationErrors(i)}">
                          <p-dropdown [options]="fromYearOpts" formControlName="year" [filter]="true" placeholder="YYYY"
                            [ngClass]="{'has-val': workExpForm.get('from.year').value}">
                            <ng-template let-year pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{year.label}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding">
                          <p-dropdown [options]="monthOpts" formControlName="month" [filter]="true" placeholder="MM">
                            <ng-template let-it pTemplate="selectedItem">
                              <span style="vertical-align:middle; float:left;">{{it.label | translate |
                                slice:0:3}}</span>
                            </ng-template>
                            <ng-template let-month pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{month.label | translate}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                      </div>
                    </div>

                    <!-- To Date -->
                    <div formGroupName="to" class="col-lg-6 margin-bo-mo-10">
                      <div class="row to-date" style="display:flex; align-items:center;">
                        <div class="col-lg-3 col-md-2 col-xs-3 col-lg-offset-1 alignment-right">
                          <label class="control-label label-bot-bit" translate>workExperience.to</label>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding"
                          [ngClass]="{'has-error': workExpForm.get('to.year')?.invalid && shouldShowValidationErrors(i)}">
                          <p-dropdown [options]="toYearOpts" formControlName="year" [filter]="true" placeholder="YYYY"
                            [ngClass]="{'has-val': workExpForm.get('to.year').value}"
                            (onChange)="onToYearSelect(i, workExpForm)">
                            <ng-template let-it pTemplate="selectedItem">
                              <span style="vertical-align:middle; float:left;">{{it.label | translate}}</span>
                            </ng-template>
                            <ng-template let-year pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{year.label | translate}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding">
                          <p-dropdown [options]="monthOpts" formControlName="month" [filter]="true" placeholder="MM">
                            <ng-template let-it pTemplate="selectedItem">
                              <span style="vertical-align:middle; float:left;">{{it.label | translate |
                                slice:0:3}}</span>
                            </ng-template>
                            <ng-template let-month pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{month.label | translate}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Experience Field -->
              <div class="form-group focus-container has-feedback"
                [ngClass]="{'has-error': workExpForm.get('exp_field')?.invalid && shouldShowValidationErrors(i)}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right"></div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <p-dropdown [options]="expFields" optionLabel="name" [filter]="true" placeholder=" "
                    formControlName="exp_field" [ngClass]="{'has-val': workExpForm.get('exp_field')?.value?.id}"
                    (onChange)="selectExpField($event, i)">
                    <ng-template let-expf pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;min-height: 25px;">
                        <div style="font-size:14px;float:left;margin-top:4px">{{expf.label}}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>workExperience.experienceField</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="workExpForm.get('exp_field')?.invalid && shouldShowValidationErrors(i)"
                    translate>validationMessages.required</span>
                </div>
              </div>
              <!-- Job Title -->
              <div class="form-group focus-container has-feedback"
                [ngClass]="{'has-error': workExpForm.get('job_title')?.invalid && shouldShowValidationErrors(i)}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right"></div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <ng-select [items]="job_titles?.items" [virtualScroll]="true" formControlName="job_title"
                    [loading]="job_titles?.loading" bindLabel="name" [addTag]="addNewJobTitle"
                    addTagText="{{'shared.addNewItem' | translate}}"
                    notFoundText="{{'shared.noItemsFound' | translate}}" [dropdownPosition]="'bottom'"
                    (scrollToEnd)="loadMoreJobTitles()" (search)="job_titles.search($event)"
                    [searchFn]="customSearchJobTitle"
                    (keyup.delete)="deleteJobTitleAutoComplete(workExpForm.get('job_title'))"
                    [ngClass]="{'has-val': workExpForm.get('job_title')?.value?.id}"
                    class="form-control ng-select-autocomplete" (change)="selectJobTitle($event, i)">
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                      <span class="ng-value-label">{{item.name}}</span>
                      <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                        <i class="fa fa-times" aria-hidden="true"></i>
                      </span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                      {{item.name}}
                    </ng-template>
                  </ng-select>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>workExperience.jobTitle</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="workExpForm.get('job_title')?.invalid && shouldShowValidationErrors(i)"
                    translate>validationMessages.required</span>
                </div>
              </div>
              <!-- Description -->
              <div class="form-group focus-container">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right"></div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding"
                  [ngClass]="{'has-val': workExpForm.get('description').value}">
                  <textarea class="form-control" formControlName="description" rows="3"></textarea>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>workExperience.description</label>
                </div>
              </div>

              <!-- Employment Types (multi-select) -->
              <div class="form-group focus-container">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right"></div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <p-multiSelect [options]="employmentTypes" optionLabel="name" formControlName="employment_types"
                    defaultLabel=" " [filter]="true" filterBy="label,value.name"
                    [ngClass]="{'has-val': workExpForm.get('employment_types')?.value && workExpForm.get('employment_types')?.value.length}"
                    [displaySelectedLabel]="true" [showToggleAll]="false" [ngStyle]="{width: '100%'}"
                    styleClass="form-control custom-p-multiselect" [showTransitionOptions]="'1ms'"
                    [hideTransitionOptions]="'2ms'">
                  </p-multiSelect>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>workExperience.employmentType</label>
                </div>
              </div>
              <div class="minamize-certification" (click)="minimize(i)">
                <i class="fa fa-angle-up" [ngClass]="{'fa-angle-down': isMinimized[i]}" aria-hidden="true"></i>
              </div>
            </form>
          </div>
        </div>
        <div class="col-md-1 d-flex align-items-end justify-content-center pl-3">
          <button type="button" class="btn btn-gray" (click)="addWorkExp()">
            <i class="fa fa-plus"></i>
          </button>
          <button *ngIf="i > 0" (click)="removeWorkExp(i)" class="btn btn-delete btn-delete-big">
            <i class="fa fa-trash" aria-hidden="true"></i>
          </button>
        </div>
      </div>
    </ng-container>
    <div class="row form-group text-right div-margin-top-40">
      <div class="col-sm-10 col-xs-12">
        <button type="button" class="btn btn-primary btnNext" (click)="prevWorkExp()" style="margin-right: 10px;">
          <i class="fa fa-arrow-left"></i> <span translate>shared.previous</span>
        </button>
        <button type="button" class="btn btn-secondary btnExit" (click)="exitWorkExp()" style="margin-right: 10px;"><i class="fa fa-close"></i>
          <span translate>shared.exit</span>
        </button>
        <button  style="margin-right:20px;" type="submit" class="btn btn-primary btnNext" (click)="saveAndExitWorkExp()"><i class="fa fa-save"></i>
          <span translate>shared.saveAndExit</span>
        </button>
      </div>
    </div>
  </div>
</div>
