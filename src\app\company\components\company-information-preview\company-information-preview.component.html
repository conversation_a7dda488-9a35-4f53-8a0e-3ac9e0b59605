
<div class="container-fluid">

<div class="CForm">
    <form class="form-horizontal validate-form details" style="background-color: white;" *ngIf= "have_Data.length != 0">
        <div class="page-title"><i class="fa fa-user" aria-hidden="true"></i>
            <a (click)="back()" style="cursor: pointer;text-decoration: none !important;"><span translate>company_form.company_information</span> </a>
            <span style="margin: 0 10px;color: orange;">|</span> <span translate>company_form.preview</span>
        </div>
        <div class="row clearfix flex-row info1">
            <div class="col-sm-9 col-xs-12 flex-order-sm-2">

                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.companyName</p>
                    </div>
                    <div class="col-xs-7 focus-no-padding">
                        <p class="preview"> {{ company_name || " " }} </p>
                    </div>
                </div>

                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.companyIndustry</p>
                    </div>
                    <div class="col-xs-7  focus-no-padding" *ngIf="company_industry.length">
                        <p class="preview"> {{company_industry2 || " "}} </p>
                    </div>
                </div>

                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.companySpecialities</p>
                    </div>
                    <div class="col-xs-7  focus-no-padding" *ngIf="company_specialities.length">
                        <p class="preview">{{company_specialities2 || " "}}</p>
                    </div>
                </div>

                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.type</p>
                    </div>
                    <div class="col-xs-7  focus-no-padding" *ngIf="company_type.length">
                        <p class="preview">{{ company_type || " " }}</p>
                    </div>
                </div>

                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.companyWebsite</p>
                    </div>
                    <div class="col-xs-7 focus-no-padding">
                        <p class="preview">
                            <a class="break-long-url" href="{{company_website}}" target="_blank">{{ company_website || " " }}</a>
                        </p>
                    </div>
                </div>


               
            </div>
            <div class="col-sm-3 col-xs-12 flex-order-sm-1">
                <div class="user-pro-pic text-center">
                    <img  [src]="image_uploaded_url" class="img-responsive">
                </div>
            </div>
            <div style="clear: both;"></div>
            <div class="col-sm-9 col-xs-12 flex-order-sm-2">
                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>Accept Direct CV Applying</p>
                    </div>
                    <div class="col-xs-7  focus-no-padding" *ngIf="show_apply_button !== null">
                        <p class="preview">
                            <span *ngIf="show_apply_button===1">Yes</span>
                            <span *ngIf="show_apply_button===0">No</span>
                        </p>
                    </div>
                </div>
                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate >company_form.companyDescription</p>
                    </div>
                    <div class="col-xs-7  focus-no-padding">
                        <p class="preview desc-justify" style="text-align: justify !important;  text-justify: inter-word;" [innerHTML]="company_description"></p>
                    </div>
                </div>
    
                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.companySize</p>
                    </div>
                    <div class="col-xs-7  focus-no-padding" *ngIf="company_size.length">
                        <p class="preview">{{ company_size || " " }}</p>
                    </div>
                </div>
    
                <div class="custom-row clearfix">
                    <div class="col-xs-5 left-col-preview-alignright">
                        <p class="info" translate>company_form.founded</p>
                    </div>
                    <div class="col-xs-7 focus-no-padding" *ngIf="company_founded.length">
                        <span style="display: flex;" class="preview"> 
                            <span *ngIf="company_founded_is_month===1" translate> {{company_founded[0].month}}/</span>
                            {{company_founded[0].year}}
                        </span>
                    </div>
                </div>
                <div class="custom-row clearfix">
                    <div class="col-xs-5  left-col-preview-alignright">
                        <p class="info" translate>company_form.socialMedia</p>
                    </div>
                    <div *ngIf="social_type.length" class="col-xs-7 focus-no-padding">
                        <p *ngFor="let links of company_links; let i = index;" class="preview">
                            <img *ngIf="links.social_media.name !== ''" src="./assets/images/Social Media/{{links.social_media.name}}.png" style="width:20px ; height: 20px ;vertical-align:middle;" />
                            <span class="secondaryp" style="vertical-align:middle">{{links.social_media.name}} </span> <a class="break-long-url" href="{{links.company_social_media_info}}" target="_blank">{{links.company_social_media_info}}</a> </p>
                    </div>
                </div>
                
            </div>
            <div style="clear: both;"></div>
            <div class="col-xs-12 flex-order-sm-2">
                <a class="btn btn-primary btn-block editStyle" (click)="back()">
                    <i class="fa fa-pencil"></i> <span translate>shared.edit</span>
                </a>
            </div>
        </div>
    </form>
</div>
</div>