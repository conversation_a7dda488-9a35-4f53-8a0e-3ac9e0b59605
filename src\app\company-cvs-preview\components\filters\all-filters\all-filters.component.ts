import { Component, OnInit, ViewChild } from '@angular/core';
// import { AllFiltersDataModel } from './AllFiltersDataModel';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { GeneralService } from '../../../../general/services/general.service';
import { FiltersService } from '../../../services/filters.service';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
import { Options } from 'ng5-slider';
import { DataMap } from 'shared/Models/data_map';
declare var $: any;

@Component({
  selector: 'app-all-filters',
  templateUrl: './all-filters.component.html',
  styleUrls: ['./all-filters.component.css']
})
export class AllFiltersComponent implements OnInit {
  // public dataModel: AllFiltersDataModel;
  filtersForm:FormGroup;
 // filters = {};
  initFilters = {};
  initFiltersTags = [];
  tags = [];

   //backupfilters used in case user changed some filters then clicked on cancel modal without 
  //clicking on apply filters, so we reset the filters to last applied state
  backupfilters = {
    "filters":{},
    "tags":[]
  }
  
  filtersLists:any;
  skillsDD:any;
  jobAdvTitleDD:any;
  genderOpts  = [
     {'option' : 'any' , 'title' : 'Any'},
     {'option' : 'male' , 'title' : 'Male'},
     {'option' : 'female' , 'title' : 'Female'},
  ];

  ageValue = 20;
  ageHighValue = 60;
  ageOptions: Options = {
     floor: 18,
     ceil: 80,
     step: 1
  };
  // ageRangeValues: number[] = [20,60];

  distanceTool = [
    { 'value': '', 'label': '' },
    { 'value': 'km', 'label': 'KM' },
    { 'value': 'mi', 'label': 'Miles' }
  ];

  @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: any;
  data_map = new DataMap();
  
  constructor(
    private fb: FormBuilder,
    private generalService:GeneralService,
    private filtersService:FiltersService,
    private lazyloadDropdownService:LazyloadDropdownService) {
    // this.dataModel = new AllFiltersDataModel();
    
   }

  ngOnInit(): void {
    this.initializeForm();
    this.sendInitStateToWarapper();

    this.generalService.internalMessage.subscribe( (data) => {
      if (data['message'] === 'removedFilters' && data['dist'] === 'all_filters') {
        if(data['mData']['removedFilters'].length>0){
          data['mData']['removedFilters'].forEach( (name) => {
            this.filtersForm.controls[name].setValue(this.initFilters[name]);
         });
        }
        if(data['mData']['removedArrayTypeFilters'].length>0){
          data['mData']['removedArrayTypeFilters'].forEach( (filter) => {
            this.filtersForm.controls[filter.filterName].setValue(filter.filterValue);
         });
        }


        this.setTags();
      }

      if (data['message'] === 'clearAllFilters' && data['src'] === 'filters-wrapper'){
        this.clearFilters();
      }

      if (data['message'] === 'modalCancel' && data['src'] === 'filters-wrapper'){
        this.filtersForm.patchValue(this.backupfilters.filters);
        this.tags = this.backupfilters.tags;
      }
    });
  }

  ngAfterViewInit() {
    this.filtersService.getResumeFiltersLists().subscribe(res => {
      this.filtersLists = res;
    });
    this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,1);
    this.jobAdvTitleDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_adv_title',10,1);

    setTimeout(() => {
      this.getLocationPlacefromLocationAutoComplete();
    }, 5000);
  }
  

  initializeForm(){
    this.filtersForm = this.fb.group({
      skills: [[]],
      languages: [[]],
      // exp_fields: [[]],
      nationalities: [[]],
      gender: [{'option' : 'any' , 'title' : 'Any'}],
   //   age: [[this.ageValue, this.ageHighValue]],
      // age: this.fb.group({
      //   age_value: [this.ageRangeValues],
      //   selected:[false]
      // }),
      age: this.fb.group({
        age_value: [[this.ageValue, this.ageHighValue]],
        selected:[false],
        from: [this.ageValue],
        to:[this.ageHighValue]
      }),
      open_to_work: [{"id":null,"name":""}],
      job_types: [[]],
      job_adv_titles: [[]],
      // years_of_exps: [{"id":null,"name":""}],
      // degree_level: [{"id":null,"name":""}],
      // educations: [[]],
      location : this.fb.group({
        country:[''],
        city:[''],
        full_location: [''],
        latitude: [''],
        longitude: [''],
        distance: [''],
        unit_distance: [{ "value": "", "label": "" }]
      })
    });
  }

  setTags(){
    let location = '';
    if(this.locationControl.controls['distance'].value !=='' && this.locationControl.controls['unit_distance'].value !== '')
      location = this.locationControl.controls['full_location'].value + ', distance:'+ this.locationControl.controls['distance'].value + this.locationControl.controls['unit_distance'].value.label;
    else 
      location = this.locationControl.controls['full_location'].value;

    this.tags = [
      {"name":"skills" ,"title":"Skills", "value":this.filtersForm.controls['skills'].value , "type":"array"},
      {"name":"languages" ,"title":"Languages", "value":this.filtersForm.controls['languages'].value , "type":"array"},
      // {"name":"exp_fields" ,"title":"Experience Field", "value":this.filtersForm.controls['exp_fields'].value , "type":"array"},
      {"name":"nationalities" ,"title":"Nationalities", "value":this.filtersForm.controls['nationalities'].value , "type":"array"},
      {"name":"gender" ,"title":"Gender", "value":this.filtersForm.controls['gender'].value.title , "type":"string"},
      {"name":"age" ,"title":"Age", "value":this.filtersForm.controls['age'].value.age_value , "type":"string"},
    //  {"name":"age" ,"title":"Age", "value":this.filtersForm.controls['age'].value , "type":"string"},
      {"name":"open_to_work" ,"title":"Open to work", "value":this.filtersForm.controls['open_to_work'].value.name , "type":"string"},
      {"name":"job_types" ,"title":"Job types", "value":this.filtersForm.controls['job_types'].value , "type":"array"},
      {"name":"job_adv_titles" ,"title":"Job adv title", "value":this.filtersForm.controls['job_adv_titles'].value , "type":"array"},
      // {"name":"years_of_exps" ,"title":"Years of Experience", "value":this.filtersForm.controls['years_of_exps'].value.name , "type":"string"},
      // {"name":"degree_level" ,"title":"Degree Level", "value":this.filtersForm.controls['degree_level'].value.name , "type":"string"},
      // {"name":"educations" ,"title":"Educations", "value":this.filtersForm.controls['educations'].value , "type":"array"},
      {"name":"location" ,"title":"Location", "value":location , "type":"string"},
    ]
    return this.tags;
  }

  sendFilters() {
    this.generalService.notify('filters-changes', 'all_filters', 'filters-wrapper', {"filters":this.filtersForm.value, "tags":this.setTags()});
    
    this.backupfilters.filters = this.filtersForm.value;
    this.backupfilters.tags = this.tags;

    $('#filtersModal').modal('hide');
   }
  
   sendInitStateToWarapper() {
    this.initFilters = this.filtersForm.value;
    this.initFiltersTags = this.setTags();
    this.backupfilters.filters = this.initFilters;
    this.backupfilters.tags = this.initFiltersTags;
      
    this.generalService.notify('init-filters','all_filters', 'filters-wrapper', {'filters':this.initFilters,'tags':this.initFiltersTags} );
   }

   clearFilters(){
    this.filtersForm.patchValue(this.initFilters);
    this.tags = this.initFiltersTags;
   }

   clearAllFilters(){
    this.clearFilters();
  //  this.generalService.notify('clearAllFiltersModal','all_filters', 'filters-wrapper', {} );
    // $('#filtersModal').modal('hide');
   }

   handleAgeChange($event) {
    //$event.value is the new value
    this.ageControl.controls['selected'].setValue(true);
    this.ageControl.controls['from'].setValue(this.ageControl.controls['age_value'].value[0]);
    this.ageControl.controls['to'].setValue(this.ageControl.controls['age_value'].value[1]);
   }

   get ageControl() {
    return (this.filtersForm.controls['age'] as FormGroup);
   }

   get locationControl() {
    return (this.filtersForm.controls['location'] as FormGroup);
   }


  // Start location functions
  private getLocationPlacefromLocationAutoComplete() {
    const autocomplete_location = new google.maps.places.Autocomplete(this.googlelocationplaceLocationRef.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']
      });

    autocomplete_location.addListener('place_changed', (res) => {
      const place_location = autocomplete_location.getPlace();
      this.getLocationAddressInfo(place_location);
    });
  }

  getLocationAddressInfo(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place);
    this.locationControl.controls['country'].setValue(data_location.country);
    this.locationControl.controls['city'].setValue(data_location.city);
    let location = data_location.country;
    if(data_location.city)
      location = location + ', ' + data_location.city;
    this.locationControl.controls['full_location'].setValue(location);
    this.locationControl.controls['latitude'].setValue(data_location.latitude);
    this.locationControl.controls['longitude'].setValue(data_location.longitude);
  }
  // End location functions
}
