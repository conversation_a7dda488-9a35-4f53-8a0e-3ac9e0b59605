
 .modal-body {
max-height: unset !important;
overflow: unset;
}

.dropdown {
  float: right;
  margin-top:10px
}
.dropdown button.btn-secondary {
  border: 2px solid #3d7bce;
  /* border-left: none; */
  border-top: none;
  /* background-color: #dbe2ec; */
  font-weight: 600;
  color: #3d7bce;
  padding: 9px 20px;
  border-radius: 0;
  margin-right: -22px;
  margin-top: -10px;
  transition: all 0.5s ease-in-out;
}

.dropdown button.btn-secondary:hover {
  color:#FCAB42;
  /* background-color: #d9d9d9; */
}

:host ::ng-deep  app-notification .dropdown {
  border: 2px solid #3d7bce;
  border-right: none;
  border-top: none;
}


.dropdown.open button.btn-secondary {
  background-color: #d9d9d9;
  font-weight: 700;
}
div.dropdown-menu {
  right:-20px;
  left: unset;
  min-width: 250px;
}

div.dropdown-menu ul {
  padding: 20px;
}

div.dropdown-menu li.dropdown-item {
 padding: 10px 5px;
 margin-left: 10px;
 cursor: pointer;
 transition: all 0.5s ease-in-out;
}

div.dropdown-menu li.dropdown-item:hover {
  background-color: #f1f1f1;
  font-weight: bold;
  /* font-size: 15px; */

}

div.dropdown-menu li.dropdown-item a {
  text-decoration: none;

}

.table {
  width: calc(100%-5px);
}

.modal-title {
  color: rgba(51, 25, 112, 0.644);
}

.modal-content {
  border-radius: 1%;

}
#modal .modal-content {
  width: 552px;
  left: 30px;
  right:-30px;
}
.modal-content[_ngcontent-c4] {
  border-radius: 1%;
}


.ui-table .ui-table-thead>tr>th{
  color: #3D7BCE !important;
}


@media (min-width: 992px) {
.collapsed.col-md-9 {
    width: 77%;
    margin-left: -50px;
}

.expanded.col-md-9 {
  width: 90%;
  margin-left: 130px;
}
}


@media (min-width: 768px){
.modal-content {
  width: 600px;
}
}



@media (min-width: 768px) {
  .modal-dialog {
  width: 50%;
}
}


.modal-header .close:hover {

  color: black;
  font-size: 25px;

}



.container{
  width:100%;
  height: 100%;
  /* background-color: #4241501f; */
  background-color: #f5f5f5;
  min-height: 655px;
}

.col-md-9 {
  padding-left: 0px;
  padding-right: 0px;
  margin-left: -20px;
}

div.modal-header>button>span {
  font-size: 30px;
}


#modal .modal-footer  {
  padding: 32px;
  text-align: center;
  /* border-top: none;  */
}


div, p, h1, h2, h3, a , ul{
  font-family: 'Exo2-Regular', sans-serif;
}

