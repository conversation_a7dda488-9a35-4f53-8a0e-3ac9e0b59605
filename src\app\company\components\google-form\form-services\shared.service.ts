import { Injectable } from '@angular/core';
import {BehaviorSubject} from 'rxjs/BehaviorSubject';

@Injectable()
export class SharedService {

  constructor() { }
  private objOrder = new BehaviorSubject<any>(null) ;
  private changeCss = new BehaviorSubject<any>(null) ;
  currentValue = this.objOrder.asObservable();
  cssValue = this.changeCss.asObservable();

  changeOrder(order: any) {
    this.objOrder.next(order);
  }

  changeActiveClass(active:any)
  {
    this.changeCss.next(active);
  }

}
