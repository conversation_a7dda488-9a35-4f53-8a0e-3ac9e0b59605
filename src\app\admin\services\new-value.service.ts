import { Observable } from 'rxjs/observable';
import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
@Injectable()
export class NewValueService {
  url = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.url     = data + 'admin/field?type=';
          this.baseUrl = data + 'admin/field/';
    });
   }

   getAllItems(type) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + type, { headers }) ;
  }


  getDDlData(type) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + 'get_data_ddl?type=' + type, { headers }) ;
  }


  createItem(type, item) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + type, item, { headers });
  }


  updateItem(type, item, id) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.baseUrl + id + '?type=' +  type, item, { headers });
  }

  updateField( id, type, body ) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.baseUrl + id + '?type='  + type, body, { headers });
    // return this.http.post(this.baseUrl + 'update_value/' + id + '?type='  + type + '&field=' + field, body, { headers });
  }

  // deleteItem(type, itemId) {
  //   let headers = new HttpHeaders().set('Content-Type', 'application/json');
  //   return this.http.delete(this.url +  itemId + '?type=' + type, { headers });
  // }

  deleteItems(type, itemsIds) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.baseUrl +  'delete_values?type=' + type, itemsIds, { headers });
  }



}


// {
//   "url":"www.university.com",
//   "locations":[{
//       "country": "SYRIA",
// "city": "دمشق",
// "latitude": 35.520050600000000,
// "longitude": 35.789027600000054,
// "postal_code": "92",
// "street_address": "main street",
// "country_code": "SY"
//   }],
//   "institution_trans":[{
//       "name":"university",
//       "translated_language_id":1
//   },
//   {
//       "name":"جامعة",
//       "translated_language_id":2
//   }]
// }
