import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import {Subject} from 'rxjs/Subject';
import 'rxjs/add/operator/takeUntil';
import { CompanyFormService } from '../../services/company-form.service';
import {LangChangeEvent, TranslateService} from '@ngx-translate/core';
import { DataMap } from "shared/Models/data_map";
// import { ImageProcessingService } from "shared/shared-services/image-processing.service";
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';

@Component({
  selector: 'app-company-information-preview',
  templateUrl: './company-information-preview.component.html',
  styleUrls: ['./company-information-preview.component.scss']
})
export class CompanyInformationPreviewComponent implements OnInit, OnD<PERSON><PERSON> {
  image_uploaded_url = './assets/images/Capture.PNG';

  private ngUnsubscribe: Subject<any> = new Subject();
  subscription: Subscription;
  currentYear = (new Date()).getFullYear();
  current_language = Number (localStorage.getItem('current_company_language'));
  companyId = Number (localStorage.getItem('company_id'));
  companyProfileId: number = 0;
  company_name: string = '';
  username='';
  company_links;
  company_industry = [];
  have_Data = [];
  company_industry2 :any = [];
  company_specialities = [];
  company_specialities2 :any = [];
  companyTypeOpt = [];
  companySizeOpt = [];
  company_type = [];
  company_size = [];
  social_type: any = [];
  company_website = '';
  show_apply_button = 1;
  company_description = '';
  company_founded = [];
  company_founded_is_month = 1;
  edit_status = true;
  new_language;
  languageProfile = [];
  languageForm =[];
  data_map = new DataMap();
  // fileUrl: any = '';
  constructor(private route: ActivatedRoute,
              private router: Router,
              private translate: TranslateService,
              private imageProcessingService : ImageProcessingService,
              private companyFormService: CompanyFormService,
              private privateSharedURL: ExportUrlService ) {
                this.route.parent.parent.params.subscribe(res => {
                  this.username = res['username'];
                  this.have_Data = [];
                });
              }

setcompanyLanguage(langaugeId) {
  if (langaugeId === 1) {
    this.translate.setDefaultLang('en');
    
  } else if (langaugeId === 2) {
    this.translate.setDefaultLang('ar');
   
  } else {
    this.translate.setDefaultLang('en');

  }
  
}

/*--- fill up the interface with the available data ---*/

BasicForm(data_get) {
  let data = data_get;
  let company_type_id;
  
  let x = data['existing_profile_data'].industries;
  for (let i = 0 ; i < x.length ; i++) {
   this.company_industry.push(x[i].name);
  }
  this.company_industry2 = this.company_industry.join(', ');
  let y = data['existing_profile_data'].specialties;
  for (let i = 0 ; i < y.length ; i++) {
   this.company_specialities.push(y[i].name);
  }
  this.company_specialities2 = this.company_specialities.join(', ');
  // let x = data['existing_profile_data'].company_industries_for_companies;
  // for (let i = 0 ; i < x.length ; i++) {
  //  this.company_industry.push(x[i].company_industry.company_industry_translation[0].name);
  // }
  // this.company_industry2 = this.company_industry.join(', ');
  // let y = data['existing_profile_data'].company_specialties_for_companies;
  // for (let i = 0 ; i < y.length ; i++) {
  //  this.company_specialities.push(y[i].specialty.specialties_translation[0].specialty_translation_name);
  // }
  // this.company_specialities2 = this.company_specialities.join(', ');

  this.companyProfileId = data['existing_profile_data'].id;
  this.company_name = data['existing_profile_data'].company_profile_translations[0].name;
  this.company_description = data['existing_profile_data'].company_profile_translations[0].company_description;
  this.company_website = data['company_website'];
  this.show_apply_button = data['show_apply_button'];
  if(data['existing_profile_data'].path_company_imagelogo) {
    this.image_uploaded_url = this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail',data_get['existing_profile_data'].path_company_imagelogo);
  //  this.image_uploaded_url = this.fileUrl + 'storage/company/logo/'+ data_get['existing_profile_data'].path_company_imagelogo;
  }
  const    social_media_selected = data['existing_profile_data'].company_social_medias;
  const  company_description = data['existing_profile_data'].company_profile_translations[0].company_description;
  const  company_website = data['company_website'];
  const  founded_date  = data['founded'];
  const company_size_id = data['existing_profile_data'].company_size_id;

  //if ( data['existing_profile_data'].company_type_id !== null || data['existing_profile_data'].company_type !== null) 
  if ( data['existing_profile_data'].company_type_id !== null) {
    if(data['existing_profile_data'].company_type !== null)
        company_type_id = data['existing_profile_data'].company_type.company_type_translations[0].company_type_id;
  }

  this.company_founded_is_month=data['is_month'];
  if(founded_date !== null) {
    let date = founded_date.split("-");
    for (let i = 0 ; i < this.data_map.monthOpts.length ; i++) {
      if(date[1]===this.data_map.monthOpts[i].value)
        {
          date[1] = this.data_map.monthOpts[i].label
        }
    }
    this.company_founded.push({
      'month' : date[1],
      'year' : date[0]
    });
  }

  if(data['existing_profile_data'].company_type !== null)
    this.company_type.push(data['existing_profile_data'].company_type.company_type_translations[0].company_type_name);
  if ( data['existing_profile_data'].company_size !== null) {
  this.company_size.push(data['existing_profile_data'].company_size.company_size_translation[0].name);
  }
  this.company_links = data['existing_profile_data'].company_social_medias;
  for (let i = 0 ; i < this.company_links.length ; i++){
    this.social_type.push(this.company_links[i].social_media.name);
  }


}

back() {
  this.companyFormService.send_Data(this.have_Data);
  this.companyFormService.changeStatus(true);
  this.router.navigate(['/c',this.username,'profile','edit']);
 // this.router.navigate(['/c',this.username,'profile','new']);
}

/*---- here we handle the data which means that if the data
       is passed form the company form or it has been received here  */

buildFilledForm() {

  if ( this.have_Data[0].existing_profiles.length > 1) {
    if (this.have_Data['language_passed'] !== undefined) {

      this.setcompanyLanguage(this.have_Data['language_passed']);
      for ( let i = 0 ; i < this.have_Data[0].existing_profiles.length; i++ ) {
        if(this.have_Data['language_passed'] === Number(this.have_Data[0].existing_profiles[i].translated_languages_id)) {

          this.BasicForm(this.have_Data[0].existing_profiles[i]);      
        }
      }
    } else {

      this.setcompanyLanguage(this.current_language);
      for ( let i = 0 ; i < this.have_Data[0].existing_profiles.length; i++ ) {
        if(this.current_language === Number(this.have_Data[0].existing_profiles[i].translated_languages_id)) {

          this.BasicForm(this.have_Data[0].existing_profiles[i]);
        }
      }
    }

  } else {
    this.setcompanyLanguage(this.have_Data[0].existing_profiles[0].translated_languages_id);
    this.BasicForm(this.have_Data[0].existing_profiles[0]);
  }

}


/*---- here we recevie the data from the backend ----*/

getProfiles_route() {

  this.companyFormService.getProfiles(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
  (res) => {

    if(res['translation'] !== undefined) {
      let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
      this.have_Data = send_Data_to_preview;
    }

    this.buildFilledForm();
    
  });
  
  
}
ngOnInit() {
/*--- here we check if we have passed data form the company form */
  // this.privateSharedURL.publicUrl.take(1).subscribe(data => {
  //   this.fileUrl = data;
  // });
    this.subscription =  this.companyFormService.Data.takeUntil(this.ngUnsubscribe).subscribe(val => {
      this.have_Data = val;

      if (this.have_Data.length !== 0) {

        this.buildFilledForm();
      } else {

        this.getProfiles_route();
      }
    });
    this.subscription.unsubscribe();
  

}

  ngOnDestroy() {
    
      this.ngUnsubscribe.next();
      this.ngUnsubscribe.complete();
    }

}
