<!-- start of reply form -->
<div *ngIf="company.id !== null  && company.message === ''" >
  <img  alt="Logo" [src]="getImageLogo(company.logo)" />
  <table class="table " id="msg-details">
    <caption class="text-center table-caption">Company Details</caption>
    <thead>
      <tr>
        <th  class="text-right">Company ID     </th>
        <td>{{ company.id }}   </td>
      </tr>
      <tr>
        <th  class="text-right">Company Name  </th>
        <td>
          <!-- <a (click)="openCompanyProfile()">{{ company.name }}</a> -->
          <!-- <a [routerLink]="['/i/c/user.emp/' + company.id ]" target="_blank">{{ company.name }}</a> -->
          <a [routerLink]="['/i/c/' + company.username ]" target="_blank">{{ company.name }}</a>
        </td>
      </tr>
    </thead>
    <tbody>
      <tr>
        <th class="text-right">Company Industry</th>
        <td>{{company.industry}}</td>
      </tr>
      <tr>
        <th class="text-right">Location</th>
        <td>{{ company.country }}, {{ company.city}}</td>
      </tr>
      <tr>
        <th class="text-right">Description</th>
        <td *ngIf="!displayMore" >
          <span [innerHTML]="company.description | summary:120" style="margin-bottom: -8px;" ></span><span class="control-view" *ngIf="company.description.length > 120" (click)="displayMore = true">Read More</span></td>
        <td *ngIf="displayMore" >
          <span  [innerHTML]="company.description" style="margin-bottom: -8px;" ></span><span class="control-view" *ngIf="company.description.length > 120" (click)="displayMore = false">View Less</span></td>
      </tr>
      <tr>
        <th class="text-right">Website</th>
        <td>{{ company.website }}</td>
      </tr>
<!--       <tr>
        <th class="text-right">Profile</th>
        <td>{{ company.profile }}</td>
      </tr> -->
      <tr>
        <th class="text-right">email</th>
        <td>{{ company.email }}</td>
      </tr>
      <tr>
        <th class="text-right">Contact Number</th>
        <td>{{ company.mobile_number }}</td>
      </tr>
      <tr class="stared" style="border-bottom: none;">
        <th class="text-right">Current Status</th>
        <td>{{ company.status }}</td>
      </tr>
      <tr class="stared" style="border-top: none;">
        <th class="text-right">Handled By</th>
        <td>{{ company.handled_by }}</td>
      </tr>

    </tbody>

  </table>

  <hr>
  <form *ngIf="mode === 'preview_mode' && company.id !== null" [formGroup]="companyForm" >

      <table  class="table ">
        <caption class="text-center table-caption">Verification Procedure</caption>
        <tbody>
          <tr>
            <th>Method</th>
            <td colspan="2" class="border-right">
              <p-dropdown [options]="methods" formControlName="company_method_verification_id" (onChange)="setComId()"  [required]="true" [filter]="true">
                <ng-template let-title pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                        <div style="font-size:14px;float:left;margin-top:4px">{{title.label}}</div>
                    </div>
                </ng-template>
              </p-dropdown>
              <span class="alert alert-danger inline-alert" *ngIf="company_method_verification_id.touched && company_method_verification_id.invalid">required</span>
            </td>
          </tr>
          <tr>
            <th>Description</th>
            <td colspan="2" class="border-right">
              <p-editor formControlName="admin_description" [style]="{'height':'200px'}" id="answer" placeholder="" onTextChange="render()">
                <p-header>
                      <span class="ql-formats">
                        <button class="ql-bold" aria-label="Bold"></button>
                        <button class="ql-italic" aria-label="Italic"></button>
                        <button class="ql-underline" aria-label="Underline"></button>
                        <button class="ql-order" aria-label="Underline"></button>
                        <button aria-label="Ordered List" class="ql-list" value="ordered" type="button"></button>
                        <button aria-label="Bullet List" class="ql-list" value="bullet" type="button"></button>
                      </span>
                </p-header>
              </p-editor>
              <span class="alert alert-danger inline-alert" *ngIf="admin_description.touched && admin_description.invalid">required</span>
            </td>
          </tr>
          <tr>
            <th>Verification Status</th>
            <td colspan="2" class="border-right">
              <p-dropdown [options]="statuses" formControlName="company_status_id"  [required]="true" [filter]="true">
                <ng-template let-title pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                        <div style="font-size:14px;float:left;margin-top:4px">{{title.label}}</div>
                    </div>
                </ng-template>
              </p-dropdown>
              <span class="alert alert-danger inline-alert" *ngIf="company_status_id.touched && company_status_id.invalid">required</span>

            </td>
          </tr>

          <!-- <tr>
            <th>Show on home page</th>
            <td colspan="2" class="border-right">
              <input type="checkbox" formControlName="show_in_home_page" class="check-show-home">
            </td>
          </tr> -->

        </tbody>
      </table>

      <table  class="table ">
          <caption class="text-center table-caption">Permissions</caption>
        <tr>
          <th>Permissions</th>
          <td colspan="2" class="border-right">
            <p-multiSelect
              [options]="permissions"
              formControlName="permissions"
              optionLabel="name"
              [filter]="true"
              filterBy="label,value.name"
              styleClass="custom-p-multiselect"
              [showTransitionOptions]="'1ms'"
              [hideTransitionOptions]="'2ms'"
              defaultLabel=""
              [showToggleAll]=false>
            </p-multiSelect>
            <p class="alert alert-danger inline-alert under-control" *ngIf="companyForm.hasError('InvalidVerifiedPermissionError')">{{ companyForm.errors?.InvalidVerifiedPermissionError }}</p>
          </td>
        </tr>
      </table>

        <button type="submit"  class="btn btn-done submit-button"  [disabled]="companyForm.invalid" (click)="confirm(companyForm.value)" ><i class="fa fa-paper-plane"></i>confirm</button>
        <button type="button"  class="btn btn-default" (click)="cancel()" >Cancel</button>


        <!-- <p>{{ companyForm.value | json }}</p> -->


  </form>
  <form *ngIf="mode === 'install_plugin_mode' && company.id !== null" [formGroup]="companyInstallPluginForm" >

    <table  class="table">
      <caption class="text-center table-caption">Company Wordpress Domain URL </caption>
      <tbody>
        <tr>
          <th>Domain URL </th>
          <td colspan="2" class="border-right">
            <input type="text" [(ngModel)]="company.company_domain_url" formControlName="company_domain_url" />
            <span class="alert alert-danger inline-alert" *ngIf="companyInstallPluginForm.get('company_domain_url')?.invalid && companyInstallPluginForm.get('company_domain_url')?.touched">
             <span *ngIf="companyInstallPluginForm.get('company_domain_url').errors?.required">This field is required.</span>
             <span *ngIf="companyInstallPluginForm.get('company_domain_url').errors?.pattern">Must start with http:// or https://</span>
            </span>
          </td>
        </tr>
      </tbody>
    </table>

      <button type="submit"  class="btn btn-done submit-button"  [disabled]="companyInstallPluginForm.invalid" (click)="confirmInstallPlugin(companyInstallPluginForm.value)" ><i class="fa fa-paper-plane"></i>confirm</button>
      <button type="button"  class="btn btn-default" (click)="cancelInstallPlugin()" >Cancel</button>


      <!-- <p>{{ companyInstallPluginForm.value | json }}</p> -->
      


</form>
</div>

<div *ngIf="mode === 'preview_mode' && company.id === null  && company.message !== '' ">
  <p style="text-align: center;">This company does not have data yet.</p>
</div>
<!-- end of reply form -->


<!-- start of msg log -->
<div *ngIf="mode === 'log_mode'">
  <table class="table table-striped table-bordered log-table" id="msg-details">
    <caption class="text-center table-caption"> Log for Company: ({{ companyLog.name }})  with ID: {{companyLog.id }} </caption>
    <thead>
      <tr>
        <th class="text-left">Admin           </th>
        <th class="text-left">Action Performed</th>
        <th class="text-left">Company Status  </th>
        <th class="text-left">Permissions  </th>
        <th class="text-left">Date-Time       </th>
        <th class="text-left">Description     </th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngIf="companyLog.log.length !== 0">
        <tr *ngFor="let item of companyLog.log">
          <td>{{ item.admin }}      </td>
          <td>{{ item.method }}     </td>
          <td>{{ item.status }}     </td>
          <td>{{ item.permissions }}     </td>
          <td>{{item.date }}        </td>
          <td [innerHTML]=" item.description"></td>
         </tr>
      </ng-container>
       <tr *ngIf="companyLog.log.length === 0">
         <td colspan="5" class="text-center">This log is Empty </td>
       </tr>
    </tbody>
   </table>
</div>
<!-- end of msg log -->
