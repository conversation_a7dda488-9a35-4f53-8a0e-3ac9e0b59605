.actions-col {
    display: flex;
    align-items: center;
    flex-flow: column;
}

.actions-block {
    display: flex;
    flex-flow: column;
}

.actions-block .btn {
    margin-top: 2rem;
}

.modal-content {
    /* height: 450px; */
    overflow-y: scroll;
}

.actions-col {
    display: flex;
    align-items: center;
    flex-flow: column;
}

.actions-block {
    display: flex;
    flex-flow: column;
}

.actions-block .btn {
    margin-top: 2rem;
}

.modal-content {
    overflow-y: scroll;
}

.success {
    color: #30A03E;
}

.error {
    color: #a94442;
}

.container-fluid {
    padding-top: 20px;
    padding-bottom: 20px;
}

.cols-container {
    border: 1px solid #eaeaea;
    border-radius: 3px;
}

.cols-header {
    padding: 5px 5px;
    border-bottom: 1px solid #eaeaea;
    text-align: center;
    font-size: 18px;
    color: #276ea4;
}

.cols-body {
    padding: 10px 15px;
}

.check-block {
    display: flex;
    align-items: center;
    padding: 6px 0;
}

.check-block label {
    font-size: 15px;
    margin-bottom: 0;
    margin-left: 6px;
    cursor: pointer;
}

.check-block input[type=checkbox] {
    margin: 0;
    width: 17px;
    height: 17px;
}

.remainingColsMsg {
    font-size: 18px;
}


/* .modal-body {
    overflow: auto;
} */

.save-btn {
    margin-right: 12px;
}

.btn-txt {
    display: inline-block;
}

.btn-mar-right {
    margin-right: 5px;
}

.btn-mar-left {
    margin-left: 5px;
}

.btn-icon {
    display: inline-block;
}

.fixed-width-btn {
    width: 97px;
}

@media screen and (max-width:880px) {
    .remainingColsMsg {
        font-size: 14px;
    }
    .btn-txt,
    .cancel-btn {
        display: none;
    }
    .btn-mar-right,
    .btn-mar-left {
        margin: 0;
    }
    .save-btn {
        margin-right: 0;
    }
    .save-btn .btn-icon {
        font-size: 16px;
    }
    .div-margin-top-20 {
        margin-top: 0;
    }
    .fixed-width-btn {
        width: 42px;
    }
}