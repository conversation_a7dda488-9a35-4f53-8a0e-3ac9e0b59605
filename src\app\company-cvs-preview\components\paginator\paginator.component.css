.cust-paginator{
    background-color: #f4f4f4;
    border: 1px solid #c8c8c8;
}
.cust-paginator ul {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 0;
    padding: 0;
    background-color: #f4f4f4;
    border: 1px solid #c8c8c8;
    list-style-type:none;
}

li.page-block {
    list-style: none;
    cursor: pointer;
    /* border: 1px solid #ccc; */
    padding: 5px 15px;
    color:#848484;
    /* border-radius: 5px;
    box-shadow: 1px 1px 9px #f9f5f5; */
}
li.page-block:hover{
    background:#e0e0e0;
    color:#000;
}
li.page-block.active {
    background: #3D7BCE;
    color: #fff;
}
li.disabled{
    color:#CCCCCC;
    cursor:default;
}
li.disabled:hover{
    background: transparent;
    color:#CCCCCC;
}
.pages-status{
    font-weight: bold;
    margin:0 10px;
}
.block-visible{
    display:inline-block;
}
.block-hidden{
    display:none;
}