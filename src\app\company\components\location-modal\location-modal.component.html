<div class="modal-body-container">
    <div class="modal-body">
        <div class="page-content table-page-content">
            <form #form="ngForm" (keydown.enter)="$event.preventDefault()" [formGroup]="companyLocation" class="form-horizontal validate-form" (ngSubmit)="form.valid">
                <div class="row clearfix flex_row">

                    <div class="col-xs-11 custom-col-11">
                        <!--  <p style="color: #276ea4;" class="add-certification-p" translate>Add Location</p> -->
                        <div class="location">
                            <!-- <div class="form-group focus-container has-feedback">
                                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                                </div>
                                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                                    <input id="check-verify" type="checkbox" (change)="changAutoComplete($event)">

                                    <p style="display: inline;">Search Company with Google.</p>
                                </div>
                            </div> -->
                            <div class="form-group focus-container has-feedback" [ngClass]="{'has-error': form.submitted && !companyLocation.controls['name'].value }">
                                <!-- [ngClass]="{'has-error':form.submitted && !isDDValid('language_id')}" -->
                                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                                </div>
                                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding" [ngClass]="{'has-val':companyLocation.controls['name'].value}">
                                    <input formControlName="name" placeholder=" " type="text" class="form-control" >
                                    <!-- (keyup.enter)="currentLocationKeyUp($event)" -->
                                    <span class="custom-underline"></span>
                                    <!-- <span class="glyphicon  form-control-feedback" aria-hidden="true"></span> -->
                                    <span *ngIf="form.submitted && !companyLocation.controls['name'].value" style="left: 100%;
                                        top: 8px;" class="error-message" translate>validationMessages.required</span>
                                    <label class="control-label custom-control-label" translate>company_location.Name</label>
                                </div>
                                <div class="col-lg-3 col-md-2 col-sm-3">
                                    <!-- <span class="error-message" *ngIf="form.submitted && !isDDValid('language_id')" translate>validationMessages.required</span> -->
                                </div>
                            </div>

                            <div class="form-group focus-container" [ngClass]="{'has-error': form.submitted && ( !companyLocation.controls['location'].value || !companyLocation.controls['country_code'].value) }">
                                <!-- [ngClass]="{'has-error':form.submitted && !languageForm.controls['type'].valid}" -->
                                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                                </div>
                                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding" [ngClass]="{'has-val':companyLocation.controls['location'].value}">
                                    <input formControlName="location" placeholder=" " type="text" class="form-control" id="location" #googlelocationplace  (keyup)="clearLocationData()">
                                    <!-- (keyup.enter)="currentLocationKeyUp($event)" -->
                                    <span class="custom-underline"></span>
                                    <!-- <span class="glyphicon  form-control-feedback" aria-hidden="true"></span> -->
                                    <span *ngIf="form.submitted && !companyLocation.controls['location'].value" style="left: 100%;
                                    top: 8px;" class="error-message" translate>validationMessages.required</span>
                                    <span *ngIf="form.submitted && ( companyLocation.controls['location'].value && !companyLocation.controls['country_code'].value)" 
                                     class="error-message" translate>validationMessages.ChooseAutoCompleteSuggestionsError</span>
                                    <label class="control-label custom-control-label" translate>company_location.Location</label>
                                </div>
                                <div class="col-lg-3 col-md-2 col-sm-3">
                                    <!-- <span class="error-message" *ngIf="form.submitted && !isDDValid('language_id')" translate>validationMessages.required</span> -->
                                </div>
                            </div>
                            <!-- <div *ngIf="check_location" class="form-group focus-container" style="padding: 20px;">
                                <app-map [sub]="currentSub" birthOrCurrent="current" [type]="currentType" [search]="googlelocationplaceRef">

                                </app-map>
                            </div> -->
                            
                        </div>
                    </div>
                </div>
                <div class="form-group div-margin-top-70">
                    <div class="col-sm-12">
                        <div class="row">

                            <div class=" col-sm-4 col-sm-offset-4 text-center">
                                <button class="btn btn-success btn-block cust-btn" (click)="submit()">
                                    <i class="fa fa-floppy-o"></i>
                                    <span translate>shared.save</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- <p> {{ form.value | json }} </p> -->
            </form>
        </div>
    </div>
</div>