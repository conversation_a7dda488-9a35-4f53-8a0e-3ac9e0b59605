/* START HOME PAGE STYLES */
.home-page-content{
    margin-top:107px;
    /* margin-bottom:40px; */
  }

h2, h3{
  margin-top:0;
  margin-bottom:25px;
  font-size:26px;
}

.section{
  padding: 40px;
    /* padding-top:40px;
    padding-bottom:40px; */
}
.section-pad-left-right{
  padding-left:40px;
  padding-right:40px;
}
.section-bg{
  background-color: #f7f7f7;
}
.section .section-title{
  margin-top:0;
  margin-bottom:36px;
}
.flex-parent-text{
  display: flex;
  /* flex-direction: column; */
  align-items: center;
}
.flex-parent-text .flex-child{
  display: flex;
  align-items: center;
  flex: 1;
  /* align-self: center;
  justify-content:center; */
}
.flex-center-horizontal{
  justify-content: center;
}
.center-p p{
  text-align: center;
}

.equal .order-lg-2{
  order:2;
}
.equal .order-lg-1{
  order:1;
}

.cust-anchor{
  display: block;
  position: relative;
  top: -130px;
  visibility: hidden;
}
.jobs-section{
  padding:0  30px;
}
.expField-link{
  display:block;
  margin:5px 0;
  font-size: 18px;
}
/* h1 , h2 , h3 , h4 , h5 , h6{
  color:#3D7BCE;
} */
.important-txt{
  text-align: center;
  font-size:18px;
  font-weight: bold;
  /* color:#10996D; */
}
.important-link{
  display: block;
  font-size:18px;
  font-weight: bold;
  /* color:#10996D; */
  text-decoration: none;
}
.centered-heading{
  text-align: center !important;
}
.row.center-vertically{
  align-items: center;
}
p{
  /* text-align: justify; */
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 16px;
}
.job-search-link-sec a{
  text-align: center !important;
}
.company-div{
  margin-bottom: 40px;
}
.company-logo-div{
  font-size:15px;
  font-weight: bold;
  margin-bottom: 10px;
  cursor: pointer;
}
.gold-parterns-section .company-logo-div{
  color:#E3B442;
}
.silver-parterns-section .company-logo-div{
  color:#898989;
}
.company-logo-div img{
  max-height: 72px;
  margin-bottom:10px;
}
.gold-parterns-section .apply-to-company-btn{
  background: transparent;
  border: 2px solid;
  border-color: #E3B442;
  color: #E3B442;
  font-weight: bold;
}
.gold-parterns-section .apply-to-company-btn:hover{
  background: #E3B442;
  border-color: #E3B442;
  color: #fff;
}
.silver-parterns-section .apply-to-company-btn{
  background: transparent;
  border: 2px solid;
  border-color: #898989;
  color: #898989;
  font-weight: bold;
}
.silver-parterns-section .apply-to-company-btn:hover{
  background: #898989;
  border-color: #898989;
  color: #fff;
}
.section-border{
  border:1px solid #e4e4e4;
  padding:10px;
  border-radius: 25px;
}

.about-section h2{
  color:#7f7f7f;
}
.about-img{
  position: absolute;
  z-index: -1;
  left: -77px;
  top: -26px;
  width: 184px;
  animation: light-float 2.5s ease-in-out 1000ms infinite;
}
.vision-img{
  position: absolute;
  z-index: -1;
  left: -14px;
  top: -39px;
  width: 183px;
  animation: light-float 2.5s ease-in-out 1000ms infinite;
}

.write-cv{
  margin-bottom:110px;
}
.create-cv-online{
  position: relative;
}
.create-cv-online h2{
  color:#000;
}
.create-cv-online .important-txt{
  color:#3d7bce;
  font-weight: normal;
}
.create-cv-online .text-content p{
  font-size:15px;
}
.create-cv-bg-abs{
  position: absolute;
  background: #ececec;
  width: 800px;
  height: 800px;
  border-radius: 50%;
  z-index: -1;
  top: -61px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0px 20px 20px -3px rgba(0,0,0,0.1);
  -webkit-box-shadow: 0px 20px 20px -3px rgba(0,0,0,0.1);
  -moz-box-shadow: 0px 20px 20px -3px rgba(0,0,0,0.1);
}
.man-div{
  background: #e2b12e;
  border-radius: 50%;
  width: 550px;
  height: 550px;
  position: relative;
  z-index: -2;
  margin: auto;
}
.man-div img{
  position: absolute;
  z-index: 3;
  /* top: -58px; */
  /* top:-70px; */
  /* top:24px; */
  top:0;
  right: 117px;
  width: 350px;
  /* animation: UpDown 1s linear infinite; */
}

.templates-section{
  position:relative;
}
.templates-section .section-title{
  color:#e3b442;
}
.templates-number{
  position:absolute;
  top:-102px;
  color:#e5e5e5;
  font-size:150px;
}
.templates-number .plus, .templates-number .thirty{
  display:inline-block;
}
.template{
  position: absolute;
  -webkit-box-shadow: 10px 10px 5px 0px rgba(153,153,153,1);
  -moz-box-shadow: 10px 10px 5px 0px rgba(153,153,153,1);
  box-shadow: 10px 10px 5px 0px rgba(153,153,153,1);
}
.templates-div{
  height: 370px;
}
.template1{
  /* left: 0; */
  left:15%;
  top: -50px;
  animation: float 2.5s ease-in-out infinite;
}
.template2{
  top: -14px;
  /* left: 186px; */
  left: 47%;
  animation: float 2.5s ease-in-out 500ms infinite;
}
.template3{
  /* left: 100px; */
  left: 30%;
  top: 96px;
  animation: float 2.5s ease-in-out 1000ms infinite;
}
.upload-cv-pdf, .search-job{
  /* position:relative; */
  /* background: #e3b442 url(/assets/images/home/<USER>/
  background: #e3b442 url(/assets/images/home/<USER>
  /* background-color:#e3b442; */
  /* color:#525252; */
  color:#333;
  /* background-image: url("paper.gif"); */
}
.upload-cv-pdf h2{
  color:#333;
  /* color:#525252; */
}

/* .upload-cv-pdf .bg-img{
  position: absolute;
  z-index: -1;
  right: 14px;
  top: -76px;
  width: 450px;
} */

.companies-section{
  /* background: #143b8e url(/assets/images/home/<USER>/
  background: #143b8e url(/assets/images/home/<USER>
  background-size: cover;
  color:#ffffff;
}

.btn-orange {
  background: #e3b442;
  color: #fff;
  border: 0;
  outline: 0;
  font-size: 17px;
  margin: 0 6px;
}
.btn-orange:hover {
  background: #e6b541;
  border: 0;
  outline: 0;
  animation: rubberBand 1s;
}

.cveek-partners-section{
  background:#e5e5e5;
}
.gold-parterns-section, .silver-parterns-section{
  background: #fff;
  padding: 40px 20px 20px 20px;
  box-shadow: 0 1px 3px 0 rgb(0 0 0/20%),0 1px 1px 0 rgb(0 0 0/14%),0 2px 1px -1px rgb(0 0 0/12%);
}
.gold-parterns-section{
  margin-bottom: 30px;
}
.gold-partenrs-title-div , .silver-partenrs-title-div{
  position:relative;
}
.gold-partenrs-title-div img, .silver-partenrs-title-div img{
  position: absolute;
  left: 24px;
  top: -24px;
  height: 100px;
}
.gold-parterns-section h2{
  padding: 8px;
  background: #e3b442;
  color: #fff;
}

.silver-parterns-section h2{
  padding: 8px;
  background: #898989;
  color: #fff;
}
.partners-div{
  margin-top:70px;
}
.search-job h2{
  color:#fff;
  line-height: 35px;
}
.search-job .important-link{
  color:#fff;
}
img.search-jobs-cveek{
  max-width: 100%;
  margin:0 auto 20px auto;
}
img.customize-jobs{
  max-width: 400px;
  margin:0 auto 20px auto
}
/* start rtl styles */
/* .home-rtl{
  direction:rtl;
} */
.home-rtl h1 , .home-rtl h2 , .home-rtl h3 ,.home-rtl h4 , .home-rtl span , .home-rtl a{
  direction:rtl;
  text-align: right;
}
.home-rtl p {
  direction:rtl;
}

/* commented font till uncomment arabic content */
/* .home-rtl{
  font-family: 'BAHIJ','Times New Roman', Times, serif;
}
@font-face {
  font-family: 'BAHIJ';
  src: url('assets/fonts/BAHIJ/BAHIJ_THESANSARABIC-PLAIN.TTF') format('truetype');
  font-style: normal;
} */

/* end rtl styles */


 /* start video styles */
.video-container{
  width:70%;
  margin:auto;
}
.video-section video{
  width:100%;
}
/* to fix black borders */
.video-section{
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  height: 0;
}
.video-section iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/* end video styles */

/* start responsive styles */
@media (min-width: 1000px){
  .section-paragraph{
    max-width: 760px;
    text-align: center;
    margin: auto;
  }
}

@media (min-width: 768px) {
  .row.equal {
    display: flex;
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 1280px){
  .man-div img {
    top: -58px;
    right: 156px;
    width: 320px;
  }
}
@media screen and (max-width: 1200px){
  .man-div img {
    right: 209px;
    width: 300px;
  }
}
@media screen and (max-width: 1100px){
  .man-div img {
    right: 260px;
  }
}
@media screen and (max-width: 991px) {
  .sec-mar-bot-md{
    margin-bottom: 50px;
  }
  .video-container{
    width:100%;
  }
  .company-logo-div{
    font-size:13px;
  }
  .company-logo-div img{
    max-height: 48px;
  }

  .equal .order-md-2{
    order:2;
  }
  .equal .order-md-1{
    order:1;
  }

  .man-div{
    z-index:3;
  }
  .man-div img{
    top: 71px;
    right: 117px;
    width: 300px;
  }
  .create-cv-bg-abs{
    display: none;
  }
  .create-cv-bg-mob{
    background: #ececec;
    padding: 35px 20px 20px 20px;
    margin-bottom: 20px;
    box-shadow: 0px 20px 20px -3px rgba(0,0,0,0.1);
    -webkit-box-shadow: 0px 20px 20px -3px rgba(0,0,0,0.1);
    -moz-box-shadow: 0px 20px 20px -3px rgba(0,0,0,0.1);
  }
  .section{
    padding: 20px;
  }
  .about-section{
    padding:40px;
  }

  .templates-number{
    top: -52px;
    font-size: 100px;
  }
  .templates-div{
    max-width: 570px;
    margin:auto;
    position: relative;
  }
  .template1{
    left:0%;
  }
  .template2{
    left:55%;
  }
  .template3{
    left:30%;
  }
  h2{
    font-size: 24px;
  }
  p{
    font-size:15px;
  }
}

@media screen and (max-width: 990px) and (min-width: 768px){
  .home-page-content {
    margin-top: 96px;
  }
}
@media (max-width: 767px) {
  .row.equal {
    display: flex;
    flex-wrap: wrap;
  }
  .equal .order-2{
    order:2;
  }
  .equal .order-1{
    order:1;
  }
  h1 , h2 , h3 ,h4 {
    text-align: center;
  }
  h2{
    font-size: 21px;
    line-height: 35px;
  }
  .important-txt{
    font-size:17px;
  }
  .section{
    padding: 30px  20px;
  }
  .sec-mar-bot-mob{
    margin-bottom: 88px;
  }
  .about-img {
    left: -60px;
    top: -26px;
    width: 150px;
  }
  .vision-img{
    left: -72px;
    top: -39px;
    width: 166px;
  }
  .gold-partenrs-title-div img, .silver-partenrs-title-div img{
    position: absolute;
    left: 10px;
    top: -19px;
    height: 75px;
  }

  .write-cv {
    margin-bottom: 50px;
    padding:30px  0px;
  }
  .important-link , .important-txt{
    font-size:16px;
  }
}
@media (max-width: 650px) {
  .company-logo-div,.gold-parterns-section .apply-to-company-btn, .silver-parterns-section .apply-to-company-btn{
    font-size:12px;
  }
}
@media (max-width: 600px) {
  .man-div{
    width: 320px;
    height: 320px;
  }
  .man-div img {
    /* top: 71px; */
    top:0;
    right: unset;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
  }

  .template{
    width:140px;
  }
  .templates-div{
    height: 300px;
  }
}
@media (max-width: 507px){
  .create-cv-online .text-content p{
    font-size:13px;
  }
 
  /* .create-cv-bg-abs{
    width: 900px;
    height: 900px;
  } */
}

@media (max-width: 500px) {
  .gold-parterns-section, .silver-parterns-section{
    padding:24px 20px 13px 20px;
  }
  .gold-partenrs-title-div h2, .silver-partenrs-title-div h2{
    font-size: 18px;
    padding:0;
  }
  .gold-partenrs-title-div img, .silver-partenrs-title-div img{
    position: absolute;
    left: -15px;
    top: -13px;
    height: 55px;
  }
  .partners-div {
    margin-top: 38px;
  }
  img.customize-jobs{
    max-width: 368px;
    margin:0 auto 20px auto
  }
}
/* end responsive styles */

/* start animation keyframes */
@keyframes UpDown {
  0% {
    top:0;
  }
  50% {
    top:-250px;
  }
  100% {
    top:0;
  }
}

@keyframes zoom-it{
  0%{transform: scale(1);}
  50%{transform: scale(2.5);}
  100%{transform: scale(1);}
}
@keyframes float {
	0% {
		box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6);
		transform: translatey(0px);
	}
	50% {
		box-shadow: 0 25px 15px 0px rgba(0,0,0,0.2);
		transform: translatey(-20px);
	}
	100% {
		box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6);
		transform: translatey(0px);
	}
}

@keyframes light-float {
	0% {
		transform: translatey(0px);
	}
	50% {
		transform: translatey(-5px);
	}
	100% {
		transform: translatey(0px);
	}
}

@keyframes lightSpeedInLeft {
  from {
    transform: translate3d(-100%, 0, 0) skewX(30deg);
    opacity: 0;
  }

  60% {
    transform: skewX(-20deg);
    opacity: 1;
  }

  80% {
    transform: skewX(5deg);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes rubberBand {
  from {
    transform: scale3d(1, 1, 1);
  }

  30% {
    transform: scale3d(1.25, 0.75, 1);
  }

  40% {
    transform: scale3d(0.75, 1.25, 1);
  }

  50% {
    transform: scale3d(1.15, 0.85, 1);
  }

  65% {
    transform: scale3d(0.95, 1.05, 1);
  }

  75% {
    transform: scale3d(1.05, 0.95, 1);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}
@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }

  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }

  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }

  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }

  to {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

/* end animation keyframes */

/* start carousel styles */

.carousel-caption {
    animation-name: example;
    animation-duration: 4s;
}
@keyframes example {
    from {top:0;}
    to {top:60%;}
}

.slide{
  position:relative;
}
.background{
  max-width: 100%;
  /* position: absolute;
  z-index: 1;
  animation-name: bg-anim;
  animation-duration: 1s; */
}
.lucky{
  position: absolute;
  right: 9%;
  top: 24%;
  z-index: 2;
  max-width: 37% !important;
  animation-name: lucky-anim;
  animation-duration: 1s;
  animation-delay:1.5s;
}
.your{
  position: absolute;
  right: 27%;
  top: 20%;
  /* top:0; */
  opacity:0;
  font-size: 3.7vw;
  font-family: Exo2;
  z-index: 2;
  /* animation-name: your-anim;
  animation-duration: 1s; */
  /* animation-delay:1.5s; */
}
.day{
  position: absolute;
  /* right: 3%; */
  right:-100%;
  top: 29%;
  font-size: 3.7vw;
  font-family: Exo2;
  z-index: 2;
  /* animation-name: day-anim;
  animation-duration: 1s;
  animation-delay:2s; */
  /* animation-direction: alternate; */
  /* animation-iteration-count: infinite; */
}
.because{
  position: absolute;
  right: 19%;
  top: 48%;
  font-size: 2.5vw;
  font-family: Exo2;
  z-index: 2;
  opacity:0;
}
.colleagues{
  position: absolute;
  /* right: 5%; */
  right:-100%;
  top: 63%;
  font-size: 1.7vw;
  font-family: Exo2;
  z-index: 2;
}
.deal{
  position: absolute;
  left: 30%;
  /* left:-100%; */
  bottom: 5%;
  font-size: 1.5vw;
  font-family: Exo2;
  z-index: 2;
  opacity: 0;
}
/* .carousel-caption {
  animation-name: lucky-anim;
  animation-duration: 1s;
} */

@keyframes bg-anim {
  from {bottom:-100%;}
  to {bottom:0%;}
}
@keyframes lucky-anim {
  from {top:0;}
  to {top:24%;}
}
/* @keyframes your-anim {
  from {opacity:0;}
  to {opacity:1;}
} */
@keyframes day-anim {
  from {right:-100%;}
  to {right:3%;}
}
@keyframes because-anim {
  0%   {transform:scale(0);opacity:1;}
  33%   {transform:scale(0.5);}
  66%  {transform:scale(0.8);}
  80%  {transform:scale(1);}
  90%  {transform:scale(1.2);}
  100%  {transform:scale(1);}
}
@keyframes colleagues-anim {
  from {right:-100%;}
  to {right:5%;}
}
@keyframes deal-anim {
  from {opacity:0;}
  to {opacity:1;}
}

/* end carousel styles */

.contact.container
{
    height: 100%;
    width:100%;

	background-size: cover;
	background-repeat:no-repeat;
}
.contact.container-box
{
	display:inline-block;
	padding:10px;
	background: rgba(255, 255, 255, .8);
	border: 1px solid #fff;
	position: fixed;
  	top: 55%;
  	left: 0;

}

.contact.modal-content
{
	background: rgba(255, 255, 255, .7);
}


.contact.rotated
{
	-moz-transform:rotate(-90deg);
    -ms-transform:rotate(-90deg);
    -o-transform:rotate(-90deg);
    -webkit-transform:rotate(-90deg);
    transform-origin: top left;
}
