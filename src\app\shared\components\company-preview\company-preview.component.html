<app-pre-loader [show]="showLoader"></app-pre-loader>

<div class="public-preview-page-content" *ngIf="!showLoader">

    <div *ngIf="profile.length ===0 && advs.length === 0 && branches.length === 0 ; else publicProfileContent">
        <div class="alert alert-warning" role="alert">
            Company does not have a Registered Profile! please enter your profile data
            <a [routerLink]="['/c', username, 'profile','new']">Add profile</a>
        </div>
    </div>

    <ng-template #publicProfileContent>
        <p-toast position="bottom-right"></p-toast>

        <div class="public-preview-wrapper">
            <div class="section section-mar-bot-mob" *ngIf="profile">
                <div class="dark-background-section">
                    <div class="row top-flex-row ">
                        <div class="col-md-3 col-xs-4 col-xxs-3 text-center">
                            <div class="public-preview-company-img" *ngIf="profile.logo">
                                <img [src]="companyLogoPath"  alt="{{profile.company_name}} logo">
                            </div>
                            <div *ngIf="profile.show_apply_button">
                                <button *ngIf="!appliedToCompany" type="submit" class="btn btn-primary apply-to-company-btn" (click)="applyToCompany()" [disabled]="role==='ROLE_EMPLOYER'">Apply Now</button>
                                <button *ngIf="appliedToCompany" type="submit" class="btn btn-primary apply-to-company-btn" disabled>Applied</button>
                            </div>
                        </div>
                        <div class="col-md-9 col-xs-8 col-xxs-9">
                            <div class="public-preview-user-name">
                                <h1 *ngIf="profile.company_name">{{profile.company_name}}</h1>
                                <img src="./assets/images/verified-company.svg" *ngIf="profile.company_verified == 'Verified'" alt="Verified company" class="verified-icon">
                                <img src="./assets/images/cveek-gold-partner.svg" *ngIf="profile.company_verified == 'Golden Verified'" alt="CVeek Gold Partner" class="verified-icon">
                                <img src="./assets/images/cveek-silver-partner.svg" *ngIf="profile.company_verified == 'Silver Verified'" alt="CVeek Silver Partner" class="verified-icon">
                            </div>
                            <h2 style="font-size:15px;margin-bottom:10px" *ngIf="profile.industries.length">
                                <span *ngFor="let industry of profile.industries; let i = index">
                                    {{industry}} <span *ngIf="i < profile.industries.length - 1"> , </span>
                                </span>     
                            </h2>
                            <h3 style="font-size:15px;" *ngIf="profile.location">{{profile.location}}</h3>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <div class="row section-mar-bot">
                    <div *ngIf="profile && (profile.specialties.length || profile.type || profile.size || profile.founded || profile.description )" class="section-mar-bot-mob" [ngClass]="profile.social_media.length > 0 || profile.company_website || branches.length > 0 ? 'col-md-8' : 'col-xs-12'">
                        <div class="custom-fieldset profile-info">
                            <h2 class="custom-legend">Profile Info</h2>
                            <ng-content select=".profile-edit"></ng-content>
                            <div class="fieldset-content">
                                <div class="row" *ngIf="profile.specialties.length">
                                    <div class="col-md-3 col-sm-4 col-xs-5 col-xxs-12 left-col-preview-align-right">
                                        <span translate>Specialities:</span>
                                    </div>
                                    <div class="col-md-9 col-sm-8 col-xs-7 col-xxs-12 no-padding-left-xs">
                                        <span *ngFor="let speciality of profile.specialties"> {{speciality}} </span>
                                    </div>
                                </div>
                                <div class="row" *ngIf="profile.type">
                                    <div class="col-md-3 col-sm-4 col-xs-5 col-xxs-12 left-col-preview-align-right">
                                        <span translate>Type:</span>
                                    </div>
                                    <div class="col-md-9 col-sm-8 col-xs-7 col-xxs-12 no-padding-left-xs">                       
                                        <span> {{profile.type}} </span>
                                    </div>
                                </div>
                                <div class="row" *ngIf="profile.size">
                                    <div class="col-md-3 col-sm-4 col-xs-5 col-xxs-12 left-col-preview-align-right">
                                        <span translate>Company Size:</span>
                                    </div>
                                    <div class="col-md-9 col-sm-8 col-xs-7 col-xxs-12 no-padding-left-xs">                       
                                        <span> {{profile.size}} </span>
                                    </div>
                                </div>
                                <div class="row" *ngIf="profile.founded">
                                    <div class="col-md-3 col-sm-4 col-xs-5 col-xxs-12 left-col-preview-align-right">
                                        <span translate>Founded:</span>
                                    </div>
                                    <div class="col-md-9 col-sm-8 col-xs-7 col-xxs-12 no-padding-left-xs">                       
                                        <span> {{profile.founded}} </span>
                                    </div>
                                </div>
                                <div class="row" *ngIf="profile.description">
                                    <div class="col-md-3 col-sm-4 col-xs-5 col-xxs-12 left-col-preview-align-right">
                                        <span translate>Description:</span>
                                    </div>
                                    <div class="col-md-9 col-sm-8 col-xs-7 col-xxs-12 no-padding-left-xs">                       
                                        <p class="desc-justify" [innerHTML]="profile.description"></p>
                                    </div>
                                </div>
                            </div>   
                                
                        </div>
                    </div>
                    <div class="col-md-4" *ngIf="profile || branches.length > 0">
                        <div class="custom-fieldset social-media section-mar-bot-mob" *ngIf="profile.social_media.length > 0 || profile.company_website">
                            <h2 class="custom-legend">Social Media</h2>
                            <ng-content select=".social-media-edit"></ng-content>
                            <div class="fieldset-content text-center">
                                <p *ngIf="profile.company_website">
                                    <a (click)="goURLnew(profile.company_website)" style="cursor: pointer;">{{profile.company_website}}</a>
                                    <!-- <a [href]="profile.company_website">{{profile.company_website}}</a> -->
                                </p>
                                <div *ngIf="profile.social_media.length > 0">
                                    <span *ngFor="let socialMedia of profile.social_media">
                                        <a (click)="goURLnew(socialMedia.info)" style="cursor: pointer;">
                                            <img src="./assets/images/Social Media/{{socialMedia.social_media}}.png" alt="{{socialMedia.social_media}}">
                                        </a>
                                    </span>                      
                                </div>                           
                            </div>
                        </div>
                        <div class="custom-fieldset section-mar-bot-mob" *ngIf="branches.length > 0">
                            <h2 class="custom-legend">Branches</h2>
                            <ng-content select=".branches-edit"></ng-content>
                            <div class="fieldset-content">
                                <p *ngFor="let branch of branches">
                                    <span *ngIf="branch.is_main_office == 1"><i style="color:#3d7bce;" class="fa fa-map-marker" aria-hidden="true"></i>&nbsp;</span>
                                    <span *ngIf="branch.name">{{branch.name}} , </span>
                                    <span *ngIf="branch.street_address">{{branch.street_address}} , </span>
                                    <span *ngIf="branch.postal_code">{{branch.postal_code}} , </span>
                                    <span *ngIf="branch.city">{{branch.city}} , </span>
                                    <span *ngIf="branch.country">{{branch.country}} </span>
                                    <span *ngIf="branch.country_code">{{branch.country_code}}</span>
                                </p>          
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="section">
                <div class="custom-fieldset">
                    <h2 class="custom-legend" *ngIf="profile && profile.company_name">Active Jobs at {{profile.company_name}}</h2>
                    <h2 class="custom-legend" *ngIf="profile && profile.company_name && profile.publisher_other_company">Active Jobs posted by {{profile.company_name}}</h2>
                    <h2 class="custom-legend" *ngIf="!profile && !profile.company_name">Active Jobs at Company</h2>
                    <div class="fieldset-content">
                        <div *ngIf="advs.length === 0">There are no jobs currently available</div>
                        <div *ngIf="advs.length > 0">
                                <!-- (click)="displayAdvrModal(adv.id,sourceInterface)" -->
                            <div class="row jobs-row" *ngFor="let adv of advs" style="cursor:pointer" (click)="displayAdvrPage(adv.id,adv.slug)">
                                <div class="col-lg-5 col-sm-6 mar-bot-mob-10" *ngIf="adv.job_adv_title">
                                    <div class="adv-title-padding">
                                        <div>
                                            <h3 class="job-title">
                                                {{adv.job_adv_title}}
                                            </h3>
                                            <span *ngIf="adv.status_new" class="adv-label">New</span>
                                        </div>
                                        <span *ngIf="adv.date" class="adv-time">{{adv.date | date}}</span>
                                    </div>
                                </div>       
                                <div class="col-lg-7 col-sm-6">
                                    <div class="row">
                                        <div class="col-xs-6">
                                            <div *ngIf="adv.is_online == 1">
                                                <span>Remote</span>
                                            </div>
                                            <div *ngIf="adv.country || adv.city">
                                                <span *ngIf="adv.country">{{adv.country}}</span> <span *ngIf="adv.city"> , {{adv.city}}</span>
                                            </div>
                                        </div>
                                        <div class="col-xs-6">
                                            <span *ngIf="adv.employment_type">{{adv.employment_type}}</span>
                                        </div>
                                    </div>
                                </div>             
                            </div>  
                        </div>                  
                    </div>
                </div>
            </div>
        </div>
    </ng-template>
</div>


<!-- <div class="modal fade" id="AdvrsModal"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal3Label" translate>Advertisement Preview</h4>
            </div>
            <app-advr-preview >

            </app-advr-preview>
        </div>
    </div>
</div> -->

<div class="modal fade" id="authModal" tabindex="-1" role="dialog" aria-labelledby="authLabelModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="authLabelModal" translate>Job Seeker Sign In</h4>
            </div>
            <login [fromPage]="'company-public-preview'"></login>
        </div>
    </div>
</div>