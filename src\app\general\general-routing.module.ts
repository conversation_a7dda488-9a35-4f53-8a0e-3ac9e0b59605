import { NgModule } from '@angular/core';
import { RouterModule , Routes } from '@angular/router';
import { WrapperComponent } from './components/wrapper/wrapper.component';
import { FaqComponent } from './components/faq/faq.component';
import { HelpComponent } from './components/help/help.component';
import { HelpTopicComponent } from './components/help-topic/help-topic.component';
import { ArticlesUserInterfaceComponent } from "app/user/components/articles-user-interface/articles-user-interface.component";
import { ContactFormComponent } from './components/contact-form/contact-form.component';
import { SubHelpComponent } from './components/sub-help/sub-help.component';
import { MainHelpComponent } from './components/main-help/main-help.component';
// import { CompanyPreviewComponent } from 'shared/components/company-preview/company-preview.component';
import { ResumePreviewComponent } from 'app/general/components/resume-preview/resume-preview.component';
import { CompanyPreviewComponent } from 'app/general/components/company-preview/company-preview.component';
import { HelpSearchComponent } from './components/help-search/help-search.component';
import { ErrorPageComponent } from './components/error-page/error-page.component';
import { ForbiddenErrorComponent } from './components/forbidden-error/forbidden-error.component';
import { OfflineErrorComponent } from './components/offline-error/offline-error.component';
import { TermsComponent } from './components/terms/terms.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { ResumeBuilderComponent } from './components/resume-builder/resume-builder.component';
import { CvTemplatesComponent } from './components/cv-templates/cv-templates.component';
import { AtsFriendlyCvComponent } from './components/ats-friendly-cv/ats-friendly-cv.component';
import { AdvancedJobSearchComponent } from './components/advanced-job-search/advanced-job-search.component';
import { InterviewTipsComponent } from './components/interview-tips/interview-tips.component';
import { AiJobPostingComponent } from './components/ai-job-posting/ai-job-posting.component';
import { AdvertisementDashboardComponent } from './components/advertisement-dashboard/advertisement-dashboard.component';
import { InboxDashboardComponent } from './components/inbox-dashboard/inbox-dashboard.component';
import { CveekPluginComponent } from './components/cveek-plugin/cveek-plugin.component';
import { AboutUsComponent } from './components/about-us/about-us.component';

const routes: Routes = [
  // { path: '/contact-us', component: ContactFormComponent },
  {
    path: '', component: WrapperComponent, children: [
    //  {path: 'articles', component: ArticlesUserInterfaceComponent},
      {path: 'faq', component: FaqComponent},
      {path: 'help/:mainCat/:subCat?/:id/:slug', component: HelpTopicComponent},
    //  {path: 'help/:mainCat/:id/:slug', component: HelpTopicComponent},
      {path: 'help/:mainCat/:mainCatId/s/:subCat/:subCatId', component: SubHelpComponent},
      {path: 'help/:mainCat/:mainCatId', component: MainHelpComponent},
      {path: 'help-search/:query', component: HelpSearchComponent},
      {path: 'help', component: HelpComponent},
      {path: 'contact-us', component: ContactFormComponent},
      {path: 'c/:username' , component:CompanyPreviewComponent},
    //  {path: 'u/:username/:resumeId' , component:ResumePreviewComponent},
    //  {path: 'c/:username/:companyId' , component:CompanyPreviewComponent}
      {path: 'error',component: ErrorPageComponent},
      {path: 'forbidden',component: ForbiddenErrorComponent},
      {path: 'offline',component: OfflineErrorComponent},
      {path: 'terms-conditions',component: TermsComponent},
      {path: 'privacy-policy',component: PrivacyPolicyComponent},
      {path: 'resume-builder' , component: ResumeBuilderComponent },
      {path:'cv-templates' , component: CvTemplatesComponent},
      {path:'ats-friendly-cv' , component: AtsFriendlyCvComponent},
      {path:'advanced-job-search' , component: AdvancedJobSearchComponent},
      {path:'interview-tips' , component: InterviewTipsComponent},
      {path:'ai-job-posting' , component: AiJobPostingComponent},
      {path:'advertisement-dashboard' , component: AdvertisementDashboardComponent},
      {path:'inbox-dashboard' , component: InboxDashboardComponent},
      {path:'cveek-plugin' , component: CveekPluginComponent},
      {path:'about-us' , component: AboutUsComponent},
    ]},
];

@NgModule({
    imports: [ RouterModule.forChild(routes) ],
    exports: [ RouterModule ]
})
export class GeneralRoutingModule { }
