import {AbstractControl, ValidationErrors, AsyncValidatorFn} from '@angular/forms';
import { PostJobService } from '../../services/post-job.service';

export class CompanyPostJobValidators {

    static validJobSeekerLocationValidator(control: AbstractControl): ValidationErrors | null {
      let fullLocation = control.get('location').value;
      let countryCode = control.get('current_location').get('country_code').value; 
      if ((fullLocation !== null &&  fullLocation !== '') &&  (countryCode === null || countryCode === '')){
        return {'InvalidLocationError' : 'validationMessages.ChooseAutoCompleteSuggestionsError '};
      }
  
      return null;
    }


    // Async validator
    static enteredLogoExistValidator(postJobService:PostJobService): AsyncValidatorFn {
      return (control: AbstractControl): Promise<ValidationErrors | null> => {
          return new Promise((resolve , reject) => {
            if(control.value !== null && control.value !== ''){
              postJobService.checkOtherEmployerLogoExist({"logo_name":control.value}).subscribe(res => {
                if(res['error'] && res['type']==='logo_not_exist'){
                    resolve({logoNotFound:true});
                }
                else resolve(null);                    
              });
            }
            else resolve(null);
            
          });
      };
    }

  }