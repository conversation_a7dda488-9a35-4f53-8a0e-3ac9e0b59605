<div class="header">
    <!--<span data-toggle="modal" data-target="#companyModal" (click)="Display_Profile_Languages()" style="color: rgb(126,184,22);margin-right: 5px;cursor: pointer" class="fa fa-plus-circle"></span>
    <a data-toggle="modal" data-target="#companyModal" (click)="Display_Profile_Languages()" style="color:rgb(91,211,55);cursor: pointer">Profile Languages</a>
    <span (click)="changeProfile(lang.data,lang.profile_language_id)" class="language" *ngFor="let lang of company_profiles_languages"
     [class.active]="lang.profile_language_id === current_language">
    <button class="close2" *ngIf="lang.data" (click)="delete_profile(lang.profile_language_id)"><span aria-hidden="true">&times;</span></button>

    {{ lang.language}}
    </span>
    <span *ngIf="new_language_profile && tab_exist" class="language" 
    (click)="changeProfile(new_language_profile[0].data,new_language_profile[0].profile_language_id)" 
    [class.active]="new_language_profile.length">
      <button class="close2" *ngIf="tab_exist" (click)="delete_tab()"><span aria-hidden="true">&times;</span></button>
    {{ new_language_profile[0].language}}
    </span>-->
</div>

<div class="CForm">
        
        <!-- (ngSubmit)="form.valid && submit()" -->
    <form #form="ngForm" [formGroup]="companyForm" class="form-horizontal validate-form" (ngSubmit)="form.valid && submit(form)" [appInvalidControlScroll]="'normalComponent'"
    >
        <div class="row clearfix flex-row">
            <div class="col-sm-8 col-xs-12 flex-order-sm-2">
                <div class="custom-row clearfix" [ngClass]="{'has-error': form.submitted && !companyForm.controls['name'].value }">
                    <!-- isInvalid('name') -->
                    <div class="col-sm-4 alignment-right">
                    </div>
                    <div class="col-sm-8 focus-no-padding CScreen validate-input" [ngClass]="{'has-val':companyForm.controls['name'].value}" data-validate="Company Name is required">
                        <input type="text" formControlName="name" class="form-control">
                        <span class="custom-underline"></span>
                        <!-- <span *ngIf="form.submitted && !companyForm.controls['name'].value" class="glyphicon form-control-feedback glyphicon-remove" aria-hidden="true"></span> -->
                        <span *ngIf="form.submitted && !companyForm.controls['name'].value" class="error-message" translate>validationMessages.required</span>
                        <label class="control-label custom-control-label " translate>company_form.companyName</label>
                    </div>
                </div>
                <div class="custom-row clearfix" [ngClass]="{'has-error': form.submitted && companyForm.controls['industries'].value.length ===0 }">
                    <div class=" col-sm-4 alignment-right">
                    </div>
                    <div class="col-sm-8 focus-no-padding validate-input CScreen translate p-multiselect-container"> 
                        <p-multiSelect
                            [options]="companyIndustriesSpecialtiesOpt"
                            [(ngModel)]="selectedIndus"  formControlName="industries"
                            name = "selectedIndus"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            styleClass="form-control custom-p-multiselect"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            [selectionLimit]="4"
                            [maxSelectedLabels]="4"
                            defaultLabel="Company Industry"
                            [ngClass]="{'has-val':!companyForm.controls['industries'].errors?.required}"
                            >
                        </p-multiSelect>
                        <!-- <span class="custom-underline"></span> -->

                        <!-- <ejs-multiselect [dataSource]='companyIndustriesSpecialtiesOpt' [fields]='remoteFields' mode='Box' [allowFiltering]='true' 
                            [ignoreAccent]='true' maximumSelectionLength="4" style="color:rgb(91,211,55)" 
                            [(ngModel)]="selectedIndus"  formControlName="industries" [value]='value'
                            [ngClass]="{'has-val':!companyForm.controls['industries'].errors?.required}">
                        </ejs-multiselect> -->
                       
                        <span *ngIf="form.submitted && !companyForm.controls['industries'].value.length" class="error-message" translate>validationMessages.required</span>
                        <label class="control-label custom-control-label " translate>company_form.companyIndustry</label>
                    </div>
                </div>
                <div class="custom-row clearfix">  
                    <div class=" col-sm-4 alignment-right">
                    </div>
                    <div class=" col-sm-8 focus-no-padding validate-input CScreen p-multiselect-container">
                        <p-multiSelect
                            [options]="companyIndustriesSpecialtiesOpt"
                            formControlName="specialties"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            styleClass="form-control custom-p-multiselect"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            [selectionLimit]="4"
                            [maxSelectedLabels]="4"
                            defaultLabel="Company Specialities"
                            [disabled]="selectedIndus.length === 0 ? true : false"
                            [ngClass]="{'has-val':companyForm.controls['specialties'].value && companyForm.controls['specialties'].value.length > 0 }"
                            >
                        </p-multiSelect>
                       
                        <!-- <ejs-multiselect [dataSource]='companyIndustriesSpecialtiesOpt' [fields]='remoteFields' mode='Box' 
                            [allowFiltering]='true' [ignoreAccent]='true' [enabled]="selectedIndus.length === 0 ? false : true" 
                            formControlName="specialties" [value]='value2'
                            [ngClass]="{'has-val':companyForm.controls['specialties'].value }">
                        </ejs-multiselect> -->
                        <label class="control-label custom-control-label " translate>company_form.companySpecialities</label>
                    </div>
                </div>
                <div class="custom-row clearfix">
                    <div class="col-sm-4 col-md-4 col-xl-4 col-alignment-right">
                    </div>
                    <div class="col-sm-8 focus-no-padding validate-input CScreen" [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}">
                        <!--don't forget this  [ngClass]="{'has-val':educationForm.controls['degree_level_id'].value}" -->
                        <p-dropdown [options]="companyTypeOpt" optionLabel="company_type_name" formControlName="company_type_id_temp" [filter]="true" [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}">
                            <ng-template let-degree pTemplate=" item ">
                                <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                    <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <span class="custom-underline"></span>
                        <!-- <span class="glyphicon form-control-feedback " aria-hidden="true "></span> -->
                        <label class="control-label custom-control-label " translate>company_form.type</label>
                    </div>

                </div>
                <div class="custom-row clearfix" [ngClass]="{'has-error': form.submitted && !companyForm.controls['company_website'].valid }">
                    <div class=" col-sm-4 alignment-right">
                    </div>
                    <div class=" col-sm-8 focus-no-padding validate-input CScreen" [ngClass]="{'has-val':companyForm.controls['company_website'].value}">
                        <input type="text" formControlName="company_website" class="form-control">
                        <span class="custom-underline"></span>
                        <label class="control-label custom-control-label " translate>company_form.companyWebsite</label>
                        <span class="error-message " *ngIf="form.submitted && companyForm.controls['company_website'].errors?.invalidUrlError" translate>{{companyForm.controls['company_website'].errors?.invalidUrlError}}</span>
                    </div>
                </div>

                <p-dialog header="Direct apply for cv's" [(visible)]="displayApplyInfoDialog" styleClass="direct-apply-info-dialog" [draggable]="false">          
                    <div class="dialog-content-container">
                        <p>If you enable this option, interested people can apply their CVs directly to your company, without any Job Advertisement.</p>
                        <p>you will receive these CVs in a special Folder named "Direct Apply"</p>
                    </div>  
                </p-dialog>

                <div class="custom-row clearfix" [ngClass]="{'has-error': form.submitted && !companyForm.controls['show_apply_button'].valid }">
                    <div class=" col-sm-4 alignment-right">
                        <label class="fixed-label">Accept Direct CV Applying</label>
                        <button type="button" class="btn btn-primary btn-fa-info direct-apply-info-btn" (click)="showApplyInfoDialog()"  pTooltip="info" tooltipPosition="top">
                            <i class="fa fa-info" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class=" col-sm-8 focus-no-padding validate-input CScreen">
                        <div class="row">
                            <div class="col-xs-6 col-xxs-12">
                                <label class="container radio-choose" translate>Yes / Show Apply Button
                                    <input type="radio"  formControlName="show_apply_button" [value]="1">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-xs-6 col-xxs-12">
                                <label class="container radio-choose" translate>No / Hide Apply Button
                                    <input type="radio"  formControlName="show_apply_button" [value]="0">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                        </div>
                        <span class="error-message" *ngIf="form.submitted && companyForm.controls['show_apply_button'].errors?.required" translate>validationMessages.required</span>
                    </div>
                </div>
                <div class="custom-row clearfix" [ngClass]="{'has-error':form.submitted && !companyForm.controls['description'].valid}">
                    <div class="col-sm-10 col-sm-offset-3 focus-no-padding">
                        <p-editor formControlName="description" [style]="{'height':'150px'}" onTextChange="render()" placeholder="{{'company_form.companyDescription' | translate}}">
                            <p-header>
                                <span class="ql-formats">
                                    <button class="ql-bold" aria-label="Bold"></button>
                                    <button class="ql-italic" aria-label="Italic"></button>
                                    <button class="ql-underline" aria-label="Underline"></button>
                                    <button class="ql-order" aria-label="Underline"></button>                                              
                                    <select title="Text Alignment" class="ql-align">
                                        <option selected>Gauche</option>
                                        <option value="center" label="Center"></option>
                                        <option value="right" label="Right"></option>
                                        <option value="justify" label="Justify"></option>
                                    </select>
                                    <button aria-label="Ordered List" class="ql-list" value="ordered" type="button"></button>
                                    <button aria-label="Bullet List" class="ql-list" value="bullet" type="button"></button>
                                </span>
                            </p-header>
                        </p-editor>
                        <span class="error-message" *ngIf="form.submitted && companyForm.controls['description'].errors?.maxlength" translate>validationMessages.tooLongText</span>
                    </div>
                    <div class="col-sm-2 alignment-right">
                    </div>
                </div>

            </div>
            <div class="col-sm-3 col-sm-offset-1 col-xs-12  flex-order-sm-1 text-center persPhoto-col">
                
                <div class="upload-image-container">
                    <div class="inner-container">
                        <a data-toggle="modal" data-target="#imageEditorModal">
                            <img [src]="perPhotoSrc" class="img-responsive">
                            <span class="upload-label" *ngIf="uploadLabelDisplay">Upload Logo</span>
                        </a>
                    </div>
                    <div class="upload-actions">
                        <a  *ngIf="!uploadLabelDisplay" class="edit-image" data-toggle="modal" data-target="#imageEditorModal">
                            <i class="fa fa-edit" aria-hidden="true"></i>
                        </a>
                        <a *ngIf="!uploadLabelDisplay" class="delete-image"  (click)="deleteProfilePicture()">
                            <i class="fa fa-trash" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>

                <!-- <div class="persPhoto-div">
                    <div class="persPhotoContainer" [ngClass]="{'imgError': imgError}">
                        <label for="persPhoto" class="persPhotoLabel">
                            <img [src]=perPhotoSrc class="img-responsive persPhoto">
                            <input id="persPhoto" type="file" style="display:none;" accept="image/jpeg, image/png" (change)="onFileChanged($event);">
                            <span class="upload" *ngIf="uploadLabelDisplay">Upload Logo</span>
                            <span  *ngIf="!uploadLabelDisplay" class="edit-photo">
                                <i class="fa fa-edit" aria-hidden="true"></i>
                            </span>
                        </label>

                        <span *ngIf="!uploadLabelDisplay" class="delete-photo"  (click)="deleteProfilePicture()">
                            <i class="fa fa-trash" aria-hidden="true"></i>
                        </span>
                    </div>
                    
                    <div class="imgError-div">
                        <span style="margin-left: 18px;" *ngIf="imgError" class="error-message" translate>{{imgError}}</span>
                    </div>   
                </div> -->
        
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-12 exInformation">
                <div class="row focus-container">
                    <div class="col-sm-12 text-center checkbox">
                        <div class="strike">
                            <span>
                            <label class="control-label">
                                <input id="extra" type="checkbox" [checked]="extraChecked" (change)="minimize()" > <span translate>shared.extraInformation</span> &nbsp;
                            </label><br>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="extraInformation">

            <div class="custom-row clearfix">
                <div class="col-sm-8 focus-no-padding-textarea validate-input">
                    <div class="form-group focus-container has-feedback ">
                        <div class="col-sm-4 col-md-4 col-xl-4 col-alignment-right">
                        </div>
                        <div class="col-sm-8 focus-no-padding validate-input CScreen" [ngClass]="{'has-val':companyForm.controls['company_size_id_temp'].value.company_size_id}">
                            <!--don't forget this  [ngClass]="{'has-val':educationForm.controls['degree_level_id'].value}" -->
                            <p-dropdown [options]="companySizeOpt" optionLabel="name" formControlName="company_size_id_temp" [filter]="true" [ngClass]="{'has-val':companyForm.controls['company_size_id_temp'].value.company_size_id}">
                                <ng-template let-degree pTemplate=" item ">
                                    <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                        <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                                    </div>
                                </ng-template>
                            </p-dropdown>
                            <span class="custom-underline"></span>
                            <!-- <span class="glyphicon form-control-feedback " aria-hidden="true "></span> -->
                            <label class="control-label custom-control-label " translate>company_form.companySize</label>
                        </div>

                    </div>
                </div>
            </div>

            <div class="custom-row clearfix" formGroupName="founded_date">
                <div class=" col-sm-2 alignment-right">
                </div>
                <div class="col-sm-8 focus-no-padding-textarea validate-input">
                    <div class="form-group focus-container has-feedback">
                        <!-- <label class="col-sm-2 alignment-right col-xs-2 col-xxs-2" style="color :#4f94df;font-size:16px">
                                    Founded
                                </label> -->
                        <div class="col-sm-4 focus-no-padding col-sm-offset-1 validate-input CScreen col-xs-5 col-xxs-5">
                            <!-- [ngClass]="{'has-val':founded_Date.controls['year'].value}" -->

                            <!--  [ngClass]="{'has-val':dateOfBirthControl.controls['year'].value}" -->
                            <p-dropdown [options]="yearOpts" formControlName="year" [filter]="true" placeholder="YYYY" [ngClass]="{'has-val':founded_Date.controls['year'].value}">
                                <!-- [ngClass]=" {'has-val':getValueToDD(dateOfBirthControl.controls['year'])}" #selectedYear (onChange)="onChangeYear(selectedYear.value, selectedMonth.value, selectedDay.value)" -->
                                <ng-template let-year pTemplate="item">
                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                        <div style="font-size:14px;float:left;margin-top:4px">{{year.label}}</div>
                                    </div>
                                </ng-template>
                            </p-dropdown>
                            <span class="custom-underline"></span>
                            <label class="control-label custom-control-label" translate>company_form.founded</label>


                        </div>
                        <div class="col-sm-4 focus-no-padding validate-input CScreen col-xs-5 col-xxs-5">
                            <!-- [ngClass]="{'has-error':form.submitted && founded_Date.controls['year'].value && !founded_Date.controls['month'].value }" -->
                            <!--  -->
                            <p-dropdown [options]="monthOpts" formControlName="month" [filter]="true" filterBy="label,value.name,code" 
                            filterMatchMode="contains" placeholder="MM">
                                <!-- #selectedMonth (onChange)="onChangeMonth(selectedMonth.value, selectedYear.value, selectedDay.value)" -->
                                <ng-template let-it pTemplate="selectedItem">
                                    <span style=" vertical-align:middle; float:left;" >{{it.label}}</span>
                                    <!-- {{it.label | translate}} -->
                                </ng-template>
                                <ng-template let-month pTemplate="item">
                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                        <div style="font-size:14px;float:left;margin-top:4px" >{{month.label}}</div>
                                        <!-- {{month.label | translate}} -->
                                    </div>
                                </ng-template>
                            </p-dropdown>
                            <span class="custom-underline"></span>
                            <!-- <span *ngIf="form.submitted && founded_Date.controls['year'].value && !founded_Date.controls['month'].value " class="glyphicon form-control-feedback glyphicon-remove" aria-hidden="true"></span>
                            <span *ngIf="form.submitted && founded_Date.controls['year'].value && !founded_Date.controls['month'].value " class="error-message" translate>Required</span> -->
                        </div>


                    </div>
                </div>
            </div>

            <div formArrayName="company_social_links" *ngFor="let item of company_social_links.controls; let i =index;" 
            [ngClass]="{'has-error':  form.submitted && ( item.hasError('companyLinkError') || !item.valid )  }" class="custom-row clearfix row ">
                <span [formGroupName]="i">
                    <div class="col-sm-2 alignment-right">
                    </div>
                    <div class="col-sm-8 focus-no-padding-textarea validate-input">
                    <div class="clearfix custom-row">
                        <div class="col-sm-4 col-xs-12 margin-bo-mo-10 focus-no-padding col-sm-offset-1">
            
                        <p-dropdown [options]="socialMediaOpt"
                                    optionLabel="name"
                                    [ngClass] = " {'has-val':company_social_links.at(i)['controls']['social_media_id'].value}"
                                    formControlName="type"
                                    [filter]="true"
                                    filterMatchMode = "startsWith"
                                    #socialMedia
                                    (onChange)="selectSocialMediaChange(socialMedia, i)">
                            <ng-template let-it pTemplate="selectedItem">
                            <img *ngIf ="it.label !== ''" src="./assets/images/Social Media/{{it.label}}.png"
                                    style="width:20px ; height: 20px ;vertical-align:middle; float:right;"/>
                            <span style="vertical-align:middle">{{it.label}}</span>
                            </ng-template>
                            <ng-template let-social pTemplate="it">
                                <img *ngIf="social.label !== ''" src="./assets/images/Social Media/{{social.label}}.png" style="width:20px ; height: 20px ; font-size:14px;float:right;margin-top:4px" />
                                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                    <div style="font-size:14px;float:left;margin-top:4px">{{social.label}}</div>
                                </div>
                            </ng-template>
                            </p-dropdown>
                            <span class="custom-underline"></span>
                            <label class="control-label custom-control-label"><span translate>company_form.socialMedia</span></label>
                        </div>
                        <div class="col-md-4 col-sm-4 col-xs-10 col-xxs-12 margin-bo-mo-10 focus-no-padding">
                            <input formControlName="url" type="text" class="form-control" id="social-media1">

                            <span class="custom-underline"></span>
                            <!-- <span *ngIf="item.hasError('companyLinkError') &&  form.submitted " class="glyphicon  form-control-feedback glyphicon-remove" aria-hidden="true"></span> -->
                        </div>
                        <div class="col-md-1 col-sm-1 col-xs-2 col-xxs-12 focus-no-padding">
                            <button *ngIf="i==0" (click)="addSLinks()" class="btn btn-gray" pTooltip="{{'shared.addMoreValues' | translate}}" tooltipPosition="top">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                            </button>
                            <button *ngIf="i!==0" (click)="removeSLinks(i)" class="btn btn-delete btn-delete-big">
                                <i class="fa fa-trash" aria-hidden="true"></i>
                            </button>  
                        </div>
    
                        <div>
                            <span style="color:#a94442" *ngIf="form.submitted && item.controls['url'].errors?.invalidUrlError" translate>{{item.controls['url'].errors?.invalidUrlError}}</span>
                            <span style="color:#a94442;font-size:12px;" *ngIf=" form.submitted && item.hasError('companyLinkError')" translate>{{ item.errors?.companyLinkError }}</span>
                        </div>
                        
                    </div>


                    </div>
                </span>
            </div>


</div>
<div class="form-group div-margin-top-40">
    <div class="col-sm-12 focus-no-padding-button " id="save">
            <!-- data-toggle="modal" data-target="#companyedit" -->
        <button class="btn btn-success btn-block">
            <i class="fa fa-floppy-o"></i>
            <span translate>shared.save</span>
        </button>
    </div>
</div>


    <!-- <p>{{ form.value | json }}</p> -->

</form>

</div>

<!-- *ngIf="display_save_msg" -->
<div class="modal fade med-modal" id="companyedit"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" (click)="closeModal_save()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Are you sure you want to save?</h4>
            </div>
            <form-edit [profileId]="companyProfileId" (closeModalPopup)="handlePopup($event)">
                <!-- [submitEdit]="sendData2" [profileId]="companyProfileId" -->

            </form-edit>
        </div>
    </div>
</div>

<div class="modal fade med-modal" id="companyModal" *ngIf="display_Profile_Languages" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" (click)="closeModal_Profile_Languages()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Choose another language for company Profile info</h4>
            </div>
            <form-language (closeModalPopup)="handlePopup($event)">
            </form-language>
        </div>
    </div>
</div>

<div class="modal fade image-editor-modal" id="imageEditorModal"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Logo Editor</h4>
            </div>
            <div class="modal-body">
                <app-image-editor
                (closeModalPopup)="handleImageEditorPopup($event)">

                </app-image-editor>
            </div>
        </div>
    </div>
</div>

