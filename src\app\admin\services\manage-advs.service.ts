import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class ManageAdvsService {

  url = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
      .subscribe(data => {
        this.url = data + 'admin/manage_adv';
      });
  }

  getActiveAdvs() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    const params = new HttpParams().set('status', 'active');
    return this.http.get(this.url, { headers, params: params });
  }

  getEndedAdvs() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    const params = new HttpParams().set('status', 'ended');
    return this.http.get(this.url, { headers, params: params });
  }

  updateStatusAdv(body) {
    console.log('body', JSON.stringify(body));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url, JSON.stringify(body), { headers });
  }

  getAdvEndedLog(advId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/adv_ended_log/' + advId, { headers });
  }

}
