import {AbstractControl, ValidationErrors} from '@angular/forms';

export class CompanyModalValidators {

    static verifiedPermissionsValidator(control: AbstractControl): ValidationErrors | null {
      let company_status_id = control.get('company_status_id');
      let permissions = control.get('permissions'); 
      // console.log("permissions.value.length",permissions.value?.length);
      // console.log("company_status_id.value",company_status_id.value);
      if(permissions.value?.length !==0 && company_status_id.value !==1 && company_status_id.value !==2 && company_status_id.value !==3 )
        return {'InvalidVerifiedPermissionError' : 'This Company should be verified first'};
   
      return null;
    }
  
  }