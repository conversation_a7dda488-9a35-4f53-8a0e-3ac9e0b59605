.modal-body-container {
  height: fit-content;
}


.modal-body{
  height: fit-content;
  max-height: unset !important;
  overflow: unset;
}

.fa.fa-search {
  padding-right: 5px;
  transition: all 0.5s ease-in-out;
}

fa.fa-search:hover {
 color: blue;
 font-size: 1.1em;
}
.btn.btn-success {
  margin-left: 450px;
}

.btn {
  margin-bottom: 10px;
  margin-top: 30px;
  margin-right: 10px;
}

input {
  border: none;
  border-bottom: 1px dashed #bbb;
  background:transparent;
  width: 100%;
  transition: all 0.5s ease-in-out;
  /* overflow: auto; */
}

input:focus {
  border: none !important;
  border-bottom: 1px solid dodgerblue;
}

input.ng-dirty.ng-touched.ng-invalid {
  border-radius: 5px;
  border: 2px solid #ff000059;
  background-color: #ffeaea66 ;
}

.alert.alert-danger {
  float: left;
  padding:5px;
  width: 250px;
  margin-left: 5px;
  margin-right: 5px;
}



.alert.alert-danger.inline-alert {
  padding: 0px;
  padding-left: 5px;
  margin: 0px;
  /* margin-top: -20px; */
  background-color: transparent;
  border: none;
  margin-right: -270px;
}

.table>tbody>tr>td, .table>tbody>tr>th, .table>thead>tr>th {
  border-top: none;
}

th.lang-name {
  color: #276ea4;
  border-top: 1px solid #ccc !important;
  background-color: #c8c5e659;
}

.border-right {
  border-right: 1px solid #ccc;
}

td {
  color: #777;
  width: 250px;
}


.fa.fa-plus {
  color: #276ea4;
  padding-right: 5px;
}

.alert.alert-danger {
  padding: 0px;
  padding-left: 5px;
  margin: 0px;
  background-color: transparent;
  border: none;
}




.table>tbody>tr>td,.table>tbody>tr>th, .table>tfoot>tr>td,
.table>tfoot>tr>th, .table>thead>tr>td,  .table>thead>tr>th {
      vertical-align: middle;
 }

 .badge.badge-primary {
  min-width: 10px;
  padding: 3px 10px;
  font-size: 14px;
  background-color: #393cab30;
  color: midnightblue;
  border-radius: 10px;
  border: 2px solid #dddddd;
  /* box-shadow: 0px 0px 2px 1px #aaa; */
}

.action{
  border: 2px solid #f0f0f0;
  border-radius: 5px;
  text-align: center;
  margin-left: 100px;
  margin-right: 100px;
  padding: 7px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  font-size: 17px;
}


.action .badge.badge-primary{
  padding: 3px 40px;
  font-size: 17px;
  background-color: #28a92859;
  color: forestgreen;
  border: none;
  border-radius: 10px;
}

.action .badge.badge-default{
  padding: 3px 40px;
  font-size: 17px;
  background-color: #a09a9a69;
  color: #777;
  border: none;
  border-radius: 10px;
}


.table-caption {
  font-weight: bold;
  font-size: 16px;

}


.table.table-bordered  thead tr th {
  background-color: #f9f9f9;
  color: midnightblue;
}

.table.table-bordered tbody th, .table.table-bordered  tr th  {
  color: midnightblue;
}

.table.table-bordered {
  border: 2px solid #e9e9e9;
  margin-left: 50px;
  margin-right: 10px;
  width: 90%;
  margin-top: 25px;
}

.table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th{
  max-width: 300px;
  min-width: 200px;
}

:host ::ng-deep .ui-multiselect.ui-widget.ui-state-default.ui-corner-all {
  max-width: 200px !important;
}


:host ::ng-deep li.ui-multiselect-item.ui-corner-all {
  overflow-x: hidden !important ;
}


:host ::ng-deep  .ui-multiselect-panel .ui-multiselect-items .ui-multiselect-item {
 overflow: hidden !important;
}


:host ::ng-deep   .ui-multiselect-panel.ui-widget.ui-widget-content.ui-corner-all {
  max-width: 300px !important;
}
