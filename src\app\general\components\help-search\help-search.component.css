.result-container {
    padding: 50px 30px 30px 30px;
}
h1 , h2 , h3{
  margin:10px 0;
}
h1{
  font-size:22px;
  font-weight: bold;
  color:#4876BA;
  /* color: #3D7BCE; */
}
h2{
  font-size:18px
}
h3{
  font-size:16px;
}
.result-card {
  padding:15px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius:2%;
  margin-bottom: 10px;
  transition: all 0.6s ease-in-out;
}
.result-card p {
    padding-left: 30px;
}

h4, h5 {
    padding-left: 15px;
    font-weight: bold;
  }
  
  p.no-matches {
    /* background-color: white; */
    padding: 10px;
    padding-left: 30px;
    margin:0 0 0 -5px;
    /* margin: 10px; */
  }
  
  h5 {
    padding-left: 20px;
  }

  
.list-group-item>.badge.badge-primary.category-badge {
    float: left;
  
    /* margin-bottom: 40px; */
  }
  
  ul.list-group.sub-group-list {
    margin-top: 0px;
  
  }
  ul.list-group.sub-group-list  .list-group-item {
    margin-left: 10px;
    margin-right: 10px;
    border-color: #f9f9f9;
    transition: all 0.5s ease-in-out;
  }
  
  ul.list-group.sub-group-list  .list-group-item:hover{
    font-size: 1.1em;
    background-color: #e3ecef;
  }
  
  a {
    display: block;
    cursor: pointer;
  }
  a:hover {
   text-decoration: none;
  }
  a.show {
    padding-bottom: 10px;
  }

  @media screen and (max-width: 767px ){
    .result-container {
      padding: 40px 20px 10px 20px;
    }
    h1{
      font-size:20px;
      font-weight: bold;
      color: #3D7BCE;
    }
    h2{
      font-size:16px
    }
    h3{
      font-size:14px;
      margin:2px 0;
    }
  }