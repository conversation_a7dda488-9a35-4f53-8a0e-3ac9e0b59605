import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FiltersWrapperComponent } from './filters-wrapper.component';

describe('FiltersWrapperComponent', () => {
  let component: FiltersWrapperComponent;
  let fixture: ComponentFixture<FiltersWrapperComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FiltersWrapperComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FiltersWrapperComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
