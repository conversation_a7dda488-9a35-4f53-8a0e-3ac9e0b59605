<h3 class="verf-heading"> Message Templates </h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ messages.length }}</span>
   Selected:<span class="badge badge-primary badge-pill">{{ filteredMessages.length }}</span></p>




 <p-table #dt [value]="messages" [(selection)]="filteredMessages" dataKey="id" styleClass="ui-table-messages" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="messages.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['id','title', 'main_cat', 'sub_cat']">
    <ng-template pTemplate="caption">
         <!-- Templates -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th><i class="fa fa-plus-circle" data-toggle="modal" data-target="#msgModal" (click)="displayCreateModal()" title="Add Template"></i></th>
            <th  style="width: 70px;" pSortableColumn="id">ID <p-sortIcon field="id"></p-sortIcon></th>
            <th pSortableColumn="title">Template Title <p-sortIcon field="title"></p-sortIcon></th>
            <th pSortableColumn="main_cat">Main Cat <p-sortIcon field="main_cat"></p-sortIcon></th>
            <th pSortableColumn="sub_cat">Sub Cat <p-sortIcon field="sub_cat"></p-sortIcon></th>
            <th style="width: 100px;">Actions </th>
        </tr>
        <tr>
            <th>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i *ngIf="filteredMessages.length !== 0" class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected messages" (click)="displayDeleteAlert(4)" style="margin-right:-22px;" ></i>
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'id', 'startsWith')" placeholder="" class="ui-column-filter id">
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'title', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <p-dropdown [options]="mainCats" (onChange)="dt.filter($event.value, 'main_cat_id', 'equals')" styleClass="ui-column-filter" [(ngModel)]="main_cat"  [showClear]="false" [filter]="true">
                  <ng-template let-option pTemplate="item">
                      <span>{{option.label}}</span>
                  </ng-template>
              </p-dropdown>
            </th>
            <th>
              <p-dropdown [options]="subCats" (onChange)="dt.filter($event.value, 'sub_cat_id', 'equals')" styleClass="ui-column-filter" [(ngModel)]="sub_cat"  [showClear]="false" [filter]="true">
                  <ng-template let-option pTemplate="item">
                      <span >{{option.label}}</span>
                  </ng-template>
              </p-dropdown>
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-message>
        <tr class="ui-selectable-row" (mouseover)="message.display = true" (mouseleave)="message.display = false" >
            <td>
                <p-tableCheckbox [value]="message" (click)="addToSelected(message)"></p-tableCheckbox>
            </td>
            <td>
              {{message.id}}
            </td>
            <td>
                {{message.title}}
            </td>
            <td>
               {{ message.main_cat | summary:30}}
            </td>
            <td>
               {{ message.sub_cat | summary:30}}
            </td>
            <td>
              <span *ngIf="message.display">
                <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete this message" (click)="displayDeleteAlert(3, message.id)"></i>
                <i class="fa fa-edit"  data-toggle="modal" data-target="#msgModal"  (click)="displayEditModal(message)"></i>
              </span>
             </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="6" style="text-align:center;padding:15px;">No messages found.</td>
        </tr>
    </ng-template>
</p-table>





<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayMsgModal" id="msgModal"  tabindex="-1" role="dialog" aria-labelledby="msgModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
         <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
         <h3>
           <span *ngIf="templateMode === 'create'">Add New Template</span>
           <span *ngIf="templateMode === 'update'">Edit </span>

        </h3>
      </div>
      <div class="modal-body">
         <app-message-modal [mode]="'template_form'" [templateMode]="templateMode" [template]="tempToPreview" [main_cats]="mainCats" [sub_cats]="subCats" (updateTemplatesTable)="updateTemplatesTable($event)"  ></app-message-modal>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<!-- end of  modal-->





<!-- add  modal-->
<div class="modal fade" *ngIf="displayAddModal" id="addValueModal"  tabindex="-2" role="dialog" aria-labelledby="addModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeAddModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="opperationNum === 3 || opperationNum === 4" style="color:crimson;" >Delete </h3>
      </div>
      <div class="modal-body">
       <!-- delete alert -->
        <div *ngIf="opperationNum === 3">
           <p>Are you sure you want to delete this message? </p>
           <button type="button" class="btn btn-light" (click)="deleteMsg()">Yes</button>
        </div>

        <div *ngIf="opperationNum === 4">
          <p>Are you sure you want to delete these {{ filteredMessages.length }} message? </p>
          <button type="button" class="btn btn-light" (click)="deleteMultiMsgs()">Yes</button>
       </div>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->
