<div class="modal fade" id="ColsManagementModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="cancel()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModal2Label" translate>
                    Columns Management
                </h4>
            </div>
            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row cols-row-container">
                        <div class="col-lg-3 col-sm-4 col-xs-5">
                            <div class="cols-container">
                                <div class="cols-header">Hide</div>
                                <div class="cols-body">
                                    <div *ngFor="let item  of arr1; let i = index" class="check-block">
                                        <div *ngIf=" fromComponent == 'cvs-table' && item.showInList">
                                            <input type="checkbox" (change)="onCheckboxChange($event, item, 'temp1')" [(ngModel)]="item.selected" name="unSelectedValue" value="{{item.field}}" id="{{item.field}}" class="checkbox-label" #checkboxes />
                                            <label for="{{item.field}}">{{item.title}}</label>
                                        </div>
                                        <div *ngIf=" fromComponent == 'advr-interface'">
                                            <input type="checkbox" (change)="onCheckboxChange($event, item, 'temp1')" [(ngModel)]="item.selected" name="unSelectedValue" value="{{item.field}}" id="{{item.field}}" class="checkbox-label" #checkboxes />
                                            <label for="{{item.field}}">{{item.header}}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-4 col-xs-2 actions-col">
                            <p class="remainingColsMsg" [ngClass]="(remainingColsAllowed > 0)?'success':'error'">Remaining columns you can add : {{remainingColsToAdd}}</p>
                            <div class="form-group actions-block">
                                <button (click)="exchangeItems(1,'arr1','arr2','add')" type="button" class="btn btn-primary fixed-width-btn">
                                        <span class="btn-txt btn-mar-right">Add</span>
                                        <span class="glyphicon glyphicon-arrow-right btn-icon"></span>
                                    </button>
                                <button (click)="exchangeItems(2,'arr2','arr1','remove')" type="button" class="btn btn-danger fixed-width-btn">
                                        <span class="glyphicon glyphicon-arrow-left btn-icon"></span>
                                        <span class="btn-txt btn-mar-left">Hide</span>
                                    </button>
                            </div>
                            <!-- <div class="row div-margin-top-20">
                                    <div class="col-xs-12 text-center">
                                        <button (click)="save()" type="button" class="btn btn-success save-btn fixed-width-btn">
                                            <span class="fa fa-floppy-o btn-icon"></span>
                                            <span class="btn-txt btn-mar-left">Save</span>
                                        </button>
                                        <button (click)="cancel($event)" class="btn btn-default cancel-btn fixed-width-btn">
                                            <span class="fa fa-times btn-icon"></span>
                                            <span class="btn-txt btn-mar-left">Cancel</span>
                                        </button>
                                    </div>
                                </div> -->
                        </div>
                        <div class="col-lg-3 col-sm-4 col-xs-5">
                            <div class="cols-container">
                                <div class="cols-header">Show</div>
                                <div class="cols-body">
                                    <div *ngFor="let item  of standardItems" class="check-block">
                                        <label>{{item.header}}</label>
                                    </div>
                                    <div *ngFor="let item  of arr2; let i = index" class="check-block">
                                        <input type="checkbox" (change)="onCheckboxChange($event, item, 'temp2')" name="selectedValue" value="{{item.field}}" id="{{item.field}}" class="checkbox-label" #checkboxes />
                                        <label for="{{item.field}}" *ngIf="fromComponent == 'advr-interface'">{{item.header}}</label>
                                        <label for="{{item.field}}" *ngIf="fromComponent == 'cvs-table'">{{item.title}}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center save-cancel-container">
                        <button (click)="save()" type="button" class="btn btn-success save-btn fixed-width-btn">
                                <span class="fa fa-floppy-o btn-icon"></span>
                                <span class="btn-txt btn-mar-left">Save</span>
                        </button>
                        <button (click)="cancel()" class="btn btn-default cancel-btn fixed-width-btn">
                                <span class="fa fa-times btn-icon"></span>
                                <span class="btn-txt btn-mar-left">Cancel</span>
                        </button>
                        <!-- <p-splitButton class="btn-inline add-remove-btn save" icon="pi pi-check" label="save"  (click)="submit(form)" styleClass="ui-button-success"></p-splitButton>
                            <p-splitButton class="btn-inline add-remove-btn cancel" icon="pi pi-times" label="cancel" (click)="cancel($event)"  styleClass="ui-button-secondary"></p-splitButton> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
