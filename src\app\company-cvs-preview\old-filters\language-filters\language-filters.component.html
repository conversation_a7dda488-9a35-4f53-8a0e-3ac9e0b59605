<div class="container-fluid form-horizontal">
    <div class="top-tools">
    <div class="form-group">
            <input type="checkbox" class="form-check-input" [(ngModel)]="dataModel.allMandatory" />
            <label class="form-check-label" for="activation">All Mandatory</label>
    </div>
    <div class="form-group">
        <button (click)="addLanguage()" class="btn btn-primary">Add Language</button>
    </div>
    </div>
 
    

    <div *ngFor="let language of dataModel.languages;let i = index" class="row">
        <div class="col-xs-10">
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
           
                </div>
                <div class="col-md-4 col-sm-4 col-xs-6 focus-no-padding">
                    <p-autoComplete field="name" inputId="id" styleClass="form-control" placeholder="Language" [suggestions]="temp['languages']" (completeMethod)="filterArray($event , 'languages') " [(ngModel)]="language.lang_id ">
                    </p-autoComplete>
                    <span class="custom-underline"></span>
                </div>
                <div class="col-md-4 col-sm-4 col-xs-6 focus-no-padding">
                    <p-dropdown [options]="filterData.language_levels" placeholder="Level" [(ngModel)]="language.level" optionLabel="name" optionValue="self_assessment_id"></p-dropdown>
                </div>
            </div>
        </div>
        <div class="col-xs-2 add-item">
            <button (click)="removeLanguage(i)" class="btn btn-danger">-</button>
        </div>
    </div>
</div>
<div class="filters-buttons">
    <button (click)="sendFilters()" class="btn btn-success">Apply</button>
    <button (click)="hideModal()" class="btn btn-default">Cancel</button>
</div>
