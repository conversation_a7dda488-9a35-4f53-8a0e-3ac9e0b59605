import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>} from '@angular/core';
import {FormArray, FormBuilder, FormGroup, FormControl, Validators} from '@angular/forms';
import {Subscription} from 'rxjs/Subscription';
import {QuestionService} from '../form-services/question.service';
import {Question} from './../Models/question';
import {Answer} from './../Models/answer';
import {ActivatedRoute} from '@angular/router';
import { Form } from "app/company/components/google-form/Models/form";
import { TextResponse } from "app/company/components/google-form/Models/text_response";
import { Choice } from "app/company/components/google-form/Models/choice";
import { ChoicesResponse } from "app/company/components/google-form/Models/choices_response";

@Component({
  selector: 'app-form-preview',
  templateUrl: './form-preview.component.html',
  styleUrls: ['./form-preview.component.css']
})
export class FormPreviewComponent implements OnInit, <PERSON><PERSON><PERSON>roy {

  questions: Question[];
  answers: Answer[];
  subscription: Subscription;
  myForm: FormGroup = new FormGroup({});
  responses;
  form: Form;
  form_id: number = 1;
  r: Object;
  //Edited Here....................
  hours = [
    '00','01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'
  ];

  minutes=['00','01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','22','23','24','25'
  ,'26','27','28','29','30', '31' ,'32' ,'33' ,'34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49',
    '50','51','52','53','54','55','56','57','58','59'
  ];


  constructor(private questionService: QuestionService,private route:ActivatedRoute) {

    this.subscription = this.questionService.getAllQuestions(this.form_id).subscribe(questions => {
      this.questions = questions['data'];

      this.form = new Form(this.form_id, this.questions);
      this.responses = [];

      this.buildForm(this.questions);
    });

  }

  buildForm(questions: Question[]) {
    for (let question of questions) {
      switch (question.controlType) {
        case 'Short Answer' :
          this.myForm.addControl(question.id.toString(), new FormControl('', question.isRequired ? Validators.required : null));
          break;
        case 'Paragraph' :
          this.myForm.addControl(question.id.toString(), new FormControl('', question.isRequired ? Validators.required : null));
          break;
        case 'dropDown' :
          this.myForm.addControl(question.id.toString(), new FormControl('', question.isRequired ? Validators.required : null));
          break;
        case 'Checkboxes' :
          this.myForm.addControl(question.id.toString(), new FormControl('', question.isRequired ? Validators.required : null));
          break;
        case 'Multiple Choice' :
          let group: FormGroup = new FormGroup({});
          for (let answer of question.choices) {
            group.addControl(answer.id.toString(), new FormControl(''));
          }
          this.myForm.addControl(question.id.toString(), group);
          break;
        case 'Date' :
          this.myForm.addControl(question.id.toString(), new FormControl('', question.isRequired ? Validators.required : null));
          break;
        case 'Time' :
          let time: FormGroup = new FormGroup({});
          time.addControl(`H${question.id}`, new FormControl('', question.isRequired ? Validators.required : null));
          time.addControl(`M${question.id}`, new FormControl('', question.isRequired ? Validators.required : null));
          time.addControl(`T${question.id}`, new FormControl('', question.isRequired ? Validators.required : null));
          this.myForm.addControl(question.id.toString(), time);
          break;
      }
    }

  }



  validateCheckBoxes() {
    let questionsToValidate: Question[] = [];
    let isValid = true;

    for (let question of this.questions) {
      if (question.controlType === 'Multiple Choice' && question.isRequired) {
        questionsToValidate.push(question);
      }
    }

    for (let question of questionsToValidate) {
      let isValidQuestion = false;
      let group: FormGroup = new FormGroup({});
      group = this.myForm.controls[question.id].value;
      for (let answer of question.choices) {
        if (group[answer.id] === true) {
          isValidQuestion = true;
          //console.log("isValidQuestion : ", isValidQuestion);
          break;
        }
      }
      if (!isValidQuestion) {
        isValid = false;
        break;
      }
    }
    return isValid;
  }

  answer() {

    let i = 0;

    for (let question of this.form.questions) {
     
      let answer: string = this.myForm.value[question.id];
      if (this.myForm.value[question.id] !== null) {

        if (question.controlType == 'Time') {
          // let answer = this.myForm.value[question.id];
          let h = answer[Object.keys(answer)[0]];
          let m = answer[Object.keys(answer)[1]];
          let t = answer[Object.keys(answer)[2]];

          // let endResult = h + ':' + m + ' ' + t;
          // let text_response: TextResponse = new TextResponse(question.id, endResult);
          // this.responses.push(text_response);
          if(h && m && t) {
            let endResult = h + ':' + m + ' ' + t;
            let text_response: TextResponse = new TextResponse(question.id, endResult);
            this.responses.push(text_response);
          }

        }

        else if (question.controlType == 'Short Answer' || question.controlType == 'Paragraph' || question.controlType == 'Date') {

          if(answer) {
          let text_response: TextResponse = new TextResponse(question.id, answer);
          // this.responses[i] = text_response;
          // i++;
          this.responses.push(text_response);
          }
        } else if (question.controlType == 'Checkboxes' || question.controlType == 'dropDown') {
          if(answer) { let choice: Choice = new Choice(this.myForm.value[question.id], question.id);

          this.responses.push(choice);
          // this.responses[question.id] = choice;
        } }else {
          let choices: Choice[] = [];
          let j = 0;
          for (let choice of question.choices) {
            if (this.myForm.value[question.id][choice.id] == true) {
              choices[j] = new Choice(choice.id);
              j++;
            }
          }
          if(choices.length > 0) {
            let choices_response = new ChoicesResponse(question.id, choices);
            this.responses.push(choices_response);
          }
        }

      }
    }

    this.route.paramMap.switchMap(params=>{
      let user_id:any=params.get('id');
     return this.questionService.addResponse(this.responses,1,user_id)
    }).subscribe(data =>{

      this.responses = [];
      this.myForm.reset();
    });

  }

  ngOnInit() {
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}









