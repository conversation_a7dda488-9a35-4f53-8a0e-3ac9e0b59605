import { Router } from '@angular/router';
import { LanguageService } from './../../../services/language.service';
import {Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {Question} from '../../../models/question';
import {TranslateService} from '@ngx-translate/core';
import {FaqsService} from '../../../services/faqs.service';
import { Subject } from 'rxjs/Subject';
import { Language } from 'app/admin/models/language';
import { Validators, FormBuilder, FormGroup, FormControl, FormArray } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
declare var $: any;

@Component({
  selector: 'app-edit-modal',
  templateUrl: './edit-modal.component.html',
  styleUrls: ['./edit-modal.component.css'],

})
export class EditModalComponent implements OnInit, OnDestroy {

  @Output() closeCreateModal = new EventEmitter();
  @Output() closeUpdateModal = new EventEmitter();
  @Output() closeDeleteModal = new EventEmitter();
  // @Input('question') question: Question = { id: null , category: '', question: '', answer: '',
  // order: null, activation: null, type: '', langId: 1, catId: null};
  @Input() question: Question = { id: null , category: '', question: '', answer: '',
  order: null, activation: null, type: '', langId: 1, catId: null};
  faqForm;
  private ngUnsubscribe: Subject<any> = new Subject();
  orders: { 'value': number, 'label': string}[] = [];
  @Input('languagesArray')languagesArray: Language[] = [];
  @Input('mode')mode = 'create';
  @Input('openedFromSidebar')openedFromSidebar = true;
  @Input('categories')categories: {'user_categories': { 'value': number, 'label': string}[][],
         'employer_categories': { 'value': number, 'label': string}[][]}
            = { 'user_categories': [], 'employer_categories': []};
  questionCategories:    { 'label': string, 'value': number, 'translated_languages_id': number}[] = [];
  questionTranslations: { 'translated_language_id': number, 'question': string, 'answer': string}[] = [];
  currentLangId = 1;
  oldQuestion;
  insertedLanguagesIds: number[] = [];


constructor(private fb: FormBuilder,
            private translate: TranslateService,
            private faqsService: FaqsService,
            private languageService: LanguageService,
            public sanitizer: DomSanitizer,
            private router: Router) {
  translate.addLangs(['en', 'ar']);
  translate.setDefaultLang('en');
  let browserLang = translate.getBrowserLang();
  translate.use('en');




}

ngOnInit() {
  if (this.mode === 'create') {
      this.buildEmptyForm();
      if ( this.openedFromSidebar) {
        this.getLanguages();
      }
  } else if (this.mode === 'edit') {
      this.buildFilledForm();
      this.oldQuestion = {
        'id'        : this.question.id,
        'catId'     : this.question.catId,
        'category'  : this.question.category,
        'order'     : this.question.order,
        'activation': this.question.activation,
        'type'      : this.question.type,
        'question'  : this.question.question,
        'answer'    : this.question.answer,
        'langId'    : this.question.langId
      };
  } else if (this.mode === 'preview') {
      this.initializeView();
      this.buildFilledForm();
      this.oldQuestion = {
        'id'        : this.question.id,
        'catId'     : this.question.catId,
        'category'  : this.question.category,
        'order'     : this.question.order,
        'activation': this.question.activation,
        'type'      : this.question.type,
        'question'  : this.question.question,
        'answer'    : this.question.answer,
        'langId'    : this.question.langId
      };
  }

}

buildEmptyForm() {
  // filling order dropdown:
  for (let order = 1; order <= 100; order++) {
    this.orders.push({ 'value': order, 'label': order.toString() });
  }
  this.orders.unshift({ 'value': null, 'label': ''});
  // console.log('orders', this.orders);


  this.faqForm = this.fb.group({
    'type'      : ['User Faq', Validators.required],
    'faq_category_id': [null, Validators.required],
    'order'          : [null, Validators.required],
    'active_temp'    : [false],
    'active'         : [0],
    'faq_trans'      : this.fb.array([])

  });
  console.log('form', this.faqForm);
  if (!this.openedFromSidebar) {
    for ( let lang of this.languagesArray) {
      this.fillFaqTrans(lang.id);
    }
  }
}
getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log(res);
      let temp = res['data'];
      for ( let lang of temp) {
        this.languagesArray.push({
          'id'  : lang.id,
          'name': lang.name
        });
        this.fillFaqTrans(lang.id);
      }

      console.log('languages array', this.languagesArray);
      console.log('lang arr length', this.languagesArray.length);
      this.getCategories();
  });
}

getCategories() {
  for (let i = 0; i < this.languagesArray.length; i++) {
    console.log('inside cat loop', i);
    this.faqsService.getFaqsCategories(i + 1).takeUntil(this.ngUnsubscribe).subscribe( categoriesRes => {
    console.log('cat res', categoriesRes);
    // filling user_cat array
    let categoriesTemp = categoriesRes['user_categories'];
    this.categories.user_categories[i] = [];
    for (let category of categoriesTemp) {
      this.categories.user_categories[i].push({
            'label': category.faq_category_translation[0].name,
            'value':  category.id,
      });
    }
    this.categories.user_categories[i].unshift({ 'label': ' ', 'value': null });
    console.log('user category[' + i + '] ', this.categories.user_categories[i]);

    // filling employer_cat array
    let categoriesTemp2 = categoriesRes['employer_categories'];
    this.categories.employer_categories[i] = [];
    for (let category of categoriesTemp2) {
      this.categories.employer_categories[i].push({
            'label': category.faq_category_translation[0].name,
            'value':  category.id,
      });
    }
  this.categories.employer_categories[i].unshift({ 'label': ' ', 'value': null });
  console.log('employer category[' + i + '] ', this.categories.employer_categories[i]);


    });

  }
}

private fillFaqTrans(langId) {
  this.faq_trans.insert(this.faq_trans.length, this.createFaqTransControls(langId));
}
private createFaqTransControls(langId: number) {
  if (langId === 1) {
    return new FormGroup({
      'translated_languages_id': new FormControl(langId),
      'question'               : new FormControl('', Validators.required),
      'answer'                 : new FormControl('', Validators.required)
    });
  }
  return new FormGroup({
      'translated_languages_id': new FormControl(langId),
      'question'               : new FormControl(''),
      'answer'                 : new FormControl('')
    });

}


buildFilledForm() {
  // filling order dropdown:
  for (let order = 1; order <= 100; order++) {
  this.orders.push({ 'value': order, 'label': order.toString() });
  }
  this.orders.unshift({ 'value': null, 'label': ''});
  // console.log('orders', this.orders);

  this.faqForm = this.fb.group({
    'id'             : [ this.question.id],
    'type'           : [ this.question.type, Validators.required],
    'faq_category_id': [this.question.catId, Validators.required],
    'order'          : [this.question.order, Validators.required],
    'active_temp'    : [this.question.activation],
    'active'         : [this.question.activation],
    'faq_trans'      : this.fb.array([
      new FormGroup({
          'translated_languages_id': new FormControl(1),
          'question'               : new FormControl(this.question.question, Validators.required),
          'answer'                 : new FormControl(this.question.answer, Validators.required)
      })
  ])

  });
  console.log('form', this.faqForm);
  this.insertedLanguagesIds.push(1);
  // this.getLanguages();
  this.getQuestionTranslations();

}

initializeView() {
  this.questionTranslations.push({
    'question': this.question.question,
    'answer'  : this.question.answer,
    'translated_language_id': this.question.langId
   });
   this.insertedLanguagesIds.push(1);
   this.questionCategories.push({
     'label': this.question.category,
     'value': this.question.catId,
     'translated_languages_id': this.question.langId
   });
   this.getTranslations();
}


getTranslations() {
  this.faqsService.getFaq(this.question.id).takeUntil(this.ngUnsubscribe)
  .subscribe( res => {
    let temp = res['feq'];
    console.log('temp', temp);
    for (let i = 0; i < temp.faq_translation.length; i++) {
      if ( temp.faq_translation[i].translated_languages_id !== 1 && !this.isAdded( temp.faq_translation[i].translated_languages_id)) {
        this.questionTranslations.push({
          'question'  : temp.faq_translation[i].question,
          'answer'    : temp.faq_translation[i].answer,
          'translated_language_id': temp.faq_translation[i].translated_languages_id,
        });
        this.insertedLanguagesIds.push(temp.faq_translation[i].translated_languages_id);
        console.log('inside 1 if', i);
      }
    }

    console.log('inserted lang', this.insertedLanguagesIds);
    for (let i = 0; i < this.languagesArray.length; i++) {
      if ( temp.faq_category.faq_category_translation[i].translated_languages_id !== 1) {
        this.questionCategories.push({
          'label': temp.faq_category.faq_category_translation[i].name,
          'value': temp.faq_category_id,
          'translated_languages_id': temp.faq_category.faq_category_translation[i].translated_languages_id,
        });

    }
    }

    console.log('questionTranslations', this.questionTranslations);
    console.log('questioCat', this.questionCategories);

  });
}



getQuestionTranslations() {
  this.faqsService.getFaq(this.question.id).takeUntil(this.ngUnsubscribe).subscribe(res => {
      let temp = res['feq'];
      console.log('get faq', temp);
      for ( let i = 0; i < temp.faq_translation.length; i++) {
         if (temp.faq_translation[i].translated_languages_id !== 1) {
            this.faq_trans.insert(this.faq_trans.length,
              new FormGroup({
                'translated_languages_id': new FormControl(temp.faq_translation[i].translated_languages_id),
                'question'               : new FormControl(temp.faq_translation[i].question),
                'answer'                 : new FormControl(temp.faq_translation[i].answer)
              })
              );
              this.insertedLanguagesIds.push(temp.faq_translation[i].translated_languages_id);
          }
      }

      console.log('faqForm filled', this.faqForm);
  });

}

isAdded(n: number) {
  for (let i = 0; i < this.insertedLanguagesIds.length; i++) {
    if (n === this.insertedLanguagesIds[i]) {
      return true;
    }
  }
  return false;
}






changeLang( langId: number) {
  this.translate.use(this.getLangAbbrev(langId));
  this.currentLangId = langId;

}


getLangAbbrev(langId: number) {
  return this.languageService.getLangAbbrev(langId);
}

saveQuestion(formValue) {
  console.log('is form valid?', this.faqForm.valid);
  console.log(this.faqForm.value);

  if (this.faqForm.valid) {
      let dataToSend = this.faqForm.value;
          if (dataToSend.active_temp) {
            console.log('inside if');
            let flag = false;
            for (let i = 0; i < this.languagesArray.length; i++) {
              if ((dataToSend.faq_trans[i].question === null) ||
                  (dataToSend.faq_trans[i].question === '') ||
                  (dataToSend.faq_trans[i].answer === null) ||
                  (dataToSend.faq_trans[i].answer ===  '')) {
                flag = true;
              }
            }
            if (flag) {
              this.active_temp.setValue(0);
              alert('you cannot set the activation to true unless you enter all the faq translations ');
              return;
            }
          }

          dataToSend.active = (dataToSend.active_temp === 0 || dataToSend.active_temp === false) ?  0 : 1;
          console.log('active temp', dataToSend.active_temp, ',active:', dataToSend.active);
          if (this.mode === 'create') {
              this.faqsService.createFaq(dataToSend).subscribe(res => {
                console.log('create faq res', res);
                if (this.openedFromSidebar) {
                  // let temp = res['data'];
                  // let newQuestion = {
                  //   'id'        : temp.id,
                  //   'category'  : temp.faq_category.faq_category_translation[0].name,
                  //   'question'  : temp.faq_translation[0].question,
                  //   'answer'    : temp.faq_translation[0].answer,
                  //   'order'     : temp.order,
                  //   'activation': temp.active,
                  //   'type'      : temp.faq_type,
                  //   'catId'     : temp.faq_category_id,
                  //   'langId'    : temp.faq_translation[0].translated_languages_id,
                  // };
                  // this.faqsService.refreshQuestionValue(newQuestion);
                  this.closeCreateModal.emit();
                  this.router.navigateByUrl('/manage/dashboard/faqs');
                } else {
                  this.closeCreateModal.emit({'data': res['data'] });
                }
              });
          } else if (this.mode === 'edit') {
              this.faqsService.updateFaq(dataToSend, this.id.value).subscribe(res => {
                console.log('update faq res', res);
                this.closeUpdateModal.emit({'new': res['data'], 'old': this.oldQuestion  });
              });
          }

      } else {
           alert('please enter valid values, english translation(question & answer) ' +
                 'must be filled at least');
      }





}

deleteFaq(q) {
  this.faqsService.deleteFaq(q.id).subscribe(res => {
      console.log('delete res', res);
      let temp = res['data'];
      console.log('temp in modal', temp);
      this.closeDeleteModal.emit({ 'data': res['data']});
    }
     , (error) => this.closeDeleteModal.emit({ 'error': error})
  );

}



get faq_category_id() {
  return this.faqForm.get('faq_category_id');
}

get id() {
  return this.faqForm.get('id');
}

get category() {
  return this.faqForm.get('category');
}

get order() {
  return this.faqForm.get('order');
}

get active_temp() {
  return this.faqForm.get('active_temp');
}

get type() {
  return this.faqForm.get('type');
}

// get question() {
//   return this.faqForm.get('faq_trans.question') ;
// }

// get answer() {
//   return this.faqForm.get('faq_trans.answer') ;
// }

get faq_trans() {
  return this.faqForm.get('faq_trans') ;
}

get faq_transArray() {
  return this.faqForm.get('faq_trans') as FormArray ;
}

set setCategory(category: string) {
  this.faqForm.set('category', category) ;
}


ngOnDestroy(): void {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();

}

}





