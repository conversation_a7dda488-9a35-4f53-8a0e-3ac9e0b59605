<!-- search bar replaced with filters and filter-wrapper component-->
<!-- Start of header -->
<div class="header container-fluid page-navbar">
    <div class="row search-row">
        <div class="col-md-6 col-sm-9 col-xs-12">
            <div class="toggle-folders-btn">
                <i class="fa fa-bars" (click)="toggleFolders()"></i>
            </div>
            <input type="text" class="search-input" [(ngModel)]="keyword" pTooltip="Search for name, job title, education, job type" tooltipPosition="top" (keyup.enter)="search()">
            <button class="btn search-btn" (click)="search()"><i aria-hidden="true" class="fa fa-search"></i></button> 
        </div>
    </div>
</div>
<!-- End of header -->