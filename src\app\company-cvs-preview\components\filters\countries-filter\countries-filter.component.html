<p-multiSelect
    [options]="countriesList"                            
    [(ngModel)]="countries"
    optionLabel="name"
    [filter]="true"
    filterBy="label,value.name"
    [showTransitionOptions]="'1ms'"
    [hideTransitionOptions]="'2ms'"
    styleClass="cust-p-multiselect"
    defaultLabel="Countries"
    [displaySelectedLabel]="false"
    (onChange)="sendFiltersOnChange($event)"
   >
   <!-- (onPanelHide)="sendFiltersOnHide($event)" -->
    <ng-template let-code pTemplate="it">
        <div class="ui-helper-clearfix" style="position: relative;">
            <div style="font-size:14px;float:left;margin-top:4px;width:275px;">
                <span style="width:250px;display: inline-block;">
                    {{code.label}}
                </span>
                <img *ngIf="code.label !== 'Canada'" src="./assets/images/CountryCode/{{code.value.code}}.gif" style="display: inline-block;width:16px;" />
                <img *ngIf="code.label === 'Canada'" src="./assets/images/CountryCode/+1(1).gif" style="display: inline-block;width:16px;" />
            </div>
        </div>
    </ng-template>
</p-multiSelect>
