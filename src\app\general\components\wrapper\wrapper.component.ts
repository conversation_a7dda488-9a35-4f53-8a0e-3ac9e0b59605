import { Component, OnInit } from '@angular/core';
import { GeneralService } from '../../services/general.service';

@Component({
  selector: 'app-wrapper',
  templateUrl: './wrapper.component.html',
  styleUrls: ['./wrapper.component.css']
})
export class WrapperComponent implements OnInit {

  role = '';
  constructor(
    private generalService:GeneralService
  ) { }

  ngOnInit() {
    if (localStorage.getItem('role')) {
      this.role = localStorage.getItem('role');
    } else {
      this.role = 'unauth';
    }

    this.generalService.internalMessage.subscribe((data) => {
      if (data['dist'] === 'general-wrapper') {
        if(data['message'] ==='roleChanged')
          this.role = localStorage.getItem('role');
      }
    });
  }

}
