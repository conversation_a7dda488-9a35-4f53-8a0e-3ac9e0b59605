import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, Validators } from '@angular/forms';
import { Title, Meta } from '@angular/platform-browser';
import { EmailValidator } from 'shared/validators/email.validators';
import { AuthService } from 'shared/shared-services/auth-service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-forget-password',
  templateUrl: './forget-password.component.html',
  styleUrls: ['./forget-password.component.css']
})
export class ForgetPasswordComponent implements OnInit {
  forgetPasswordForm;
  resetMessage;
  constructor(
    private authService: AuthService,
    private router: Router,
    private  fb: FormBuilder,
    private title: Title,
    private meta:Meta) 
    { }

  ngOnInit(): void {
    this.title.setTitle('CVeek Website | Forget password ');
    this.meta.updateTag({ name: 'robots', content: 'noindex' });

    this.forgetPasswordForm = this.fb.group({
      email: ['', [Validators.required, EmailValidator.isValidEmailFormat] ],
    });
  }


  forgetPassword() {
    if(this.forgetPasswordForm.valid){
      const data = {'email' : this.forgetPasswordForm.get('email').value};
      localStorage.setItem('oldUser', JSON.stringify(data));
      this.authService.forgetPassword(data).subscribe(res => {
        if (res['error']) {
          this.resetMessage = res['error'];
          
          // if user trying to reset password but its account is not verified yet, we navigate the user to verification page
          if(res['type']==='UnverifiedEmailRest'){
            this.authService.changeError(res['error']);
            this.router.navigate([res['help']]);
          }
        }
        if (res['data']) {
          this.router.navigate(['/m/user/reset-password-verification']);
        }
      });
    }
  }
}
