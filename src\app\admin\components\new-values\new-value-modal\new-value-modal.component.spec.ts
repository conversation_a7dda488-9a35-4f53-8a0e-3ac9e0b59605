import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { NewValueModalComponent } from './new-value-modal.component';

describe('NewValueModalComponent', () => {
  let component: NewValueModalComponent;
  let fixture: ComponentFixture<NewValueModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NewValueModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NewValueModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
