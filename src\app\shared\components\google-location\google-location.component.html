<div class="row">
  <!-- Location autocomplete -->
  <div class="form-group row equal-height-row-cols">
    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
      <span>Location</span>
    </div>
    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
      <input placeholder="Location" type="text" [(ngModel)]=" this.dataModel.locationSlected"
        class="form-control" #googlelocationplaceLocation>
        <span class="custom-underline"></span>
    </div>
  </div>

  <!-- Distance -->
  <div *ngIf=" this.display_distanse">
      <div class="form-group row equal-height-row-cols">
          <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
              <span>Distance Range</span>
          </div>
          <div class="col-sm-3 col-xs-12 focus-no-padding">
            <input type="number" class="form-control" [(ngModel)]=" this.dataModel.distance" (keyup)="setDistanse($event)"
              style="height:100%;" placeholder="Distance Range">
            <span class="custom-underline"></span>
          </div>
          <div class="col-sm-2 col-xs-12 label-fixed mar-top-mob">
              <span>Distance Unit</span>
          </div>
          <div class="col-sm-3 col-xs-12 focus-no-padding">
              <p-dropdown [options]=" this.dataModel.distanceTool" optionLabel="label"
                [(ngModel)]=" this.dataModel.unit_distance" placeholder="Distance Unit"
                (onChange)="setDistanse($event)" styleClass="unit_distance">
              </p-dropdown>
          </div>
      </div>
      <!-- <div class="form-group row equal-height-row-cols">
        <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
          
        </div>
        <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
            <p style="color: #555;font-weight: normal;text-align:justify"><span style="font-weight:bold">Note:</span> If you want to filter based on location field, please choose both distance and unit distance fields to get the required results</p>
        </div>
      </div> -->
  </div>

  <!-- map -->
  <!-- <div class="form-group row">
    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed"> </div>
    <div class=" col-md-8 col-sm-8 col-xs-12 focus-no-padding">
      <app-map [sub]="currentSub" birthOrCurrent="current" [type]="currentType"
        [search]="googlelocationplaceLocationRef"></app-map>
    </div>
  </div> -->

</div>
