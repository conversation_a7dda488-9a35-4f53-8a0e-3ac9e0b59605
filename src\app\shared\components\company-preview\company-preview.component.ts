import { Component, OnInit, Input } from '@angular/core';
import { PublicPreviewService } from '../../../company/services/public-preview.service';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PostJobService } from '../../../company/services/post-job.service';
import { Title, Meta } from '@angular/platform-browser';
import { MessageService } from 'primeng/api';
import { GeneralService } from '../../../general/services/general.service';
declare var $: any;

@Component({
  selector: 'company-preview',
  templateUrl: './company-preview.component.html',
  styleUrls: ['./company-preview.component.css'],
  providers: [MessageService]
})
export class CompanyPreviewComponent implements OnInit {
  @Input('companyId') companyId: any;
  @Input('sourceInterface') sourceInterface: String;
  company_Id ;
  advs;
  profile;
  branches;
  companyLogoPath = '';
  username = '';
  role='';
  showLoader = true;
  appliedToCompany = null;
  actionAfterLogin = null;
  constructor(private publicPreviewService:PublicPreviewService,
              private imageProcessingService: ImageProcessingService,
              private route: ActivatedRoute,
              private router: Router,
              private postJobService: PostJobService,
              private title: Title,
              private meta:Meta,
              private messageService: MessageService,
              private generalService:GeneralService) { }

  setRoutingParams(){
    this.route.params.subscribe(res => {
      this.username = res['username'];
    //   this.company_Id = res['companyId'];
    });
  }

  ngOnInit(): void {
    if (localStorage.getItem("role")) {
      this.role = localStorage.getItem("role");
    }

    //for public company preview 
     //example:  https://www.cveek.com/i/c/username
    if(this.companyId === undefined){
      this.setRoutingParams();
      this.publicPreviewService.getPreviewDataByUsername(this.username).subscribe(res => {
        this.initalizaData(res);
      });
    }
    //for public company preview tab in company account
    else {
      this.company_Id = this.companyId;
      this.publicPreviewService.getPreviewData(this.company_Id).subscribe(res => {
        this.initalizaData(res);
      });
    }


    this.generalService.internalMessage.subscribe((data) => {
      if (data['src'] === 'login') {
        if (data['message'] === 'loginSuccess' && this.actionAfterLogin === 'apply' && this.companyId!==undefined) {
          $('#authModal').modal('hide');
          this.role = localStorage.getItem('role');
          this.generalService.notify('roleChanged', 'company-preview', 'general-wrapper', {'role':this.role });
          this.applyToCompany();
        }
      }
    });
  }

  initalizaData(data){
    let previewData;
    previewData = data;
    this.profile = previewData.profile;
    this.branches = previewData.branches;
    this.advs = previewData.advs;

  //  this.companyLogoPath = this.imageProcessingService.getImage(this.profile.logo,'storage/company/logo/','');
    this.companyLogoPath = this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail',this.profile.logo);
    if(this.profile && this.profile.company_name){
      //set meta title and meta description for company
      if(this.profile.meta_title){
        this.title.setTitle(this.profile.meta_title);
        this.meta.updateTag({ property: 'og:title', content: this.profile.meta_title });
      }
      if(this.profile.meta_description){
        this.meta.updateTag({ name: 'description', content: this.profile.meta_description });
        this.meta.updateTag({ property: 'og:description', content: this.profile.meta_description });
      }
      // let pageTitle = this.profile.company_name + ' | CVeek';
      // this.title.setTitle(pageTitle);
    }
    else this.title.setTitle('CVeek');

    if(this.profile.logo)
      this.meta.updateTag({ property: 'og:image', content: this.companyLogoPath, itemprop: 'image' });

    this.company_Id = previewData.profile.company_id;
    this.companyId = previewData.profile.company_id;
    this.appliedToCompany= previewData.directly_applied;
    this.showLoader = false;
  }

  goURLnew(url: string) {
    if (url) {
      if ( url.substr(0, 4) !== 'http' ) {
        window.open('http://' + url, '_blank');
      }
      if ( url.substr(0, 4) == 'http' ) {
        window.open(url, '_blank');
      }
    }
  }

  changeAdvId(Advid, AdvLang, source){
    this.postJobService.changeAdvId_lang(Advid, AdvLang,source)
  }
  // displayAdvrModal(job_adv_id , source) {
  //   if(source === 'manage_advs'){
  //     let status = {label: "publish", value: "publish"};
  //     let source_type_adv = ['manage_advs', status];
  //     this.changeAdvId(job_adv_id, 1,source_type_adv);
  //     $('#AdvrsModal').modal('toggle');
  //   }
  //   else if(source === 'search-job'){
  //     this.changeAdvId(job_adv_id, 1,source);
  //     $('#AdvrsModal').modal('toggle');
  //   }

  // }

  displayAdvrPage(job_adv_id, slug) {
    const url = this.router.serializeUrl(this.router.createUrlTree(['/jobs', job_adv_id, slug]));
    window.open(url, '_blank');
    // if(this.sourceInterface ==='company-account-public-preview'){
    //   const bc = new BroadcastChannel('company-preview-advrPreview');
    //   bc.postMessage(this.sourceInterface);
    // }
    
  }

  applyToCompany(){
    if (this.role !== 'unauth'){
      this.publicPreviewService.applyToCompany(this.companyId).subscribe(res => {
        if (res['error']) {
          this.messageService.add({ severity: 'error', summary: 'Apply to company', detail: res['error'],life:5000 });
          if(res['type']==='directly_applied')
            this.appliedToCompany = true;
        } 
        else {
          let successMessage = '';
          successMessage = 'Applied Successfully to ' + this.profile.company_name;
          this.messageService.add({ severity: 'success', summary: 'Apply to company', detail: successMessage,life:5000 });
          this.appliedToCompany = true;
        }
      });
    }
    else{
      this.actionAfterLogin = 'apply';
      $('#authModal').modal('toggle');
    }
    
  }


}
