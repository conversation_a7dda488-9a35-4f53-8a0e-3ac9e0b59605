<app-pre-loader [show]="items.length ===0"></app-pre-loader>

<div [hidden]="items.length === 0">
    <h3 class="verf-heading"> Manage {{ title }}(s) </h3>
    <br><br>
    <p>Count: <span class="badge badge-primary badge-pill">{{ items.length }}</span>
    Selected: <span class="badge badge-primary badge-pill">{{ filteredItems.length }}</span></p>

    <div *ngIf="showNotif" class="text text-danger">{{ notification }}</div>

    <p-table #dt [value]="items" dataKey="id" styleClass="ui-table-items" [reorderableColumns]="true"
        [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
        [paginator]="items.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
        [responsive]="true"
        [columns]="cols"  [(selection)]="filteredItems"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
        [filterDelay]="0" [rowHover]="true">

        <ng-template pTemplate="caption">

            <div class="ui-table-globalfilter-container">
                <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
            </div>

        </ng-template>
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th>
                    <!-- <i class="fa fa-plus-circle" data-toggle="modal" data-target="#itemModal" (click)="displayCreateModal()"></i> -->
                </th>
                <th [class]="col.field" *ngFor="let col of columns" class="ui-column-title" [pSortableColumn]="col.field" >
                    {{col.header}}
                    <p-sortIcon [field]="col.field"></p-sortIcon>
                </th>
                <th>Actions</th>
            </tr>
            <tr>
            <th>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i *ngIf="filteredItems.length !== 0" class="fa fa-trash" data-toggle="modal" data-target="#deleteModal" title="delete selected messages" (click)="displayDeleteAlert(4)" ></i>
            </th>
            <th *ngFor="let col of columns">
                <p-dropdown *ngIf="col.type=== 'dropdown'" [options]="col.value" [(ngModel)]="dropdown"   [filter]="true" (onChange)="dt.filter($event.value, col.field, col.filterMatchMode)">
                <ng-template let-parent pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                        <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                    </div>
                </ng-template>
                </p-dropdown>
                <p-dropdown *ngIf="col.type=== 'multi-select'" [options]="col.value" [(ngModel)]="multiSelect"   [filter]="true" (onChange)="dt.filter($event.value, col.field, col.filterMatchMode)">
                <ng-template let-parent pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                        <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                    </div>
                </ng-template>
                </p-dropdown>
                <input *ngIf="col.type !== 'dropdown' && col.type !== 'multi-select'" style="width: 100%;" pInputText type="text" (input)="dt.filter($event.target.value, col.field, col.filterMatchMode)"
                    placeholder="" class="ui-column-filter">
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns"
        let-index="rowIndex">
            <tr class="ui-selectable-row" id=" {{ data.id }}" [pReorderableRow]="index"  (mouseover)="displayActions[index] = true" (mouseleave)="displayActions[index] = false" >

                <td>
                <i class="pi pi-bars" pReorderableRowHandle></i>
                <p-tableCheckbox [value]="data" ></p-tableCheckbox>
                </td>

                <td *ngFor="let col of columns"  pEditableColumn>
                    <ng-container *ngIf="col.field === 'location'"  #locationTemp>
                        <span *ngIf="data.location !== undefined ">{{  data.location.city?data.location.city + ', ' + data.location.country + ', '+ data.location.country_code + '\n':data.location.street_address + ', ' + data.location.country + ', '+ data.location.country_code + '\n' }}</span>
                        <!-- <span *ngIf="data.location !== null ">{{  data.location.city?data.location.city + ', ' + data.location.country + ', '+ data.location.country_code + '\n':data.location.street_address + ', ' + data.location.country + ', '+ data.location.country_code + '\n' }}</span> -->
                        <i class="fa fa-external-link"  data-toggle="modal" data-target="#itemModal"  title="view & edit all locations of this item" (click)="viewLocations(data)"></i>
                    </ng-container>
                    <ng-container *ngIf="col.type === 'dropdown'">
                        <p-cellEditor>
                            <ng-template pTemplate="input">
                                <p-dropdown #ddl [options]="col.value" [(ngModel)]="this.items[index][col.field]" [style]="{'width':'100%'}" [filter]="true"
                                (onChange)="editCellData(data, 'change',ddl.value, col.field)"
                                (onFocus)="storeOldValue(ddl.value, col.field, index)" >
                                <ng-template let-parent pTemplate="item">
                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                        <div #dropdownVal style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                                    </div>
                                </ng-template>
                                </p-dropdown>
                            </ng-template>
                            <ng-template pTemplate="output">
                                {{ (data[col.field] || '----') +'-' + getLabelByValue([data[col.field]], col.value)}}
                            </ng-template>
                        </p-cellEditor>
                    </ng-container>
                    <ng-container *ngIf="col.type === 'multi-select'">
                        <p-cellEditor>
                        <ng-template pTemplate="input">
                            <p-multiSelect #ms [options]="col.value" [(ngModel)]="this.items[index][col.field]"
                            (onChange)="editCellData(data, 'change',ms.value, col.field)"
                            (onFocus)="storeOldValue(ms.value, col.field, index)"    [panelStyle]="{minWidth:'13em'}" >
                            <ng-template let-field pTemplate="item">
                                <div  style="font-size:14px;float:right;margin-top:4px">{{field.label}}</div>
                            </ng-template>
                            </p-multiSelect>
                        </ng-template>
                        <ng-template pTemplate="output">
                            {{ getLabelByValue(data[col.field], col.value) || '------' }}
                        </ng-template>
                    </p-cellEditor>
                    </ng-container>
                    <ng-container  *ngIf="col.type === 'not-editable'" >
                    <td>{{ data[col.field] }}</td>
                        <!-- <td>{{ this.items[index][col.field] }}</td> -->
                    </ng-container>
                    <ng-container  *ngIf="col.field !== 'location' && col.type !== 'dropdown' && col.type !== 'multi-select' && col.type !== 'not-editable'"   #elseTemplate >
                        <p-cellEditor>
                        <ng-template pTemplate="input">
                            <input #cell [type]="col.type"  (change)="editCellData(data, 'change',cell.value, col.field)"
                            required="true"  (keydown.enter)="editCellData(data, 'keydown.enter', cell.value, col.field)"
                                (focus)="storeOldValue(cell.value, col.field, index)" [(ngModel)]="data[col.field]">
                        </ng-template>
                        <ng-template pTemplate="output">
                            {{data[col.field]}}
                        </ng-template>
                        </p-cellEditor>
                        <!-- <div *ngIf="showNotif && (index === indeX) && (col.field === field)" class="text text-danger ">{{ notification }}</div> -->
                    </ng-container>

                </td>
                <td >
                <span *ngIf="displayActions[index]">
                <i class="fa fa-trash"   data-toggle="modal" data-target="#deleteModal" title="delete this item" (click)="displayDeleteAlert(3, data.id)" ></i>
                <i class="fa fa-edit"   data-toggle="modal" data-target="#itemModal"  title="view & edit"  (click)="displayEditModal(data)" ></i>
                <!-- <i class="fa fa-info-circle" title="view message log"  data-toggle="modal" data-target="#msgModal"  (click)="displayMsgLog(message)" ></i> -->
                </span>
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="summary">
        There are {{items?.length}} items
        </ng-template>
        <ng-template pTemplate="emptytrans">
        <tr>
            <td colspan="9" style="text-align:center;padding:15px;">No items found.</td>
        </tr>
    </ng-template>
    </p-table>
</div>


<p-toast *ngIf="showNotif" position="bottom-center" key="c"  [baseZIndex]="5000" class="">
  <ng-template let-message pTemplate="message" >
      <div class="p-flex p-flex-column" style="flex: 1">
          <div class="p-text-center">
              <i class="pi pi-exclamation-triangle" style="font-size: 3rem"></i>
              <h4>{{message.summary}}</h4>
              <p>{{message.detail}}</p>
          </div>
          <div class="p-grid p-fluid">
              <div class="p-col-6">
              </div>
              <div class="p-col-6">
                  <button type="button" pButton  label="No" (click)="clear()" class="p-button-secondary"></button>
              </div>
          </div>
      </div>
  </ng-template>
</p-toast>






<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayItemModal" id="itemModal"  tabindex="-1" role="dialog" aria-labelledby="itemModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
         <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
         <h3 *ngIf="mode === 'edit'">Edit </h3>
         <h3 *ngIf="mode === 'create'" >Add New {{ title }} </h3>
         <h3 *ngIf="mode === 'location'" >View {{ title }} locations </h3>
      </div>
      <div class="modal-body">
            <!-- [majorParents]="majorParents" [minorParents]="minorMajors"  [compIndustries]="compIndustries" -->
         <app-new-value-modal [mode]="mode" [item]="itemToPreview" [openedInAModal]="true" [locationsI]="locations"
         [experienceFields]="expFields" [majors]="majorJobTitles"  [majorEducationField]="majorEducationField"
         [compInParents]="compInParents" [skillCats]="skillCats" [jobTitles]="jobTitles" 
         [type]="type"  [languagesArray]="languagesArray" (parentAdded)="parentAdded($event)" (closeModal)="closeItemModal($event)" (closeLocationModal)="closeLocationModal($event)" >
         </app-new-value-modal>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<!-- end of  modal-->







<!-- delete  modal-->
<div class="modal fade" *ngIf="displayDeleteModal" id="deleteModal"  tabindex="-2" role="dialog" aria-labelledby="deleteModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeDeleteModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="opperationNum === 3 || opperationNum === 4" style="color:crimson;">Delete </h3>
      </div>
      <div class="modal-body">
       <!-- delete alert -->
        <div *ngIf="opperationNum === 3 && !showNotific">
           <p >Are you sure you want to delete this item? </p>
           <button type="button" class="btn btn-light" style="float: right" (click)="deleteItem()">Delete</button>
        </div>

        <div *ngIf="opperationNum === 4 && !showNotific">
          <p>Are you sure you want to delete these {{ filteredItems.length }} items? </p>
          <button type="button" class="btn btn-light" style="float: right" (click)="deleteMultiItems()">Delete</button>
       </div>
       <div *ngIf="showNotific">
         <p>{{ notification }}.  Are you Sure?</p>

         <button type="button" class="btn btn-light" (click)="confirmDelete()" style="float: right">Yes</button>
       </div>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->
