.form-group{
  margin-bottom: 17px;
}
.google-btn{
  background-color: #ec382d;
  border-color: #ec382d;
  color:#ffffff;
}
.google-btn:hover, .google-btn:focus, .google-btn:active{
  background-color:#c62f26;
  border-color:#c62f26;
  color:#ffffff;
}

.facebook-btn{
  background-color: #2e579f;
  border-color: #2e579f;
  color:#ffffff;
}
.facebook-btn:hover, .facebook-btn:focus, .facebook-btn:active{
  background-color:#26457c;
  border-color:#26457c;
  color:#ffffff;
}
.has-error .error-message{
  left: 0;
  width: auto;
}
.has-error .control-label a{
  color: #db1a1a !important;
}
@media screen and (max-width: 768px){
  .has-error .error-message{
    right: 15px;
  }
}
@media screen and (max-width: 418px){
  .facebook-btn{
    margin-bottom: 18px;
    margin-right: 0;
  }
}
:host >>> .strength-meter:after {
  right: 20% !important;
  width: 20% !important;
}
:host >>> .strength-meter:before {
  left: 20% !important;
  width: 20% !important;
}
