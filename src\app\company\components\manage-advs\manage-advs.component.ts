import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { ManagePostService } from "app/company/services/manage-post.service";
import { SelectItem } from "primeng/api";
import { FormBuilder, FormControl, Validator, Validators } from "@angular/forms";
import { Subject, Subscription } from "rxjs";
import { PostJobService } from "app/company/services/post-job.service";
import { ActivatedRoute, Router } from "@angular/router";
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { InputTextModule } from 'primeng/inputtext';
import { DataMap } from "shared/Models/data_map";
import { FilterUtils } from "primeng/utils";
import { DatePipe } from "@angular/common";
import { Table } from "primeng/table";
import { Title } from '@angular/platform-browser';

declare var $: any;
@Component({
  selector: 'app-manage-advs',
  templateUrl: './manage-advs.component.html',
  styleUrls: ['./manage-advs.component.scss']
})
export class ManageAdvsComponent implements OnInit, OnDestroy {

  // ##############Old manage advs component - currently not used #############
  createdDates: any;
  updatedDates: any;
  templatesDates: any;
  AdvrId: any;
  published_dates: any[] = [];
  option_ar: SelectItem[];
  passed_Data_to_translated: any;
  companyId = localStorage.getItem('company_id');
  current_language = Number(localStorage.getItem('current_company_language'));
  private ngUnsubscribe: Subject<any> = new Subject();
  options: SelectItem[];
  options_advrs: SelectItem[];
  option: SelectItem[];
  slcLang: any;
  choice;
  selectedlang: selectedLang;
  data_map = new DataMap();
  available_languages_temp: selectedLang[] = [];
  data_type: any[][] = [[]];
  data_Source: any = [];
  total_advr;
  available_languages = [];
  companyAdversOrder = [];
  companyAdversAfterReorder: string[] = [];
  public start: number = 50;
  public page_number: number = 1;
  public current_page: number = 1;
  public autoHide: boolean = false;
  public responsive: boolean = true;
  status: SelectItem = { label: 'Publish', value: 'publish' };
  available_langs;
  username;
  selected_lang: any;
  subscription: Subscription;
  status_passed;
  page_number_passed;
  no_adver = false;
  statuses: any[];
  totalRecords;
  showLoader: boolean;
  @ViewChild('dt') private _table: Table;
  constructor(
    private managePost: ManagePostService,
    private postJobService: PostJobService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private translate: TranslateService,
    private datepipe: DatePipe,
    private title: Title
  ) {

    this.showLoader = false;
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
    this.subscription = postJobService.Data.takeUntil(this.ngUnsubscribe).subscribe(val => {
      this.status_passed = val[0];
      this.page_number_passed = val[1];
    });
    this.option = [
      { label: 'Publish', value: 'publish' },
      { label: 'Expired', value: 'expired' },
      { label: 'Draft', value: 'draft' },
      { label: 'Template', value: 'template' }
    ];
    this.option_ar = [
      { label: 'منشورة', value: 'publish' },
      { label: 'منتهية الصلاحية', value: 'expired' },
      { label: 'مسودات', value: 'draft' },
      { label: 'قوالب', value: 'template' }
    ];
    this.available_langs = this.fb.group({
      'languages': ['', Validators.required],
    });
  }


  ngOnInit() {
    this.title.setTitle('CEVAST');
    if (this.status_passed !== undefined) {
      this.options = this.status_passed.value;
      this.status = this.status_passed;
      this.page_number = this.page_number_passed;
      this.postJobService.send_Data([])
    } else {
      this.options = this.status.value;
    }
    this.BuildTable(this.page_number);
    this.choice = this.status;
    this.options_advrs = (this.current_language === 1) ? this.option : this.option_ar;

    let _self = this
    FilterUtils['betweenCreatedDates'] = (value, filter): boolean => {

      var s = _self.createdDates[0].getTime();
      var e;
      if (_self.createdDates[1]) {
        e = _self.createdDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    }

    FilterUtils['betweenUpdatedDates'] = (value, filter): boolean => {

      var s = _self.updatedDates[0].getTime();
      var e;
      if (_self.updatedDates[1]) {
        e = _self.updatedDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    }

    FilterUtils['betweenTemplateDates'] = (value, filter): boolean => {

      var s = _self.templatesDates[0].getTime();
      var e;
      if (_self.templatesDates[1]) {
        e = _self.templatesDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    }
  }


  BuildTable(page_number, langId?) {
    if (langId) {
      this.current_language = langId;
      this.data_Source = [];
    } else {
      this.current_language = Number(localStorage.getItem('current_company_language'));
    }

    this.setcompanyLanguage(this.current_language);
    this.available_languages_temp = [];

    /*---- get the advertisement using page number and page size and the desired advertisement categroy ---*/
     this.showLoader = true  ;
    this.managePost.getAdverByType(this.companyId, this.status.value, page_number, this.start, this.current_language).subscribe(
      (res) => {
        this.showLoader = false;
        if (res[this.status.value].length === 0) {
          this.no_adver = true;
        } else {
          this.no_adver = false
          this.data_type[page_number] = [];
          this.data_Source = []
          this.totalRecords = res[this.status.value]['total'];
          if (res[this.status.value].length !== 0) {
            for (let i = 0; i < res[this.status.value]['data'].length; i++) {

              this.data_type[page_number][i] = res[this.status.value]['data'][i];
            }

            for (let i = 0; i < this.data_type[page_number].length; i++) {

              this.data_Source[i] = this.data_type[page_number][i];
            }

            for (let i = 0; i < this.data_Source.length; i++) {
              this.published_dates.push({
                'color': this.calculateDiff(this.data_Source[i].refresh_at).backgroundColor,
                'date': this.calculateDiff(this.data_Source[i].refresh_at).diffDays
              })
            };

            for (let i = 0; i < res['available_languages'].length; i++) {
              this.available_languages_temp[i] = res['available_languages'][i]
            }

            this.available_languages_temp.unshift({ 'id': '', 'name': '' });
            this.current_page = res[this.status.value]['current_page'];
            if (this.status.value === 'publish') {
              this.initCompanyAdvrOrder(this.data_Source)
              setTimeout(() => {
                this.dragTable();
              }, 1000);
            }
          }
        }

      });
  }

  initCompanyAdvrOrder(companyAdvs: any) {
    this.companyAdversOrder = [];
    for (let advr of companyAdvs) {
      let x = { 'jobAdvId': advr.id, 'orderId': advr.order };
      this.companyAdversOrder.push(x);
    }
  }

  changeOption($event) {

    this.status.value = $event.type
    $event.type[0].toUpperCase();
    this.status.label = $event.type
    this.choice = this.status
    this.page_number = 1;
    this.data_type = [[]];
    this.data_Source = [];
    this.totalRecords = 0;
    this.BuildTable(this.page_number)

  }

  getData(row_number, page_number, edit?) {
    this.page_number = page_number;
    if (this.data_type[this.page_number] === undefined) {
      this.managePost.getAdverByType(this.companyId, this.status.value, this.page_number, row_number, this.current_language).subscribe(
        (res) => {

          if (res[this.status.value].length === 0) {
            this.no_adver = true;
            this.data_Source = [];
          } else {
            this.no_adver = false
            this.data_type[this.page_number] = [];
            for (let i = 0; i < res[this.status.value]['data'].length; i++) {

              this.data_type[this.page_number][i] = res[this.status.value]['data'][i];
            }

            for (let i = 0; i < this.data_type[this.page_number].length; i++) {

              this.data_Source[i] = this.data_type[this.page_number][i];
            }

            this.initCompanyAdvrOrder(this.data_Source)
          }
        });

    } else {
      for (let i = 0; i < this.data_type[this.page_number].length; i++) {

        this.data_Source[i] = this.data_type[this.page_number][i];
      }

    }

    setTimeout(() => {
      this.dragTable();
    }, 1000);
  }

  reOrderTable() {

    if (this.companyAdversAfterReorder.length !== 0) {
      for (let i = 0; i < this.companyAdversAfterReorder.length; i++) {
        let tirm = this.companyAdversAfterReorder[i].trim();

        this.companyAdversAfterReorder[i] = tirm;

      }

      for (let advr of this.companyAdversOrder) {

        let orderedItem = this.companyAdversAfterReorder.indexOf(advr.orderId.toString()) + 1;

        advr.orderId = orderedItem;
      }

    } else { console.log('empty'); }
  }

  dragTable() {
    const component = this;
    const componentcompanyAdvrsAfterReorder = this.companyAdversAfterReorder;
    $('.body').sortable({
      start: function (e, ui) {
        ui.placeholder.height(ui.item.height());

        while (componentcompanyAdvrsAfterReorder.length > 0) {
          componentcompanyAdvrsAfterReorder.pop();
        }
      }
    });

    $('.body').sortable({

      stop: function (e, ui) {

        $.map($(this).find('tr'), function (el) {
          componentcompanyAdvrsAfterReorder.push(el.id);
        });
        component.reOrderDataWithServeInvokation();
      }
    });


  }

  getResumes(adv_id) {

    this.managePost.getResumes(adv_id).subscribe(
      (res) => {

      }
    )
  }

  reOrderDataWithServeInvokation() {

    this.reOrderTable();
    let orderedData = { "orderAdv": this.companyAdversOrder };
    this.managePost.orderCompanyAdvertisement(this.companyId, orderedData).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {

      }
    )
  }

  renew(AdvId) {
    let holder = this.data_Source;
    this.managePost.renewCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {


        this.refresh_data(res['data']['renew_count'], 'renew_count', AdvId, holder)


      });


  }

  activate(AdvId, active, adv_id_by_company = 0) {

    let active_status;
    let holder = this.data_Source;
    if (active) {
      if (confirm("Are you sure you want to deactivate Adverstisement " + adv_id_by_company)) {
        active_status = { "active": !active }
        this.managePost.activateCompanyAdvertisement(AdvId, active_status).takeUntil(this.ngUnsubscribe).subscribe(
          (res) => {

            this.refresh_data(res['data']['active_status'], 'active_status', AdvId, holder)
          });
      }
    } else {

      active_status = { "active": !active }
      this.managePost.activateCompanyAdvertisement(AdvId, active_status).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          this.refresh_data(res['data']['active_status'], 'active_status', AdvId, holder)
        });
    }

  }

  refreshListing(AdvId) {
    let holder = this.data_Source;
    this.managePost.refreshListingCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        if (res['error']) {
          window.alert(res['error'])
        } else {
          this.refresh_data(res['data']['refresh_count'], 'refresh_count', AdvId, holder)
        }
      })


  }

  endWorkFlow(AdvId, adv_id_by_company) {
    if (confirm("Are you sure you want to make Advertisement " + adv_id_by_company + " as an Expired Advertisement?")) {
      let holder = this.data_Source;
      this.managePost.endWorkFlowCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          this.refresh_data(res['data']['expires_at'], 'expires_at', AdvId, holder)
        })
    }
  }

  refresh_data(newData, dataName, AdvId, holder) {


    if (newData !== undefined) {
      /* for(let i = 1; i < this.data_type.length ; i++) {
        if(this.data_type[i] != undefined) {
          this.BuildTable(i)
        } 
      } */
      this.BuildTable(this.page_number)
      this.data_Source = []
      if (this.data_type[this.page_number].length) {
        this.data_Source = this.data_type[this.page_number]
      } else {
        this.data_Source = this.data_type[this.page_number - 1]

      }
      /* let result = this.data_map.update_advs_data(this.page_number,this.start,
        this.totalRecords,holder,
        this.status.value,
        this.data_type,dataName,AdvId,newData,this.companyId)

      if(!result) {
        this.totalRecords= this.totalRecords - 1;
        this.page_number = this.page_number- 1;
        if(this.data_type[this.page_number].length !== 0) {
          for(let i = 0 ; i < this.data_type[this.page_number].length ; i++) {
            
            holder[i] =  this.data_type[this.page_number][i];
          }
        } else {
          this.getData(this.page_number, this.start);
        }
        this.data_Source = holder

      } else {
        this.data_type = result.full_data;
        this.data_Source = result.data_holder;
        this.page_number = result.page_number;
        this.totalRecords = result.totalRecords
      } */
    }

  }

  saveAsTemplate(AdvId, has_template = false) {

    let holder = this.data_Source;
    if (has_template === null) {
      this.managePost.saveAsTemplateCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          if (res['data']) {
            window.alert('Advertisement has been saved as a Template Successfully!')
          }
          this.refresh_data(res['data']['has_template'], 'has_template', AdvId, holder)
        })
    } else {
      if (confirm("this Advertisement " + AdvId + " has a Template.. Are you sure you want to save another template for it?")) {
        this.managePost.saveAsTemplateCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
          (res) => {
            if (res['data']) {
              window.alert('Advertisement has been saved as a Template Successfully!')
            }
            this.refresh_data(res['data']['has_template'], 'has_template', AdvId, holder)
          })
      }
    }

  }

  deleteAdvr(AdvId, adv_id_by_company) {

    if (confirm("Are you sure you want to delete this Advertisement " + adv_id_by_company + "?")) {
      let holder = this.data_Source;

      this.managePost.deleteCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          if (res['error']) {
            window.alert(res['error'])
          } else {

            this.refresh_data(res['data']['id'], 'deleted', AdvId, holder)
          }

        })
    }

  }

  delete_temp(AdvId, adv_template_id_by_company) {
    if (confirm("Are you sure you want to delete this Advertisement' template " + adv_template_id_by_company + "?")) {
      let holder = this.data_Source;
      this.managePost.deleteCompanyAdvertisementTemplate(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          this.refresh_data(res['data']['id'], 'deleted', AdvId, holder)
        })
    }
  }

  saveAsPublish(AdvId, adv_id_by_company) {

    let holder = this.data_Source;
    if (confirm("this Advertisement " + adv_id_by_company + " will be published.. Are you sure you want to continue?")) {
      this.managePost.saveAsPublishCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          window.alert('Draft advertisement has been published successfuly')

        })
    }

  }

  saveTemplateAsPublish(AdvId, adv_template_id_by_company) {

    let holder = this.data_Source;
    if (confirm("this Advertisement " + adv_template_id_by_company + " will be published.. Are you sure you want to continue?")) {
      this.managePost.saveTemplateAsPublishCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {


        })
    }

  }

  edit_advr(AdvId, langId) {

    this.managePost.getCompanyAdvertisementData(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['advertisement_info']);
        this.passed_Data_to_translated.push({
          'translated_language_id': langId,
          'status': this.status,
          'page_number': this.page_number
        })

        this.postJobService.send_Data(this.passed_Data_to_translated);

        this.router.navigate(['/c', this.username, 'post-job']);
      })
  }

  edit_advr_temp(AdvId, langId) {

    this.managePost.getCompanyTemplateAdvertisementData(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {

        this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['adv_temp_info']);
        this.passed_Data_to_translated.push({
          'translated_language_id': langId,
          'status': this.status,
          'page_number': this.page_number
        })
        this.postJobService.send_Data(this.passed_Data_to_translated);

        this.router.navigate(['/c', this.username, 'post-job']);
      })
  }

  onChange(event) {
    this.BuildTable(this.page_number, event.value.id)
  }

  setcompanyLanguage(languageId) {
    if (languageId && languageId === 1) {
      this.translate.setDefaultLang('en');

    } else if (languageId === 2) {
      this.translate.setDefaultLang('ar');

    } else {
      this.translate.setDefaultLang('en');
    }

  }

  calculateDiff(sentDate) {

    var Offset: any = new Date(sentDate).getTimezoneOffset();
    var backend_time: any = new Date(sentDate)
    var current_Date_ms = new Date().getTime()
    var current_Date: any = new Date(current_Date_ms + Offset * 60 * 1000);

    var diffDays: any = Math.floor((current_Date - backend_time) / (1000 * 60 * 60 * 24));
    var seconds = Math.floor(Math.abs(backend_time - (current_Date)) / 1000);
    var minutes = Math.floor(seconds / 60);
    var hours = Math.floor(minutes / 60);

    var backgroundColor = 'gray'
    if (diffDays === 0) {
      diffDays = hours + ' hours ago'
    } else {
      diffDays = diffDays + ' days ago'
    }
    return { diffDays, backgroundColor };
  }

  changeAdvId(Advid, AdvLang, source) {
    this.postJobService.changeAdvId_lang(Advid, AdvLang, source)
  }

  changeAdvIdLog(source_type_adv) {
    this.managePost.send_adv_id_log(source_type_adv)
  }

  displayAdvrModal(job_adv_id) {
    this.AdvrId = job_adv_id
    let source_type_adv = ['manage_advs', this.status]
    this.changeAdvId(job_adv_id, 1, source_type_adv)
    $('#AdvrsModal').modal('toggle');
  }

  displayAdvrLogModal(job_adv_id) {
    let source_type_adv = [job_adv_id, this.status]
    this.changeAdvIdLog(source_type_adv)
    $('#AdvrsLogModal').modal('toggle');
  }

  handlePopup($event) {
    if ($event.AdvType === 'template' && $event.edit) {
      this.edit_advr_temp($event.AdvId, 1)
    } else if ($event.AdvType != 'template' && $event.edit) {
      this.edit_advr($event.AdvId, 1)
    }

    if (!$event.edit) {
      
      this[$event.action]($event.AdvId)
    }

  }


  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  recieveAdvType($event) {
    // console.log($event)
  }

  paginate(event) {
    //event.first = Index of the first record
    //event.rows = Number of rows to display in new page
    //event.page = Index of the new page
    //event.pageCount = Total number of pages
    this.data_Source = [];
    this.page_number = event.page + 1;

    this.getData(event.rows, event.page + 1)
  }

  Clear($event) {

    this._table.reset()
  }

  move_to_received_cvs(folder_id) {

    this.managePost.send_folder_id(folder_id)
    this.router.navigate(['/c', this.username, 'cvs_preview'])
  }

}


interface adrv_data {
  advr: any
}

interface selectedLang {
  id: any
  name: any
}



