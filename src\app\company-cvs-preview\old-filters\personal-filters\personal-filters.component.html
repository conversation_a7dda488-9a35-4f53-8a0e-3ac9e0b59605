<div class="container-fluid form-horizontal">
    <div class="row">
        <div class="col-lg-6">
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-checkbox">
                    <span>Gender</span>
                </div>
                <div class="col-lg-7 col-md-5 col-sm-6 col-xs-7 focus-no-padding">
                    <div class="row">
                        <div *ngFor="let item  of dataModel.genderOpts;let i = index" class="check-block col-xs-6">
                            <input type="checkbox" id="{{item.option}}"[(ngModel)]="item.value" name="genderValueOpts" />
                            <label for="{{item.option}}">{{item.title}}</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-checkbox">
                    <span>Martial Status</span>
                </div>
                <div class="col-lg-7 col-md-5 col-sm-6 col-xs-7 focus-no-padding">
                    <div class="row">
                        <div *ngFor="let item  of dataModel.martialOpts;let i = index" class="col-xs-6 check-block">
                            <input type="checkbox" id="{{item.option}}" [(ngModel)]="item.value" name="martialValueOpts" />
                            <label for="{{item.option}}">{{item.title}}</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Age</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div">
                    <ng5-slider [options]="dataModel.ageOptions" [(value)]="dataModel.ageValue" [(highValue)]="dataModel.ageHighValue"></ng5-slider>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Nationality</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-multiSelect 
                        styleClass="cust-p-multiselect" 
                        [options]="filterData.nationalities" 
                        optionLabel="name" 
                        [filter]="true" 
                        filterBy="label,value.name" 
                        [(ngModel)]="dataModel.nationalities" 
                        defaultLabel="Nationality">
                    </p-multiSelect>
                    <span class="custom-underline"></span>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>First Name :</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <input class="form-control"  [(ngModel)]="dataModel.firstname" type="text" placeholder="First Name">
                    <span class="custom-underline"></span>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Last Name :</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <input class="form-control" [(ngModel)]="dataModel.lastname" type="text" placeholder="Last Name">
                    <span class="custom-underline"></span>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <!-- <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Country</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-multiSelect #count 
                        [options]="filterData.countries"
                        optionLabel="name" 
                        [filter]="true" 
                        filterBy="label,value.name" 
                        styleClass="cust-p-multiselect" 
                        (onChange)="selectMultiValues($event , 'country')" 
                        [(ngModel)]="dataModel.countries" 
                        defaultLabel="Country"
                        >
                      <ng-template let-it pTemplate="selectedItem">
                            <img src="./assets/images/CountryCode/{{it.value.code}}.gif" class="country-img">
                        </ng-template>
                        <ng-template let-code pTemplate="item">
                            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">
                                    <span style="width:400px;display: inline-block;">{{code.label}}</span>
                                    <img src="./assets/images/CountryCode/{{code.value.code}}.gif" style="display: inline-block;width:16px;">
                                </div>
                            </div>
                        </ng-template>
                    </p-multiSelect> 
                  <span class="custom-underline"></span>
                </div>
            </div>  -->
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Location:</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <input  placeholder="Location" type="text" [(ngModel)]="dataModel.location" 
                    class="form-control col-sm-6" 
                    style="width: 200px !important;"
                     #googlelocationplaceLocation>
                </div>
            </div>
        </div>
    </div>
    <div class="filters-buttons">
        <button (click)="sendFilters()" class="btn btn-success">Apply</button>
        <button (click)="hideModal()" class="btn btn-default">Cancel</button>
    </div>
</div>
