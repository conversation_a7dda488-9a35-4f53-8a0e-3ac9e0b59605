.page-title{
    font-size: 1.3rem;
    color: #3D7BCE;
    font-weight: bold;
}
a{
    text-decoration: none;
}
:host ::ng-deep button {
    // margin-right: .25em;
    margin-right:0;
}

:host ::ng-deep .ui-table-customers {
    

}
:host ::ng-deep .custom-toast .ui-toast-message {
    background: #FC466B;
    background: -webkit-linear-gradient(to right, #3F5EFB, #FC466B);
    background: linear-gradient(to right, #3F5EFB, #FC466B);
}

:host ::ng-deep .custom-toast .ui-toast-message div {
    color: #ffffff;
}

:host ::ng-deep .custom-toast .ui-toast-message.ui-toast-message-info .ui-toast-close-icon {
    color: #ffffff;
}

.check{
    width: 50%;
    height: 19px !important;
    border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));
  
  
  }

  


.tooltip {
    position: relative;
    display: inline-block;
    opacity: 1;
    z-index: 1;
  }
  
  .tooltip .tooltiptext {
    visibility: hidden;
    width: 110px;
    background-color: #007ad9;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    left: -40px;
    bottom: 60px;
    font-size: 15px;
    font-family: "Open Sans", "Helvetica Neue", sans-serif;
}  
.tooltip:hover .tooltiptext {
    visibility: visible;
  }
.ui-button-icon-only {
    text-indent: initial;
    }

.prev-border {
    border: 2px solid #ddd;
    padding-top: 30px;
    border-radius: 35px;
}

.has-val ~ .custom-control-label {
    transform: translate(0, -95%);
}

.border-div{
    border-bottom: 1px groove #b39b99;
    border-top: 1px groove #b39b99;
}


.e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after {
    background: #276ea4;
}

.e-multi-select-wrapper input.e-dropdownbase::-webkit-input-placeholder {
    color: #999;
    font-size: 16px;
    padding-left: 14px;
}

.e-multi-select-wrapper .e-searcher {
    width: 50%;
}

.ui-autocomplete .ui-autocomplete-input {
    color: white;
  }
  :host ::ng-deep .ui-table-customers {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);
    margin:15px 0;
    .customer-badge {
        border-radius: 2px;
        padding: .25em .5em;
        text-transform: uppercase;
        font-weight: 700;
        font-size: 15px;
        letter-spacing: .3px;

       
        &.status-new {
            /*background-color:#c8e6c9;*/
            color: #2df047;
        }

        &.status-medium {
            /*background-color:#ffcdd2 ;*/
            color: #4be0eb;
        }

        &.status-long {
            /*background-color: #FEEDAF;*/
            color: #ffaf02;
        }

       

        &.status-renewal {
            /*background-color: #ECCFFF;*/
            color: #694382;
        }

        &.status-proposal {
            /*background-color: #FFD8B2;*/
            color: #805B36;
        }
    }

    .flag {
        vertical-align: middle;
        width: 30px;
        height: 20px;
    }

  

    .ui-multiselect-representative-option {
        display: inline-block;
        vertical-align: middle;

        img {
            vertical-align: middle;
            width: 24px;
        }

        span {
            margin-top: .125em;
            vertical-align: middle;
            margin-left: .5em
        }
    }

    // :host ::ng-deep .ui-paginator {
    //     margin:0 15px;
    //     .ui-dropdown {
    //         float: left;
    //     }

    //     .ui-paginator-current {
    //         float: right;
    //     }
    // }

    .ui-progressbar {
        height: 8px;
        background-color: #D8DADC;

        .ui-progressbar-value {
            background-color: #00ACAD;
        }
    }

    .ui-column-filter {
        display: block;
        font-weight: 500;
        width: 80px;
        border-radius: 2px;
        border:none;

        input {
            width: 100%;
            border-radius: 10px;
        }
    }

    .ui-table-globalfilter-container {
        float: right;
        font-weight: 300;
        border-radius: 5px;

        input {
            width: 200px;
            border-radius: 2px;

        }
    }

    .ui-datepicker {
        min-width: 25em;

        td {
            font-weight: 400;
        }
    }

    .ui-table-caption {
        border: 0 none;
        padding: 12px;
        text-align: left;
        font-size: 17px;
    }

    .ui-paginator {
        border: 0 none;
        padding: 1em;
    }
    .ui-table-thead{
        background-color: #f4f4f4;
    }
    .ui-table-thead > tr > th {
        border: 0 none;
        text-align: left;
        font-size: 14px;
        font-weight:800;
        background-color: transparent;
        //padding-bottom: 30px !important;

    

        &.ui-filter-column {
            border-top: 1px solid #c8c8c8;
          
        }

        &:first-child {
            width: 14em;
            text-align: center;
           
        }

        &:last-child {
            width: 14em;
            text-align: center;
        }
    }

   
    .ui-table-tbody > tr > td {

        border: 0 none;
        cursor: auto; 
s

        &:first-child {
            width: 14em;
            text-align: center;
        }

        &:last-child {
            width: 8em;
            text-align: center;
        }
    }
    

    .ui-dropdown-label:not(.ui-placeholder) {
        text-transform: uppercase;
    }

    // .ui-table-tbody > tr > td .ui-column-title {
    //     display: none;
    // }
}
.head{
    margin-top: 30px;
}



/* Responsive */
@media screen and (max-width: 64em) {
    :host ::ng-deep .ui-table {
        &.ui-table-customers {
            .ui-table-thead > tr > th,
            .ui-table-tfoot > tr > td {
              //  display: none !important;
            }

            // .ui-table-tbody > tr > td {
            //     text-align: left;
            //     display: block;
            //     border: 0 none !important;
            //     width: 100% !important;
            //     float: left;
            //     clear: left;
            //     border: 0 none;

            //     .ui-column-title {
            //         padding: .4em;
            //         min-width: 30%;
            //         display: inline-block;
            //         margin: -.4em 1em -.4em -.4em;
            //         font-weight: bold;
            //     }
               
            // }
            .last .tooltip{
                float: right;
                margin-left: 7px;
            }
            .last svg{
                float: right;
                margin-left: 7px;

            }
            .last .dropdown{
                float: right;
                margin-left: 7px;


            }
            
            .card{
                display: none;
            }
        }
    }
}
.title{
    font-size:14px; 
    // font-weight: bold; 
    // color:#30457c;
    width: 180px !important;
}
.name{
     font-weight: bold; color:#30457c !important;  
}
.logo{
    width: 140px !important;
}
.close2 {
    border:none;
    filter: alpha(opacity=20);
}
.close2:hover{
    color: red;
}
.header {
    width: 70px !important;
    padding-left:3px !important;
    padding-right: 3px !important;
    text-align: center !important;
    // margin-left: 10px;
    // margin-top: 30px !important;
}
.industry , .skills , .languages , .nationality{
                /*word-wrap: break-word !important;*/     
}
.search input{
    font-family: "FontAwesome";
  
 }
.apply{
    background-color: #3bb34b !important;
    color:white;
    padding:0px;
    border: none;
    font-size: 14px;
    border-radius: 0px;
    letter-spacing: 1px;
    float: left !important;
}
:host ::ng-deep button {
    // margin-right: .25em;
    margin-right:0;
}

:host ::ng-deep .ui-splitbutton {
    margin-left: .25em;
}

:host ::ng-deep .ui-splitbutton button {
    margin-right: 0;
}
:host ::ng-deep .p-mr-2{
    background-color: #3F5EFB;
    color:white;
    border-radius: 0px;
    letter-spacing: 1px;

}
:host ::ng-deep .p-button-warning{
    background-color: #fbc02d !important;
    color:white
}
:host ::ng-deep .p-button-success{
    //background-color: #30a03e;
    //color:white;
    letter-spacing: 1px;
}
:host ::ng-deep .b-search{
    background-color: #f6ba36;
    width: 30px;
    height: 32px;
    padding:2px !important;
    margin-left: 197px;
    letter-spacing: 1px;

}
:host ::ng-deep .auto{
    background-color: white !important;
    border-radius: 2px;
    margin-left: 10px !important;
    width: 185px !important;
}
:host ::ng-deep .p-button-succ{
    background-color: #f6ba36;
    margin-left: 180px;
    color:white;
    height: 32px;
    padding:6px 4px 2px 4px;
}
.fas{
    height: 40px !important;
    margin-left: 100px;
}
#cog{
    font-size:24px !important;
    cursor: pointer;
}
#apply{
    visibility: hidden;
    width: 82px;
}
.tr-hover:hover #apply{
    visibility: visible;
}

.applied{
    background-color: #b8beb9 !important;
    color:white;
    padding:0px;
    border: none;
    font-size: 14px;
    border-radius: 0px;
    letter-spacing: 1px;
    float: left !important;
    width: 91px;
}
:host ::ng-deep  .ui-button:enabled:focus{
    box-shadow: none;
}
#applied {
    visibility: hidden;
}
.tr-hover:hover #applied{
    visibility: visible;
}
.custom-container{
    // margin-top: 120px;
    padding:10px 15px 20px 15px;
}

:host ::ng-deep .location{
    width: 40% !important;
    margin-top:300px !important;
}
:host ::ng-deep .filters{
    width :70%;
     left: 20% ;
     margin-top:100px !important;
}
:host ::ng-deep .button-success{
    background-color: #f2f2f2 !important;
    //color:white !important;
    letter-spacing: 1px;
    border-radius: 0px;
    border: none !important;
    color:#808080 !important;
    //padding: 5px 10px 5px 10px;
    //float: right !important;
}
:host ::ng-deep .button-success:hover{
    color:#808080 !important;
}
.button-info{
    //background-color: #3d7bce;
    //color:white;
    letter-spacing: 1px;
    border-radius: 0px;
    //padding: 5px 15px 5px 15px;
}
.dropdown{
    float: left;
}
.dropdown-menu{
    width:800px !important;
}

:host ::ng-deep .cus-selectButton .ui-button {
    padding:2px;
}
:host ::ng-deep .p-multiselect {
    width: 70px !important;
}
:host ::ng-deep .multiselect-custom-virtual-scroll .p-multiselect {
    min-width: 20rem;
}

:host ::ng-deep .multiselect-custom {
    margin-top:5px !important;
    border:none !important;
    // background: #f4f4f4 !important;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
    .p-multiselect-label {
        padding-top: .25rem;
        padding-bottom: .25rem;
        color: #808080 !important;
    }
    .ui-multiselect-label{
        color:#808080 !important;

    }
    .country-item-value {
        padding: .25rem .5rem;
        border-radius: 3px;
        display: inline-flex;
        margin-right: .5rem;
        background-color: var(--primary-color);
        color: var(--primary-color-text);

        img.flag {
            width: 17px;
        }
    }

    .country-placeholder {
        padding: 0.25rem;
    }
    .ui-multiselect-trigger{
        background: none !important;
        border: none !important;
    }
}
:host ::ng-deep .multiselect-custom3 {
    margin-top:5px !important;
    border:none !important;
    background: #f4f4f4 !important;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
    .p-multiselect-label {
        padding-top: .25rem;
        padding-bottom: .25rem;
        color: #e2e2 !important;
    }
    .ui-multiselect-label{
        color: #808080 !important;

    }
    .country-item-value {
        padding: .25rem .5rem;
        border-radius: 3px;
        display: inline-flex;
        margin-right: .5rem;
        background-color: var(--primary-color);
        color: var(--primary-color-text);

        img.flag {
            width: 17px;
        }
    }

    .country-placeholder {
        padding: 0.25rem;
    }
    .ui-multiselect-trigger{
        background: none !important;
        border: none !important;
    }
}
:host ::ng-deep .multiselect-custom2 {
    margin-top:5px !important;
    border:none !important;
    // background: #f4f4f4 !important;  
    .p-multiselect-label {
        padding-top: .25rem;
        padding-bottom: .25rem;
        color: #e2e2 !important;
    }
    .ui-multiselect-header{
        width:300px !important
    }
    .ui-multiselect-label{
        color: #808080 !important;

    }
    .country-item-value {
        padding: .25rem .5rem;
        border-radius: 3px;
        display: inline-flex;
        margin-right: .5rem;
        background-color: var(--primary-color);
        color: var(--primary-color-text);

        img.flag {
            width: 17px;
        }
    }

    .country-placeholder {
        padding: 0.25rem;
    }
    .ui-multiselect-trigger{
        background: none !important;
        border: none !important;
    }
}
.FilterL{
    font-size: 14px;
    font-weight: bold;
}
:host ::ng-deep .ng5-slider{
    margin:0;
}
:host ::ng-deep.ng5-slider-bubble{
   
    font-size: 12px !important;
}
:host ::ng-deep.ng5-slider .ng5-slider-pointer{
    width: 15px;
    height: 15px;
    top: -5px; /* to remove the default positioning */
    bottom: 0px;
}
:host ::ng-deep.ng5-slider .ng5-slider-pointer-min::after{
    top:4.3px;
    left:2.9px;
}
:host ::ng-deep.ng5-slider .ng5-slider-pointer-max::after{
    top:4.3px;
    left:2.9px;
}
.vl {
    border-left: 3px solid #eee;
    height: 500px;
    position: absolute;
    left: 50%;
    margin-left: -3px;
    top: 60px;}
    .headFilter{
        font-size: 14px;
        color:grey;
        letter-spacing: 1px;
    }
    :host ::ng-deep .p-button {
        margin: 0 .5rem 0 0;
        min-width: 10rem;
    }
    
    p {
        margin: 0;
    }
    
    .confirmation-content {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
   
    :host ::ng-deep .overpanel{
        margin-top: 50px !important;
        width: 800px !important;
        left: -700px !important;
        //margin-right: 10px !important;
    }
    :host ::ng-deep .overpanel1{
        margin-top: 50px !important;
        //margin-right: 10px !important;
        width: 500px !important;
        left: -250px !important;

    }
    :host ::ng-deep .custom-toast .ui-toast-message {
        background: #FC466B;
        background: -webkit-linear-gradient(to right, #3F5EFB, #FC466B);
        background: linear-gradient(to right, #3F5EFB, #FC466B);
    }

    :host ::ng-deep .custom-toast .ui-toast-message div {
        color: #ffffff;
    }

    :host ::ng-deep .custom-toast .ui-toast-message.ui-toast-message-info .ui-toast-close-icon {
        color: #ffffff;
    }

    :host ::ng-deep .drop{
        width: 187px !important;
            border:none !important;
            .ui-placeholder{
                color:#808080 !important;
                font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
                font-size: 1em !important;
           
        }
        .ui-dropdown-trigger{
            background: none !important;
            margin-top: -3px !important;

        }
    }
    :host ::ng-deep .ui-dropdown .ui-dropdown-trigger .ui-dropdown-trigger-icon:before {
        content: "\f107" !important;
        font-family: fontAwesome;
        font-size: 26px;
        font-weight: 500;

    }
    :host ::ng-deep .ui-toolbar{
        position:sticky;
        z-index: 999;
        bottom: 0px;
        font-size:14px !important;
        background-color:  #f2f2f2 !important;
        color:#808080 !important;
        width: 100% !important;
        padding: 0px !important;
        height: 39px;
        top:81px;
        margin-top: 0px;
   }
.fa-times-thin:before {
    content: '\00d7';
}
.selectLabel{
    font-size: 14px;
    font-weight: bold;
}
.main{
    margin: 10px;
    margin-right: 5px;
    margin-top: 0px;
    height: auto;
    
}

.filter-cells{
    float:left;
    padding: 0 13px;
}
.all-filters-cell{
    float:right;
    padding: 0 13px;
}
.first-filter-cell{
    margin-left:100px;
}
.last-filter-cell{
    margin-right:100px;
}

.filter-cells .ui-button:enabled:focus , .all-filters-cell .ui-button:enabled:focus {
    box-shadow: none;
}

.filters-container{
    position: fixed;
    z-index: 999;
    // top: 71px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background: #3D7BCE;
    padding-top: 7px;
    padding-bottom: 7px;
}
.filter-btn{
    display: inline-block;
    font-family: 'Exo2' !important;
    font-size: 1.15em !important;
    color: #fff;
    padding: 0 16px;
    cursor: pointer;
    position: relative;
}
.filter-btn-multiselect{
    min-width: 100px;
}
.filter-btn-icon{
    width: 22px;
    margin-right: 4px;
}
.all-filters-btn{
    margin-left: 50px;
}
:host ::ng-deep .multiselect-filter {
    background: none;
    border: none;
}
:host ::ng-deep .multiselect-filter .ui-multiselect-label-container{
    position: absolute;
    top: -16px;
    left: -26px;
    margin-left: 26px;
}
:host ::ng-deep .multiselect-filter .ui-multiselect-label{
    color:#fff  !important;
    padding:0 !important;
    font-family: 'Exo2' !important;
    font-size: 1.1em !important;
}
:host ::ng-deep .multiselect-filter .ui-multiselect-trigger{
    background-color: transparent !important;
    color: transparent !important;
    border-left: 0;
}
:host ::ng-deep .multiselect-filter .ui-widget.ui-widget-content{
    min-width:250px;
    left:-28px !important;
    top: 14px !important;
}
:host ::ng-deep .drop .ui-listbox-list .ui-listbox-item{
    font-size: 14px;
}

// .filter-btn-ng-select-multiselect{
//     width:100px;
// }
:host ::ng-deep .filter-btn-ng-select-multiselect .ng-select{
    position:absolute;
    top:0;
    width: 92px;
}
:host ::ng-deep .filter-btn-ng-select-multiselect .ng-select .ng-arrow-wrapper .ng-arrow{
    display:none;
}
:host ::ng-deep .filter-btn-ng-select-multiselect  .ng-select-opened{
    top: 32px;
    width: 220px;
    border: 2px solid #e6e6e6;
}
:host ::ng-deep .filter-btn-ng-select-multiselect .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value,
:host ::ng-deep .filter-btn-ng-select-multiselect .ng-select .ng-clear-wrapper,
:host ::ng-deep .filter-btn-ng-select-multiselect .ng-select .ng-arrow-wrapper{
    display:none;
}

.filter-btn .checkbox{
    margin:0;
    accent-color: #fff;
}
.verified-company-filter .checkbox label{
    color: #d1d1d1;
    font-size: 13px;
    font-weight: normal;
}
.filter-btn .checkbox label img{
    display:none;
}
.filter-btn .checkbox label input{
    width: 18px;
    height: 15px;
    margin-left: -48px;
    cursor: pointer;
}
.filter-btn .checkbox label img{
    display:inline-block;
}

.label-fixed{
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    color:#4f94df;
    font-weight: normal;
}
.label-fixed-dd{
    align-items: center;
}
.equal-height-row-cols{
    display: flex;
    flex-flow: row wrap;

    &::before {
        display: block;
    }
}
.all-filters-form{
    padding-top:35px;
    padding-bottom: 35px;
    padding-left:30px;
}
.form-control.ui-autocomplete.ui-widget{
    padding: 0 15px 0 15px;
}
//added class form-control1 to minors control instead of form-control class, 
// because of style position:absolute and z-index in form-control class that was making issue
// when dropdown of majors control open
:host ::ng-deep .form-control1{
    width: 100%;
    padding: 0 15px 0 0;
}
// start custom p-multiselect styles
:host ::ng-deep .cust-p-multiselect{
    border: 0;
    border-bottom: 1px solid #ccc;
    border-radius: 0;
    width: 100%;
    background: transparent;
    .ui-corner-right{
        border:0;
    }
    .ui-multiselect-label{
        color: #808080;
    }
}
:host ::ng-deep .cust-p-multiselect:not(.ui-state-disabled):hover {
    border-color: #ccc;
}
:host ::ng-deep .cust-p-multiselect:focus {
    border:3px solid #4f94df;
}

// end custom p-multiselect styles
// unify placeholer style for all different controls
::placeholder , :host ::ng-deep .ui-dropdown label.ui-dropdown-label ,:host ::ng-deep .ui-autocomplete ::placeholder , :host ::ng-deep .ui-inputtext { /* ::placeholder Chrome, Firefox, Opera, Safari 10.1+ */
    color: #808080;
    opacity: 1; /* Firefox */
    font-weight: normal;
    font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
    font-size: 15px !important;
}
.tag-label{
    color: #808080; 
    background-color:#f2f2f2; 
    padding:3px; 
    margin-left: 5px !important;
}
.error-msg{
    color:#a94442;
}
:host ::ng-deep .ui-table-loading-content{
    color:#186ba0;
    font-size: 20px;
    top: 80px;
}
:host ::ng-deep .unit_distance{
    height: 100%;
}
// :host ::ng-deep .ui-table .ui-sortable-column.ui-state-highlight, :host ::ng-deep .ui-table .ui-sortable-column.ui-state-highlight .ui-sortable-column-icon{
//     color: #000;
// }
// :host ::ng-deep .ui-table .ui-sortable-column:focus{
//     box-shadow: none;
//     background:#e0e0e0;
// }
:host ::ng-deep .ui-table .ui-sortable-column.ui-state-highlight{
    background:#3D7BCE;
}
:host ::ng-deep .ui-sortable-column .pi-sort-alt{
    display:none;
}
// start responsive styles
@media screen and (max-width:1199px){
    #apply , #applied{
        visibility: visible;
    }
    .vl{
        display: none;
    }
}
@media screen and (max-width:1160px) and (min-width:900px) {
    // .filters-container{
    //     top:73px;
    // }
    // .custom-container{
    //     margin-top:122px;
    // }
    :host ::ng-deep .ui-toolbar{
      top:73px;
    }
}
@media screen and (min-width:992px){
    .title{
        min-width:270px;
        // min-width:230px;
    }
    .name{
        min-width:186px;
    }
}

  @media screen and (max-width:899px){
    // .filters-container{
    //     top:66px;
    // }
    // .custom-container{
    //     margin-top:114px;
    // }
    :host ::ng-deep .ui-toolbar{
      top:66px;
    }

    .first-filter-cell{
        margin-left:52px;
    }
    .last-filter-cell{
        margin-right:52px;
    }
    .all-filters-btn{
        margin-left:0;
    }
    .filter-btn{
        font-size: 1.1em !important;
    }
  } 

//   @media screen and (max-width:850px){
//     .all-filters-btn{
//         margin-left:0;
//     }
//     .filter-btn{
//         font-size: 1.1em !important;
//     }
//   }
  

  .customPanel{
      position:absolute;
      top:50px;
      z-index: 1000;
      background: #fff;
      overflow: scroll;
      min-height: 100%;
  }

  .show{
      display:block;
  }
  .hide{
    display:none;
  }


:host ::ng-deep .ui-dialog{
    top: 38px;
    // left: 10%;
    width: 80%; 
}
:host ::ng-deep .ui-dialog .ui-dialog-titlebar{
    border:0;
    border-bottom: 1px solid #c8c8c8;
    background:#fff;
    padding:3px 5px;
}
:host ::ng-deep .ui-dialog .ui-dialog-content{
    border:0;
    padding-top:10px;
}
.dialog-close-btn{
    width: 30px;
    height: 30px;
    line-height: 25px;
    text-align: center;
    float: right;
    padding:2px;
    cursor:pointer;
}
.dialog-close-btn i{
    color:#888;
}
.location-filter-interface{
    padding-top:20px;
    padding-bottom:50px;
}
.group-mar{
    margin-bottom:15px;
}
.row-mar{
    margin-bottom:15px;
}
.tags-container{
    display:flex;
    margin-top:15px;
    margin-bottom:5px;
}
.tags-col-1{
    display: inline-block;
    width: calc(100% - 50px);
}
.tags-col-2{
    display: inline-block;
    width: 50px;
    text-align: right;
}
.tag-separator{
    display: inline-block;
    padding: 0 10px;
    margin: 12px 0;
    color: #30457c;
    font-size: 24px;
}
.config-btn-mobile{
    display: none;
}

.verification-status-desktop{
    display:inline-block;
}
.verification-status-desktop img{
    width:24px;
}
// :host ::ng-deep .p-tooltip .p-tooltip-text{
//     white-space: nowrap;
//     max-width: fit-content;
//     width: 100em;
// }
// :host ::ng-deep .tooltip .ui-tooltip-text {
//     white-space: nowrap;
//     max-width: fit-content;
//     width: 100em; 
// }
// :host ::ng-deep .wide-tooltip .ui-tooltip-text {
//     min-width:350px;
// }

// .minor .form-control{
//     z-index: 0 !important;
// }

// :host ::ng-deep .multiselect-filter .ui-multiselect-panel .ui-multiselect-item , :host ::ng-deep .cust-p-multiselect .ui-multiselect-panel .ui-multiselect-item{
//     white-space: normal;
// }
// @media screen and (max-width:900px){
//     .filters-container{
//         top:61px;
//     }
// }
@media screen and (min-width:768px){
    .ng5-slider-div{
        min-height:0;
    }
}

@media screen and (max-width:767px){
    .page-title{
        font-size: 1rem;
        line-height: 23px;
    }
    // .filters-container{
    //     top:72px;
    // }
    .group-mar{
        margin-bottom:20px;
    }
    // :host ::ng-deep .ui-toolbar{
    //     height: 85px !important;
    // }
    .first-filter-cell{
        margin-left:0px;
    }
    .last-filter-cell{
        margin-right:0px;
    }

    .config-btn-mobile {
        display:block;
        width: 28px;
        height: 28px;
        line-height: 29px;
        background-color: #3D7BCE;
        text-align: center;
        color: #fafafa;
        font-size: 16px;
        overflow: hidden;
        cursor: pointer;
        margin-top: 4px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        transition: background-color .2s,box-shadow .2s;
        box-shadow: 0 7px 8px -4px rgba(0,0,0,.2), 0 5px 22px 4px rgba(0,0,0,.12), 0 12px 17px 2px rgba(0,0,0,.14);
    }
    .not-visible-mobile{
        display:none;
    }

    //p-table responsive layout , override orginal breakpoint 
    :host ::ng-deep .ui-table-responsive{
        .ui-table-thead > tr > th,
        .ui-table-tfoot > tr > td{
            display: none !important;
        }
        colgroup {
            display: none !important;
        }
        .ui-table-tbody > tr > td {
            text-align: left;
            display: block;
            border: 0 none;
            width: 100% !important;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }
        .ui-table-tbody > tr > td .ui-column-title {
            padding: .4em;
            min-width: 30%;
            display: inline-block;
            margin: -.4em 1em -.4em -.4em;
            font-weight: bold;
            color: #000;
        }
        
    }
    .logo-div{
        float:left;
        // height:49px;
        width:80px;
        margin-bottom: 4px;
        text-align: left;
    }
    .logo-div img{
        width:58px;
    }
    .content-div{
        float:left;
        // min-height: 18px;
        width:calc(100% - 80px);
        text-align: left;
    }
    .content-div .comp-name{
        font-weight: bold;
        margin-bottom:4px;
    }
    .content-div .job-title{
        font-size: 1em;
        // font-weight: bold;
        // color: #30457c;
        margin-bottom: 6px;
        margin-top: 6px;
    }
    .content-div .location-mob{
        margin-bottom:4px;
    }
    .apply , .applied{
        visibility: visible !important;
        width:82px;
    }
    
    // .comp-name-div , .job-title-div{
    //     float:left;
    //     min-height: 18px;
    //     width:calc(100% - 80px);
    //     text-align: left;
    // }
    // .more-adv-info-div{
    //     clear:both;
    //     text-align: left;
    //     padding: 0 7px;
    // }
    .publish-date-div{
        position:absolute;
        top:5px;
        right:5px;
    }
    :host ::ng-deep .ui-table-customers .customer-badge{
        font-size: 9px;
        text-transform: lowercase;
        padding:0.25em 0.4em;
    }
    .apply , .applied{
        float: right !important;
    }

    .label-fixed{
        padding-left:0;
        justify-content: flex-start;
    }
    // .focus-no-padding{
    //     padding-left:15px !important;
    // }
    :host ::ng-deep .ng5-slider{
        margin-top:35px;
    }
    .mar-top-mob{
        margin-top: 35px;
    }
    .all-filters-btn{
        margin-left: 0;
        font-size: 1em !important;
    }
    .filter-btn{
        margin: 0 10px;
    }
    .filter-btn-label{
        display:none;
    }
    .filter-btn-multiselect{
        min-width:0;
    }
    :host ::ng-deep .multiselect-filter .ui-multiselect-label{
        display:none;
    }
    :host ::ng-deep .filter-btn-ng-select-multiselect .ng-select {
        width: 38px;
    }
    :host ::ng-deep .filter-btn-ng-select-multiselect .ng-select-opened{
        width: 220px;
    }
    // :host ::ng-deep .ui-table .ui-table-tbody > tr{
    //     min-height: 108px;
    // }

    // .filter-btn .checkbox label input{
    //     width: 18px;
    //     height: 15px;
    //     margin-left: -48px;
    // }
    // .filter-btn .checkbox label img{
    //     display:inline-block;
    // }
    .filter-btn .checkbox label span{
        display:none;
    }

    .adv-summary-mob{
        display: flex;
        align-items: center;
        width:100%;
        min-height: 90px;
    }
    .adv-summary-mob .adv-summary{
        width:calc(100% - 100px);
    }
    .adv-summary-mob .adv-apply{
        width:100px;
        height:100%;
    }
    .adv-summary-mob .applied , .adv-summary-mob .apply{
        width: 70px !important;
    }
    :host ::ng-deep .adv-summary-mob .applied .ui-button-text, :host ::ng-deep .adv-summary-mob .apply .ui-button-text{
        padding: 5px 6px  5px 17px;
        font-size: 11px;
    }
    :host ::ng-deep .adv-summary-mob .apply .ui-button-icon-left , :host ::ng-deep .adv-summary-mob .applied .ui-button-icon-left{
        left: 5px;
        font-size: 0.9em;
    }
    .verification-status{
        display: inline-block;
    }
    .verification-status img{
        width:17px;
    }

    :host ::ng-deep .ui-dialog{
        width: 87%; 
    }
    
} 
@media screen and (max-width:670px) {
    :host ::ng-deep .ui-toolbar{
        top:60px !important;
    }
}
@media screen and (max-width:600px){
    .filter-cells{
        padding:0  3px;
    }
    .all-filters-cell{
        padding:0  3px;
    }
    .all-filters-cell button{
        padding-left: 0px;
        padding-right: 0px;
    }
    // :host ::ng-deep .ui-button.ui-button-text-icon-left .ui-button-text {
    //     padding: 0.429em 0em 0.429em 1em;
    // }
}
@media screen and (max-width:550px){
    .adv-summary-mob .adv-summary{
        width:calc(100% - 66px);
        // width:calc(100% - 53px);
    }
    .adv-summary-mob .adv-apply{
        width:66px;
        // width:53px;
    }
    .logo-div{
        width: 36px;
        margin-right: 5px;
    }
    .logo-div img{
        width:100%;
    }
    .content-div{
        width: calc(100% - 66px);
        // width: calc(100% - 53px);
    }
    .adv-summary-mob .applied , .adv-summary-mob .apply{
        width: 66px !important;
        // width: 53px !important;
    }
    :host ::ng-deep .adv-summary-mob .apply .ui-button-icon-left , :host ::ng-deep .adv-summary-mob .applied .ui-button-icon-left{
        left: 6px;
        font-size: 0.8em;
    }
    .content-div  .job-title{
        font-size: 0.9em;
    }
    .location-mob , .employment-type-mob{
        font-size:11px;
    }
    .filter-btn{
        margin: 0;
    }
}

@media screen and (max-width:375px){
    .filter-btn{
        padding: 0px 10px;
    }
}

// @media screen and (max-width:400px){
//     .filter-btn{
//         margin: 0;
//     }
// }

:host ::ng-deep .ui-table-responsive .ui-table-tbody>tr>td{
    float:none;
    // max-width: 150px;
}   

// table {
//     display: table;
// }
// tbody {
//     display: table-row-group;
// }
// th {
//     display: table-cell;
// }
// tr {
//     display: table-row;
// }
// td{
//     display: table-cell;
// }