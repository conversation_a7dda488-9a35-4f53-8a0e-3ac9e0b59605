

.material-form-wrapper{
    background-color:#EDF0F3;
  
  }
  .top-banner{
    height:100px;
    background-color:#314E63;
  }
  
  .material-form-body{
    width:100%;
    margin:10px auto 0;
    margin-top:0px ;
    padding-top:30px;
    padding-bottom:30px;
  }
  .form-section{
    background-color: #fff;
    padding:30px 30px 0;
    transition:box-shadow 0.3s ease;
  }
  .form-section.active{
    box-shadow: 0 0 5px #777;
    transform:translateZ(1px);
  }
  .form-section.header-form-section{
    padding:40px;
  }
  .material-form-body .form-control{
    border-bottom: 1px solid #ccc;
    border-top:none;
    border-left:none;
    border-right:none;
    box-shadow: none;
    border-radius: 0;
  }
  .material-form-body {
    font-size: 1.8rem;
  }
  .material-form-body .form-control#form-title{
    font-size:3rem;
  }
  .material-form-body .form-control:focus{
    box-shadow: none;
  }
  
  .flex-vertical-end{
    display: flex;
    align-items:flex-end;
  }
  
  
  
  
  /* End  Custom toggle button  */
  
  /* Start input animation  */
  
  .material-group {
    position:relative;
  }
  .form-title-material-group{
    margin-bottom:50px;
    padding: 1px;
  }
  .material-group input {
    display:block;
    width:100%;
    border:none;
    border-bottom:1px solid #ccc;
  }
  .material-group input:focus {
    outline:none;
  }
  
  
  
  /* BOTTOM BARS ================================= */
  .material-group .bar    { position:relative; display:block; width:100%; }
  .material-group .bar:before, .material-group .bar:after     {
    content:'';
    height:2px;
    width:0;
    top:32px;
    z-index: 2;
    position:absolute;
    background:#5264AE;
    transition:0.2s ease all;
    -moz-transition:0.2s ease all;
    -webkit-transition:0.2s ease all;
  }
  .material-group .bar:before {
    left:50%;
  }
  .material-group .bar:after {
    right:50%;
  }
  
  /* active state */
  .material-group input:focus ~ .bar:before,.material-group  input:focus ~ .bar:after {
    width:50%;
  }
  
  /* HIGHLIGHTER ================================== */
  .material-group .highlight {
    position:absolute;
    height:60%;
    width:100px;
    top:25%;
    left:0;
    pointer-events:none;
    opacity:0.5;
  }
  
  /* active state */
  .material-group input:focus ~ .highlight  {
    -webkit-animation:inputHighlighter 0.3s ease;
    -moz-animation:inputHighlighter 0.3s ease;
    animation:inputHighlighter 0.3s ease;
  }
  
  /* ANIMATIONS ================ */
  @-webkit-keyframes inputHighlighter {
    from { background:#5264AE; }
    to    { width:0; background:transparent; }
  }
  @-moz-keyframes inputHighlighter {
    from { background:#5264AE; }
    to    { width:0; background:transparent; }
  }
  @keyframes inputHighlighter {
    from { background:#5264AE; }
    to    { width:0; background:transparent; }
  }
  
  /* End input animation  */
  
  
  
  
  /* Start custom buttons */
  
  
  
  .add-question{
    width: 70px;
    height: 70px;
    position: fixed;
    z-index: 300;
    left: 7%;
    top: 296px;
    border-radius: 50%;
    color:#fff;
    border:0;
    background:#314e63;
    outline: none;
    box-shadow: 0 4px 4px #a1a1a1;
  }

  .add-question[disabled] {
    cursor: not-allowed;
  }

  .add-form {
    width: 70px;
    height: 70px;
    position: fixed;
    z-index: 300;
    left: 7%;
    top: 216px;
    border-radius: 50%;
    color:#fff;
    border:0;
    background:#314e63;
    outline: none;
    box-shadow: 0 4px 4px #a1a1a1;
  }
  
  /* End custom buttons */
  
  
  @media screen and (min-width:950px) {
    .material-form-body{
      width: 856px;
    }
  }

  @media screen and (max-width: 671px) {
    .add-question {
      font-size: 12px;
      width: 60px;
      height: 60px;
      left: 6%;
      top: 278px;
    }

    .add-form {
      font-size: 12px;
      width: 50px;
      height: 50px;
    }

  }

  @media screen and (min-width: 672px) and (max-width:1050px) {
    .add-question {
      font-size: 12px;
      width: 60px;
      height: 60px;
      left: 4%;
      top: 278px;
    }

    .add-form {
      font-size: 12px;
      width: 60px;
      height: 60px;
      left: 4%
    }

  }
  /************************** START PREVIEW ****************************/
  
  .form-section.inactive{
    padding-bottom:30px;
  }
  .form-section.inactive form select , .form-section.inactive form .add-btn ,.form-section.inactive form .footer-form-group , .form-section.inactive form .del-fa-icon{
    display:none;
  }
  .form-section.inactive input{
    cursor:text;
    background:transparent;
    border:0;
    color:black;
  }
  /************************** END PREVIEW ****************************/
  