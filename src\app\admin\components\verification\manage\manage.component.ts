import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { Component, OnInit, OnDestroy, Type, ViewChild } from '@angular/core';
import { VerificationService } from 'app/admin/services/verification.service';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs/Subject';
import { Language } from 'app/admin/models/language';
import { LanguageService } from 'app/admin/services/language.service';
import { Table } from 'primeng/table';
import { FilterUtils } from 'primeng/utils';
import { Calendar } from 'primeng/calendar';
declare var $: any;
@Component({
  selector: 'app-manage',
  templateUrl: './manage.component.html',
  styleUrls: ['./manage.component.css']
})
export class ManageComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  @ViewChild('cl') calendar: Calendar;
  rangeDates: Date[];
  order = [false, false, false, false];
  transactionToPreview: any;
  private ngUnsubscribe: Subject<any> = new Subject();
  transactionsArray: any[] = [];
  transactionsArrayTemp: any[] = [];
  filteredTransactions: any[] = [];
  languagesArray: Language[] = [];
  transCount: number;
  currentLangId = 1;
  skillParentArr: { 'value': number, 'label': string, 'parent_id': number }[][] = [];
  skillCatsArr: { 'value': number, 'label': string }[][] = [];
 // majorArr: { 'value': number, 'label': string, 'major_id': number , 'parent_id': number }[][] = [];
  majorArr: { 'value': number, 'label': string }[][] = [];
 // majorParentArr: { 'value': number, 'label': string, 'parent_id': number }[][] = [];
  experienceFieldsArr: { 'value': number, 'label': string, 'experience_field_id': number }[][] = [];
  jobTitlesArr: { 'value': number, 'label': string, 'job_title_id': number }[][] = [];

  displayModal = false;
  displayAddModal = false;
  opperationNum;
  majorForm;
  skillForm;
  minorTrans: {
    id: any; cv_id: any, adv_id: any; unverified_item_id: any; type: any; date: any;
    major_id: any; major_name: any; major_verified: any;
    name: any; verified: any;
  };
  expFieldForm;
  jobTitleSynTrans;
  loading = true;
  types = [
    { 'value': null, 'label': '' },
    { 'value': 'UNIVERSITY', 'label': 'University' },
    { 'value': 'SKILL', 'label': 'Skill' },
    { 'value': 'MAJOR', 'label': 'Major' },
    { 'value': 'MINOR', 'label': 'Minor' },
    { 'value': 'JOB_TITLE', 'label': 'Job Title' },
    { 'value': 'JOB_TITLE_SYNONYMS', 'label': 'Job Title Synonyms' },
  ];
  type;
message='';
  constructor(private translate: TranslateService,
    private verifictionService: VerificationService,
    private fb: FormBuilder,
    private languageService: LanguageService) {
    translate.addLangs(['en', 'ar']);
    translate.setDefaultLang('en');
    const browserLang = translate.getBrowserLang();
    translate.use(browserLang.match(/en|ar/) ? browserLang : 'en');
    if (this.translate.currentLang === 'en') {
      this.currentLangId = 1;
    } else { this.currentLangId = 2; }

    FilterUtils['isBetween'] = (value: any, filter: any): boolean => {
      let newDate = new Date(value);
      console.log(newDate);
      filter = new Date(filter);
      if (this.rangeDates['1'] === null) {
        if (newDate === filter) {
          let startDate = new Date(this.rangeDates['0']);
          if (startDate <= newDate) {
            return true;
          }
        }
      } else {
        let startDate = new Date(this.rangeDates['0']);
        let finishDate = new Date(this.rangeDates['1']);
        console.log(startDate, finishDate);
        if (startDate <= newDate && finishDate >= newDate) {
          {
            return true;
          }
        }
      }
    };
  }

  ngOnInit() {
    this.getLanguages();
    // this.getTransactions();
  }

  getTransactions() {
    // getting transactions
    this.verifictionService.getAllTransactions().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log('res', res);
      if(!res['actionlog']){
      this. message = 'there are no system action logs';
        alert(this.message);
        this.loading = false;
      }
      else{
      let temp = res['actionlog'];
      let temp2 = res['skill_type_job_title'];
      let temp3 = res['skill_category'];
      // let temp4 = res['major_parent'];
      // let temp5 = res['major'];
      let temp5 = res['education_fields'];
      let temp6 = res['experience_fields'];
      let temp7 = res['job_title'];
      let experience_fields = [];
      for (let t of temp) {
        if (t.operation.code === 'UNIVERSITY') {
          this.transactionsArray.push({
            'trans_id': t.id,
            'id': t.cv_id ? t.cv_id : t.adv_id,
            'cv_id': t.cv_id,
            'adv_id': t.adv_id,
            'unverified_item_id': t.unverified_item_id,
            'type': t.operation.code,
            'date': t.created_at,
            'url': t.unverified_item.url,
            'country': t.unverified_item.country,
            'country_code': t.unverified_item.country_code,
            'city': t.unverified_item.city,
            'street_address': t.unverified_item.street_address,
            'verified': t.unverified_item.verified,
            'name': t.unverified_item.institution_translation[0].name
          });
        } else if (t.operation.code === 'MAJOR') {
          this.transactionsArray.push({
            'trans_id': t.id,
            'id': t.cv_id ? t.cv_id : t.adv_id,
            'cv_id': t.cv_id,
            'adv_id': t.adv_id,
            'unverified_item_id': t.unverified_item_id,
            'type': t.operation.code,
            'date': t.created_at,
          //  'parent_id': t.unverified_item.major_parent_id,
            'verified': t.unverified_item.verified,
            'name': t.unverified_item.name
          });
        } else if (t.operation.code === 'MINOR') {
          this.transactionsArray.push({
            'trans_id': t.id,
            'id': t.cv_id ? t.cv_id : t.adv_id,
            'cv_id': t.cv_id,
            'adv_id': t.adv_id,
            'unverified_item_id': t.unverified_item_id,
            'type': t.operation.code,
            'date': t.created_at,
            'major_id': t.unverified_item.major_education_field_id,
         //   'major_parent_id': t.unverified_item.major.major_parent_id,
            'major_name': t.unverified_item.major_education_field_name,
            'major_verified': t.unverified_item.major_education_field_verified,
            'name': t.unverified_item.name,
            'verified': t.unverified_item.verified,
          });
        } else if (t.operation.code === 'SKILL') {
          this.transactionsArray.push({
            'trans_id': t.id,
            'id': t.cv_id ? t.cv_id : t.adv_id,
            'cv_id': t.cv_id,
            'adv_id': t.adv_id,
            'unverified_item_id': t.unverified_item_id,
            'type': t.operation.code,
            // 'time'               : t.time,
            'date': t.created_at,
            'name': t.unverified_item.skill_type_trans[0].name,
            'verified': t.unverified_item.verified
          });


        } else if (t.operation.code === 'JOB_TITLE') {
          if (t.unverified_item.job_title_translation !== undefined && t.unverified_item.code !== 404) {
            experience_fields = t.unverified_item.experience_fields;
            this.transactionsArray.push({
              'trans_id': t.id,
              'id': t.cv_id ? t.cv_id : t.adv_id,
              'cv_id': t.cv_id,
              'adv_id': t.adv_id,
              'unverified_item_id': t.unverified_item_id,
              'type': t.operation.code,
              // 'time'               : t.time,
              'date': t.created_at,
              'name': t.unverified_item.job_title_translation[0].name,
              'job_title_id': t.unverified_item.job_title_translation[0].job_title_id,
              'experience_fields': experience_fields,
              'major_job_title_id': t.unverified_item.major_job_title_id,
              'verified': t.unverified_item.verified
            });
          }

        } else if (t.operation.code === 'JOB_TITLE_SYNONYMS') {
          // if(t.unverified_item.job_title_synonyms_trans===null ||undefined ||[])console.log('syno',t);
          if (t.unverified_item.job_title_synonyms_trans !== undefined && t.unverified_item.code !== 404) {
            this.transactionsArray.push({
              'trans_id': t.id,
              'id': t.cv_id ? t.cv_id : t.adv_id,
              'cv_id': t.cv_id,
              'adv_id': t.adv_id,
              'unverified_item_id': t.unverified_item_id,
              'type': t.operation.code,
              // 'time'               : t.time,
              'date': t.created_at,
              'name': t.unverified_item.job_title_synonyms_trans[0].name,
              'job_title_id': t.unverified_item.job_title_id,
              'job_title_name': t.unverified_item.job_title.job_title_translation[0].name,
              'job_title_verified': t.unverified_item.job_title.verified,
              'verified': t.unverified_item.verified
            });
          }

        }

      }
      console.log('transactionsArray', this.transactionsArray);
      this.loading = false;
      this.table.filter(1, 'cv_id', 'startsWith');
      this.table.filter(null, 'cv_id', 'startsWith');

      // get skill parent data
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.skillParentArr[i] = [];
        for (let s of temp2) {
          if (s.job_title_translation[i] !== undefined) {
            this.skillParentArr[i].push({
              'value': s.id,
              'label': s.job_title_translation[i].name,
              'parent_id': s.job_title_translation[i].skill_type_parent_id
            });
          }

        }
        this.skillParentArr[i].unshift({ 'value': null, 'label': '', 'parent_id': null });
      }
      console.log('skill parent data', this.skillParentArr);

      // get skill categories data
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.skillCatsArr[i] = [];
        for (let s of temp3) {
          if (s.skill_category_translation[i] !== undefined) {
            this.skillCatsArr[i].push({
              'value': s.id,
              'label': s.skill_category_translation[i].name
            });
          }
        }
        this.skillCatsArr[i].unshift({ 'value': null, 'label': '' });
      }
      console.log('skill categories data', this.skillCatsArr);

      // get major parent data
      // for (let i = 0; i < this.languagesArray.length; i++) {
      //   this.majorParentArr[i] = [];
      //   for (let m of temp4) {
      //     if (m.major_parent_translation[i] !== undefined) {
      //       this.majorParentArr[i].push({
      //         'value': m.id,
      //         'label': m.major_parent_translation[i].name,
      //         'parent_id': m.major_parent_translation[i].major_parent_id
      //       });
      //     }
      //   }
      //   this.majorParentArr[i].unshift({ 'value': null, 'label': '', 'parent_id': null });
      // }
      // console.log('major parent data', this.majorParentArr);

      // get major data
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.majorArr[i] = [];
        for (let m of temp5) {
            this.majorArr[i].push({
              'value': m.id,
              'label': m.name,
            });
        }
        this.majorArr[i].unshift({ 'value': null, 'label': ''});
      }
      console.log('major data', this.majorArr);

      // get major data
      // for (let i = 0; i < this.languagesArray.length; i++) {
      //   this.majorArr[i] = [];
      //   for (let m of temp5) {
      //     if (m.trans[i] !== undefined) {
      //       this.majorArr[i].push({
      //         'value': m.id,
      //         'label': m.trans[i].name,
      //         'major_id': m.trans[i].major_id,
      //         'parent_id':m.major_parent_id
      //       });
      //     }
      //   }
      //   this.majorArr[i].unshift({ 'value': null, 'label': '', 'major_id': null , 'parent_id':null});
      // }
      // console.log('major data', this.majorArr);

      // get experience fields data
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.experienceFieldsArr[i] = [];
        for (let m of temp6) {
          if (m.experience_field_translation[i] !== undefined) {
            this.experienceFieldsArr[i].push({
              'value': m.id,
              'label': m.experience_field_translation[i].name,
              'experience_field_id': m.experience_field_translation[i].experience_field_id
            });
          }
        }
        this.experienceFieldsArr[i].unshift({ 'value': null, 'label': '', 'experience_field_id': null });
      }
      console.log('experience fields data', this.experienceFieldsArr);

      // get job titles data
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.jobTitlesArr[i] = [];
        for (let m of temp7) {
          if (m.job_title_translation[i] !== undefined) {
            this.jobTitlesArr[i].push({
              'value': m.id,
              'label': m.job_title_translation[i].name,
              'job_title_id': m.job_title_translation[i].job_title_id
            });
          }
        }
        this.jobTitlesArr[i].unshift({ 'value': null, 'label': '', 'job_title_id': null });
      }
      console.log('job titles data', this.jobTitlesArr);
    }
    });
    this.filteredTransactions = this.transactionsArray;
  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log(res);
      let temp = res['data'];
      for (let lang of temp) {
        this.languagesArray.push({
          'id': lang.id,
          'name': lang.name
        });
      }

      console.log('languages array', this.languagesArray);
      console.log('lang arr length', this.languagesArray.length);
      this.getTransactions();
    });
  }

  removeTransFromTable(event) {
    console.log('event', event);
    let trans = event['old'];
    let newTrans = event['new'][0];
    let index = this.getTransactionIndex(trans);
    console.log('index', index);
    this.transactionsArrayTemp = this.transactionsArray.splice(index, 1);
    this.table._totalRecords = this.transactionsArray.length;
    this.transactionsArrayTemp = this.transactionsArray;
    this.transactionsArray = this.transactionsArrayTemp.slice(0);
  //  if (trans.type === 'MAJOR' && trans.openedFromMinor === true)

    if (trans.type === 'MAJOR' && event['endTransaction'] === false) {
      console.log("inside first if - type = major");
      if(trans.openedFromMinor === true){
        console.log("openedFromMinor = true");
        console.log("this.minorTrans",this.minorTrans);
        this.minorTrans.major_verified = 1;
        this.displayTModal(this.minorTrans);
        this.minorTrans = null;
        if (newTrans.verified_entry&& newTrans.verified_entry.verified == 1) {
          console.log("inside second if newTrans",newTrans);
          for (let i = 0; i < this.languagesArray.length; i++) {
            this.majorArr[i].push({
              'value': newTrans.verified_entry.id,
              'label': newTrans.verified_entry.name,
              // 'major_id': newTrans.verified_entry.id,
              // 'parent_id':newTrans.verified_entry.major_parent_id
            });
            console.log('eventNew ',this.majorArr[i]);
          }
        }
      }
      else{
        // add verified major to majors array
        if (newTrans.verified_entry&& newTrans.verified_entry.verified == 1) {
          console.log("inside second if newTrans",newTrans);
          for (let i = 0; i < this.languagesArray.length; i++) {
            this.majorArr[i].push({
              'value': newTrans.verified_entry.id,
              'label': newTrans.verified_entry.name,
            });
            console.log('eventNew ',this.majorArr[i]);
          }
        }
        this.closeModal();
      }

    } 
    else if (trans.type === 'JOB_TITLE' && trans.openedFromSynonym === true) {
      this.jobTitleSynTrans.job_title_verified = 1;
      this.displayTModal(this.jobTitleSynTrans);
      this.jobTitleSynTrans = null;
      if (newTrans.verified_entry&& newTrans.verified_entry.verified == 1) {
        for (let i = 0; i < this.languagesArray.length; i++) {
          this.jobTitlesArr[i].push({
              'value':newTrans.verified_entry.id,
            'label': newTrans.verified_entry.job_title_translation[i].name,
            'job_title_id': newTrans.verified_entry.id,
          
          });
        }
      }
    } else {
      this.closeModal();
    }
    this.table._totalRecords = this.transactionsArray.length;
  }

  closeModal() {
    this.displayModal = false;
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
    $('#transModal').hide();
    $('div.modal-backdrop.fade.in').remove();
  }

  displayTModal(t) {
    console.log('displayTModal', t);
    if (t.type === 'UNIVERSITY') {
      this.transactionToPreview = {
        'id': t.trans_id ? t.trans_id : t.id,
        'cv_id': t.cv_id,
        'adv_id': t.adv_id,
        'unverified_item_id': t.unverified_item_id,
        'type': t.type,
        'date': t.date,
        'url': t.url,
        'country': t.country,
        'country_code': t.country_code,
        'city': t.city,
        'street_address': t.street_address,
        'verified': t.verified,
        'name': t.name
      };
    } else if (t.type === 'SKILL') {
      this.transactionToPreview = {
        'id': t.trans_id ? t.trans_id : t.id,
        'cv_id': t.cv_id,
        'adv_id': t.adv_id,
        'unverified_item_id': t.unverified_item_id,
        'type': t.type,
        'date': t.date,
        'name': t.name,
        'verified': t.verified
      };
    } else if (t.type === 'MAJOR') {
      this.transactionToPreview = {
        'id': t.trans_id ? t.trans_id : t.id,
        'cv_id': t.cv_id,
        'adv_id': t.adv_id,
        'unverified_item_id': t.unverified_item_id,
        'type': t.type,
        'date': t.date,
        'name': t.name,
        // 'parent_id': t.parent_id,
        'verified': t.verified,
        'openedFromMinor': false
      };

    } else if (t.type === 'MINOR') {
      this.transactionToPreview = {
        'id': t.trans_id ? t.trans_id : t.id,
        'cv_id': t.cv_id,
        'adv_id': t.adv_id,
        'unverified_item_id': t.unverified_item_id,
        'type': t.type,
        'date': t.date,
        'major_id': t.major_id,
        // 'major_parent_id': t.major_parent_id,
        'major_name': t.major_name,
        'major_verified': t.major_verified,
        'name': t.name,
        'verified': t.verified,
      };
    } else if (t.type === 'JOB_TITLE') {
      this.transactionToPreview = {
        'id':t.trans_id ? t.trans_id : t.id,
        'cv_id': t.cv_id,
        'adv_id': t.id,
        'unverified_item_id': t.unverified_item_id,
        'type': t.type,
        'date': t.date,
        'name': t.name,
        'job_title_id': t.job_title_id,
        'experience_fields': t.experience_fields,
        'major_job_title_id': t.major_job_title_id,
        'verified': t.verified,
        'openedFromSynonym': false
      };

    } else if (t.type === 'JOB_TITLE_SYNONYMS') {
      this.transactionToPreview = {
        'id': t.trans_id ? t.trans_id : t.id,
        'cv_id': t.cv_id,
        'adv_id': t.adv_id,
        'unverified_item_id': t.unverified_item_id,
        'type': t.type,
        'date': t.date,
        'name': t.name,
        'job_title_id': t.job_title_id,
        'job_title_name': t.job_title_name,
        'job_title_verified': t.job_title_verified,
        'verified': t.verified
      };

    }

    console.log('trans t prev', this.transactionToPreview);
    this.displayModal = true;

  }


  openMajorVerify() {
    console.log("in openMajorVerify");
    // this.closeModal();
    this.minorTrans = {
      'id': this.transactionToPreview.id,
      'cv_id': this.transactionToPreview.cv_id,
      'adv_id': this.transactionToPreview.adv_id,
      'unverified_item_id': this.transactionToPreview.unverified_item_id,
      'type': this.transactionToPreview.type,
      'date': this.transactionToPreview.date,
      'major_id': this.transactionToPreview.major_id,
    //  'major_parent_id': this.transactionToPreview.major_parent_id,
      'major_name': this.transactionToPreview.major_name,
      'major_verified': this.transactionToPreview.major_verified,
      'name': this.transactionToPreview.name,
      'verified': this.transactionToPreview.verified,
    };
    console.log('old Minor',this.minorTrans);
    for (let trans of this.transactionsArray) {
      // if (trans.type === 'MAJOR') console.log('openMajorVerify', trans);
      if (trans.type === 'MAJOR' && trans.name === this.transactionToPreview.major_name &&
        trans.unverified_item_id === this.transactionToPreview.major_id) {
          console.log('openMajorVerify', trans);
        this.transactionToPreview = {
          'id': trans.trans_id,
          // 'id': trans.id,
          'cv_id': trans.cv_id,
          'adv_id': trans.adv_id,
          'unverified_item_id': trans.unverified_item_id,
          'type': trans.type,
          'date': trans.date,
          'name': trans.name,
       //   'parent_id': trans.parent_id,
          'verified': trans.verified,
          'openedFromMinor': true
        };
        console.log('trans to prev', this.transactionToPreview);
      }
    }
    // this.displayModal  = true;
    // $('body').addClass('modal-open');
    // $('body').addAttr('style');
    // $('body').appendChild('div.modal-backdrop.fade.in');
  }

  openJobTitleVerify() {
    this.jobTitleSynTrans = {
      'id': this.transactionToPreview.id,
      'cv_id': this.transactionToPreview.cv_id,
      'adv_id': this.transactionToPreview.adv_id,
      'unverified_item_id': this.transactionToPreview.unverified_item_id,
      'type': this.transactionToPreview.type,
      'date': this.transactionToPreview.date,
      'name': this.transactionToPreview.name,
      'job_title_id': this.transactionToPreview.job_title_id,
      'job_title_verified': this.transactionToPreview.job_title_verified,
      'job_title_name': this.transactionToPreview.job_title_name,
      'verified': this.transactionToPreview.verified
    };

    for (let trans of this.transactionsArray) {
      if (trans.type === 'JOB_TITLE' && trans.job_title_id === this.transactionToPreview.job_title_id) {
        console.log('trans openJobTitleVerify', trans);

        this.transactionToPreview = {
          'id': trans.trans_id,
          'cv_id': trans.cv_id,
          'adv_id': trans.adv_id,
          'unverified_item_id': trans.unverified_item_id,
          'type': trans.type,
          'date': trans.date,
          'name': trans.name,
          'job_title_id': trans.job_title_id,
          'experience_fields': trans.experience_fields,
          'major_job_title_id': trans.major_job_title_id,
          'verified': trans.verified,
          'openedFromSynonym': true
        };

        console.log('trans to prev', this.transactionToPreview);
      }
    }
  }




  addMajorParent() {
    this.opperationNum = 1;
    this.buildEmptyForm();
    this.openAddModal();
  }
  


  addSkillCategory() {
    this.opperationNum = 2;
    this.buildEmptyForm();
    this.openAddModal();
  }

  addExpField() {
    this.opperationNum = 3;
    this.buildEmptyForm();
    this.openAddModal();
  }


  openAddModal() {
    // this.displayModal    = false;
    // $('#transModal').hide();
    $('#addValueModal').show();
    this.displayAddModal = true;
  }

  closeAddModal() {
    this.displayAddModal = false;
    this.displayModal = true;
    $('body').addClass('modal-open');
  }

  buildEmptyForm() {
    if (this.opperationNum === 1) {
      this.majorForm = this.fb.group({
        'major_parent_trans': this.fb.array([])
      });
      console.log('form', this.majorForm);
    } else if (this.opperationNum === 2) {
      this.skillForm = this.fb.group({
        'skill_category_trans': this.fb.array([])
      });
      console.log('form', this.skillForm);
    } else if (this.opperationNum === 3) {
      this.expFieldForm = this.fb.group({
        //'major_experience_field_id': [1],
        'experience_fields_trans': this.fb.array([])
      });
      console.log('form', this.expFieldForm);
    }

    for (let lang of this.languagesArray) {
      this.fillTrans(lang.id);
    }

  }

  private fillTrans(langId) {
    if (this.opperationNum === 1) {
      this.major_parent_trans.insert(this.major_parent_trans.length, this.createTransControls(langId));
    } else if (this.opperationNum === 2) {
      this.skill_category_trans.insert(this.skill_category_trans.length, this.createTransControls(langId));
    }
    else if (this.opperationNum === 3) {
      this.experience_fields_trans.insert(this.experience_fields_trans.length, this.createTransControls(langId));
    }
  }
  private createTransControls(langId: number) {
    return new FormGroup({
      'translated_language_id': new FormControl(langId),
      'name': new FormControl('', Validators.required),
    });

  }

  // get name() {
  //   return this.majorForm.get('major_parent_trans.answer') ;
  // }

  get major_parent_trans() {
    return this.majorForm.get('major_parent_trans');
  }

  get skill_category_trans() {
    return this.skillForm.get('skill_category_trans');
  }

  get experience_fields_trans() {
    return this.expFieldForm.get('experience_fields_trans');
  }

  changeLang(langId) {
    // this.translate.use(langId);
    this.currentLangId = langId;
  }


  getTransactionIndex(transaction): number {
    let index: number = null;
    for (let t of this.transactionsArray) {
      if (t.trans_id === transaction.id) {
        //if (t.id === transaction.trans_id) {
        index = this.transactionsArray.indexOf(t);
      }
    }
    return index;
  }


  addNewValue() {
    let dataToSend;
    if (this.opperationNum === 1) {
      dataToSend = this.majorForm.value;
      console.log('major data', dataToSend);
      // this.verifictionService.addNewMajorParent(dataToSend).subscribe(res => {
      //   console.log('res', res);
      //   let temp = res['data'];
      //   for (let i = 0; i < this.languagesArray.length; i++) {
      //     this.majorParentArr[i].push({
      //       'value': temp.major_parent_translation[i].major_parent_id,
      //       'label': temp.major_parent_translation[i].name,
      //       'parent_id': temp.id
            
      //     });

      //   }
      //   console.log('new major parent arr', this.majorParentArr);
      //   this.closeAddModal();
      // });
    } else if (this.opperationNum === 2) {
      dataToSend = this.skillForm.value;
      console.log('skill data', dataToSend);
      this.verifictionService.addNewSkillCategory(dataToSend).subscribe(res => {
        console.log('res', res);
        let temp = res['data'];
        for (let i = 0; i < this.languagesArray.length; i++) {
          this.skillCatsArr[i].push({
            'label': temp.skill_category_translation[i].name,
            'value': temp.skill_category_translation[i].skill_category_id
          });
        }
        console.log('new skillCatsArr', this.skillCatsArr);
        this.closeAddModal();
      });
    } else if (this.opperationNum === 3) {
      dataToSend = this.expFieldForm.value;
      console.log('exp field data', dataToSend);
      this.verifictionService.addNeExperienceField(dataToSend).subscribe(res => {
        console.log('res', res);
        let temp = res['data'];
        for (let i = 0; i < this.languagesArray.length; i++) {
          this.experienceFieldsArr[i].push({
            'label': temp.experience_field_translation[i].name,
            'value': temp.id,
            'experience_field_id': temp.experience_field_translation[i].experience_field_id
          });
        }
        console.log('new expFieldArr', this.experienceFieldsArr);
        this.closeAddModal();
      });
    }
  }

  onDateSelect(value) {
    this.table.filter(this.formatDate(value), 'date', 'in');
  }

  formatDate(date) {
    let month = date.getMonth() + 1;
    let day = date.getDate();

    if (month < 10) {
      month = '0' + month;
    }

    if (day < 10) {
      day = '0' + day;
    }

    return date.getFullYear() + '-' + month + '-' + day;
  }


  clearAll() {
    this.table.filter(null, 'id', 'startsWith');
    this.table.filter(null, 'trans_id', 'contains');
    this.table.filter('', 'date', 'contains');
    this.table.filter(null, 'type', 'equals');
    this.table.filter(null, 'duration', 'contains');
    this.table.filterGlobal(null, 'contains');
    $('span.ui-column-filter.ui-calendar input').val(null);
    $('.ui-table-globalfilter-container input').val(null);
    console.log($('.ui-column-filter').val());
    $('.ui-column-filter').val(null);
    this.type = '';
    this.rangeDates = [new Date(''), new Date('')];
    this.calendar.onClearButtonClick('');
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
