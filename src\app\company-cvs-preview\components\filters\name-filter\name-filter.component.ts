import { Component, OnInit, Input, SimpleChanges } from '@angular/core';
import { FiltersService } from '../../../services/filters.service';
import { GeneralService } from '../../../../general/services/general.service';
import { Subject, Subscription } from 'rxjs';
import { Router } from '@angular/router';
declare var $: any;

@Component({
  selector: 'app-name-filter',
  templateUrl: './name-filter.component.html',
  styleUrls: ['./name-filter.component.css']
})
export class NameFilterComponent implements OnInit {

  namesDD = {
    "items":[],
    "name":null,
    "loading":false
  }

  initFilters = {};
  initFiltersTags = [];
  tags = [];

  currentFolderName: string = null;
  currentFolder:number = null;
  @Input() pageType;
  private ngUnsubscribe: Subject<any> = new Subject();
  subscriptionsArray: Subscription[] = [];
  namePlaceHolder = null;

  constructor(
    private filtersService:FiltersService,
    private generalService:GeneralService,
    private router: Router) {
      if (this.router.getCurrentNavigation().extras.state) {
        let state;
        state = this.router.getCurrentNavigation().extras.state;
        if(state.folder_id){
          this.currentFolder = state.folder_id;
          if(state.folder_label){
            this.currentFolderName = state.folder_label;
            this.namePlaceHolder = "Search for names in "+ this.currentFolderName;
          } 
        //  this.initiateNameItems();
        }
      }
      // else{
      //   this.namePlaceHolder = "Search for names in "+ this.pageType;
      // }
    }


  ngOnInit(): void {
    if(this.pageType === 'inbox'){
      this.currentFolder=1;
      this.namePlaceHolder = "Search for names in "+ this.pageType;
    }
    else if(this.pageType === 'cv_folders' && this.namePlaceHolder === null){
      this.namePlaceHolder = "Search for names in Custom Folders";
    }
      

    this.generalService.internalMessage.takeUntil(this.ngUnsubscribe).subscribe((data) => {
      if (data['message'] === 'changeNameFilterList' && data['mData']['folder_label'] !== undefined) {
        this.currentFolder = data['mData']['folder_id'];
        this.currentFolderName = data['mData']['folder_label'];
        this.namePlaceHolder = "Search for names in "+ this.currentFolderName;
       // this.initiateNameItems();
      }

      if (data['message'] === 'removedFilters' && data['dist'] === 'name_filter') {
        this.clearFilters();
      }

      if (data['message'] === 'clearAllFilters' && data['src'] === 'filters-wrapper'){
        this.clearFilters();
      }

      // if(data['message']==='sendFolderTitleAfterColsChange' && data['src']==='cvs-table'){
      //   this.currentFolderName = data['mData']['folderTitle'];
      //   console.log(" this.currentFolderName", this.currentFolderName);
      //   this.namePlaceHolder = "Search for names in "+ this.currentFolderName;
      //   console.log("this.namePlaceHolder",this.namePlaceHolder);
      // }
    });

    this.sendInitStateToWarapper();
  }

  // ngAfterViewInit() {
  //   this.initiateNameItems();
  // }

  initiateNameItems(){
    this.namesDD.items = [];
    this.namesDD.loading=true;
    this.filtersService.searchJobSeekerName("",this.pageType,{'folder_id': this.currentFolder}).subscribe(res => {
      this.namesDD.items = res['data'];
      this.namesDD.loading=false;
    });
  }

  search($event){
    this.namesDD.loading = true;
    this.cancelAllRequests();
    const subscription = this.filtersService.searchJobSeekerName($event.term,this.pageType,{'folder_id': this.currentFolder}).subscribe(res => {
      this.namesDD.loading = false;
      this.namesDD.items = res['data'];
    });
    this.subscriptionsArray.push(subscription);
  }

  setFilters(){
    let filters = {"name":this.namesDD.name};
    return filters;
  }
  setTags(){
    this.tags = [
      {"name":"name" ,"title":"Name", "value":(this.namesDD.name === null)? this.namesDD.name:this.namesDD.name.name , "type":"string"}
      // {"name":"name" ,"title":"Name", "value":this.namesDD.name.name , "type":"string"}
     ]
    return this.tags;
  }

  sendFilters() {
   this.generalService.notify('filters-changes', 'name_filter', 'filters-wrapper', {"filters":this.setFilters(), "tags":this.setTags()});
 }

  sendInitStateToWarapper() {
    this.initFilters = {"name":this.namesDD.name};
    this.initFiltersTags = this.setTags();
    this.generalService.notify('init-filters',
      'name_filter', 'filters-wrapper', {'filters':this.initFilters , 'tags':this.initFiltersTags});
  }

  clearFilters(){
    this.namesDD.name =  null;
  //  this.namesDD.name =  {"id":null,"name":""};
    this.tags = this.initFiltersTags;
  }

  clearName(){
    this.clearFilters();
  }

  //cancel unnecessary requests: 1-In case the user makes multiple similar requests in a short timeframe
  // 2-or if user makes a request then navigate to another page
  cancelAllRequests() {
    if (!this.subscriptionsArray) {
      return;
    }
    this.subscriptionsArray.forEach(s => s.unsubscribe());
    this.subscriptionsArray = [];
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.cancelAllRequests();
  }
}
