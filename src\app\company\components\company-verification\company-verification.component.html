<div class="CForm">
    <form #form="ngForm" [formGroup]="companyVerfication" class="form-horizontal validate-form">
        <div class="custom-row clearfix">
            <div class="col-sm-12 col-xs-12">
                <div class=" col-sm-2 focus-no-padding validate-input CScreen translate">
                    <p-dropdown [options]="" formControlName="id" [filter]="true" placeholder="ID">
                        <!-- [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}" -->
                        <!-- <ng-template let-degree pTemplate=" item ">
                            <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                            </div>
                        </ng-template> -->
                    </p-dropdown>
                </div>
                <div class=" col-sm-2 focus-no-padding validate-input CScreen translate">

                    <!-- optionLabel="" -->
                    <p-dropdown [options]="" formControlName="company_name" placeholder="Company Name" [filter]="true">
                        <!-- [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}" -->
                        <!-- <ng-template let-degree pTemplate=" item ">
                            <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                            </div>
                        </ng-template> -->
                    </p-dropdown>
                </div>
                <div class=" col-sm-2 focus-no-padding validate-input CScreen translate">
                    <!-- optionLabel="" -->
                    <p-dropdown [options]="" placeholder="Company Industry" formControlName="company_industry" [filter]="true">
                        <!-- [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}" -->
                        <!-- <ng-template let-degree pTemplate=" item ">
                            <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                            </div>
                        </ng-template> -->
                    </p-dropdown>
                </div>
                <div class=" col-sm-2 focus-no-padding validate-input CScreen translate">
                    <!-- optionLabel="" -->
                    <p-dropdown [options]="" placeholder="Company Type" formControlName="company_type" [filter]="true">
                        <!-- [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}" -->
                        <!-- <ng-template let-degree pTemplate=" item ">
                            <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                            </div>
                        </ng-template> -->
                    </p-dropdown>
                </div>
                <div class=" col-sm-2 focus-no-padding validate-input CScreen translate">
                    <!-- optionLabel="" -->
                    <p-dropdown [options]="" placeholder="Status" formControlName="status" [filter]="true">
                        <!-- [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}" -->
                        <!-- <ng-template let-degree pTemplate=" item ">
                            <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                            </div>
                        </ng-template> -->
                    </p-dropdown>
                </div>
                <div class=" col-sm-2 focus-no-padding validate-input CScreen translate">
                    <!-- optionLabel="" -->
                    <p-dropdown [options]="" formControlName="country" placeholder="Country" [filter]="true">
                        <!-- [ngClass]="{'has-val':companyForm.controls['company_type_id_temp'].value.company_type_id}" -->
                        <!-- <ng-template let-degree pTemplate=" item ">
                            <div class="ui-helper-clearfix " style="position: relative;height: 25px; ">
                                <div style="font-size:14px;float:left;margin-top:4px ">{{degree.label}}</div>
                            </div>
                        </ng-template> -->
                    </p-dropdown>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-xs-12 custom-row clearfix">
            <div class="col-sm-11">
                <button>
                    <span>Result</span>
                </button>
            </div>
            <div class="col-sm-1">
                <button>
                    <span>Search</span>
                </button>
            </div>
        </div>
        <div class="row div-margin-top-40">
            <div class="col-sm-12">
                <div class="table-preview-container">
                    <table class="table-preview">
                        <thead>
                            <tr>
                                <th><span translate>ID</span></th>
                                <th><span translate>Logo</span></th>
                                <th><span translate>Company Name</span></th>
                                <th><span translate>City/Country</span></th>
                                <th><span id="sepcial" translate>Registration
                                    <br>
                                    <i style="
                                    font-size: 12px;">Date - time</i>
                                </span>
                                </th>
                                <th><span translate>Status</span></th>
                                <th><span translate>Handled by</span></th>
                                <th><span translate>Log</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td></td>
                                <td>img</td>
                                <td>Smart Medical Services</td>
                                <td>Cairo/Egypt</td>
                                <td></td>
                                <td>Verified</td>
                                <td>AlaaN.</td>
                                <td>
                                    <button type="button" class="btn btn-primary btn-fa-info" data-toggle="modal" data-target="#locationModal">
                                            <!-- (click)="displayModal(language)" -->
                                        <i class="fa fa-info" aria-hidden="true"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>
</div>