import { Component, OnInit } from '@angular/core';
import {QuestionService} from '../form-services/question.service';
import {ActivatedRoute} from '@angular/router';
import 'rxjs/add/operator/switchMap';

@Component({
  selector: 'app-user-reply',
  templateUrl: './user-reply.component.html',
  styleUrls: ['./user-reply.component.css']
})
export class UserReplyComponent implements OnInit {
  questionWithChoices:any[]=[];
  questionsWithChoicesResponse:any[]=[];
  questionsWithTextResponse:any[]=[];
  user_id:any;
  constructor(private questionService:QuestionService , private route:ActivatedRoute) { }

  ngOnInit() {

     this.route.paramMap.switchMap(params => {
       this.user_id=params.get('id');
       return this.questionService.GetResponseByUser(1,this.user_id);
       })
       .subscribe(res => {
         this.questionWithChoices = res['questionWithChoices'],
         this.questionsWithChoicesResponse=res['questionsWithChoicesResponse'],
         this.questionsWithTextResponse=res['questionsWithTextResponse']
       });

     }



  checkQADependency(question_id,choice_id){
    for (let obj of this.questionsWithChoicesResponse){
      let questionsIds=Object.keys(obj)[0];
      let choicesIds:string[] =Object.values(obj);
      if(question_id.toString() ==questionsIds){
        for (let i of choicesIds[0]){
          if (i == choice_id) return true;
        }
      }
    }
    return false;
  }

  getTextAnswer(question_id){
    for (let obj of this.questionsWithTextResponse){
      let questionsIds=Object.keys(obj)[0];
      if(question_id.toString() == questionsIds){
        return Object.values(obj)[0];
      }
    }

    return null;
  }

  getDateAnswer(question_id){
    for (let obj of this.questionsWithTextResponse){
      let questionsIds=Object.keys(obj)[0];
      // if(question_id.toString() == questionsIds){
      //   return new Date(Object.values(obj)[0]);
      // }
    }

    return null;
  }


}
