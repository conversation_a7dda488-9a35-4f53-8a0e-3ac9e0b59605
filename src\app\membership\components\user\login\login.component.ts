import { Component, OnInit ,Input, NgZone,Renderer2 } from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {AuthService} from 'shared/shared-services/auth-service';
import {Router} from '@angular/router';
import { OAuthService } from 'angular-oauth2-oidc';
import {
  AuthService as SocialAuthService,
  FacebookLoginProvider
} from 'angular5-social-login';
import 'rxjs/add/operator/switchMap';
import { Errors } from 'app/membership/models/errors';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/empty';
import { EmptyObservable } from 'rxjs/observable/EmptyObservable';
import { SocketioService } from 'shared/shared-services/socketio.service';
import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from 'app/general/services/general.service';
import { EmailValidator } from 'shared/validators/email.validators';
// import { CredentialResponse, PromptMomentNotification } from 'google-one-tap';
import { CredentialResponse} from 'google-one-tap';
import { environment } from 'environments/environment.prod';
import { ScriptService } from 'shared/shared-services/script.service';
declare var $: any;

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
//  emailPattern = '^[a-z0-9._%+-]+@[a-z0-9.-]+[.]+[a-z]{2,4}$';
  loginForm;
  resetMessage;
  helpLink;
  clickForgetPassword: boolean = false;
  type= 'password';
  show = false;
  username = '';
  loader = false;
  @Input()
  fromPage: String = 'authPages' ;  // from page input  which means from where we came to login component :
  //   it hase   "authpages" as a  default parameter;
  // @ViewChild('googleLoginButtonDiv') googleLoginButtonDiv: ElementRef = new ElementRef({});
  constructor(private authService: AuthService,
              private router: Router,
              private  fb: FormBuilder,
              private socialAuthService: SocialAuthService,
              private oauthService: OAuthService,
              private socketSerivce: SocketioService,
              private title: Title,
              private meta:Meta,
              private generalService: GeneralService,
              private ngZone: NgZone,
              private renderer: Renderer2,
              private scriptService: ScriptService
              ) {
    this.loginForm = this.fb.group({
    //  email : ['', [Validators.required , Validators.email]],
      email : ['', [Validators.required , EmailValidator.isValidEmailFormat]],
      password : ['', [Validators.required , Validators.minLength(6), Validators.maxLength(20)]],
    });
    
  }

  ngOnInit() {
    if(sessionStorage.getItem("expired")){
      this.resetMessage = Errors.sessionTerminated;
      sessionStorage.removeItem("expired");
    }
    
    if(this.fromPage === 'authPages'){
      this.title.setTitle('CVeek Website  سيفيك | Job seeker Sign in ');
      this.meta.updateTag({ name: 'description', content: 'Sign in to your CVeek account, edit your CV, search for jobs, start applying for new job vacancies for free and get discovered by Employers. Search for jobs? Create CV? CVeek helps you in your online job search and free CV making.'});
    }

    this.authService.currentError.subscribe(errorMessage => this.resetMessage = errorMessage);
    
    this.initializeGoogleLogin();
  }

  // ngAfterViewInit() {
  //   this.initializeGoogleLogin();
  // }

  isInvalid(controlName: string) {
    return this.loginForm.controls[controlName].hasError('required');
  }

  isInvalidSyn(controlName: string) {
    return this.loginForm.controls[controlName].hasError('invalidEmailError');
    //return this.loginForm.controls[controlName].hasError('email');
  }

  isInvalidMin(controlName: string) {
    return this.loginForm.controls[controlName].hasError('minlength');
  }

  isInvalidMax(controlName: string) {
    return this.loginForm.controls[controlName].hasError('maxlength');
  }

  login() {
    this.authService.logoutIfAlreadyLoggedin();

    const data = {
      'email' : this.loginForm.get('email').value,
      'password' : this.loginForm.get('password').value,
    } ;
    if (this.loginForm.valid) {
      this.loader = true;
      this.oauthService.fetchTokenUsingPasswordFlow(data.email,data.password).then(
        (resp) => {
        if (resp['error'] && resp['type']=== 'UnverifiedEmailLogin'){
          // if user orginaly comes from verification page with senario he visit verification page from another browser
          // we navigate the user to login page to get his email then navigate him to verification page but without 
          // displaying error about verify in this case
          if(!localStorage.getItem('fromVerification')){
            this.authService.changeError(Errors.emailVerification);
          }
          else{
            localStorage.removeItem('fromVerification');
            this.authService.changeError("");
          }

          return Promise.reject('email verification');
        }
        else if(resp['error']){
          this.authService.changeError(resp['error']);
          return Promise.reject(resp['error']);
        }
        else{
           // Loading data about the user
            
          return this.oauthService.loadUserProfile();
        }
      },
      (err) => {
        this.loader = false;
        if (err['error']['error'] == 'Unauthorized'){
          this.resetMessage = Errors.InvalidEmailOrPassword;
          this.resetHelpLink();
        //  console.clear();
        }
      }
      ).catch(error => {
        this.loader = false;
        if(error === 'email verification'){
          if(!localStorage.getItem('newUser')){
            localStorage.setItem('newUser', data.email);
            localStorage.setItem('futureRole', 'ROLE_JOB_SEEKER');
          }

          this.router.navigate(['/m/user/verification']);
        }
      })
      .then(() => {
            // Using the loaded user data
            let claims = this.oauthService.getIdentityClaims();
            this.loader = false;
            if (claims) {
              if(claims['user_info'].roles[0].name === 'ROLE_JOB_SEEKER'){
                localStorage.setItem('role',claims['user_info'].roles[0].name);
                localStorage.setItem("fname",claims['user_info'].first_name);
                localStorage.setItem("lname",claims['user_info'].last_name);
                localStorage.setItem("username",claims['user_info'].user_name);
                localStorage.setItem("userId",claims['user_info'].id);
                localStorage.setItem("email",claims['user_info'].email);
                if(claims['user_info'].profile_picture){
                  localStorage.setItem("pic",claims['user_info'].profile_picture);
                }
                else {
                  localStorage.setItem("pic","none");
                }
                //this.generalService.notify('profilePictureChanged' , 'membership','navbar' , {'profile_picture':claims['profile_picture']});
                
                this.username = claims['user_info'].user_name;
                this.generalService.notify(
                  'roleChanged' , 'membership','contact' , 
                  {'role':claims['user_info'].roles[0].name , 'email':claims['user_info'].email}
                );
              // this.socketSerivce.storeUser(this.username);
            //   this.router.navigate(['user/resumes']);
                if (this.fromPage  === 'authPages') {
                  this.router.navigate(['u/', this.username, 'resumes']);
                } 
                else if(this.fromPage  === 'advr-interface'){
                  this.generalService.notify('loginSuccess' , 'login' , 'advr-interface' , {}) ;
                }
                else if(this.fromPage  === 'advr-preview'){
                  this.generalService.notify('loginSuccess' , 'login' , 'advr-preview' , {}) ;
                }
                else if(this.fromPage==='company-public-preview' || this.fromPage==='home'){
                  this.generalService.notify('loginSuccess' , 'login' , '' , {}) ;
                }
              //  this.router.navigate(['user/resume-wrapper/', this.resumeId, 'personal-preview'])
              } 

              // account is employer and trying to login from job seeker login page 
              else{
                this.authService.logout(false);
                this.resetMessage =  Errors.loginWithWrongRule
                this.helpLink = Errors.employerLoginLink;
              }
            }
      });

      // this.oauthService.fetchTokenUsingPasswordFlowAndLoadUserProfile(data.email,data.password).then((res) => {
      //   console.log("in response",res);
      //   let claims = this.oauthService.getIdentityClaims();
      //       if (claims) {
      //         console.log("done");
      //         localStorage.setItem('role',claims['user_info'].roles[0].name);
      //         if(localStorage.getItem("role")=== 'ROLE_EMPLOYER'){
      //           this.resetMessage = this.loginWithWrongRule;
      //         }
      //         this.router.navigate(['user/resumes']);
      //       }
      // }
      // ,(err) => {
      //   console.log(err);
      //   if (err['error']['error'] == 'Unauthorized'){
      //     this.resetMessage = 'Invalid Email or Password';
      //   }
      // }
      // )
      //.catch(error => console.log("hello",error));
    }
    // if (this.loginForm.valid) {
    //   this.authService.login(data).subscribe(data => {
    //     localStorage.setItem('token',data['access_token']);
    //     this.router.navigate(['user/resumes']);
    //   }, err => {
    //     console.log(err);
    //     if (err['error']['error'] == 'Unauthorized'){
    //       this.resetMessage = 'Invalid Email or Password';
    //     }
    //   });
    // }
  }

  // forgetPassword() {
  //   this.clickForgetPassword = true;
  //   const data = {'email' : this.loginForm.get('email').value};
  //   if (!this.isInvalid('email') && !this.isInvalidSyn('email')) {
  //     localStorage.setItem('oldUser', JSON.stringify(data));
  //     this.authService.forgetPassword(data).subscribe(res => {
  //       if (res['error']) {
  //         this.resetMessage = res['error'];
  //         this.resetHelpLink();

  //         // if user trying to reset password but its account is not verified yet, we navigate the user to verification page
  //         if(res['type']==='UnverifiedEmailRest'){
  //           this.authService.changeError(res['error']);
  //           this.router.navigate([res['help']]);
  //         }
  //       }
  //       if (res['data']) {
  //        // this.resetMessage = res['data'];
  //         this.router.navigate(['/m/user/reset-password-verification']);
  //       }
  //     });
  //   }
  // }

  public facebookLogin() {
    this.loader = true;

    this.authService.logoutIfAlreadyLoggedin();

    let socialPlatformProvider = FacebookLoginProvider.PROVIDER_ID;
    this.socialAuthService.signIn(socialPlatformProvider).then(
      (userData) => {
        // this will return user data from facebook. What you need is a user token which you will send it to the server
        // this.sendToRestApiMethod(userData.token);
        this.authService.loginWithFacebook(userData).switchMap(
          data => {
            if(data['success']){
              localStorage.setItem('access_token',data['token'].access_token);
              return this.authService.getUserInfo();
            }
            else if(data['type']=== 'noEmailFacebookAccount'){
              this.loader = false;
              this.authService.changeError(data['error']);
              this.router.navigate([data['help']]);
              return new EmptyObservable<Response>();
            }
            else{
              this.loader = false;
              if(data['error'])
                this.resetMessage = data['error'];
                this.resetHelpLink();
                if(data['help'])
                  this.helpLink = data['help'];
              // if(data['error']=== "There is no permission with Job Seeker."){
              //   this.resetMessage = Errors.loginWithWrongRule;
              // }
              return new EmptyObservable<Response>();
            }
        })
        .subscribe(data => {
          localStorage.setItem("role",data['user_info'].roles[0].name);
          localStorage.setItem("fname",data['user_info'].first_name);
          localStorage.setItem("lname",data['user_info'].last_name);
          localStorage.setItem("username",data['user_info'].user_name);
          localStorage.setItem("userId",data['user_info'].id);
          localStorage.setItem("email",data['user_info'].email);
          if(data['user_info'].profile_picture){
            localStorage.setItem("pic",data['user_info'].profile_picture);
          }
          else {
            localStorage.setItem("pic","none");
          }
          this.username = data['user_info'].user_name;
          this.generalService.notify(
            'roleChanged' , 'membership','contact' , 
            {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
          );
          this.loader = false;

          if (this.fromPage  === 'authPages') {
            this.router.navigate(['u/', this.username, 'resumes']);
          } 
          else if(this.fromPage  === 'advr-interface'){
            this.generalService.notify('loginSuccess' , 'login' , 'advr-interface' , {}) ;
          }
          else if(this.fromPage  === 'advr-preview'){
            this.generalService.notify('loginSuccess' , 'login' , 'advr-preview' , {}) ;
          }
          else if(this.fromPage==='company-public-preview' || this.fromPage==='home'){
            this.generalService.notify('loginSuccess' , 'login' , '' , {}) ;
          }
        });
      }
    );
  }

  // old sign in with google
  // public signinWithGoogle () {
  //   this.loader = true;

  //   this.authService.logoutIfAlreadyLoggedin();
    
  //   let socialPlatformProvider = GoogleLoginProvider.PROVIDER_ID;
  //   this.socialAuthService.signIn(socialPlatformProvider)
  //     .then((userData) => {
  //       this.authService.loginWithGoogle(userData).switchMap(
  //         data => {
  //           if(data['success']){
  //             localStorage.setItem('access_token',data['token'].access_token);
  //             return this.authService.getUserInfo();
  //           }
  //           else{
  //             this.loader = false;
  //             if(data['error']=== "There is no permission with Job Seeker."){
  //               this.resetMessage = Errors.loginWithWrongRule;
  //             }
  //           return new EmptyObservable<Response>();
  //           }
  //       })
  //         .subscribe(data => {
  //           localStorage.setItem("role",data['user_info'].roles[0].name);
  //           localStorage.setItem("fname",data['user_info'].first_name);
  //           localStorage.setItem("username",data['user_info'].user_name);
  //           localStorage.setItem("userId",data['user_info'].id);
  //           if(data['user_info'].profile_picture){
  //             localStorage.setItem("pic",data['user_info'].profile_picture);
  //           }
  //           else {
  //             localStorage.setItem("pic","none");
  //           }
  //           this.username = data['user_info'].user_name;
  //           this.generalService.notify(
  //             'roleChanged' , 'membership','contact' , 
  //             {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
  //           );
  //           this.loader = false;
  //           this.router.navigate(['u/',this.username,'resumes']);
  //         });
  //       });
  // }

    // new sign in with google
    initializeGoogleLogin(){
      const SCRIPT_PATH = 'https://accounts.google.com/gsi/client';
      const scriptElement = this.scriptService.loadJsScript(this.renderer, SCRIPT_PATH);
      scriptElement.onload = () => {
        // @ts-ignore
        google.accounts.id.initialize({
          client_id: environment.clientId,
          callback: this.handleGoogleLoginCredentialResponse.bind(this),
          auto_select: false,
          cancel_on_tap_outside: true
        });
        // @ts-ignore
        google.accounts.id.renderButton(
        // @ts-ignore
        document.getElementById("googleLoginButtonDiv"),
          {theme: "outline", size: "medium" , text:"signin_with", type:"standard",locale:"en-us"}
        );
      }
      scriptElement.onerror = () => {
        console.log('Could not load the Google API Script!');
      }
    }
  
    async handleGoogleLoginCredentialResponse(response: CredentialResponse) {
      this.ngZone.run( () => {
        this.loader = true;
          let idToken = {
            "idToken":response.credential
          }
          this.authService.loginWithGoogle(idToken).switchMap(
            data => {
              if(data['success']){
                localStorage.setItem('access_token',data['token'].access_token);
                return this.authService.getUserInfo();
              }
              else{
                this.loader = false;
                if(data['error']){
                  this.resetMessage = data['error'];
                  this.resetHelpLink();
                  if(data['help'])
                    this.helpLink = data['help'];
                //  this.resetMessage = Errors.loginWithWrongRule;
                }
              return new EmptyObservable<Response>();
              }
          })
          .subscribe(data => {
            localStorage.setItem("role",data['user_info'].roles[0].name);
            localStorage.setItem("fname",data['user_info'].first_name);
            localStorage.setItem("lname",data['user_info'].last_name);
            localStorage.setItem("username",data['user_info'].user_name);
            localStorage.setItem("userId",data['user_info'].id);
            localStorage.setItem("email",data['user_info'].email);
            if(data['user_info'].profile_picture){
              localStorage.setItem("pic",data['user_info'].profile_picture);
            }
            else {
              localStorage.setItem("pic","none");
            }
            this.username = data['user_info'].user_name;
            this.generalService.notify(
              'roleChanged' , 'membership','contact' , 
              {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
            );
            this.loader = false;

            if (this.fromPage  === 'authPages') {
              this.router.navigate(['u/', this.username, 'resumes']);
            } 
            else if(this.fromPage  === 'advr-interface'){
              this.generalService.notify('loginSuccess' , 'login' , 'advr-interface' , {}) ;
            }
            else if(this.fromPage  === 'advr-preview'){
              this.generalService.notify('loginSuccess' , 'login' , 'advr-preview' , {}) ;
            }
            else if(this.fromPage==='company-public-preview' || this.fromPage==='home'){
              this.generalService.notify('loginSuccess' , 'login' , '' , {}) ;
            }
          });
      });
    }

  //temp functions , trying to use refresh token
  // public access_token_expiration() {
  //   console.log(this.oauthService.getAccessTokenExpiration()) ;
  // }

  // public refresh(){
  //   console.log("in refresh");
  //   this.oauthService.events.subscribe (e => {
  //     console.log ('oauth / oidc event', e);
  //     if (e.type == 'token_expires') this.oauthService.refreshToken (). then (value => console.log (value));
  //   });
  // }

  // public signout2 () {
  //   this.authService.logout();
  // }

  toggleShowPassword() {
    this.show = !this.show;
    if (this.show) {
        this.type = 'text';
    } else {
        this.type = 'password';
    }
  }

  public signout () {
    this.socialAuthService.signOut();
  }

  hideModalandNavigateToLink(page,link?) {
    if(this.fromPage  === 'advr-interface' || this.fromPage  === 'advr-preview' || this.fromPage === 'company-public-preview' || this.fromPage==='home'){
      $('#authModal').modal('hide');
    }
    
    if(page === 'uSignup')
      this.router.navigate(['/m/user/sign-up']);
    else if(page === 'cLogin')
      this.router.navigate([link]);
    else if(page === 'uForgetPassword')
      this.router.navigate(['/m/user/forget-password']);
  }

  resetHelpLink(){
    this.helpLink = '';
  }

  // sendToRestApiMethod(token: string) : void {
  // this.http.post('url to facebook login here', { token: token }
  // .subscribe(onSuccess => {
  // // login was successful
  // // save the token that you got from your REST API in your preferred location i.e.
  // // as a Cookie or LocalStorage as you do with normal login
  // }, onFail => {
  // // login was unsuccessful
  // // show an error message
  // });
  // }

  // sendToRestApiMethod(token: string) : void {
  // this.http.post("url to google login in your rest api",
  // {
  // token: token
  // }).subscribe(
  // onSuccess => {
  // // login was successful
  // // save the token that you got from your REST API in your preferred location
  // // i.e. as a Cookie or LocalStorage as you do with normal login
  // }, onFail => {
  // // login was unsuccessful
  // // show an error message
  // }
  // );
  // }

}
