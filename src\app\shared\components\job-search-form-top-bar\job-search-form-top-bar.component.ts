import { Component, OnInit, ViewChild, ElementRef, Input } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { AdvrsViewService } from '../../../general/services/advrs-view.service';
import { Observable } from 'rxjs';
import { Router, NavigationExtras, ActivatedRoute, Params } from '@angular/router';
import { GeneralService } from '../../../general/services/general.service';
import { SearchJobContactsService } from '../../../general/services/search-job-contacts.service';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";

@Component({
  selector: 'job-search-form-top-bar',
  templateUrl: './job-search-form-top-bar.component.html',
  styleUrls: ['./job-search-form-top-bar.component.css']
})
export class JobSearchFormTopBarComponent implements OnInit {
  translated_language_id: any;
  country: any;
  jobSearchForm: FormGroup;
  jobTitles:any;
  countryOpts = [];
  choosen_coutry: any;
  //coordinates = {'lat':'', 'lng':''}
  @ViewChild('googlelocationplace') public googlelocationplaceRef: ElementRef;
  @Input("inAdvrsInterface") inAdvrsInterface : boolean;
  @Input('inHomePage') inHomePage: boolean;
  
  constructor(private advrsView: AdvrsViewService,
              private fb: FormBuilder,
              private router: Router,
              private route: ActivatedRoute,
              private generalService: GeneralService,
              private searchJobContactsService:SearchJobContactsService,
              private lazyloadDropdownService:LazyloadDropdownService
              ) {
                this.jobSearchForm = this.fb.group({
                //  job_title: [''],
                 // country: ['']
                 job_title: [null],
                 country: [{ "id": "", "name": "" }]
                });

                //get default language
                if (localStorage.getItem('defaultLang')) {
                  this.translated_language_id = +localStorage.getItem('defaultLang');
                } else {
                  this.translated_language_id = 1;
                }
               }

  ngOnInit(): void {
    this.searchJobContactsService.sharedMessage.subscribe( (data) => {
      if( data['message'] === 'jobTitleRemoved' && data['mData'].jobTitleRemoved === true) {
        this._jobSearchForm.controls['job_title'].setValue(null);
      }
      if( data['message'] === 'countryRemoved' && data['mData'].countryRemoved === true) {
        this._jobSearchForm.controls['country'].setValue("");
        this.choosen_coutry = null;
      }
      if( data['message'] === 'countryChanged' && data['mData'].countryChanged === true) {
        let choosedCountry = this.countryOpts.filter(country => country.name.toLowerCase() === data['mData'].countryName.toLowerCase());
        this._jobSearchForm.controls['country'].setValue(choosedCountry[0]);
      }
    });

    this.generalService.internalMessage.subscribe((data) => {
      if( data['message'] === 'countryChanged' && data['mData'].countryChanged === true) {
        let choosedCountry = this.countryOpts.filter(country => country.name.toLowerCase() === data['mData'].countryName.toLowerCase());
        this._jobSearchForm.controls['country'].setValue(choosedCountry[0]);
      }
    });
    
    this.searchJobTitle(event , true);
  }


  searchJobTitle(event = null , onlCountry = true) {
   let query = (event) ? event.query : '';
  // this.filteredJobTitle = [];
   this.jobTitles = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_titles',10,this.translated_language_id);
     
       this.advrsView.getjobTitles(this.translated_language_id , onlCountry , query).subscribe(
      (res) => {
      //  this.jobTitles = res['job_titles'];
        this.countryOpts = res['countries'];
        this.countryOpts.unshift({'id' : 0 , 'name' : 'All Countries','code':'all'});
        if(localStorage.getItem('country')){
          let choosedCountry = this.countryOpts.filter(country => country.name.toLowerCase() === localStorage.getItem('country').toLowerCase());
          this._jobSearchForm.controls['country'].setValue(choosedCountry[0]);
        }

        // to set jobtitle and country form values when going from any component to advr interface
        if(this.inAdvrsInterface) {
          let searchtype = this.route.snapshot.params['search-type'];
          let searchquery = this.route.snapshot.params['search-query'];
          let searchQueryArray = [] ;
          let jobTitle = '' , country = '' ;
            if (searchtype === 'p') {
              if (searchquery !== undefined) {
                if (searchquery.includes('-jobs-')) {
                  searchQueryArray = searchquery.split('-jobs-');
                  country = searchQueryArray[1];
                  if(country !==undefined)
                    country = this.replaceDashWithSpace(country);
                  else
                    country = '';
                    
                } else {
                  searchQueryArray.push(searchquery);
                }
                jobTitle = searchQueryArray[0];
                jobTitle = this.replaceDashWithSpace(jobTitle); 
              }
              
              if(jobTitle !== ''){
                let jobTitleObject; 
                //get the original job title name after it is modified in url
                this.jobTitles.getOriginalValue(jobTitle).subscribe(res =>{
                  jobTitleObject = res['data'];
                 
                  if(jobTitleObject.length === 0){
                    this._jobSearchForm.controls['job_title'].setValue({'name':jobTitle,'id':-1});
                  }
                  else {
                    this._jobSearchForm.controls['job_title'].setValue(jobTitleObject[0]);
                  }
                }); 
              }

              if(country !== ''){
                let countryObject = this.countryOpts.filter(Country =>  this.processText(Country.name).match( this.processText(country)) );
                if(countryObject.length !== 0)
                  this._jobSearchForm.controls['country'].setValue({ "id": +countryObject[0].id, "name": countryObject[0].name, "code": countryObject[0].code });
              }
            }
          }

    });
 
  }

  friendlyUrl(text){
    text = text.toLowerCase();
    text = text.replace(/[^0-9a-zء-ي\s-]+/g, '');
    text = text.replace(/[\s]+/g, "-");
    return text;
  }

  replaceDashWithSpace(text){
    //replaceDashWithSpace
    text = text.replace(/-/g, ' ');
    //replace multiple spaces with single space
    text = text.replace(/\s\s+/g, ' ');
    return text;
  }

  replaceDashWithEmptyString(text){
    text = text.replace(/-/g, '');
    return text;
  }

  processText(text){
    let processedValue:string;
    processedValue = this.friendlyUrl(text);
    processedValue = this.replaceDashWithEmptyString(processedValue);
    return processedValue;
  }

//  // replace some special characters with -
//   replaceSpecialCharacters(text){
//     text = text.replace(/[&\/\\#,+()$~%.'":*?<>{} ]/g, '-');
//     //replace multiple dashes with one dash
//     text = text.replace(/--+/g, '-');
//     return text;
//   }

changeCountry($event) {
  this.choosen_coutry = $event.value.name;
 }

 get _jobSearchForm() {
  return (this.jobSearchForm as FormGroup);
 }

 searchJob(form: any) {
  form.submitted = false;
  let jtn_check;
  if(this.jobSearchForm.value.job_title && this.jobSearchForm.value.job_title.name) {
    jtn_check =  this.jobSearchForm.value.job_title.name;
    jtn_check = this.friendlyUrl(jtn_check);
  } else {
    jtn_check =  '';
  }

  if (this.jobSearchForm.valid) {
    if(this._jobSearchForm.controls['country'].value){
      this.choosen_coutry = this._jobSearchForm.controls['country'].value.name;
    }
    else{
      this._jobSearchForm.controls['country'].setValue({ "id": "", "name": "" });
    }
    let navigationExtras: NavigationExtras = {
      queryParams: {
        //  jtn:jtn_check,
          jtn:this.jobSearchForm.value.job_title? this.jobSearchForm.value.job_title.name : "",
          cnt:this.choosen_coutry,
          cni:this.jobSearchForm.value.country.id,
          cnd:this.jobSearchForm.value.country.code
      }
    };

    let searchExtras = '';
    searchExtras = jtn_check;
    searchExtras = searchExtras.concat('-jobs-');
    //searchExtras = searchExtras.concat((this.choosen_coutry === undefined || this.choosen_coutry === null) ? '' : '-jobs-');
    searchExtras = searchExtras.concat((this.choosen_coutry === undefined || this.choosen_coutry === null) ? '' : this.friendlyUrl(this.choosen_coutry) );
  
    if (!this.inAdvrsInterface) {
      this.router.navigate(['/search-job', 'p', searchExtras]);
    }
    else {
      this.searchJobContactsService.notify('inJobSearch' , 'jobSreach-form-navBar' , 'advr-interface' , navigationExtras) ;
    }
  }
}

}
