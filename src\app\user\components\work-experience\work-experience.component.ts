import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OnDestroy, OnInit, ViewChild, ElementRef, AfterViewInit} from '@angular/core';
import {Subject} from 'rxjs/Subject';
import {AbstractControl, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {WorkExperiencesService} from '../../cv-services/work-experiences.service';
import {ActivatedRoute} from '@angular/router';
import {SideToFormSignalService} from '../../cv-services/side-to-form-signal.service';
import {EducationValidators} from '../education/education.validators';
// import { } from 'googlemaps';
import { ResumeService } from '../../cv-services/resume.service';
import { forkJoin } from 'rxjs/observable/forkJoin';
import { DataMap } from 'shared/Models/data_map';
import { ExperienceField } from '../../Models/ExperienceField';
// import { MessageService } from 'primeng';
import { WorkExperienceValidators } from './work-experience.validators';
import { HelpTipsService } from '../../cv-services/help-tips.service';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
//  //  //  import { google } from '@agm/core/services/google-maps-types';
declare var $: any;

@Component({
  selector: 'app-work-experience',
  templateUrl: './work-experience.component.html',
  styleUrls: ['./work-experience.component.css']
})
export class WorkExperienceComponent implements OnInit , OnDestroy, AfterViewInit {
  locationService = new DataMap();
  workExperienceForm ;
  toYearOpts = [];
  fromYearOpts = [];
  currentYear = (new Date()).getFullYear();
  monthOpts = [
    {'value': '0', 'label': 'MM'},
    {'value': '1', 'label': 'January'},
    {'value': '2', 'label': 'February'},
    {'value': '3', 'label': 'March'},
    {'value': '4', 'label': 'April'},
    {'value': '5', 'label': 'May'},
    {'value': '6', 'label': 'June'},
    {'value': '7', 'label': 'July'},
    {'value': '8', 'label': 'August'},
    {'value': '9', 'label': 'September'},
    {'value': '10', 'label': 'October'},
    {'value': '11', 'label': 'November'},
    {'value': '12', 'label': 'December'}
  ];

//  daysOpts = [{'value':0, 'label' :"DD"}];
  workExperiences=[];
  workExperienceOrder=[];
  workExperienceAfterReorder = [];
  // subscribe: Subscription;
  private ngUnsubscribe: Subject<any> = new Subject();
  marginLeft = '';
  resumeId: number = null;
  userResumeId:'';
  username='';
  workExperienceToBePassedToModal;
  display: boolean = false;
  @ViewChild('googlePlace') googlePlace: any;
  @ViewChild('googlePlace') public googlePlaceRef: ElementRef;
  @ViewChild('googleLocation') googleLocation: any;
  @ViewChild('googleLocation') public googleLocationRef: ElementRef;

  city: string;
  country: string;
  countryCode: string;
  website: string;
  name: string;
  //companyIndustries = [];
  employmentTypes = [];
  empTypes = [];
  companySizeTemp = [];
  companySize = [];
  expFields: ExperienceField[]=[];
  jobTitles = [];
 // filteredJobTitles: any[] = [];
  errorMsg = '';
  displayError: boolean;
 // filterdJobTitlesFromExpField = [];
  helpTips : any;
  checkVerify = true;
  jobTitleDD:any;
  resumeLang:number;
  
  //options for ngx-sortablejs(library for reorder table rows)
  options:any;
  tableLoader: boolean = true;
  constructor(private  fb: FormBuilder,
              private workExperienceService: WorkExperiencesService,
              private route: ActivatedRoute,
              private sideToFormShared: SideToFormSignalService,
              // private messageService: MessageService,
              private resumeService: ResumeService,
              private helpTipService:HelpTipsService,
              private lazyloadDropdownService:LazyloadDropdownService,
  ) {
    this.fromYearOpts.push({'value' : '' , 'label' : 'YYYY'});
    this.toYearOpts.push({'value' : '' , 'label' : 'YYYY'});
    this.toYearOpts.push({'value' : 'Present' , 'label' : 'shared.present'});
    for (let year = this.currentYear; year >= 1918; year--) {
      this.toYearOpts.push({'value': year.toString(), 'label': year});
      this.fromYearOpts.push({'value': year.toString(), 'label': year});
    }
    this.setRoutingParams();
  }


  ngOnInit() {
    this.sideToFormShared.expandFormCss.takeUntil(this.ngUnsubscribe).subscribe(value => {
      if (value === 'collapsed') {
        this.marginLeft = '100px';
      }
      if (value === 'expand') {
        this.marginLeft = '';
      }
    });

   // init dropdown first time till i get resume language
   // because if it didn't initialized here it will throw error in console
    this.jobTitleDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_titles',10,1);

    this.workExperienceForm = this.fb.group({
      resume_id : [this.resumeId ? this.resumeId : '', Validators.required],
    //  company_name: ['', Validators.required],
      company: this.fb.group({
        name: ['', Validators.required],
        city: [''],
        country: [''],
        country_code: [''],
        company_size: [''],
        company_size_id: [''],
        company_website: [''],
        company_description: [''],
        verified_by_google: [1]
      },{validator: WorkExperienceValidators.googleVerifiedCompanyValidator}),
      location: [''],
      from: this.fb.group({
        year: ['', Validators.required],
        month: ['']
      } ),
      to: this.fb.group({
        year: ['', Validators.required],
        month: ['']
      }),
      exp_field_id:['', Validators.required],
      exp_field:[''],
      job_title_s:['', Validators.required],
      // job_title_s:this.fb.group({
      //   id:[-1, Validators.required],
      //   name:['', Validators.required]
      // }),
      isPresent: [false, Validators.required],
      isFromMonthPresent : [''],
      isToMonthPresent : [''],
      description: ['']
    }, {validator : [EducationValidators.compareDatesValidator, WorkExperienceValidators.validLocationValidator]});
    this.fromDateControl.setValidators( EducationValidators.fromMonthValidator) ;
    this.toDateControl.setValidators( EducationValidators.toMonthValidator) ;

    this.helpTipService.nextHelpTip("");
 //   this.setResumeId();
   // this.render();
    this.minimize();
    this.reorderTableRows();
    this.getWorkExperienceData();

  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.companyControl.controls['verified_by_google'].value === 1) {
        this.getPlaceAutocomplete();
      } else {
        this.getLocationPlaceAutocomplete();
      }

      // if ($('#check-verify').prop('checked') === true) {
      //   this.getPlaceAutocomplete();
      // } else {
      //   this.getLocationPlaceAutocomplete();
      // }
    }, 1);
  }

  setRoutingParams(){
    this.route.parent.parent.params.subscribe(res => {
      this.username = res['username'];
    });
    this.route.parent.params.subscribe(res => {
      this.userResumeId = res['resumeId'];
    });
  }

  clearCompanyInfo(){
    this.companyControl.controls['name'].setValue('');
    this.clearLocationData();
    this.workExperienceForm.controls['location'].setValue('');
  }
  changAutoComplete() {
    this.clearCompanyInfo();
    if (this.companyControl.controls['verified_by_google'].value === 1) {
      this.checkVerify = true;
      this.getPlaceAutocomplete();
      google.maps.event.clearInstanceListeners(this.googleLocation.nativeElement);
    } else {
      this.checkVerify = false;
      this.getLocationPlaceAutocomplete();
      google.maps.event.clearInstanceListeners(this.googlePlace.nativeElement);
    }

    // if ($('#check-verify').prop('checked') === true) {
    //   this.checkVerify = true;
    //   this.getPlaceAutocomplete();
    //   google.maps.event.clearInstanceListeners(this.googleLocation.nativeElement);
    // } else {
    //   this.checkVerify = false;
    //   this.getLocationPlaceAutocomplete();
    //   google.maps.event.clearInstanceListeners(this.googlePlace.nativeElement);
    // }
  }
  private getLocationPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googleLocation.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']  // 'name'
      });
    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      this.getLocationAddress(place);
    });
  }
  getLocationAddress(place: object) {
    let newPlace = this.locationService.getLocationDataFromGoogleMap(place);
    this.city = newPlace.city;
    this.country = newPlace.country;
    this.countryCode = newPlace.country_code;
    this.companyControl.controls['country'].setValue(this.country);
    this.companyControl.controls['country_code'].setValue(this.countryCode);
    this.companyControl.controls['city'].setValue(this.city);
  //  this.companyControl.controls['name'].setValue(this.workExperienceForm.controls['company_name'].value);
    this.location.setValue((this.city || '') + ' ' + (this.country || ''));
  }
  private getPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googlePlace.nativeElement,
      {
        types: ['establishment'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry','name','website']
      });
    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      this.getAddress(place);
    });
  }
  getAddress(place: object) {
    let newPlace = this.locationService.getLocationDataFromGoogleMap(place);
    this.city = newPlace.city;
    this.country = newPlace.country;
    this.countryCode = newPlace.country_code;

    if (place['website']) {
      this.website = place['website'];

    } else {
      this.website = '';
    }
    this.name = place['name'];
    this.companyControl.controls['country'].setValue(this.country);
    this.companyControl.controls['country_code'].setValue(this.countryCode);
    this.companyControl.controls['city'].setValue(this.city);
    this.companyControl.controls['company_website'].setValue(this.website);
    this.companyControl.controls['name'].setValue(this.name);
  //  this.workExperienceForm.controls['company_name'].setValue(this.name);
    this.location.setValue((this.city || '') + ' ' + (this.country || ''));
    $('#location').focus();
  }

  clearLocationData() {
    this.companyControl.controls['country'].setValue('');
    this.companyControl.controls['country_code'].setValue('');
    this.companyControl.controls['city'].setValue('');
    this.companyControl.controls['company_website'].setValue('');
  }


  // setFullLocation() {
  //   let country = this.companyControl.controls['country'].value;
  //   let city = this.companyControl.controls['city'].value;
  //   if (country !== '' && city !== ''){
  //     return city + ', ' + country;
  //   } else if (country !== '' ) {
  //     return country;
  //   } else if (city !== '') {
  //     return city;
  //   } else {
  //     return '';
  //   }
  // }


  // render() {
  // }

  submit(form){
    form.submitted = false;
    if (this.workExperienceForm.valid) {
      if (this.fromDateControl.controls['month'].value !== '' && this.fromDateControl.controls['month'].value !== '0' && this.fromDateControl.controls['month'].value !== null)
        this.workExperienceForm.controls['isFromMonthPresent'].setValue(true);
      else this.workExperienceForm.controls['isFromMonthPresent'].setValue(false);

      if (this.toDateControl.controls['month'].value !== '' && this.toDateControl.controls['month'].value !== '0' && this.toDateControl.controls['month'].value !== null)
        this.workExperienceForm.controls['isToMonthPresent'].setValue(true);
      else this.workExperienceForm.controls['isToMonthPresent'].setValue(false);

      if (this.workExperienceForm.controls['isPresent'].value == true)
        this.workExperienceForm.controls['isToMonthPresent'].setValue(false);

      // if ($('#check-verify').prop("checked") == true) {
      //   this.companyControl.controls['verified_by_google'].setValue(true);
      // } else {
      //   this.companyControl.controls['verified_by_google'].setValue(false);
      // }

      // if(this.jobTitleGroup.controls['id'].value !== -1){
      //   this.jobTitleGroup.controls['name'].setValue(this.jobTitleGroup.controls['name'].value.name);
      // }

      this.workExperienceService.addWorkExperience(this.workExperienceForm.value).takeUntil(this.ngUnsubscribe).subscribe(
        (result) => {
          
          for(let i =0; i<this.workExperiences.length; i++){
            this.workExperiences[i].order += 1;
          }
          this.workExperiences.unshift(result['data']);
          this.initWorkExperienceOrder(this.workExperiences);
        }
      );

      let currentverifiedByGoogle = this.companyControl.controls['verified_by_google'].value;
      this.workExperienceForm.reset();
      this.toDateControl.controls['month'].enable();
      this.workExperienceForm.controls['resume_id'].setValue(this.resumeId);
      this.companyControl.controls['verified_by_google'].setValue(currentverifiedByGoogle);
    }
  }

  getWorkExperienceData(){
    this.workExperiences = [];
    this.resumeService.getGlobalResumeId(this.userResumeId).switchMap(res =>{
      this.resumeId  = res['data'].id;
      this.workExperienceForm.controls['resume_id'].setValue(this.resumeId);
      this.resumeLang = res['data'].translated_languages_id;
      this.jobTitleDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_titles',10,this.resumeLang);
      return forkJoin(
        this.workExperienceService.getWorkExperiencesData(this.resumeId),
        this.workExperienceService.getWorkExperiences(this.resumeId),
        this.helpTipService.getSectionHelpTips(this.resumeId,5))
    })
    .subscribe(
      (res) => {
      
        //work experience data response
        this.expFields = res[0]['exp_field'];
        this.expFields.unshift({'experience_field_id': '', 'name': ''});
      //  this.jobTitles = res[0]['job_titles'];
      //  this.companyIndustries = res[0]['company_industries'];
        this.employmentTypes = res[0]['employment_type_parents'];
        
        this.empTypes = res[0]['employment_types'];
        this.companySizeTemp = res[0]['company_size'];
        for (let companySize of this.companySizeTemp) {
          this.companySize.push({'company_size_id': companySize.company_size_translation[0].company_size_id ,
            'name': companySize.company_size_translation[0].name});
        }
        this.companySize.unshift({'company_size_id': '', 'name': ''});

        //work experience response
       
        this.workExperiences = res[1]['work_experiences'].length !==0 ? res[1]['work_experiences'] : [] ;

        this.initWorkExperienceOrder(this.workExperiences);

        //help response
        this.helpTips = res[2];

        this.tableLoader = false;
      });
  }

  changeExperiencefield($event){
    this.workExperienceForm.get('exp_field_id').setValue(this.workExperienceForm.controls['exp_field'].value.id);
  }

  // get jobTitleGroup() {
  //   return (this.workExperienceForm.controls['job_title_s'] as FormGroup);
  // }

  // filterJobTitle(event) {
  //   this.filteredJobTitles = [];
  //   for(let i = 0; i < this.jobTitles.length; i++) {
  //     let jobTitle = this.jobTitles[i];
  //     if(jobTitle){
  //       if(jobTitle['name'].toLowerCase().indexOf(event.query.toLowerCase()) == 0) {
  //         this.filteredJobTitles.push(jobTitle);
  //       }
  //     }
  //   }
  // }

  // selectJobTitle(event){
  //   if(event.id !== null){
  //     this.jobTitleGroup.controls['id'].setValue(event.id);
  //   }
  // }
  // onJobTitleKeyUP(){
  //   this.jobTitleGroup.controls['id'].setValue(-1);
  // }

  initWorkExperienceOrder(workExperiences:any){
    this.workExperienceOrder = [];
    for (let workExperience of workExperiences){
      let x = {'workExperienceId' : workExperience.id, 'orderId' : workExperience.order };
      this.workExperienceOrder.push(x);
    }
  }


  removeWorkExperience(workExperience){

    if(confirm("Are you sure to delete ")) {
      let index  : number = this.workExperiences.indexOf(workExperience);
      if (index !== -1) {
        this.workExperienceService.deleteWorkExperience(workExperience.id).takeUntil(this.ngUnsubscribe).subscribe(() => {

          this.workExperiences.splice(index, 1);
          let order = this.workExperienceAfterReorder.find(x => {return x.workExperienceId === workExperience.id;});
          for (let i=0 ; i<this.workExperiences.length;i++) {
            this.workExperiences[i].order = i+1;
          }
          this.initWorkExperienceOrder(this.workExperiences);
        });
      }
    }
  }

    
  //reorder table rows on drag and drop using ngx-sortablejs library
  reorderTableRows(){
    this.options = {
      onUpdate: (event: any) => {
        let orderedArray = [];
        for(let i=0; i < this.workExperiences.length;i++){
          this.workExperiences[i].order = i+1;
          orderedArray.push({'workExperienceId':this.workExperiences[i].id,'orderId':this.workExperiences[i].order});
        }
        let orderedData ={"orderData":orderedArray};
        this.workExperienceService.orderData(this.resumeId , orderedData).takeUntil(this.ngUnsubscribe).subscribe(console.log);
      }
    };
  }

  displayModal(workExperience){
    this.display = !this.display;
    this.workExperienceToBePassedToModal = workExperience ;
  }

  closeModal() {
    this.display = false;
  }

  handlePopup(event: any) {
    let index = this.workExperiences.indexOf(event['old']);
    this.workExperiences[index] = event['new'];
    this.display = false;
  }

  get descriptionControl() {
    return (this.workExperienceForm.get('description'));
  }

  minimize() {
    // Slide certeficate bottom and top
    $(document).ready(function(){
      $('.minamize-certification').click(function() {
        $( '.add-certification .form-group' ).slideToggle( 'slow' );
        $('.minamize-certification .fa').toggleClass('rotate');
      });

    });
  }
  onToYearSelect(year: any) {
    if (year === 'Present') {
      // this.toDateControl.controls['month'].clearValidators();
      this.toDateControl.controls['month'].disable();
      this.workExperienceForm.controls['isPresent'].setValue(true);

    } else {
      // this.toDateControl.controls['month'].setValidators(Validators.required);
      this.toDateControl.controls['month'].enable();
      this.workExperienceForm.controls['isPresent'].setValue(false);
    }
  }
  get fromDateControl() {
    return (this.workExperienceForm.controls['from'] as FormGroup);
  }
  get companyControl() {
    return (this.workExperienceForm.controls['company'] as FormGroup);
  }

  get location() {
    return this.workExperienceForm.controls['location'];
  }
  get toDateControl() {
    return (this.workExperienceForm.controls['to'] as FormGroup);
  }

  get countryCodeControl() {
    return this.workExperienceForm.controls['company'].controls['country_code'];
  }


  getValueToDD(item: AbstractControl) {
    let x  = item.value;

    if (x === '00' || x === '') {
      return false;
    }
    return true;
  }

  //change object value in autocomplete controls when adding new value to be like
  // objectName = {"id":-1,"name"="name example"}
  checkIfNewValue(control){
    if( control.value !=="" && control.value !== null && control.value.id === undefined && control.value.name !== ""){
      control.setValue({"id":-1,"name":control.value});
    }
  }

  //help tips
  setHelpTip(fieldId){
    if(this.helpTips){
      let helpTipsIndex = this.helpTips.findIndex(x => x.field_id === fieldId);
      this.helpTipService.nextHelpTip(this.helpTips[helpTipsIndex].description);
    }
  }
  clearHelpMessage(){
    this.helpTipService.nextHelpTip("");
  }

  ngOnDestroy(): void {
    //  this.subscribe.unsubscribe();
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}


