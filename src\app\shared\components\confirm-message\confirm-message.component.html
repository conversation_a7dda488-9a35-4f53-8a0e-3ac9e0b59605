<!-- class="sticky-confirm-toast" -->
<p-toast position="bottom-right"  baseZIndex="1" (onClose)="changeMsgClosedState()" *ngIf="!closeState && !emailConfirmed ">
    <ng-template let-message pTemplate="message">
        <span>{{message.detail}}</span> &nbsp;
        <a *ngIf="role ==='ROLE_JOB_SEEKER'" [routerLink]="['/u', username, 'settings','account']" class="warning-msg-link">Confirm</a>
        <a *ngIf="role === 'ROLE_EMPLOYER'" [routerLink]="['/c', username, 'account-settings']" class="warning-msg-link">Confirm</a>
    </ng-template>
</p-toast>