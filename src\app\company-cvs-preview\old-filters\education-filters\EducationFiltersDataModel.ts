import { Options } from 'ng5-slider';
export class  EducationFiltersDataModel {
digreeValue = 60;
degreeLevel = []  ;
major = [];
minor = [];
digreeOptions: Options = {
     floor: 50,
     ceil: 100
   };
///
///
   filters = {};

   setFilters() {
     console.log(this.degreeLevel);
     this.filters = {/// *
           'min_degree' : this.digreeValue,
           'd_level_id'  : this.degreeLevel.map(el => el.id),
            'minor_id'   : this.major['id'],
            'major_id'   : this.minor['id']
     };
}

constructor() {
}
}
