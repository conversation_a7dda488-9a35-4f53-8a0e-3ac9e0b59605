<!-- start of reply form -->
<div  *ngIf="mode==='reply_form' || mode === 'preview_mode'" class="row" style="margin-bottom:-20px" >
  <div class="col-md-10">
    <table class="table " id="msg-details">
      <!-- <caption class="text-center table-caption">Message Details</caption> -->
      <thead>
        <!-- <tr>
          <th class="text-right" colspan="">  ID :</th><td>{{ msg.id }}</td>
        </tr>
        <tr>
          <th class="text-right" colspan="">Main Cat</th>
          <td colspan="3"> {{ msg.main_cat}}</td>
        </tr>
        <tr>
          <th class="text-right" colspan="">Sub Cat</th>
          <td colspan="3"> {{ msg.sub_cat}}</td>
        </tr> -->
        <tr>
          <th class="text-right" colspan="">Email</th>
          <td  colspan="">{{msg.email}}</td>
          <th class="text-right" colspan="" >Full Name:</th>
          <td>{{  msg.name }}</td>
        </tr>
      </thead>
      <tbody>
        <tr rowspan="3" style="min-height:100px;">
          <th class="text-right">Message</th>
          <!-- <td colspan="5"  style="overflow: auto;">{{ msg.message }} </td> -->
          <td colspan="5"  style="overflow: auto;">
            <p  [innerHTML]="msg.message"></p>
          <!-- <span [innerHTML]="msg.message"></span> -->
          </td>
        </tr>

        <tr *ngIf="msg.images.length > 0">
          <th class="text-right">Attachments</th>
          <td colspan="6">
            <div class="row">
              <div class="col-md-2 col-sm-3 col-xs-6" *ngFor="let image of msg.images;" (click)="displayBiggerImageAttachment(image,'user-reply')">
                <img src="{{baseUrl+image.url+image.name}}" class="img-responsive">
              </div>
            </div>
          </td>
        </tr>
        <tr *ngIf="currentAttachment !==null">
            <th class="text-right"></th>
            <td colspan="4" class="text-center">
                <img [src]="currentAttachment" class="img-responsive">
            </td>
        </tr>
      </tbody>
    </table>
    
    

  </div>

  <div class="col-md-2">
    <p-card>
      <table id="summary">
        <tr>
          <!-- <th>Main Cat</th> -->
          <td>{{ msg.main_cat | summary:20}}</td>
        </tr>
        <tr  *ngIf="msg.sub_cat !== '....'">
          <!-- <th>Sub Cat</th> -->
          <td>{{ msg.sub_cat | summary:20}} </td>
        </tr>
        <tr *ngIf="mode !== 'preview_mode' && msg.assign_log.length !== 0">
          <!-- <th>Assigned To</th> -->
          <td title="Assigned To"> {{ msg.assign_log[0].from_assigned_admin || '---' }}</td>
        </tr>
        <tr *ngIf="mode === 'preview_mode'">
          <!-- <th>Handeled By</th> -->
          <td title="Handeled By"> {{ msg.handled_by || '---' }}</td>
        </tr>
        <tr>
          <!-- <th></th> -->
          <td><span id="messageId" (click)="copyToClipboard()" title="Copy to clipboard">{{ msg.id +'-'+ msg.year }}</span> / <span [class]="'message-badge status-' + msg.status">{{ msg.status }}</span></td>
        </tr>
      </table>
    </p-card>
    <!-- <button class="btn btn-success btn-block" (click) = "onCopy(msg.id +'-'+ msg.year)" >{{ msg.id +'-'+ msg.year }}</button> -->
    
  </div>
</div>



  <div *ngIf="mode==='reply_form' && msg.status !== 'done' && msg.status !== 'replied'">
    <hr>
    <form [formGroup]="msgForm" >

        <table  class="table ">
          <!-- <caption class="text-center table-caption">Reply Section</caption> -->
          <tbody>
            <tr>
              <th>
                <span *ngIf="!displayMore">
                    CC<i title="add more recipients"  class="fa fa-plus" (click)="displayMore = true" style="font-size: 12px;"></i>
                </span>
                <span *ngIf="displayMore">
                    CC<i title="collapse" class="fa fa-minus" (click)="displayMore = false" style="font-size: 12px;"></i>
                </span>
                 &nbsp;
                To
              </th>
              <td colspan="2" class="border-right">
                <input type="email" value="{{msg.email}}" name="to" />
              </td>
            </tr>
            <tr *ngIf="displayMore">
              <th Title="separate between multipule email addresses with enter">Cc <i class="fa fa-info-circle" title="separate between multipule email addresses with enter"></i></th>
              <td colspan="2" class="border-right">
              <input #ccInput type="email" formControlName="cc_temp" (keyup.enter) ="addEmailToCc(ccInput.value)" placeholder="<EMAIL>" />
                <ng-container *ngIf="emails.length > 0">
                  <ng-container *ngFor="let email of emails">
                    <span class="emails">{{ email }} <i class="fa fa-remove" (click)="removeEmail(email)"></i></span>
                  </ng-container>
                </ng-container>
                <!-- <span *ngIf="cc_temp.errors.email" class="alert alert-danger inline-alert">invalid email</span> -->
              <!-- <span class="alert alert-danger inline-alert" *ngIf="cc_temp.touched && cc_temp.invalid">required</span> -->
              </td>
            </tr>
            <tr>
              <th>
                Choose Template
              </th>
              <td colspan="2" class="border-right">
                <p-dropdown [options]="template_titles" [editable]="true" formControlName="template_title" (onChange)="fillFormWithTemp()" [filter]="true" >
                  <ng-template let-title pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{title.label}}</div>
                      </div>
                  </ng-template>
                </p-dropdown>
              </td>
            </tr>
            <!-- <tr>
              <th>Template Title</th>
              <td colspan="2" class="border-right">
                <input type="text" name="email-title" formControlName="replied_email_title" />
                <span class="alert alert-danger inline-alert" *ngIf="replied_email_title.touched && replied_email_title.invalid">required</span>

              </td>
            </tr> -->
            <!-- <tr>
              <th>Notification Reply</th>
              <td colspan="2" class="border-right">
                <input type="text" formControlName="short_reply" />
                <span class="alert alert-danger inline-alert" *ngIf="short_reply.touched && short_reply.invalid">required</span>
              </td>
            </tr> -->
            <tr>
              <th>Email Reply</th>
              <td colspan="2" class="border-right">
                <p-editor formControlName="detailed_reply" [style]="{'height':'200px'}">
                  <p-header>
                    <span class="ql-formats">
                      <button class="ql-bold" aria-label="Bold"></button>
                      <button class="ql-italic" aria-label="Italic"></button>
                      <button class="ql-underline" aria-label="Underline"></button>
                      <button class="ql-order" aria-label="Underline"></button>
                      <button aria-label="Ordered List" class="ql-list" value="ordered" type="button"></button>
                      <button aria-label="Bullet List" class="ql-list" value="bullet" type="button"></button>
                    </span>
                  </p-header>
                </p-editor>
                <span class="alert alert-danger inline-alert" *ngIf="detailed_reply.touched && detailed_reply.invalid">required</span>
              </td>
            </tr>
            
            <tr>
              <th>Attach Images</th>
              <td colspan="2">
                <div class="container-fluid">
                  <div class="row custom-row" formArrayName="attach_images" *ngFor="let image of attachImages.controls ; let i=index;">
                      <div class="col-sm-8 col-xs-10 col-xxs-12 margin-bo-mo-10 focus-no-padding input-file-container" [formGroupName]="i">
                          <input id="persPhoto" type="file" accept="image/jpeg, image/png" (change)="onFileChanged($event);">
                      </div>
                      <div class="col-sm-1 col-xs-2 col-xxs-12 focus-no-padding text-center">
                          <button type="button" (click)="addImage()" *ngIf="i===0" class="btn btn-gray">
                              <i class="fa fa-plus" aria-hidden="true"></i>
                          </button>
                          <button type="button" (click)="removeImage(i)" *ngIf="i!=0" class="btn btn-delete btn-delete-big">
                              <i class="fa fa-trash" aria-hidden="true"></i>
                          </button>
                      </div>  
                  </div>
                  <div class="row custom-row">
                      <div class="col-xs-12">
                          <div class="imgError-div col-xs-12">
                              <span *ngIf="imgError" class="error-message" translate>{{imgError}}</span>
                          </div> 
                      </div>
                  </div>
                </div>
              </td>
            </tr>

            <!-- <tr>
              <td colspan="2"></td>
              <td colspan="">
                <div *ngIf="viewNotific === 2" style="color:green;">{{ notification }}</div>
                <button type="submit"  class="btn btn-done submit-button"  [disabled]="msgForm.invalid" (click)="send(msgForm.value)" ><i class="fa fa-paper-plane"></i>Send</button>
                 <i class="fa fa-save" title="Save As Template" (click)="saveAsTemplate(msgForm.value)"></i>
              </td>
              <td colspan=""></td>
            </tr> -->
          </tbody>
        </table>

        <div class="row">
          <div class="col-md-12">
            <div *ngIf="viewNotific === 2"  style="color:green; font-size:18px; margin-left:200px;">{{ notification }}</div>
          </div>
          <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
            <button type="submit"  class="btn btn-done submit-button"  [disabled]="msgForm.invalid" (click)="send(msgForm.value)" ><i class="fa fa-paper-plane"></i>Send</button>
            <!-- <i class="fa fa-save" title="Save As Template" (click)="saveAsTemplate(msgForm.value)"></i> -->
          </div>
        </div>

          <!-- <p>{{ msgForm.value | json }}</p> -->


    </form>
  </div>
  <table  *ngIf="mode==='reply_form' || mode === 'preview_mode' || mode === 'preview_comments' || mode === 'preview_replys'" class="table">
    <tbody>
      <tr *ngIf="mode !== 'preview_comments'">
        <td colspan="5">
          <p-panel header="Replies" [collapsed]="collapsed" [toggleable]="true" [style]="{'background-color:':'white'}">
            <div *ngIf="msg.replies.length !== 0">
                <div *ngFor="let reply of msg.replies; let i = index">
                    <!-- <b>Reply{{i + 1}}:</b> -->
                    <p class="admin"><i>{{reply.admin_name }}:</i></p>
                    <p class="description"  [innerHTML]="sanitizer.bypassSecurityTrustHtml(reply.detailed_reply)"></p>
 
                    <div class="row"*ngIf="reply.reply_images.length > 0">
                      <div class="col-sm-4 col-xs-6" *ngFor="let image of reply.reply_images;" (click)="displayBiggerImageAttachment(image,'admin-reply')">
                        <img src="{{baseUrl+image.url+image.name}}" class="img-responsive">
                      </div>
                    </div>
                    <div *ngIf="currentAdminAttachment !==null" class="text-center">
                        <img [src]="currentAdminAttachment" class="img-responsive">
                    </div>
                    
                     <!-- <p> {{reply.short_reply}}</p> -->
                    <sub>{{ reply.created_at}}</sub>
                </div>
            </div>
            <div *ngIf="msg.replies.length === 0">
                <p>No Replies</p>
            </div>
          </p-panel>
        </td>
      </tr>
      <tr  *ngIf="mode !== 'preview_replys'">
        <td colspan="5">
          <p-panel   [collapsed]="collapsed" [toggleable]="true" [style]="{'margin-bottom':'20px'}">
            <p-header>
              <div class="ui-helper-clearfix" style="height:0px;">
                  <span class="ui-panel-title">Comments</span>
                  <!-- <a [routerLink]="['/admin/messages']" fragment="add-new-comment" > -->
                    <button *ngIf="mode ==='reply_form' || mode ==='preview_comments'" class="btn btn-light btn-comment" title="add new comment" [disabled]="doneBtnPressed || msg.status === 'done'" (click)="openCommentForm()"><i class="fa fa-comment"></i></button>
                  <!-- </a> -->
              </div>
            </p-header>
            <div *ngIf="msg.comments.length !== 0">
                <div *ngFor="let comment of msg.comments;let i = index">
                  <b>Comment{{i + 1}}:</b>
                  <p class="admin"><i>{{ comment.admin_name }}:</i></p>
                  <p [innerHTML]="sanitizer.bypassSecurityTrustHtml(comment.comment)"></p>
                  <sub>{{ comment.created_at}}</sub>
                </div>
            </div>
            <div *ngIf="msg.comments.length === 0">
              <p> No comments</p>
            </div>
          </p-panel>
        </td>
      </tr>
      <tr  *ngIf="displayForm && opperationNum === 1">
        <Th class="text-right">
          <!-- Add Comment<span *ngIf="comment.touched && comment.invalid" class="required">**</span> -->
        </Th>
        <td colspan="4">
          <div id="add-new-comment" class="row" style="margin-top: -15px;">

           <form  [formGroup]="commentForm" *ngIf="opperationNum === 1">
             <div class="col-md-9">
               <input formControlName="comment" name="comment" type="text" id="comment" style="margin-top: 0px;padding: 5px;width:600px" >
               <div *ngIf="comment.touched && comment.invalid" >
                 <div *ngIf="comment.errors.required" class="alert alert-danger inline-alert" >Name is Required</div>
               </div>
             </div>
             <div class="col-md-3">
               <button type="submit" class="btn btn-success" [disabled]="commentForm.invalid"
                  (click)="addNewComment()" style="margin-top: 0px;margin-bottom: 0px;margin-left: 10px;" >Add </button>
               <button class="btn btn-light"  (click)="closeFormCA()" style="margin-top: -50px; margin-left: 70px;" >Close </button>
             </div>
             <!-- <p>{{commentForm.value | json }}</p> -->
           </form>
         <!-- end of comment form -->
          </div>
         </td>
      </tr>
      <tr *ngIf="mode ==='reply_form'">
        <th colspan="2"></th>
        <td colspan="2">
            <!-- <button class="btn btn-light btn-comment"  [disabled]="doneBtnPressed || msg.status === 'done'" (click)="openCommentForm()"><i class="fa fa-comment"></i>Commment</button> -->
            <button *ngIf="!doneBtnPressed && msg.status !== 'done'" class="btn btn-light btn-done"    (click)="setAsDone()"      ><i class="fa fa-check"></i>Mark As Done</button>
            <Span *ngIf="doneBtnPressed || msg.status === 'done'" class="" style="color:green; font-weight: bold;"><i class="fa fa-check"></i>Marked As Done</Span>
            <button *ngIf="doneBtnPressed  || msg.status === 'done'" class="btn btn-light btn-not-done"    (click)="setAsNotDone()" ><i class="fa fa-times"></i>Mark As Not Done</button>
            <button *ngIf="!doneBtnPressed && msg.status !== 'done'" class="btn btn-light btn-assign"   [disabled]="doneBtnPressed || msg.status === 'done'"  (click)="openAssignForm()"  title="Assign to another admin"><i class="fa fa-user-plus"></i></button>
        </td>
        <td colspan="2" >
          <div class="row" style="margin-top:0px;" *ngIf="displayForm && opperationNum === 2" >
            <form  [formGroup]="assignForm" *ngIf="opperationNum === 2">
              <div class="col-md-9" style="margin-top: 10px;margin-left: -18px;">
                <p-dropdown [options]="admins" formControlName="to_assigned_admin_user_id"  [required]="true" [filter]="true" >
                  <ng-template let-admin pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{admin.label}}</div>
                      </div>
                  </ng-template>
                </p-dropdown>
                <div *ngIf="admin_user_id.touched && admin_user_id.invalid" >
                  <div *ngIf="admin_user_id.errors.required" class="alert alert-danger inline-alert" >Name is Required</div>
                </div>
              </div>
              <div class="col-md-3" style="margin-top: 15px;">
                <button type="submit" class="btn btn-success" [disabled]="assignForm.invalid"
                   (click)="assignTo()"  style="margin-left:-15px;margin-top: 0px;margin-bottom: 0px; color:white;" title="assign"><i class="fa fa-plus" style="color:white;"></i></button>
                <button class="btn btn-light" (click)="closeFormCA()" style="margin-top: -50px; margin-left: 40px;"  title="Close"><i class="fa fa-times"></i></button>
              </div>
              <!-- <p>{{ assignForm.value | json }}</p> -->
             </form>
           </div>
        </td>
      </tr>
       <!-- <tr *ngIf="displayForm && opperationNum === 2">
         <th class="text-right">Assign To<span *ngIf="admin_user_id.touched && admin_user_id.invalid" class="required">**</span></th>
         <td colspan="3">
           <div class="row" style="margin-top:0px;">
            <form  [formGroup]="assignForm" *ngIf="opperationNum === 2">
              <div class="col-md-9">
                <p-dropdown [options]="admins" formControlName="to_assigned_admin_user_id"  [required]="true" [filter]="true" >
                  <ng-template let-admin pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{admin.label}}</div>
                      </div>
                  </ng-template>
                </p-dropdown>
                <div *ngIf="admin_user_id.touched && admin_user_id.invalid" >
                  <div *ngIf="admin_user_id.errors.required" class="alert alert-danger inline-alert" >Name is Required</div>
                </div>
              </div>
              <div class="col-md-3">
                <button type="submit" class="btn btn-success" [disabled]="assignForm.invalid"
                   (click)="assignTo()"  style="margin-top: 0px;margin-bottom: 0px;" >Add </button>
                <button class="btn btn-light" (click)="closeFormCA()" style="margin-top: -50px; margin-left: 70px;" >Close </button>
              </div>
              <p>{{ assignForm.value | json }}</p>
             </form>
           </div>
         </td>
       </tr> -->
       <tr *ngIf="viewNotific === 1">
         <th></th>
         <td colspan="5"  style="color:green; font-size:18px;">{{ notification }}</td>
       </tr>
    </tbody>
  </table>

<!-- end of reply form -->


<!-- start of  template form-->
<div *ngIf="mode ==='template_form'">
  <form [formGroup]="msgForm" >

    <table  class="table">
      <!-- <caption *ngIf="templateMode === 'create'" class="text-center table-caption">Add New Template</caption>
      <caption  *ngIf="templateMode === 'update'"  class="text-center table-caption">Edit Template</caption> -->
      <tbody>
        <tr>
          <th>Template Title</th>
          <td colspan="2" class="border-right">
            <input type="text" formControlName="template_title" />
            <span class="alert alert-danger inline-alert" *ngIf="template_title.touched && template_title.invalid">required</span>
          </td>
        </tr>
        <!-- <tr>
          <th>Email Title</th>
          <td colspan="2" class="border-right">
            <input type="text" name="email-title" formControlName="email_title" />
            <span class="alert alert-danger inline-alert" *ngIf="email_title.touched && email_title.invalid">required</span>
          </td>
        </tr> -->
        <tr>
          <th>Main Category</th>
          <td colspan="2" class="border-right">
            <p-dropdown [options]="main_cats" formControlName="contact_main_cat_id" (onChange)="filter()" [required]="true" [filter]="true">
              <ng-template let-category pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <span class="alert alert-danger inline-alert" *ngIf="contact_main_cat_id.touched && contact_main_cat_id.invalid">required</span>

          </td>
        </tr>
        <tr *ngIf="filteredSubCats.length > 1">
          <th>Sub Category</th>
          <td colspan="2" class="border-right">
            <p-dropdown [options]="filteredSubCats" formControlName="contact_sub_cat_id" [required]="filteredSubCats.length > 1"  [filter]="true">
              <ng-template let-category pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <!-- <span class="alert alert-danger inline-alert" *ngIf="contact_sub_cat_id.touched && contact_sub_cat_id.invalid">required</span> -->
          </td>
        </tr>
        <!-- <tr>
          <th>Short Reply</th>
          <td colspan="2" class="border-right">
            <input type="text" formControlName="short_reply" />
            <span class="alert alert-danger inline-alert" *ngIf="short_reply.touched && short_reply.invalid">required</span>
          </td>
        </tr> -->
        <tr>
          <th>Detailed Reply</th>
          <td colspan="2" class="border-right">
            <p-editor formControlName="detailed_reply" [style]="{'height':'200px'}">
              <p-header>
                <span class="ql-formats">
                  <button class="ql-bold" aria-label="Bold"></button>
                  <button class="ql-italic" aria-label="Italic"></button>
                  <button class="ql-underline" aria-label="Underline"></button>
                  <button class="ql-order" aria-label="Underline"></button>
                  <button aria-label="Ordered List" class="ql-list" value="ordered" type="button"></button>
                  <button aria-label="Bullet List" class="ql-list" value="bullet" type="button"></button>
                </span>
              </p-header>
            </p-editor>
            <span class="alert alert-danger inline-alert" *ngIf="detailed_reply.touched && detailed_reply.invalid">required</span>
          </td>
        </tr>

      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific === 3"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit" *ngIf="templateMode === 'create'"  class="btn btn-done submit-button" [disabled]="msgForm.invalid" (click)="addNewTemplate(msgForm.value)"><i class="fa fa-plus" style="color:white"></i>Add To Templates</button>
        <button type="submit" *ngIf="templateMode === 'update'"  class="btn btn-done submit-button" [disabled]="msgForm.invalid" (click)="addNewTemplate(msgForm.value)"><i class="fa fa-save" style="font-size: 1em;"></i>Save</button>
      </div>
    </div>


      <!-- <p>{{ msgForm.value | json }}</p> -->


  </form>
</div>
<!-- end of  template form-->


<!-- start of msg log -->
<div *ngIf="mode === 'log_mode'">
  <table class="table table-striped table-bordered log-table" id="msg-details">
    <caption class="text-center table-caption">Message Activity Log</caption>
    <thead>
      <tr>
        <th  class="text-center">Action     </th>
        <th  class="text-center">Date /Time </th>
        <th  class="text-center">Action Time</th>
        <th  class="text-center">Handled By </th>
        <th  class="text-center">Details    </th>

      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let action of msgLog; let i = index">
        <td> {{ action.action }}     </td>
        <td> {{action.created_at }}  </td>
        <td> {{ action.action_time }}</td>
        <td> {{ action.admin }}      </td>
        <td> {{ action.details  }}   </td>
      </tr>
      <tr *ngIf="msgLog.length === 0" >
        <td colspan="5" rowspan="3">This messege is New (is not opened Yet ) , the message log is empty  </td>
      </tr>
    </tbody>
  </table>
</div>
<!-- end of msg log -->
