<div class="modal-body">
    <div class="table-preview-container" [ngClass]="{'loading-custom-table': loading === true , 'loaded-custom-table' : loading === false }">
        <div class="loader-container">
            <p-progressSpinner  [style]="{width: '30px', height: '30px'}" strokeWidth="5"></p-progressSpinner>
        </div> 
        <table class="table-preview">
            <thead>
                <tr>
                    <th>Job Adv Title</th>
                    <th>Date of Application</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let job of jobAdvsInfo">
                    <td>
                        <span class="th-mobile">Job Adv Title</span>
                        {{job.folder}}
                    </td>
                    <td>
                        <span class="th-mobile">Date of Application</span>
                        {{job.data_of_application}}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
</div>

       

