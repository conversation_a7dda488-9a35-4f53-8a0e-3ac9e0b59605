<app-pre-loader [show]="loader"></app-pre-loader>
<div class="home-page-content">
  <div class="container">
    <div>
      <form  [formGroup]="verificationForm"
             #form="ngForm"
             class="form-horizontal validate-form"
             (ngSubmit)="verify()">
        <br>
        <div class="row">
          <h1>Reset Password Verification Code</h1>
          <p style="margin-top: 30px;">Please check your email inbox to get the verification code</p>
          <br>
          <div>
            <p>
              <span style="font-weight: bold">Gmail users:</span> 
              CVEEK emails may be viewed in your promotion folder.
            </p>
            <p>
              <span style="font-weight: bold">Yahoo and Hotmail Users:</span> 
              CVEEK emails may be viewed in your junk folder.
            </p>
          </div>
        </div>
        
        <br>

        <div class="row" *ngIf="warningMessage">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            <div class="alert alert-warning alert-dismissible" role="alert" *ngIf="warningMessage">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              {{warningMessage}}
            </div>
          </div>
        </div>

        
        <div class="row" *ngIf="successMessage">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            <div class="alert alert-success alert-dismissible" role="alert" *ngIf="successMessage">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              {{successMessage}}
            </div>
          </div>
        </div>
        
        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && isInvalid('resetToken')) || resetMessage}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':verificationForm.controls['resetToken'].value}">
            <input  type="text" formControlName="resetToken" class="form-control" id="verification-code" (keydown)="resetMessage=null" >
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Enter Verification Code</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('resetToken')">Required</span>
            <span class="error-message " *ngIf="resetMessage">{{resetMessage}}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            <a (click)="sendResetTokenAgain()" style="cursor:pointer">Send Code Again?</a>
          </div>
        </div>
        <div class="text-center">
          <button type="submit" class="btn btn-success">Send</button>
        </div>
      </form>
    </div>
  </div>
</div>
