<page-navbar [navType]="'empty'"></page-navbar>

<div class="article-container">
    <h1>ATS Friendly</h1>

    <div class="section">
        <p><strong>Don&rsquo;t Let Artificial Intelligence Stand in Your Way &mdash; Build a Resume That Both Systems </strong>Understand and Recruiters Love!<br />
        In today&rsquo;s competitive job market, the first decision about your resume isn&rsquo;t made by a human &mdash; it&rsquo;s made by an Applicant Tracking System (ATS) that filters candidates in seconds.<br />
        Your resume could be rejected before it&rsquo;s even seen &mdash; not because you lack skills, but because it wasn&rsquo;t formatted correctly.</p>
        
        <p><strong>That&rsquo;s where <PERSON><PERSON><PERSON> comes in.</strong><br />
        We don&rsquo;t let you get lost in the maze of technical standards. Instead, we give you smart tools that ensure your resume passes through ATS filters with ease and professionalism.</p>
        
        <p>From proper formatting and choosing ATS-safe fonts, to clear headings and automatically embedding the right keywords &mdash; we handle the technical part, so you can shine with your experience.</p>
        
        <p>With <PERSON><PERSON>eek, your resume doesn&rsquo;t get discarded over details &mdash; it gets read, reviewed, and given the opportunity it deserves.</p>
        
        <p>Start today &mdash; and let artificial intelligence work <em>for</em> you, not <em>against</em> you.</p>
        
        <p><strong>Top of Form</strong></p>
        
        <p><strong>Bottom of Form</strong></p>
            
    </div>

    <div class="section">
        <h2>What is ATS (Applicant Tracking System)?</h2>
        
        <p>An ATS is a tool that <strong>automatically scans, ranks, and filters resumes</strong> based on keywords, formatting, and content structure.<br />
        If your resume isn&rsquo;t compatible, <strong>it may be rejected before a recruiter even sees it.</strong></p>
        
        <h3>Why Resumes Get Rejected by ATS:</h3>
        
        <ul>
            <li>Use of <strong>tables, columns, or images</strong></li>
            <li>Fancy or <strong>non-standard fonts</strong> (e.g., Papyrus, Comic Sans, custom Arabic fonts)</li>
            <li>Creative section names instead of standard labels</li>
            <li>Lack of <strong>keywords</strong> from the job description</li>
            <li>PDF files with embedded text/images, not actual text</li>
        </ul>
    </div>

    <div class="section">
        <h2>How to Make Your Resume ATS-Friendly &mdash; Practical Steps</h2>
        
        <ol>
            <li><strong>Use a Simple and Clean Format</strong><br />
            &bull; Avoid columns and tables<br />
            &bull; Do not embed text within images<br />
            &bull; Stay away from very light colors or dark backgrounds</li>
            <li><strong>Use Safe, Readable Fonts</strong><br />
            Recommended fonts: <strong>Arial, Calibri, Times New Roman, Verdana</strong><br />
            Avoid using decorative or uncommon fonts like <strong>&ldquo;Lemonada,&rdquo; &ldquo;Kufi,&rdquo; or &ldquo;Papyrus&rdquo;</strong></li>
            <li><strong>Stick to Standard Section Headings</strong><br />
            Correct: <em>Work Experience, Education, Skills, Certifications</em><br />
            Avoid creative titles like &ldquo;My Career Adventures&rdquo; or &ldquo;My Blessed Education&rdquo;</li>
            <li><strong>Include Keywords from the Job Description</strong><br />
            For example: if the job listing includes &ldquo;JavaScript&rdquo; and &ldquo;Agile,&rdquo; make sure these exact terms are in your resume.</li>
            <li><strong>Use Reverse Chronological Order</strong><br />
            Always start with your <strong>most recent</strong> position and work backward.</li>
            <li><strong>Don&rsquo;t Use Symbols or Emojis Instead of Headings or Bullets</strong><br />
            Avoid icons like (❌, ✔️, 💼)<br />
            Use standard bullets like (&bull;) or simple dashes (-)</li>
            <li><strong>Save Your Resume as a Text-Based PDF (Not an Image)</strong><br />
            &bull; Use <strong>&ldquo;Save as PDF&rdquo;</strong> from a Word processor<br />
            &bull; Avoid exporting from design tools like Photoshop as image-based PDFs</li>
        </ol>
             
        <h3>Real-World Before/After Example:</h3>
        
        <h4><u>Before (ATS-unfriendly):</u></h4>
        
        <p>💼 My Professional Journey&nbsp;</p>
        
        <p>Dev Ninja&nbsp;</p>
        
        <p>TechNow! Inc &mdash; 2021 to Present</p>
        
        <p>⚡ Built awesome stuff using magic frameworks&nbsp;</p>
        
        <p>🎯 Boosted KPIs to the moon 🌕</p>
        
        <p>🎓 Learnings:</p>
            
        <p>B.Sc. in Code Wizardry, Hogwarts</p>
        
        <br>
        
        <h4><u>After (ATS-compliant):</u></h4>
        
        <p>Work Experience&nbsp;</p>
        
        <p>Front-End Developer&nbsp;</p>
        
        <p>TechNow Inc. | May 2021 &ndash; Present&nbsp;</p>
        
        <br>
            
        <p>&bull; Developed web apps using JavaScript and React&nbsp;</p>
        
        <p>&bull; Improved site performance by 35%&nbsp;</p>
        
        <br>
        
        <p>Education&nbsp;</p>
        
        <p>B.Sc. in Computer Science &mdash; ABC University &mdash; 2019</p>
        
        <br>
            
        <p><strong>Don&rsquo;t let formatting ruin your chances.<br />
        With CVeek, your resume gets seen &mdash; and seriously considered.</strong></p>
            
    </div>
    
</div>
