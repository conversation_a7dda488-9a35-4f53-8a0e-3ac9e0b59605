
import { Options } from 'ng5-slider';
export class  PeronsalFiltersDataModel  {

    genderOpts  = [// *2
        // *3
         {'option' : 'male' , 'title' : 'Male', 'value' : false},
         {'option' : 'female' , 'title' : 'Female', 'value' : false},
    ];

 //   *2  vlaue   for every  object  connected to status  of checked  box,
 ///  it's  changed according to checkbox status
 ///  *3
 //// option  (number value  of option of checkbox list), title (label  of option of checkbox list),
 ///  value  (status)
    martialOpts  = [
        {'option' : '1' ,   'title' : 'Single',   'value' : false},
        {'option' : '2' ,  'title' : 'Married',  'value' : false},
        {'option' : '3' ,  'title' : 'Engaged',  'value' : false},
        {'option' : '4', 'title' : 'Seperated', 'value' : false},
        {'option' : '5' , 'title' : 'Divorced' , 'value' : false},
        {'option' : '6' , 'title' : 'Widowerd',  'value' : false},
   ];

   //// gender : ['male' , 'female']
   ///
   /// Age
ageValue = 20;
ageHighValue = 60;
ageOptions: Options = {
     floor: 18,
     ceil: 80
   };
/// nationalities
nationalities: any;
///////
firstname = '';
lastname = '';
cvWithPhoto = '';
///
location: any;
// countries:  any ;
// cities: any;
country = [{'long_name': ''}];
city = [{'long_name': ''}];
///
   filters = {};

   setFilters() {
       //// * gender values  : first we filter genderOpts array, then get its option
       /// ex: 'gender' : ['male'] if   genderOpts array as the following :
       ////  genderOpts  = [
       //  'option' : 'male' , 'title' : 'Male', 'value' : true},
      //  {'option' : 'female' , 'title' : 'Female', 'value' : false},
      //  ];
     this.filters = {/// *
           'gender' : this.genderOpts.filter((el) => el.value  === true).map(el => el.option),
           'm_status' : this.martialOpts.filter((el) => el.value  === true).map(function (el){return parseInt(el.option, 10);}),
           'age' : {'from' : this.ageValue , 'to' : this.ageHighValue},
           'nationalities': {'all_mandatory' : true , 'values' : this.nationalities.map(el => el.id)},
           'first_name' : this.firstname,
           'last_name' : this.lastname,
            'location' :  {
            'country' : this.country[0]['long_name'],
            'city' : this.city[0]['long_name'],
         }
     };
}



constructor() {
   this.nationalities = [] ;
   // this.cities = [] ;
}



}
