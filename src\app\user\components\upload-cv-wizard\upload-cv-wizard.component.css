:host ::ng-deep .ui-steps {
  position: relative;
  margin-bottom: 40px;
}
:host ::ng-deep .ui-steps .ui-steps-item{
  width: 20%;
}
:host ::ng-deep .ui-state-default , :host ::ng-deep  .ui-state-highlight{
  border:none;
}

.step-title{
  color:#3D7BCE;
  margin-left: 20px;
  margin-bottom:30px;
}
.custom-container{
  padding-left:30px;
  padding-right:30px;
}

.custom-col-3{
  width: 22.22%;
  float: left;
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 0;
}
.flex-row{
  display:flex;
  flex-wrap:wrap;
}
.flex-order-xs-1 , .flex-order-xs-2{
  padding:0;
}

/* .persPhotoContainer{
  max-width:180px;
  position:relative;
  margin: auto;
}
.persPhoto{
  margin: auto;
  margin-bottom: 16px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
}
.persPhotoLabel , .delete-photo{
  cursor: pointer;
  color: #337ab7;
}
.persPhotoLabel i , .delete-photo i{
  font-size:21px;
}
.persPhotoLabel i{
  margin-right:20px;
}
.imgError .error-message{
  color: #a94442;
} */
.skillsLangsForm .has-error .error-message {
  left: 0;
  width: 100%;
}
.location-error-message{
  width:300px;
  top:0;
}
.margin-bo-25{
  margin-bottom:25px;
}
.upload-cv-container{
  margin-bottom: 25px;
}

/* Start upload image styles */
.upload-image-container{
  max-width: 180px!important;
  margin: auto;
}
.upload-image-container .inner-container a{
    cursor:pointer;
    text-decoration: none;
}
.upload-image-container .inner-container img{
  margin: auto auto 16px;
  width: 120px;
  border-radius: 50%;
}
.upload-image-container .upload-actions a i{
  font-size: 21px;
  color: #337ab7;
  cursor: pointer;
}
.upload-image-container .upload-actions .edit-image{
  margin-right: 15px;
}
/* End upload image styles */

/* Start responsive styles */
@media screen and (max-width:767px) {
  :host ::ng-deep .ui-steps .ui-steps-item .ui-menuitem-link .ui-steps-title{
    display: none;
  }
  :host ::ng-deep .ui-steps .ui-steps-item .ui-menuitem-link .ui-steps-number{
    top:7px;
  }
  .custom-col-3{
    width: 33.33%;
  }
  margin-bo-mo-10{
    margin-bottom:10px;
  }
  .flex-order-xs-1{
    order:1;
    margin-bottom:25px;
  }
  .flex-order-xs-2{
    order:2;
  }
  .step-title{
    margin-left: -10px;
    font-size: 21px;
  }
  .upload-cv-container .step-title{
    margin-left: 5px;
  }
  .location-error-message {
    width: calc(100% - 15px);
    top: 105%;
  }

  .upload-image-container{
    margin:auto;
  }
}
