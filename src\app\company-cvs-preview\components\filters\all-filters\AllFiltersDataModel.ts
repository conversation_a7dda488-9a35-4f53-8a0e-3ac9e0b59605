import { Options } from 'ng5-slider';
export class  AllFiltersDataModel  {

    genderOpts  = [// *2
        // *3
         {'option' : 'any' , 'title' : 'Any'},
         {'option' : 'male' , 'title' : 'Male'},
         {'option' : 'female' , 'title' : 'Female'},
    ];

 //   *2  vlaue   for every  object  connected to status  of checked  box,
 ///  it's  changed according to checkbox status
 ///  *3
 //// option  (number value  of option of checkbox list), title (label  of option of checkbox list),
 ///  value  (status)


   //// gender : ['male' , 'female']
   ///
   /// Age
ageValue = 20;
ageHighValue = 60;
ageOptions: Options = {
     floor: 18,
     ceil: 80
   };
/// nationalities
nationalities: any;
///////

///

   filters = {};

   setFilters() {
       //// * gender values  : first we filter genderOpts array, then get its option
       /// ex: 'gender' : ['male'] if   genderOpts array as the following :
       ////  genderOpts  = [
       //  'option' : 'male' , 'title' : 'Male', 'value' : true},
      //  {'option' : 'female' , 'title' : 'Female', 'value' : false},
      //  ];
     this.filters = {/// *
        //   'gender' : this.genderOpts.filter((el) => el.value  === true).map(el => el.option),
        //    'm_status' : this.martialOpts.filter((el) => el.value  === true).map(function (el){return parseInt(el.option, 10);}),
        //    'age' : {'from' : this.ageValue , 'to' : this.ageHighValue},
        //    'nationalities': {'all_mandatory' : true , 'values' : this.nationalities.map(el => el.id)},
        //    'first_name' : this.firstname,
        //    'last_name' : this.lastname,
        //     'location' :  {
        //     'country' : this.country[0]['long_name'],
        //     'city' : this.city[0]['long_name'],
       //  }
     };

   //  this.filtersForm.setValue(this.genderOpts.filter((el) => el.value  === true).map(el => el.option));
}



constructor() {
   this.nationalities = [] ;


}



}
