<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">
<div class="container">
  <br>
  <br>
  <div class="row">
    <div class="col-md-8 offset-2">
      <form>
         <input
           type="text"
           class="form-control"
         (keyup.enter)="addTopic(topic)" #topic
         >
         <ul class="list-group">
           <li class="list-group-item"
               *ngFor="let topic of Topics.controls let i = index;"
               (click)="removeTopic(i)"
           >
             {{topic.value}}


           </li>
         </ul>
      </form>
    </div>
  </div>
</div>
