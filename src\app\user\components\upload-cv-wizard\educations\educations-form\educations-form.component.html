<p-toast [style]="{marginTop: '100px'}">
  <ng-template let-message pTemplate="message">
    <span>{{message.detail}}</span> &nbsp;
  </ng-template>
</p-toast>

<app-pre-loader [show]="showLoader"></app-pre-loader>
<div class="custom-container">
  <div *ngIf="!showLoader">
    <ng-container *ngFor="let educationForm of educationControls; let i = index">
      <div class="row flex-row justify-content-center align-items-stretch">
        <div class="col-md-1 d-flex align-items-end justify-content-center pl-3">
        </div>
        <div class="col-md-9 col-sm-12 prev-border add-certification mr-3" style="margin-right: 16px;">
          <div class="education-form-container" [ngClass]="{'minimized': isMinimized[i]}">
            <form #formDir="ngForm" (keydown.enter)="$event.preventDefault()" [formGroup]="educationForm"
              (ngSubmit)="formDir.form.valid && addToBeSubmitted(educationForm)" class="form-horizontal validate-form"
              [appInvalidControlScroll]="'normalComponent'">
              <p class="add-certification-p" translate>education.addCertification</p>

              <!-- Degree Level -->
              <div class="form-group focus-container  has-feedback"
                [ngClass]="{'has-error': (formDir.submitted || shouldShowValidationErrors(i)) && !isDDValid(educationForm.get('degree_level'))}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding"
                  [ngClass]="{'has-val': educationForm.get('degree_level').value}">
                  <p-dropdown [options]="degreeLevelValues" optionLabel="name" formControlName="degree_level"
                    [filter]="true" (onChange)="changeDegreeLevel(i)"
                    [ngClass]="{'has-val': educationForm.get('degree_level').value}">
                    <ng-template let-degree pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                        <div style="font-size:14px;float:left;margin-top:4px">{{degree.label}}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>education.degreeLevel</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && !isDDValid(educationForm.get('degree_level'))"
                    translate>validationMessages.required</span>
                </div>
              </div>

              <!-- Institution -->
              <div formGroupName="institution" class="form-group focus-container has-feedback"
                [ngClass]="{'has-error': (formDir.submitted || shouldShowValidationErrors(i)) && !educationForm.get('institution').get('name').valid}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <ng-select [items]="institutionDD?.items" [virtualScroll]="true" formControlName="name"
                    [loading]="institutionDD?.loading" bindLabel="name" [addTag]="addNewInstitution"
                    addTagText="{{'shared.addNewItem' | translate}}"
                    notFoundText="{{'shared.noItemsFound' | translate}}" [dropdownPosition]="'bottom'"
                    (scrollToEnd)="loadMoreInstitutions()" (search)="institutionDD.search($event)"
                    [searchFn]="customSearchInstitution"
                    (keyup.delete)="deleteInstitutionAutoComplete(educationForm.get('institution').get('name'))"
                    [ngClass]="{'has-val': educationForm.get('institution').get('name').value}"
                    class="form-control ng-select-autocomplete" (change)="selectUniversity($event, i)">
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                      <span class="ng-value-label">{{item.name}}</span>
                      <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                        <i class="fa fa-times" aria-hidden="true"></i>
                      </span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                      {{item.name}}
                    </ng-template>
                  </ng-select>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>education.institution</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && !educationForm.get('institution').get('name').valid"
                    translate>validationMessages.required</span>
                </div>
              </div>

              <!-- Location -->
              <div formGroupName="institution" class="form-group focus-container" id="location" [ngClass]="{'has-val': educationForm.get('institution').get('fullLocation').value,
                            'has-error': (formDir.submitted || shouldShowValidationErrors(i)) && (educationForm.get('institution').get('fullLocation').value == '' ||
                                        educationForm.get('institution').get('city').errors ||
                                        educationForm.get('institution').hasError('InvalidLocationError'))}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <input type="text" class="form-control" id="exampleInputName2" #googlePlace placeholder=""
                    formControlName="fullLocation" (keyup)="LocationClearOnChange($event, i)">
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>education.location</label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && educationForm.get('institution').get('fullLocation').value == ''"
                    translate>validationMessages.required</span>
                  <span class="error-message" *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && educationForm.get('institution').get('city').errors?.required &&
                                                !educationForm.get('institution').get('fullLocation').errors &&
                                                !educationForm.get('institution').hasError('InvalidLocationError')"
                    translate>validationMessages.cityRequired</span>
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && educationForm.get('institution').hasError('InvalidLocationError')"
                    translate>{{ educationForm.get('institution').errors?.InvalidLocationError }}</span>
                </div>
              </div>

              <!-- Education Field / Major -->
              <div class="form-group focus-container"
                [ngClass]="{'has-error': (formDir.submitted || shouldShowValidationErrors(i)) && !educationForm.get('education_field').valid}">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                  <p-autoComplete [suggestions]="filteredEducationFields"
                    (completeMethod)="filterEducationFields($event, i)" field="name" [minLength]="1"
                    styleClass="form-control" formControlName="education_field" inputId="float-input"
                    [ngClass]="{'has-val': educationForm.get('education_field').value}"
                    (onBlur)="checkIfNewValue(educationForm.get('education_field'), i)">
                  </p-autoComplete>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" for="float-input"
                    pTooltip="{{'education.educationField' | translate}}" tooltipPosition="top" translate>
                    education.major
                  </label>
                </div>
                <div class="col-lg-3 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && !educationForm.get('education_field').valid"
                    translate>validationMessages.required</span>
                </div>
              </div>

              <!-- Dates -->
              <div class="form-group focus-container">
                <div class="col-lg-2 col-md-2 col-sm-3 alignment-right">
                </div>
                <div class="col-lg-8 col-md-8 col-sm-6">
                  <div class="row">
                    <!-- From Date -->
                    <div formGroupName="from" class="col-lg-6 margin-bo-mo-10">
                      <div class="row" style="display:flex; align-items:center;">
                        <div class="col-lg-3 col-md-2 col-xs-3 alignment-right">
                          <label class="control-label label-bot-bit" translate>education.from</label>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding">
                          <p-dropdown [options]="fromYearOpts" formControlName="year" [filter]="true"
                            placeholder=" YYYY "
                            [ngClass]="{'has-val': getValueToDD(educationForm.get('from').get('year'))}">
                            <ng-template let-year pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{year.label}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding">
                          <p-dropdown [options]="monthOpts" formControlName="month" [filter]="true" placeholder="MM">
                            <ng-template let-it pTemplate="selectedItem">
                              <span style="vertical-align:middle; float:left;">{{it.label | translate |
                                slice:0:3}}</span>
                            </ng-template>
                            <ng-template let-month pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{month.label | translate}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                      </div>
                    </div>

                    <!-- To Date -->
                    <div formGroupName="to" class="col-lg-6 margin-bo-mo-10">
                      <div class="row to-date" style="display:flex; align-items:center;">
                        <div class="col-lg-3 col-md-2 col-xs-3 col-lg-offset-1 alignment-right">
                          <label class="control-label label-bot-bit" translate>education.to</label>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding"
                          [ngClass]="{'has-error': (formDir.submitted || shouldShowValidationErrors(i)) && !educationForm.get('to').get('year').valid}">
                          <p-dropdown [options]="toYearOpts" formControlName="year" [filter]="true" placeholder=" YYYY"
                            #toYear (onChange)="onToYearSelect(toYear.value, i)"
                            [ngClass]="{'has-val': getValueToDD(educationForm.get('to').get('year'))}">
                            <ng-template let-it pTemplate="selectedItem">
                              <span style="vertical-align:middle; float:left;">{{it.label | translate}}</span>
                            </ng-template>
                            <ng-template let-year pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{year.label | translate}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                        <div class="col-xs-4 col-no-padding-left focus-no-padding">
                          <p-dropdown [options]="monthOpts" formControlName="month" [filter]="true" placeholder="MM">
                            <ng-template let-it pTemplate="selectedItem">
                              <span style="vertical-align:middle; float:left;">{{it.label | translate |
                                slice:0:3}}</span>
                            </ng-template>
                            <ng-template let-month pTemplate="item">
                              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <div style="font-size:14px;float:left;margin-top:4px">{{month.label | translate}}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <span class="custom-underline"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-3">
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && !educationForm.get('to').get('year').valid"
                    translate>validationMessages.yearRequired</span>
                  <span class="error-message"
                    *ngIf="(formDir.submitted || shouldShowValidationErrors(i)) && educationForm.hasError('compareError')"
                    translate>{{educationForm.errors?.compareError}}</span>
                </div>
              </div>

              <!-- Description -->
              <div class="form-group focus-container">
                <div class="col-lg-3 col-md-2 col-sm-3 alignment-right"></div>
                <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding"
                  [ngClass]="{'has-val': educationForm.get('description').value}">
                  <textarea class="form-control" formControlName="description" rows="3"></textarea>
                  <span class="custom-underline"></span>
                  <label class="control-label custom-control-label" translate>education.description</label>
                </div>
              </div>

              <div class="minamize-certification" (click)="minimize(i)">
                <i class="fa fa-angle-up" [ngClass]="{'fa-angle-down': isMinimized[i]}" aria-hidden="true"></i>
              </div>
            </form>
          </div>
        </div>
        <div class="col-md-1 d-flex align-items-end justify-content-center pl-3">
          <button type="button" class="btn btn-gray" (click)="addEducation()">
            <i class="fa fa-plus"></i>
          </button>
          <button *ngIf="i > 0" (click)="removeEducation( i)" class="btn btn-delete btn-delete-big">
            <i class="fa fa-trash" aria-hidden="true"></i>
          </button>
        </div>
      </div>
    </ng-container>
    <div class="row form-group text-right div-margin-top-40">
      <div class="col-sm-10 col-xs-12">
        <button type="button" class="btn btn-primary btnNext" (click)="prevEducation()" style="margin-right: 10px;">
          <i class="fa fa-arrow-left"></i> <span translate>shared.previous</span>
        </button>
        <button type="button" class="btn btn-secondary btnNext" (click)="skipEducation()" style="margin-right: 10px;">
          <span translate>shared.skip</span>
        </button>
        <button type="button" class="btn btn-primary btnNext" (click)="nextEducation()">
          <span translate>shared.next</span> <i class="fa fa-arrow-right"></i>
        </button>
      </div>
    </div>
  </div>
</div>
