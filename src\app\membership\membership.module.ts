import { NgModule } from '@angular/core';
import { SharedModule } from 'shared/shared.module';
import { MembershipRoutingModule } from './membership-routing.module';
import {AccountVerificationComponent} from './components/user/account-verification/account-verification.component';
import {SignUpComponent} from './components/user/sign-up/sign-up.component';
import {ResetPasswordVerificationComponent} from './components/user/reset-password-verification/reset-password-verification.component';
import {ResetPasswordComponent} from './components/user/reset-password/reset-password.component';
import { LoginCompanyComponent } from './components/company/login/login-company.component';
import {AccountVerificationCompanyComponent} from './components/company/account-verification/account-verification-company.component';
import {SignUpCompanyComponent} from './components/company/sign-up/sign-up-company.component';
import {ResetPasswordVerificationCompanyComponent} from
    './components/company/reset-password-verification/reset-password-verification-company.component';
import {ResetPasswordCompanyComponent} from './components/company/reset-password/reset-password-company.component';
import {MembershipWrapperComponent} from './components/membership-wrapper/membership-wrapper.component';
import {PasswordStrengthMeterModule} from 'angular-password-strength-meter';
import { LoginAdminComponent } from './components/admin/login/login.component';
import { CommonModule } from '@angular/common';
import { ForgetPasswordComponent } from './components/user/forget-password/forget-password.component';
import { ForgetPasswordCompanyComponent } from './components/company/forget-password-company/forget-password-company.component';
// import {LoginComponent} from '../membership/components/user/login/login.component';
// import { ForgetPasswordComponent } from './components/user/forget-password/forget-password.component';

@NgModule({
  imports: [
    SharedModule,
    MembershipRoutingModule,
    PasswordStrengthMeterModule,
    CommonModule
  ],
  declarations: [
    AccountVerificationComponent,
    SignUpComponent,
    ResetPasswordVerificationComponent,
    ResetPasswordComponent,
    MembershipWrapperComponent,
    LoginCompanyComponent,
    SignUpCompanyComponent,
    AccountVerificationCompanyComponent,
    ResetPasswordVerificationCompanyComponent,
    ResetPasswordCompanyComponent,
    LoginAdminComponent,
    ForgetPasswordComponent,
    ForgetPasswordCompanyComponent
  ],
  exports: [
    SignUpCompanyComponent,
    AccountVerificationCompanyComponent,
    LoginCompanyComponent,
    ResetPasswordVerificationCompanyComponent,
    ResetPasswordCompanyComponent,
    MembershipWrapperComponent,
    LoginCompanyComponent,
    SignUpCompanyComponent,
    AccountVerificationCompanyComponent,
    ResetPasswordVerificationCompanyComponent,
    ResetPasswordCompanyComponent,
    
  ]
})
export class MembershipModule { }
