import { NativeDateAdapter } from '@angular/material/core';


export class CustomDateAdapter extends NativeDateAdapter {
    format(date: Date, displayFormat: Object): string {
        if (displayFormat === 'input') {
            const day = date.getUTCDate() + 1;
            const month = date.getUTCMonth() + 1;
            const year = date.getFullYear();
            // Return the format as per your requirement
            return `${year}-${month}-${day}`;
        } else {
            return date.toDateString();
        }
    }
}

export const MY_DATE_FORMATS = {
    parse: {
        dateInput: {month: 'short', year: 'numeric', day: 'numeric'}
    },
    display: {
        dateInput: 'input',
        monthYearLabel: {year: 'numeric', month: 'short'},
        dateA11yLabel: {year: 'numeric', month: 'long', day: 'numeric'},
        monthYearA11yLabel: {year: 'numeric', month: 'long'},
    }
};
