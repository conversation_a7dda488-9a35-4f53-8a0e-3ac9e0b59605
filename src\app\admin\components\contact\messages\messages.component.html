<h3 class="verf-heading"> Browse Messages </h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ messages.length }}</span>
   Selected: <span class="badge badge-primary badge-pill">{{ filteredMessages.length }}</span> &nbsp;&nbsp;&nbsp;
   <i class="fa fa-refresh" style="font-size:23px;cursor:pointer;color:#3d7bce;" (click)="initAndRefreshTable()"></i>
  </p>




<p-table #dt [value]="messages" [(selection)]="filteredMessages" dataKey="id" styleClass="ui-table-messages" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="messages.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['id', 'year' ,'email','type', 'language', 'date', 'main_cat', 'sub_cat', 'status']">
    <ng-template pTemplate="caption">
        <!-- Received Messages -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th></th>
            <th pSortableColumn="id"  style="width:70px;">ID <p-sortIcon field="id"></p-sortIcon></th>
            <th pSortableColumn="year"  style="width:70px;">Year <p-sortIcon field="year"></p-sortIcon></th>
            <th pSortableColumn="email"  style="min-width:100px;" colspan="3">Email <p-sortIcon field="email"></p-sortIcon></th>          
            <th pSortableColumn="type" style="width:80px;" title="type">Type <p-sortIcon field="type" ></p-sortIcon></th>
            <th pSortableColumn="language" style="width:70px;" title="language">Lan<p-sortIcon field="language"></p-sortIcon></th>
            <th pSortableColumn="date" style="width:150px;">Date <p-sortIcon field="date"  ></p-sortIcon></th>
            <!-- <th pSortableColumn="assigned_from" style="width:100px;">Assigned From <p-sortIcon field="assigned_from"></p-sortIcon></th> -->
            <th pSortableColumn="main_cat"  style="min-width:110px;" colspan="2">Main Cat <p-sortIcon field="main_cat" ></p-sortIcon></th>
            <th pSortableColumn="sub_cat"  style="min-width:110px;" colspan="2">Sub Cat <p-sortIcon field="sub_cat" ></p-sortIcon></th>
            <!-- <th pSortableColumn="last_reply">Last Reply <p-sortIcon field="last_reply"></p-sortIcon></th> -->
            <th pSortableColumn="status" style="width:140px;">Status <p-sortIcon field="status" ></p-sortIcon></th>
            <!-- <th pSortableColumn="handled_by">Handled By <p-sortIcon field="handled_by"></p-sortIcon></th> -->
            <th  style="min-width:80px;">Clear</th>
        </tr>
        <tr>
            <th>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i *ngIf="filteredMessages.length !== 0" class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected messages" (click)="displayDeleteAlert(4)"  style="margin-right:-20px;"></i>
              </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'id', 'startsWith')" placeholder="" class="ui-column-filter" style="width: 40px;">
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'year', 'startsWith')" placeholder="" class="ui-column-filter" style="width: 40px;">
            </th>
            <th colspan="3">
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'email', 'contains')" placeholder="" style="width:100%" class="ui-column-filter">
            </th>
            <th>
                <p-dropdown [options]="types" (onChange)="dt.filter($event.value, 'type', 'equals')" styleClass="ui-column-filter type"  [(ngModel)]="type"  placeholder="" [showClear]="false">
                  <ng-template let-option pTemplate="item">
                      <span [class]="">{{option.label}}</span>
                  </ng-template>
                </p-dropdown>
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'language', 'contains')" placeholder="" class="ui-column-filter" style="width: 50px;">
            </th>
            <th>
              <p-calendar #cl [(ngModel)]="rangeDates" (onSelect)="dt.filter(rangeDates, 'date', 'isBetween')" (onClearClick)="dt.filter('', 'date', 'contains')" [showButtonBar]="true" yearRange="2020:2030" selectionMode="range" styleClass="ui-column-filter" placeholder="" [readonlyInput]="true" dateFormat="yy-mm-dd"  inputId="range"></p-calendar>
            </th>
            <!-- <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'assigned_from', 'contains')" placeholder="" class="ui-column-filter">
            </th> -->
            <th colspan="2">
              <!-- <input pInputText type="text" (input)="dt.filter($event.target.value, 'main_cat', 'contains')" placeholder="" class="ui-column-filter"> -->
              <p-dropdown [options]="mainCats" (onChange)="dt.filter($event.value, 'main_cat_id', 'equals')" styleClass="ui-column-filter main" [(ngModel)]="main_cat"  [showClear]="false" [filter]="true">
                <ng-template let-option pTemplate="item">
                    <span>{{option.label}}</span>
                </ng-template>
               </p-dropdown>
            </th>
            <th colspan="2">
              <!-- <input pInputText type="text" (input)="dt.filter($event.target.value, 'sub_cat', 'contains')" placeholder="" class="ui-column-filter"> -->
              <p-dropdown [options]="subCats" (onChange)="dt.filter($event.value, 'sub_cat_id', 'equals')" styleClass="ui-column-filter sub" [(ngModel)]="sub_cat"  [showClear]="false" [filter]="true">
                <ng-template let-option pTemplate="item">
                    <span >{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <!-- <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'last_reply', 'contains')" placeholder="" class="ui-column-filter">
            </th> -->
            <th>
                <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'status', 'equals')" styleClass="ui-column-filter status" [(ngModel)]="status"    [showClear]="false">
                    <ng-template let-option pTemplate="item">
                        <span [class]="'message-badge status-' + option.value">{{option.label}}</span>
                    </ng-template>
                </p-dropdown>
            </th>
            <!-- <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'handled_by', 'contains')" placeholder="" class="ui-column-filter">
            </th> -->
            <th> <i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-message let-rowIndex >
        <tr class="ui-selectable-row" [class.new-msg]="message.status === 'new'"
            (mouseover)="message.display= true" (mouseleave)="message.display=false" >
            <td>
                <p-tableCheckbox [value]="message" (click)="addToSelected(message)"></p-tableCheckbox>
            </td>
            <td data-toggle="modal" data-target="#msgModal" class="clickable-cell"   (click)="displayModal(message)">
              {{message.id}}
            </td>
            <td data-toggle="modal" data-target="#msgModal" class="clickable-cell"   (click)="displayModal(message)">
                {{message.year}}
              </td>
            <td colspan="3" data-toggle="modal" data-target="#msgModal" class="clickable-cell"   (click)="displayModal(message)">
                {{message.email | summary:15 }}...
            </td>
            <td data-toggle="modal" data-target="#msgModal" class="clickable-cell"   (click)="displayModal(message)">
              <span *ngIf="message.type === 'ROLE_JOB_SEEKER'"><i class="fa fa-user-circle" title="job seeker"></i></span>
              <span *ngIf="message.type === 'ROLE_EMPLOYER'"><i class="fa fa-suitcase" title="Employer"></i></span>
              <span *ngIf="message.type === 'ROLE_VISITOR'"><i class="fa fa-question" title="Visitor"></i></span>
              <span *ngIf="message.type === 'ROLE_ADMIN'"><i class="fa fa-user" title="Admin"></i></span>
              <span *ngIf="message.type === 'ROLE_CONTACT_ADMIN'"><i class="fa fa-user" title="Sub Admin"></i></span>
            </td>
            <td data-toggle="modal" data-target="#msgModal" class="clickable-cell"   (click)="displayModal(message)">
               <span [title]="message.language" class="language">{{ getLangAbbrev(message.language) }}</span>
            </td>
            <td [title]="message.date | date: 'long'" data-toggle="modal" data-target="#msgModal" class="clickable-cell"  (click)="displayModal(message)">
               {{message.date | date: 'MMM.dd '+ '&nbsp;&nbsp;&nbsp;' + 'hh:mm a'}}
            </td>
            <!-- <td>
               {{ (message.assign_log.length !== 0) ? message.assign_log[0].from_assigned_admin : null }}
            </td> -->
            <td colspan="2" data-toggle="modal" data-target="#msgModal" class="clickable-cell" [title]="message.main_cat"   (click)="displayModal(message)">
               {{ message.main_cat | summary:10}}
            </td>
            <td colspan="2" data-toggle="modal" data-target="#msgModal" class="clickable-cell" [title]="message.sub_cat"  (click)="displayModal(message)">
               {{ message.sub_cat | summary:10}}
            </td>
            <!-- <td>
               {{(message.last_reply )? message.last_reply.short_reply : null  | summary:10 }}
            </td> -->
            <td class="text-center">
                <span *ngIf="message.status !== 'commented' && message.status !== 'assigned' && message.status !== 'replied'" [class]="'message-badge status-' + message.status" >{{message.status}}  </span>
                <span *ngIf="message.status === 'commented'" [class]="'message-badge status-' + message.status" [title]="message.comments[message.comments.length-1].admin_name + ': ' + message.comments[message.comments.length-1].comment || ''" data-toggle="modal" data-target="#msgModal"   (click)="displayComments(message)">{{message.status}}  </span>
                <span *ngIf="message.status === 'assigned' && message.assign_log.length !== 0" [class]="'message-badge status-' + message.status" [title]="message.assign_log[0].from_assigned_admin || ''" >{{message.status}}  </span>
                <span *ngIf="message.status === 'assigned' && message.assign_log.length === 0" [class]="'message-badge status-' + message.status"  >{{message.status}}  </span>
                <span *ngIf="message.status === 'replied'" [class]="'message-badge status-' + message.status" [title]="message.last_reply.admin_name + ': ' + message.last_reply.short_reply || ''" data-toggle="modal" data-target="#msgModal"   (click)="displayReplys(message)" >{{message.status}}  </span>
                <span *ngIf="message.comments.length !== 0"><i class="fa fa-comment" [title]="message.comments[message.comments.length-1].admin_name + ': ' + message.comments[message.comments.length-1].comment || ''" style="padding-left:5px;" data-toggle="modal" data-target="#msgModal"   (click)="displayComments(message)" ></i></span>
            </td>
            <!-- <td>
              {{message.handled_by | summary:10 }}
           </td> -->
            <td >
              <span *ngIf="message.display">
                <i class="fa fa-trash"   data-toggle="modal" data-target="#addValueModal" title="delete this message" (click)="displayDeleteAlert(3, message.id)" ></i>
                <i class="fa fa-edit"   data-toggle="modal" data-target="#msgModal"   (click)="displayModal(message)" ></i>
                <i class="fa fa-info-circle" title="view message log"  data-toggle="modal" data-target="#msgModal"  (click)="displayMsgLog(message)" ></i>
                <!-- <i class="fa fa-trash" (pointerover)="changeStyle()" (close)="resetStyle()"  data-toggle="modal" data-target="#addValueModal" title="delete this message" (click)="displayDeleteAlert(3, message.id)"></i>
                <i class="fa fa-edit" (mouseover)="changeStyle()" (mouseleave)="resetStyle()"  data-toggle="modal" data-target="#msgModal"   (click)="displayModal(message)"></i> -->
              </span>
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr *ngIf="loading === false && messages.length ===0">
            <td colspan="11" style="text-align:center;padding:15px;">No messages found.</td>
        </tr>
    </ng-template>
    <!-- <p-paginator [rows]="9" totalRecords="messages.length" (onPageChange)="paginate($event)" showCurrentPageReport="true"></p-paginator> -->
</p-table>






<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayMsgModal" id="msgModal"  tabindex="-1" role="dialog" aria-labelledby="msgModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
         <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
         <h3 *ngIf="mode === 'preview_comments'">Comments </h3>
         <h3 *ngIf="mode === 'preview_replys'" >Replys </h3>
         <h3 *ngIf="mode === 'reply_form' || mode === 'log_mode'">View Message <span *ngIf="mode === 'log_mode'"> Log</span> </h3>
      </div>
      <div class="modal-body">
         <app-message-modal [mode]="mode" [msg]="msgToPreview" [msgLog]="msgLog" [templates]="templates"
         [template_titles]="templateTitles" [admins]="admins" (msgAssigned)="assignTo($event)"
          (commentAdded)="addNewComment($event)" (newReplyAdded)="addReplyResult($event)" (sendMessageStatus)="updateMsgStatus($event)" >
         </app-message-modal>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<!-- end of  modal-->


<!-- add  modal-->
<div class="modal fade" *ngIf="displayAddModal" id="addValueModal"  tabindex="-2" role="dialog" aria-labelledby="addModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeAddModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="opperationNum === 3 || opperationNum === 4" style="color:crimson;">Delete </h3>
      </div>
      <div class="modal-body">
       <!-- delete alert -->
        <div *ngIf="opperationNum === 3">
           <p >Are you sure you want to delete this message? </p>
           <button type="button" class="btn btn-light" (click)="deleteMsg()">Delete</button>
        </div>

        <div *ngIf="opperationNum === 4">
          <p>Are you sure you want to delete these {{ filteredMessages.length }} message? </p>
          <button type="button" class="btn btn-light" (click)="deleteMultiMsgs()">Delete</button>
       </div>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->








<!-- <select #type (change)="dt.filter(type.value, 'type', 'equals')">
  <option value=""></option>
  <option value="ROLE_VISITOR">?</option>
  <option value="ROLE_ADMIN">Admin</option>
  <option value="ROLE_JOB_SEEKER">Job Seeker</option>
  <option value="ROLE_EMPLOYER">Employer</option>
</select> -->

<!-- <select #status (change)="dt.filter(status.value, 'status', 'equals')">
  <option value=""></option>
  <option value="new">new</option>
  <option value="opened">opened</option>
  <option value="commented">commented</option>
  <option value="done">done</option>
  <option value="replied">replied</option>
  <option value="assigned">assigned</option>
</select> -->
