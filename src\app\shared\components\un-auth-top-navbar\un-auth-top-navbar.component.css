.cus-navbar{
  border: 0;
  /* border-bottom: 1px solid #ddd; */
  position: fixed;
  z-index: 1000;
  /* z-index: 10000; */
  top: 0;
  width: 100%;
  background: #ffffff;
}
nav {
  transition: all 0.3s ease;
}

.nav-container{
  padding:0 20px;
  display: flex;
}

.nav-header{
  width:27%;
  display: flex;
  align-items: center;
}
.nav-header , .nav-menu{
  display: flex;
  align-items: center;
}
.nav-header .logo-img{
  display:inline-block;
}
.nav-header .logo-img img{
  height:70px;
  padding: 9px 0;
  /* width:70px; */
}
.nav-header .site-title{
  position: absolute;
  clip: rect(1px, 1px, 1px, 1px);
  -webkit-clip-path: inset(0px px 0px 99.9% 99.9%);
  clip-path: inset(0px 0px 99.9% 99.9%);
  overflow: hidden;
  height: 1px;
  width: 1px;
  padding: 0;
  border: 0;
}
/* .nav-header .site-title{
  padding:0 10px;
  color:#3D7BCE;
  font-family: 'Exo2';
  font-size: 1.6rem;
} */

.nav-menu1{
  width:45%;
  justify-content: center;
}
.nav-menu2{
  width:28%;
  justify-content: flex-end;
}
.nav-menu ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
}
.nav-menu ul li{
  float:left;
}
.nav-menu ul li a{
  display: inline-block;
  padding: 10px;
  cursor: pointer;
}
.nav-menu2 ul li a{
  padding: 3px 8px;
  border-radius: 1px;
  margin-right: 5px;
}
.nav-menu ul li a svg{
  /* width:50px; */
  height:41px;
}
.nav-menu1 li{
  margin:0 16px;
}
.cus-toggle-btn{
  padding: 6px;
  width: 35px;
  height: 35px;
  opacity: 1;
  border-radius: 50%;
  cursor: pointer;
}
.cus-toggle-btn:hover{
  background-color: rgba(60,64,67,0.08);
  transition:all .3s ease;
}

.cus-toggle-btn svg{
  width: 24px;
  height: 24px;
  color: #999;
  opacity: 1;
  fill: currentColor;
}


.top-navbar-profile-li{
  width: 122px;
  text-align: center;
}
.top-navbar-profile{
  display: inline-block;
  padding: 0 0 0 12px;
  color: #999;
  cursor: pointer;
}
.img-div{
  display: inline-block;
  vertical-align: middle;
  width: 35px;
  height: 35px;
}
.img-div img{
  border-radius: 50%;
}
.top-navbar-profile .profile-name{
  padding: 0 15px;
}
.top-navbar-profile .fa {
  font-size: 1.2rem;
}
.top-navbar-profile-li .dropdown-menu .dropdown-item {
  display: block;
  text-decoration: none;
  color: #999;
  padding: 5px 10px;
  transition: all .2s ease;
}
.top-navbar-profile-li .dropdown-menu .dropdown-item:hover{
  color:#555;
  background: #f9f9f9;
}
.toggle-menu .dropdown-menu .dropdown-item{
  text-decoration: none;
  color: #555;
  width: 31%;
  margin: 8px 2px;
  padding: 0;
}
.toggle-menu .dropdown-menu .dropdown-item img {
  display:block;
  margin: auto;
  width: 35px;
  height: 35px;
  border-radius: 4px;
}
.toggle-menu .dropdown-menu .dropdown-item span{
  display:block;
  font-size: 11px;
  color:#777;
}
.toggle-menu .dropdown-menu{
  transform: translate(-75%,0px);
  width: 300px;
  padding: 10px;
}

.job-search{
  display: none;
}

.search-state .nav-menu1{
  display:none;
}
.search-state .job-search{
  /* display:inline-block; */
  display: flex;
  align-items: center;
  width:45%;
}
.search-state .job-search job-search-form-top-bar{
  width:100%;
}
.search-state .country-link-li{
  display:none;
}
/* .toggle-menu .aditional-links , .toggle-menu .mobile-links , .toggle-menu .mobile-links-block{
  display:none !important;
} */
.toggle-menu .mobile-links-block{
  display:none !important;
}
.search-state .toggle-menu .aditional-links{
  display:inline-block !important;
}
.search-state .countryDD-home{
  display:none;
}

/* start nav icons styling */

.nav-home .cls-1 , .nav-home .cls-2{
  fill:none;
  stroke:#2e3871;
  stroke-miterlimit:10;
}
.nav-home .cls-1{
  stroke-linecap:round;
  stroke-width:40px;
}
.nav-home .cls-2{
  stroke-width:30px;
}
.nav-home .cls-3{
  fill:#2e3871;
}

.nav-articles .cls-1, .nav-articles .cls-2, .nav-articles .cls-3{
  fill:none;
}
.nav-articles .cls-2, .nav-articles .cls-3, .nav-articles .cls-5{
  stroke:#b69963;stroke-miterlimit:10;
}
.nav-articles .cls-2, .nav-articles .cls-5{
  stroke-width:11px;
}
.nav-articles .cls-3{
  stroke-width:25px;
}
.nav-articles .cls-4{
  fill:#f3cf71;
}
.nav-articles .cls-5{
  fill:url(#Unnamed_Pattern_4);
}
.nav-articles .cls-6{
  font-size:116.39px;fill:#fff;font-family:ArialMT, Arial;
}

.nav-search-people .cls-1{
  fill:#324572;
}
.nav-search-people .cls-2{
  fill:#5291cd;
}

.top-navbar-links a{
  transition:all .3s ease;
}

.top-navbar-links a svg{
  transition:all .3s ease;
}
.top-navbar-links a svg path{
  transition:all .3s ease;
}
svg .ani-last {
  transition:all 1s ease !important;
}
.top-navbar-links a.nav-home.active , .top-navbar-links a.nav-home:hover {
  background: #2e3871;
}
.top-navbar-links a.nav-search-job.active , .top-navbar-links a.nav-search-job:hover {
  background: rgb(53,181,88);
}
.top-navbar-links a.nav-write-cv.active, .top-navbar-links a.nav-write-cv:hover {
  background: rgb(79,148,223);
}
.top-navbar-links a.nav-articles.active, .top-navbar-links a.nav-articles:hover {
  background:  #f3cf71;
}
.top-navbar-links a.nav-search-people.active, .top-navbar-links a.nav-search-people:hover {
  background: #5291cd;
}


.nav-search-job svg{
  width: 66px;
}
.nav-home svg{
  width: 46px;
}

.nav-search-job .cls-1, .nav-search-job .cls-4{
  fill:none;
}
.nav-search-job .cls-2 , .nav-search-job .cls-6{
  fill:#2fb256;
}
.nav-search-job .cls-3{
  fill:url(#New_Pattern_Swatch_4);
}
.nav-search-job .cls-4{
  stroke-miterlimit:10;
  stroke-width:6px;
  stroke:url(#New_Pattern_Swatch_4);
}


.nav-write-cv .cls-1, .nav-write-cv  .cls-3,.nav-write-cv  .cls-5,.nav-write-cv  .cls-6, .nav-write-cv  .cls-7{
    fill:none;
  }
  .nav-write-cv  .cls-2{
      fill:#628db6;
  }
  .nav-write-cv  .cls-3,.nav-write-cv  .cls-6{
      stroke-linecap:round;
  }
  .nav-write-cv  .cls-3 , .nav-write-cv .cls-5, .nav-write-cv  .cls-6,.nav-write-cv  .cls-7{
      stroke-miterlimit:10;
      stroke: #628db6;
  }
  .nav-write-cv  .cls-3{
      stroke-width:30px;
  }
  .nav-write-cv  .cls-4{
      fill:#628db6;
  }
  .nav-write-cv  .cls-5{
      stroke-width:5px;
  }
  .nav-write-cv .cls-6{
      stroke-width:12px;
  }
  .nav-write-cv .cls-7{
      stroke-width:6px;
  }


.top-navbar-links a.active svg , .top-navbar-links a:hover svg{
  fill:#fff;
}
.top-navbar-links a.active svg path , .top-navbar-links a:hover svg path{
  stroke:#fff;
} 
.top-navbar-links a.nav-home.active .cls-3 , .top-navbar-links a.nav-home:hover .cls-3{
  fill:#fff;
}
.top-navbar-links a.nav-search-people.active .cls-1 , .top-navbar-links a.nav-search-people:hover .cls-1 , 
.top-navbar-links a.nav-search-people.active .cls-2 , .top-navbar-links a.nav-search-people:hover .cls-2{
  fill:#fff;
}

.top-navbar-links a.nav-articles.active .cls-4 , .top-navbar-links a.nav-articles:hover .cls-4{
  fill:#fff;
}
.top-navbar-links a.nav-articles.active .cls-2 , .top-navbar-links a.nav-articles:hover .cls-2,
.top-navbar-links a.nav-articles.active .cls-3 , .top-navbar-links a.nav-articles:hover .cls-3,
.top-navbar-links a.nav-articles.active .cls-5 , .top-navbar-links a.nav-articles:hover .cls-5{
  stroke:#fff;
}
.top-navbar-links a.nav-articles.active .cls-6 , .top-navbar-links a.nav-articles:hover .cls-6{
  fill:#b69963;
}
.top-navbar-links a.nav-search-job.active .cls-3 , .top-navbar-links a.nav-search-job:hover .cls-3 {
  fill: #fff;
}

.top-navbar-links a.nav-search-job.active .cls-4 , .top-navbar-links a.nav-search-job:hover .cls-4  {
  stroke: #fff;
  fill:#fff;
}
.top-navbar-links a.nav-search-job:hover svg path{
  stroke: #fff;
  fill: #fff;
}

.top-navbar-links a.nav-write-cv.active .cls-4 , .top-navbar-links a.nav-write-cv:hover .cls-4{
  fill:#fff;
}
.top-navbar-links a.nav-write-cv.active .cls-6 , .top-navbar-links a.nav-write-cv:hover .cls-6{
  stroke:#fff;
}

.nav-help .cls-1{
    fill:none;stroke:#7f1416;stroke-miterlimit:10;stroke-width:46px;
  }
.nav-help .cls-2{
    fill:#7f1416;
  }

  .nav-faqs .cls-1{
    fill:#6298d1;
  }

/* .toggle-menu .dropdown-menu a svg{
  width: 36px !important;
  height: 36px !important;
} */
.toggle-menu .dropdown-menu .aditional-links svg , .toggle-menu .dropdown-menu .mobile-links svg ,
.toggle-menu .dropdown-menu .fixed-toggle-links svg{
  height: 29px !important;
}
.toggle-menu .dropdown-menu .nav-search-job svg{
  width: 42px !important;
}
.toggle-menu .dropdown-menu .nav-search-job .cls-3{
  fill:#2fb256;
}
.toggle-menu .dropdown-menu .nav-faqs svg{
  width: 42px !important;
}
.toggle-menu .dropdown-menu a{
    cursor: pointer;
}

.notif-link{
    position: relative;
}
.notif-link i{
  font-size: 19px;
}
.notif-count{
  position: absolute;
  top: 0px;
  right: 5px;
  width: 15px;
  padding: 4px;
  font-size: 10px;
  line-height: 6px;
  height: 15px;
  border-radius: 50%;
  background: red;
  color: #fff;
  border: 1px solid #fff;
  font-weight: bold;
}

.topbar-icons svg path , .topbar-icons .st1{
  fill:#3D7BCE;
  opacity: 0.7;
  transition: all .1s ease;
}

.topbar-icons:hover path , .topbar-icons:hover svg .st1{
  opacity: 1  !important;
}
/* end nav icons styling */

/* .country-dropdown .country-img{
  width:30px;
  margin-top:4px;
  float:right;
  border-radius: 2px;
} */
.join-btn{
  color:#fff;
  background: #e3b442;
  font-weight: bold;
  border-radius: 4px  !important;
}
.login-btn{
  background: #3D7BCE;
  color: #fff;
  font-weight: bold;
  border-radius: 4px !important;
}
.login-li  .dropdown-menu{
  transform: translate(-67%,0px);
}
.join-li  .dropdown-menu{
  transform: translate(-52%,0px);
}
.options-as-list  .dropdown-menu .dropdown-item{
  display:block !important;
  text-decoration:none;
  color:#999;
  padding:5px 10px;
  transition: all .2s ease;
}
.options-as-list .dropdown-menu .dropdown-item:hover{
  color:#555;
  background:#f9f9f9;
}

@media screen  and (max-width:1160px){
  .nav-menu ul li a svg{
      height: 31px;
  }
  .nav-search-job svg{
      width: 55px;
  }
  .nav-home svg{
      width: 38px;
  }
  /* .nav-header .logo-img img {
      width: 72px;
  } */
  .nav-header .site-title {
      font-size: 1.4rem;
  }
  .nav-header .logo-img img {
    padding: 15px 0;
  }
  /* .search-container .form-group {
      margin-bottom: 3px;
  } */
}

@media screen  and (max-width:992px){
  /* .nav-menu ul li a svg{
      height: 37px;
  } */
  .nav-search-job svg{
      width: 44px;
  }
  .nav-home svg{
      width: 31px;
  }
}

@media screen  and (max-width:900px){
  .nav-header .logo-img img {
    height:60px;
      /* width: 60px; */
  }
  .nav-header .site-title {
      font-size: 1.2rem;
  }
  .nav-header{
      width:23%;
  }
  .nav-menu2{
      width:23%;
  }
  .nav-menu1 , .job-search{
      width:54% !important;
  }
  .top-navbar-profile .profile-name {
      display: none;
  }
  .top-navbar-profile .fa {
      display: none;
  }
  .top-navbar-profile-li{
      width: 47px;
  }
  /* .search-container .form-group {
      margin-bottom: 0px;
  }
  .search-container .form-horizontal{
      margin-top: 8px;
  } */
}
@media screen  and (max-width:860px){
  .nav-menu ul li a svg{
      height: 29px;
  }
}
@media screen  and (max-width:767px){
  .nav-header .logo-img img {
    height:56px;
    padding: 10px 0;
    /* width: 56px; */
  }
  .mobile-view .nav-menu1{
      display:none;
  }
  .mobile-view .toggle-menu .aditional-links , .mobile-view .toggle-menu .mobile-links{
      display:inline-block !important;
  }
  .mobile-view .mobile-hide{
      display:none;
  }
  .mobile-view .toggle-menu .dropdown-menu{
      transform: translate(-93%,0px);
  }
  .mobile-view  .mobile-links-block{
      display:block !important;
      width:100% !important;
      margin-top: 20px;
  }
  .mobile-view  .mobile-links-block .login-btn{
    min-width: 160px;
    margin-bottom: 10px;
    height: 34px;
    line-height: 29px;
  }
  .mobile-view .list-style img {
      display:inline-block !important;
      margin-right: 8px !important;
      width: 18px !important;
      height: 18px !important;
  }
  .mobile-view .list-style span{
      display:inline-block !important;
      font-size:12px !important;
  }
  .profile-mobile{
      margin-top:12px;
  }
  .profile-mobile img{
      max-width:30px;
  }
  .job-search{
      display: inline-block;
      visibility: hidden;
  }
  .search-state .job-search{
      visibility: visible;
      padding-left: 20px;
  }
  .join-li , .login-li{
    display: none;
  }

  /* start fix nav items width */
  .nav-container{
    padding: 0 10px;
  }
  .search-state .nav-header{
    width:20% !important;
  }
  .nav-header{
    width:62% !important;
  }
  .job-search , .search-state .job-search{
    width: 76% !important;
  }
  .nav-menu2{
    width: 7%;      
  }
   /* end fix nav items width */
}

