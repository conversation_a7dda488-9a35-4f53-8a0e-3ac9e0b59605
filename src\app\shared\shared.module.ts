import { GoogleLocationComponent } from './components/google-location/google-location.component';
import { NotificationComponent } from 'shared/components/notification/notification.component';
import { TableModule } from 'primeng/table';
import { AgmCoreModule } from '@agm/core';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {ToolbarModule} from 'primeng/toolbar';
import {InputTextModule} from 'primeng/inputtext';
import {TieredMenuModule} from 'primeng/tieredmenu';
import { MenuModule } from 'primeng/menu';
import {DialogModule} from 'primeng/dialog';

import {BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { CollapseModule} from 'ngx-bootstrap/collapse';
// import { TooltipModule} from 'ngx-bootstrap/tooltip';
import {TooltipModule} from 'primeng/tooltip';
import { EditorModule } from 'primeng/editor';
import { ListboxModule } from 'primeng/listbox';
import {PanelModule} from 'primeng/panel';

import { AutoCompleteModule } from 'primeng/autocomplete';
import { DropdownModule } from 'primeng/dropdown';
import {  MultiSelectModule } from 'primeng/multiselect';
import { MultiSelectAllModule } from '@syncfusion/ej2-angular-dropdowns';
import { SocialLoginModule, AuthServiceConfig, GoogleLoginProvider , FacebookLoginProvider } from 'angular5-social-login';
import { getAuthServiceConfigs } from 'socialloginConfig';

// import { MapComponent } from 'shared/components/map/map.component';
import { PageNavbarComponent } from 'shared/components/page-navbar/page-navbar.component';
import { TopNavbarComponent } from 'shared/components/top-navbar/top-navbar.component';


import { MapToFormService } from '../user/cv-services/map-to-form.service';
import { MapService } from '../user/cv-services/map.service';
import { TopToSideSignalService } from '../user/cv-services/top-to-side-signal.service';
import { SideToFormSignalService } from '../user/cv-services/side-to-form-signal.service';
import { AuthService } from 'shared/shared-services/auth-service';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
// import { AuthGuardService } from 'shared/shared-services/auth-guard.service';
import { LoginAuthGuardService } from 'shared/shared-services/login-auth-guard.service';
import {UnAuthTopNavbarComponent} from 'shared/components/un-auth-top-navbar/un-auth-top-navbar.component';
import { UserGuardService } from 'shared/shared-services/user-guard.service';
import { CompanyGuardService } from 'shared/shared-services/company-guard.service';
import { CompanyTopbarComponent } from 'shared/components/company-topbar/company-topbar.component';
import { WrapperLanguageComponent } from '../company/components/wrapper-language/wrapper-language.component';
import { CompanyWrapperService } from '../company/services/company-wrapper.service';
import { Ng5SliderModule } from 'ng5-slider';
import {SelectButtonModule} from 'primeng/selectbutton';
import { SpinnerModule } from 'primeng/spinner';
import {AccordionModule} from 'primeng/accordion';
import { AdminGuardService } from 'shared/shared-services/admin-guard.service';
import { UserTopbarComponent } from './components/user-topbar/user-topbar.component';
// import {CarouselModule} from 'primeng/carousel';
import {MessagesModule} from 'primeng/messages';
import {MessageModule} from 'primeng/message';
import {ButtonModule} from 'primeng/button';
import {PaginatorModule} from 'primeng/paginator';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import {CardModule} from 'primeng/card';
import {ChartModule} from 'primeng/chart';
import {ToastModule} from 'primeng/toast';
import {FileUploadModule} from 'primeng/fileupload';
import { ArchwizardModule } from 'angular-archwizard';
import {RadioButtonModule} from 'primeng/radiobutton';
import {SplitButtonModule} from 'primeng/splitbutton';


import { AdvrsInterfaceComponent } from 'app/advrs-interface/advrs-interface.component';

import { CalendarModule } from 'primeng/calendar';
import {ProgressSpinnerModule} from 'primeng/progressspinner';

import { FooterComponent } from './components/footer/footer.component';
import { TableColsManagementComponent } from "app/table-cols-management/table-cols-management.component";
import { PostJobService } from "app/company/services/post-job.service";
import { ManagePostService } from "app/company/services/manage-post.service";
import { AdvrPreviewComponent } from "app/company/components/advr-preview/advr-preview.component";
import {SidebarModule} from 'primeng/sidebar';
import { FormCustomerComponent } from "app/company/components/google-form/form-customer/form-customer.component";
import { SharedService } from "app/company/components/google-form/form-services/shared.service";
import { FormPreviewComponent } from "app/company/components/google-form/form-preview/form-preview.component";
import { FullFormCustomerGeneratorComponent } from "app/company/components/google-form/full-form-customer-generator/full-form-customer-generator.component";
import { TestStorgeComponent } from "app/company/components/google-form/test-storge/test-storge.component";
import { UserReplyComponent } from "app/company/components/google-form/user-reply/user-reply.component";
import { FormJobService } from "app/company/components/google-form/form-services/form-job.service";
import { FocusDirective } from "app/company/components/google-form/focus.directive";
import { AdvrLangComponent } from "app/company/components/advr-lang/advr-lang.component";
import { ArrayStorageComponent } from "app/company/components/google-form/array-storage/array-storage.component";
import { BasicRepliesComponent } from "app/company/components/google-form/basic-replies/basic-replies.component";
import { ChartComponent } from "app/company/components/google-form/chart/chart.component";
import { QuestionService } from "app/company/components/google-form/form-services/question.service";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatRadioModule } from "@angular/material/radio";
import { MatButtonModule } from "@angular/material/button";
import { MatCardModule } from "@angular/material/card";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatNativeDateModule } from "@angular/material/core";
import { MatStepperModule } from "@angular/material/stepper";
import { ChartsModule } from "ng2-charts";
import {ConfirmDialogModule} from 'primeng/confirmdialog';
import {ConfirmationService} from 'primeng/api';
import { LanguageService } from 'app/admin/services/language.service';
import { MessageService } from 'primeng';

import {InvalidControlScrollDirective} from 'shared/directives/invalidControlScrollDirective';
import { ConfirmMessageComponent } from './components/confirm-message/confirm-message.component';
import { CompanyPreviewComponent } from './components/company-preview/company-preview.component';
import { ResumePreviewComponent } from './components/resume-preview/resume-preview.component';
import { FullPreviewService } from '../user/cv-services/full-preview.service';
import { LoginComponent } from 'app/membership/components/user/login/login.component';
import { ContactUsComponent } from './components/contact-us/contact-us.component';
import {ContactService} from './shared-services/contact.service'

import { CompanyWrapperComponent } from 'app/company/components/company-wrapper/company-wrapper.component';
import { PreLoaderComponent } from './components/pre-loader/pre-loader.component';

import { JobSearchFormTopBarComponent } from './components/job-search-form-top-bar/job-search-form-top-bar.component';
import { ColsManagementComponent } from './components/cols-management/cols-management.component';
import { SinglePostComponent } from './components/single-post/single-post.component';
// import { LazyLoadImageModule } from 'ng-lazyload-image';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { LazyloadDropdownClass } from 'shared/Models/lazyloadDropdown';
import { CountryDropdownTopbarComponent } from './components/country-dropdown-topbar/country-dropdown-topbar.component';
import { ResourcesLazyloadDirective } from './directives/resources-lazyload.directive';
import {DeferLoadModule} from '@trademe/ng-defer-load';
import { ImageCropperModule } from 'ngx-image-cropper';
import { ImageEditorComponent } from './components/image-editor/image-editor.component'
import { RoleGuardService } from './shared-services/role-guard.service';
// import { ForgetPasswordComponent } from '../membership/components/user/forget-password/forget-password.component';
//import { DragulaModule } from 'ng2-dragula';
// import { NgxSortableModule } from 'ngx-sortable'

@NgModule({
  imports: [
    CommonModule,
    CollapseModule.forRoot(),
    RouterModule.forChild([]),
    AgmCoreModule,
    FormsModule,
    ReactiveFormsModule,
    Ng5SliderModule,
    BsDropdownModule.forRoot(),
    TranslateModule,
    ArchwizardModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatButtonModule,
    MatCardModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatStepperModule,
    ChartsModule,
    // PrimeNG Modules
    ProgressSpinnerModule,
    CalendarModule,
    MessagesModule,
    MessageModule,
    ToastModule,
    PanelModule,
    TableModule,
    AccordionModule,
    DropdownModule,
    DialogModule,
    ListboxModule,
    MultiSelectModule,
    AutoCompleteModule,
    EditorModule,
    TooltipModule,
    SpinnerModule,
    SelectButtonModule,
    ToolbarModule,
    InputTextModule,
    PanelModule,
    TieredMenuModule,
    MenuModule,
  //  CarouselModule,
    TableModule,
    ButtonModule,
    PaginatorModule,
    SidebarModule,
    RadioButtonModule,
    SplitButtonModule,
    // Angular 4 MultiSelect Dropdown Modules
    MultiSelectAllModule,
    OverlayPanelModule,
    CardModule,
    ChartModule,
  //  ToastModule,
    // facebook & google config
     SocialLoginModule,
     FileUploadModule,
     ConfirmDialogModule,
  //   LazyLoadImageModule,
     NgSelectModule,
     DeferLoadModule,
     ImageCropperModule
    //  NgxSortableModule
  //  DragulaModule
  ],
  declarations: [
    NotificationComponent,
    TopNavbarComponent,
    PageNavbarComponent,
    // MapComponent,
    UnAuthTopNavbarComponent,
    CompanyTopbarComponent,
    WrapperLanguageComponent,
    AdvrsInterfaceComponent,
    TableColsManagementComponent,
    UserTopbarComponent,
    FooterComponent,
    AdvrPreviewComponent,
    FormCustomerComponent,
    FormPreviewComponent,
    FullFormCustomerGeneratorComponent,
    TestStorgeComponent,
    UserReplyComponent,
    FocusDirective,
    AdvrLangComponent,
    ArrayStorageComponent,
    BasicRepliesComponent,
    ChartComponent,
    InvalidControlScrollDirective,
    ConfirmMessageComponent,
    CompanyPreviewComponent,
    ResumePreviewComponent,
    LoginComponent,
    ContactUsComponent,

    CompanyWrapperComponent,
    PreLoaderComponent,
    JobSearchFormTopBarComponent,
    ColsManagementComponent,
    SinglePostComponent,
    GoogleLocationComponent,
    CountryDropdownTopbarComponent,
    ResourcesLazyloadDirective,
    ImageEditorComponent
    // ForgetPasswordComponent,
  ],
  exports: [
     CommonModule,
    TopNavbarComponent,
    PageNavbarComponent,
    FooterComponent,
    // MapComponent,
    UnAuthTopNavbarComponent,
    UserTopbarComponent,
    CompanyTopbarComponent,
    ConfirmMessageComponent,
    CompanyPreviewComponent,
    ResumePreviewComponent,
    CollapseModule.forRoot().ngModule,
    FormsModule,
    ReactiveFormsModule,
    Ng5SliderModule,
    BsDropdownModule.forRoot().ngModule,
    TranslateModule,
    ArchwizardModule,
    // PrimeNG Modules
    ProgressSpinnerModule,
    CalendarModule,
    MessagesModule,
    MessageModule,
    ToastModule,
    PanelModule,
    TableModule,
    ToolbarModule,
    RadioButtonModule,
    SplitButtonModule,
    AccordionModule,
    DropdownModule,
    DialogModule,
    ListboxModule,
    MultiSelectModule,
    AutoCompleteModule,
    EditorModule,
    TooltipModule,
    SpinnerModule,
    SelectButtonModule,
    InputTextModule,
    PanelModule,
  //  CarouselModule,
    MultiSelectAllModule,
    TableModule,
    ButtonModule,
    PaginatorModule,
    OverlayPanelModule,
    SidebarModule,
    CardModule,
    ChartModule,
  //  ToastModule,
    FileUploadModule,
    AdvrPreviewComponent,
    FormCustomerComponent,
    FormPreviewComponent,
    FullFormCustomerGeneratorComponent,
    TestStorgeComponent,
    UserReplyComponent,
    FocusDirective,
    AdvrLangComponent,
    ArrayStorageComponent,
    BasicRepliesComponent,
    TableColsManagementComponent,
    ChartComponent,
    ConfirmDialogModule,
    NotificationComponent,
    InvalidControlScrollDirective,
    LoginComponent,
    ContactUsComponent,
    PreLoaderComponent,
    ColsManagementComponent,
    SinglePostComponent,
    GoogleLocationComponent,
  //  LazyLoadImageModule,
    NgSelectModule,
    ResourcesLazyloadDirective,
    DeferLoadModule,
    ImageEditorComponent,
    JobSearchFormTopBarComponent
    // ForgetPasswordComponent,
  ],
  entryComponents: [
    FormCustomerComponent,

  ],
  providers: [
    MessageService,
    ContactService,
    LanguageService,
    TopToSideSignalService,
    SideToFormSignalService,
    AuthService,
    ExportUrlService,
  //  AuthGuardService,
    RoleGuardService,
    LoginAuthGuardService,
    UserGuardService,
    CompanyGuardService,
    AdminGuardService,
    MapToFormService,
    MapService,
    CompanyWrapperService,
    ManagePostService,
    PostJobService,
    SharedService,
    FormJobService,
    QuestionService,
    ConfirmationService,
    FullPreviewService,
    LazyloadDropdownService,
  //  LazyloadDropdownClass,
    {provide: AuthServiceConfig, useFactory: getAuthServiceConfigs},
  ]
})
export class SharedModule { }
