<p-confirmDialog></p-confirmDialog>
<h3 *ngIf="status=='active-advs'" class="verf-heading"> Manage Active Advertisements </h3>
<h3 *ngIf="status=='ended-advs'" class="verf-heading"> Manage Ended Advertisements </h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ advs.length }}</span>
</p>


<p-table #dt [value]="advs" dataKey="id" styleClass="ui-table-advs" [rowHover]="true" [rows]="10"
      [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading" [paginator]="advs.length"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [filterDelay]="0"
      [globalFilterFields]="['id','opportunity_for', 'company_name', 'job_adv_title','publish_date','country','city']">
      <ng-template pTemplate="caption">
            <!-- advs -->
            <div class="ui-table-globalfilter-container">
                  <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="Global Search" />
            </div>
      </ng-template>
      <ng-template pTemplate="header">
            <tr>
                  <th pSortableColumn="opportunity_for" style="width: 100px;">Company Type <p-sortIcon
                              field="opportunity_for"></p-sortIcon>
                  </th>
                  <th pSortableColumn="company_name" style="width: 100px;">Company Name <p-sortIcon
                              field="company_name"></p-sortIcon></th>
                  <th pSortableColumn="job_adv_title" style="width: 100px;">Advertisement title <p-sortIcon
                              field="job_adv_title"></p-sortIcon></th>
                  <th pSortableColumn="publish_date" style="width: 100px;">Date <p-sortIcon
                              field="publish_date"></p-sortIcon></th>
                  <th pSortableColumn="country" style="width: 100px;">Country <p-sortIcon field="country"></p-sortIcon>
                  </th>
                  <th pSortableColumn="city" style="width: 100px;">City <p-sortIcon field="city"></p-sortIcon></th>
                  <th style="width: 70px;"></th>
            </tr>

            <th>
                  <p-dropdown [options]="companyTypes" (onChange)="dt.filter($event.value, 'opportunity_for', 'equals')"
                        styleClass="ui-column-filter"  [showClear]="false">
                        <ng-template let-option pTemplate="item">
                              <span [class]="'adv-badge companyType-' + option.value">{{option.label}}</span>
                        </ng-template>
                  </p-dropdown>
            </th>
            <th>
                  <input pInputText type="text" (input)="dt.filter($event.target.value, 'company_name', 'contains')"
                        placeholder="" class="ui-column-filter" style="width: 100px;">
            </th>
            <th>
                  <input pInputText type="text" (input)="dt.filter($event.target.value, 'job_adv_title', 'startsWith')"
                        placeholder="" class="ui-column-filter" style="width: 150px;">
            </th>
            <th>
                  <input pInputText type="text" (input)="dt.filter($event.target.value, 'publish_date', 'startsWith')"
                        placeholder="" class="ui-column-filter" style="width: 100px;">
            </th>
            <th>
                  <input pInputText type="text" (input)="dt.filter($event.target.value, 'country', 'startsWith')"
                        placeholder="" class="ui-column-filter" style="width: 100px;">
            </th>
            <th>
                  <input pInputText type="text" (input)="dt.filter($event.target.value, 'city', 'startsWith')"
                        placeholder="" class="ui-column-filter" style="width: 100px;">
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>

            <tr>

            </tr>
      </ng-template>
      <ng-template pTemplate="body" let-adv>
            <tr>

                  <td (click)="showJobAdv(adv)">
                        <!-- <i style="padding-right: 100px; " title="show job adv" class="fa fa-eye" 
                              (click)="showJobAdv(adv)"></i> -->

                        <div *ngIf="adv['opportunity_for']==='my_company'" class="verification-status">
                              <img *ngIf="adv['company_verified'] === 'Golden Verified'"
                                    src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner"
                                    pTooltip="Gold Partner">
                              <img *ngIf="adv['company_verified'] === 'Silver Verified'"
                                    src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner"
                                    pTooltip="Silver Partner">
                              <img *ngIf="adv['company_verified'] === 'Verified'"
                                    src="./assets/images/verified-company.svg" alt="Verified company"
                                    pTooltip="Verified Company">

                        </div>
                        <div *ngIf="adv['opportunity_for']==='other_company'" class="verification-status">
                              <img *ngIf="adv['company_verified'] === 'Golden Verified'"
                                    src="./assets/images/icons/cveek-gold-partner-recruiting-company.svg"
                                    alt="CVeek gold partner recruiting company" pTooltip="Verified recruiting company"
                                    tooltipStyleClass="wide-tooltip">
                              <img *ngIf="adv['company_verified'] === 'Silver Verified'"
                                    src="./assets/images/icons/cveek-silver-partner-recruiting-company.svg"
                                    alt="CVeek silver partner recruiting company" pTooltip="Verified recruiting company"
                                    tooltipStyleClass="wide-tooltip">
                              <img *ngIf="adv['company_verified'] === 'Verified'"
                                    src="./assets/images/icons/verified-recruiting-company.svg"
                                    alt="Verified recruiting company" pTooltip="Verified recruiting company"
                                    tooltipStyleClass="wide-tooltip">
                              <img *ngIf="adv['company_verified'] === null"
                                    src="./assets/images/icons/recruiting-company.svg" alt="Recruiting company"
                                    pTooltip="Recruiting company">
                        </div>
                  </td>

                  <td (click)="showJobAdv(adv)">
                        <div>

                              <img alt="Logo" [src]="getImageLogo(adv)" />
                              <span> {{ adv.company_name | summary:15}}</span>

                        </div>


                  </td>
                  <td (click)="showJobAdv(adv)">
                        {{ adv.job_adv_title | summary:15}}
                  </td>
                  <td (click)="showJobAdv(adv)">

                        {{ adv.publish_date }}
                  </td >
                  <td (click)="showJobAdv(adv)">
                        {{ adv.country | summary:15 }}
                  </td>
                  <td (click)="showJobAdv(adv)">
                        {{ adv.city | summary:15 }}
                  </td>

                  <td>





                        <i *ngIf="status=='ended-advs'" title="active job adv" class="fa fa-repeat "
                              (click)="showActiveDialog(adv.id)"></i>
                        <i *ngIf="status=='active-advs'" title="stop job adv" class="fa fa-power-off"
                              (click)="showEndedDialog(adv.id)" data-toggle="modal" data-target="#showEndAdvModal"></i>
                        <i style="padding: 10px;" *ngIf="status=='ended-advs'" title="active job adv"
                              class="fa fa-info-circle" (click)="showAdvEndedLogDialog(adv.id)" data-toggle="modal"
                              data-target="#showEndAdvLogModal"></i>

                  </td>


            </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
            <tr>
                  <td style="text-align:center;padding:15px;"> No advs found.</td>
            </tr>
      </ng-template>
</p-table>


<!-- In Manage Active advs -->
<div class="modal fade" id="showEndAdvModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
      aria-hidden="true">
      <div class="modal-dialog" role="document">
            <div class="modal-content">
                  <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Stop Job Advertisement</h4>
                  </div>
                  <div class="modal-body">
                        <div class="row">
                              <div class="col-md-8 col-xs-8">
                                    <p-dropdown [options]="end_job_adv_reasons" optionLabel="name" [filter]="true"
                                          filterMatchMode="startsWith" placeholder="Select Advertisement end Category"
                                          #reason (onChange)="endReasonChange(reason)"
                                          (keydown)="endAdvReasonIdErrorMessage = null">
                                          <ng-template let-reason pTemplate="item">
                                                <div class="ui-helper-clearfix"
                                                      style="position: relative;height: 25px;">
                                                      <div style="font-size:14px;float:left;margin-top:4px">
                                                            {{reason.label}}</div>
                                                </div>
                                          </ng-template>
                                    </p-dropdown>
                                    <div class="error-message">
                                          <span *ngIf="endAdvReasonIdErrorMessage">{{endAdvReasonIdErrorMessage}}</span>
                                    </div>
                                    <div class="div-margin-top-20">
                                          <textarea [(ngModel)]="reason_comment" class="form-control"
                                                placeholder="reason"> </textarea>
                                    </div>
                                    <div class="error-message">
                                          <span *ngIf="endAdvCommentErrorMessage">{{endAdvCommentErrorMessage}}</span>
                                    </div>
                              </div>

                              <div class="col-xs-8 text-center div-margin-top-30 div-margin-bo-10">
                                    <button type="button" class="btn btn-secondary"
                                          data-dismiss="modal">Cancel</button>&nbsp;&nbsp;
                                    <button type="button" class="btn btn-success" (click)="endJobAdv()">Confirm</button>
                              </div>
                        </div>
                  </div>
            </div>
      </div>
</div>


<!-- In Manage Ended advs -->
<div class="modal fade" id="showEndAdvLogModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
      aria-hidden="true">
      <div class="modal-dialog" role="document">
            <div class="modal-content">
                  <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Log Ended Job Advertisement</h4>
                  </div>
                  <div class="modal-body">
                        <div class="">
                              <table class="table table-striped table-bordered log-table" id="msg-details">
                                    <caption class="text-center table-caption"> Log for Advertisement: ({{ advEndedTitle
                                          }}) </caption>
                                    <thead>
                                          <tr>
                                                <th class="text-left">Reason Category </th>
                                                <th class="text-left">Reason Text</th>

                                          </tr>
                                    </thead>
                                    <tbody>
                                          <ng-container *ngIf="advEndedLog.length !== 0">
                                                <tr *ngFor="let item of advEndedLog">
                                                      <td>{{ item.adv_end_category }} </td>
                                                      <td>{{ item.reason }} </td>

                                                </tr>
                                          </ng-container>
                                          <tr *ngIf="advEndedLog.length === 0">
                                                <td colspan="5" class="text-center">This log is Empty </td>
                                          </tr>
                                    </tbody>
                              </table>
                        </div>
                  </div>
            </div>
      </div>
</div>