.page-title {
    padding: 0 2% 25px;
    font-size: 1.3rem;
    color: #276ea4;
    padding-top: 20px;
}

.page-title .fa {
    font-size: 2rem;
    vertical-align: middle;
    padding: 0 15px;
}
.page-navbar {
  font-size: 16px;
  width: 100%;
  list-style: none;
  background: #f2f2f2;
  z-index: 9999;
  text-align: center;
  border-bottom: 1px solid #ddd;
}
.page-navbar2{
  font-size: 17px;
  width: 100%;
  list-style: none;
  z-index: 9999;
  background: white;
  text-align: left;
  border-bottom: none;
  padding-right: 15px;
}


.page-navbar ul {
  margin-bottom: 0;
  padding: 0px 0px 10px 0px;
}

.page-navbar ul li {
  display: inline-block;
  padding: 5px 0px 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  
}
.page-navbar2 ul {
  margin-bottom: 0;
  width: 100%;
  height: 100%;
}

.page-navbar2 ul li {
  display: inline-block;
  margin-right: 20px;
  width: 100%;

}


.page-navbar ul li::after {
  background-color: white;
}

.page-navbar ul li a {
  text-decoration: none;
  transition: all .4s ease;
  color: #276ea4 !important;

}


.left-col-preview-alignright p,
.date-col,
.left-col-preview p {
    text-align: right;
    color: #276ea4;
}
.new{
  padding-left: 40px !important;
}

.CForm .info {
    color: #276ea4;
    font-size: 16px;
}

.CForm .preview {
    font-size: 16px;
}

.CForm .secondaryp {
    color: #276ea4;
}

.user-pro-pic,
.user-pro-pic:hover {
    text-decoration: none;
}

.user-pro-pic img {
    margin: auto;
    width: 120px; 
}

body {
    position: fixed;
}

@media screen and (max-width:768px) {
    .flex-row{
      display: flex;
      flex-wrap: wrap;
    }
    .flex-order-sm-1 {
        order: 1;
    }
    .flex-order-sm-2 {
        order: 2;
    }
    .user-pro-pic{
      margin-bottom: 35px;
    }
    
}
/*add*/
body{
    background-color: #eee !important;
  }
  #page-content-wrapper {
    transition: margin-left .5s ease;
  }
  
  #page-content-wrapper .page-content {
  font-family: 'Exo2-Regular', sans-serif !important;
  
    padding: 0px 15px 0px;
  }
  
  .table-page-content {
    position: relative;
  }
  .custom-checkbox input {
    width: 80%;
    height: 23px !important;
    border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));
  
  }
  
  
  .table-page-content .page-title {
    position: absolute;
    background: white;
    padding-right: 30px;
    z-index: 1;
  }
  
  .CForm {
    padding: 0 30px;
    margin-top:20px !important;
    margin-bottom: 20px;
  }
  
  
  .input-group[class*=col-] {
    float: left !important;
    padding-right: 5px;
    padding-left: 4px;
  }
  
  .strike2 {
    border-right: 1px solid gray
  }
  
  .pad-top {
    padding-top: 20px;
  }
  .pad-top6{
    padding-top:12px;
  }
  
  .has-error .error-message {
    color: #a94442;
    position: absolute;
    left: 70%;
    top: 10px;
    width: 60%;
  }
  .editing{
  color:red;
  background-color: red;
  }
  .glyphicon{
    left:80%;
  }
  .pad-top1{
    padding-top: 30px;
  }
  
  .pad-top2 {
    padding-top: 75px;
  }
  .pad-top3{
    padding-top: 35px;
  }
  .pad-top4{
    padding-top: 25px;
  }
  .custom-btn{
    width:100px; 
    background-color: #30457c;
    border-color: #30457c; 
    border-radius: 0;
  }
  .e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
  .e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
  .e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
  .e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
  .e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
  .e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
  .e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
  .e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after {
    background: #276ea4;
  }
  
  .e-multi-select-wrapper input.e-dropdownbase::-webkit-input-placeholder {
    color: #999;
    font-size: 16px;
    padding-left: 14px;
  }
  
  .e-multi-select-wrapper .e-searcher {
    width: 50%;
  }
  
  #title {
    display: none;
  }
  
  #subtitle {
    display: none;
  }
  
  #warning{
    position: relative;
    
  }
  
  :host{
    background-color: #eee
    !important;
  }
  .container-fluid{
    background-color: #eee;
    background-size: 100% 100%;
    }  
  .container-fluid::after{
    background-color: #eee !important;
  }
  .add{
  background-color: white;
  position: absolute;
    width: 62%;
    left:410px;
  height: 78%;
   border-radius: 10px;
   padding-top: 30px;
   padding-left: 40px;
      font-family: 'Exo2-Regular', sans-serif !important;
     }
  .details{
    background-color: white;
    border-radius: 10px;
    padding-left: 0px;
    font-family: 'Exo2-Regular', sans-serif !important;
    margin: auto;
  }
  .lan{
    border-radius: 10px;
    padding-left: 40px;
    padding-top:60px;
    font-family: 'Exo2-Regular', sans-serif !important;
    text-align: center;
  }
  .info1{
    padding-left: 30px;
    // padding-top:80px;
  }
  .editStyle{
    margin-bottom: 20px;
  }
  .ng5{
    z-index: 1; margin-top:10px; height: 2px; width:73%;
  }
   .wizardButton{
      position: static;
       margin-left: 80px; 
       margin-bottom:10px; 
  }
  .next{
   background-color: #3d7bce;
    border: #3d7bce solid 1px;
    margin-right: 4px;
  }
  .back{
   background-color: #959595;
   border:#959595 solid 1px;
   margin-right: 4px;
  }
  .finish{
   background-color: #30A03E;
    border: #30A03E solid 1px;
  }
  .wizardButton{
   padding:0px 0px 0px 0px;
  }
  .wizardButton button{
   border-radius: 2px;
   width: 70px;
  }
  .wizardButton button:hover{
   opacity: 0.8;
  }
  .labelAdd{
    font-size: 15px;
    color: #4f94df;
     text-align: right; 
  }
  .details .check{
   
  }
  .custom-checkbox4{
    margin-left: -30px;
  }
  .custom-checkbox3{
    margin-left: -18px;
  }
  .company-name{
  text-align: center; margin-top:70px; margin-left: -60px;
  }
  .company-industry{
  text-align: center; margin-left: -60px;
  }
  .extra-check{
    width: 20px; height: 20px; margin-top: 2px;
  }
  .check{
    border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));
  
  }
  .margin1{
    margin-left: -1px;
  }
  .margin2{
    margin-left: -2px;
  }
  :host ::ng-deep .ng5-slider .ng5-slider-pointer{
    width:20px;
    height: 20px;
  }
  :host ::ng-deep .ng5-slider .ng5-slider-pointer:after{
    top:6px;
    left:6px;
  
  }
  @media screen and (max-width: 900px) {
    #salary {
        right: 10%;
    }
  }
  
  @media only screen and (max-width: 767px) {
     table,
    thead,
    tbody,
    th,
    td,
    tr {
        display: block;
    } 
    #page-content-wrapper .page-content {
    }
    .margin-bo-mo-10 {
        margin-bottom: 10px;
    }
    .custom-control-labels {
        padding-top: 8px;
  
    }
    .custom-btn{
        width: 70px;
    }
    
    .add{
      left:160px;
      width: 78%;
  
    }
    
    .wizardButton button{
      width:60px ;
      text-align: center;
    }

    .details{
      font-size: 10px;
      width:100%;
      margin-left: 0px;
    }
    .details .labelAdd{
      font-size: 15px;
      padding-left:0px;
    }
    .details .custom-checkbox4{
      margin-left: -20px;
    }
    .details .custom-checkbox3{
      margin-left: -5px;
    }
    .details .check{
      width:15px !important;
      height: 15 !important;
      margin-left:7px;
    }
    .company-name{
      font-size: 15px;
      margin-left: -15px;
    }
    .company-industry{
      font-size: 13px;
      margin-left: -18px;
    }
    .extra-check{
      width: 15px;
      height: 15px;
    }
    .ng5{
      width: 79%;
    }
    .companyName{
      font-size: 14px;
      margin-top: 10px;
    }

    .page-title{
      font-size: 1.1rem;
    }
    .page-title .fa{
        font-size: 1.5rem;
        padding: 0;
        margin-right: 10px;
    }
  }
  
  @media only screen and (min-width: 768px) and (max-width: 1022px) {
    #save {
        margin-left: 46px;
        margin-top: 10px;
    }
   
  }
  @media only screen and (max-width: 1000px){
    .CForm{
      padding:0;
    }
  }
  
   table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    border: 1px solid #ddd;
  }
  
  
  th,
  td {
    text-align: left;
    padding: 5px;
    padding-bottom: 0px;
    background-color: white;
  }
  
  tr:nth-child(even) {
    background-color: #f2f2f2
  } 
  th{
    text-align: center;
  }
  
  
  /* @media only screen and (max-width: 760px){
    th:last-of-type {
    display: none;
    }
  } */
  :host ::ng-deep aw-wizard {
    display: flex !important;
    justify-content: flex-start !important; }
  
  :host ::ng-deep aw-wizard .wizard-steps {
      top: 0 !important;
      display: flex !important; }
  
  :host ::ng-deep aw-wizard.vertical {
    flex-direction: row !important;
   }
  
  :host ::ng-deep aw-wizard.vertical .wizard-steps {
      min-width: calc(100% - 280px) !important;
      width: 50% !important;
      height: 100% !important;
      flex-direction: column !important; }
  
      :host ::ng-deep aw-wizard-step,
      :host ::ng-deep aw-wizard-completion-step {
    height: auto !important;
    width: 100% !important; }
  
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator * {
    -webkit-box-sizing: border-box !important;
    -moz-box-sizing: border-box !important;
    box-sizing: border-box !important; 
  background-color: white;
}
    :host ::ng-deep aw-wizard-navigation-bar ul{
      background-color: white;
      width: 350px;
      border-radius: 10px;
      height: 78%;
      border: 1px solid #bbb !important;

    }
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator{
      margin-bottom: 100px;
  
    }
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li {
      margin:30px;
    position: relative !important;
    pointer-events: none !important; }
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li:hover ul.steps-indicator{
      background-color:#30457c ;
  }
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li a .label {
      color: #30457c !important;
      line-height: 20px !important;
      font-size: 16px !important;
      text-transform: none;
      text-decoration: none !important;
    font-family: 'Exo2-Regular', sans-serif !important; }
  
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable {
    pointer-events: auto !important; }
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable a:hover {
      cursor: pointer !important;
    text-decoration: none; }
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable a:hover .label {
      color: #30457c !important; }
  
    :host ::ng-deep aw-wizard-navigation-bar.vertical {
    max-width: 280px !important;
    width: 50% !important;
    height: 100% !important; 
    position: sticky !important;
    top: 0 !important; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical:hover {
      background-color: #eee !important;
    }
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator {
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      list-style: none !important;
      margin: auto !important;
    
    z-index: 101 !important;
  }
    
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .step-indicator {
      background-color: #30457c !important;
      visibility: hidden;
  
    }
    /*:host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.done a:hover .step-indicator {
      background-color: #30A03E !important;
  
    }*/
    
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .label {
      background-color: #eee !important;
  
    }
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .label p {
      background-color: #eee !important;
  
    }
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.current  a .label{
      background-color: #eee;
    }
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.current  a .label p{
      background-color: #eee;
    }
    /*:host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.done  a .label{
      color: #30A03E !important;
    }*/
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li:not(:last-child) {
        margin-bottom: 0 !important;
        padding-bottom: 20px !important; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important; }
        aw-wizard-navigation-bar.vertical ul.steps-indicator li a .label {
          text-align: left !important; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical ul.steps-indicator li a .label {
            margin-left: 0 !important;
            margin-right: 15px !important;
            text-align: right !important; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator {
    padding: 5px 5px 5px 5px;
    display: none;
    visibility: hidden;
  }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator {
      padding: 5px 5px 5px 5px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li:not(:last-child):after {
      background-color: #E6E6E6;
      content: '';
      position: absolute;
      left: -25px;
      top: 0px;
      height: calc(100% - 50px);
      width: 0px; }
      :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li:not(:last-child):after {
        left: auto;
        right: -25px; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li a {
      min-height: 50px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li .step-indicator {
      top: 0;
      left: -10px;
      position: absolute;
      width: 25px;
      height: 25px;
      text-align: center;
      vertical-align: middle;
      line-height: 46px;
      border-radius: 50%;
      border: 1px solid #30457c;
    display: none;}
      :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li .step-indicator {
        left: auto;
        right: -50px;}
        /*:host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.optional .step-indicator {
      border: 2px solid #30A03E; }*/
      /*:host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.done .step-indicator {
      border: 2px solid #30A03E;}*/
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.current .step-indicator {
      border: 2px solid #808080; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.editing .step-indicator {
      border: 2px solid #FF0000; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.completed .step-indicator {
      border: 2px solid #339933; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable a:hover .step-indicator {
      position: absolute;
      width: 50px;
      height: 50px;
      text-align: center;
      vertical-align: middle;
      line-height: 46px;
      border-radius: 100%;
      border: 2px solid #30457c; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.optional a:hover .step-indicator {
      border: 2px solid #30457c; }
      /*:host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.done a:hover .step-indicator {
      border: 2px solid #30A03E !important; }*/
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.current a:hover .step-indicator {
      border: 2px solid #676767; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.editing a:hover .step-indicator {
      border: 2px solid #cc0000; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.completed a:hover .step-indicator {
      border: 2px solid #267326; }
  
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator {
    padding: 5px 5px 5px 50px; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator {
      padding: 5px 55px 5px 5px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li:not(:last-child):after {
      background-color: #E6E6E6;
      content: '';
      position: absolute;
      left: -25px;
      top: 50px;
      height: calc(100% - 50px);
      width: 0px; }
      :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li:not(:last-child):after {
        left: auto;
        right: -25px; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li a {
      min-height: 50px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li .step-indicator {
      top: 0;
      left: -50px;
      position: absolute;
      width: 50px;
      height: 50px;
      text-align: center;
      vertical-align: middle;
      line-height: 50px;
      border-radius: 100%;
      background-color: #30457c;
      color: black; }
      :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li .step-indicator {
        left: auto;
        right: -50px; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.optional .step-indicator {
      background-color: #38ef38;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.done .step-indicator {
      background-color: #339933;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.current .step-indicator {
      background-color: #808080;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.editing .step-indicator {
      background-color: #FF0000;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.completed .step-indicator {
      background-color: #339933;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable a:hover .step-indicator {
      position: absolute;
      width: 50px;
      height: 50px;
      text-align: center;
      vertical-align: middle;
      line-height: 50px;
      border-radius: 100%;
      background-color: #30457c;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.optional a:hover .step-indicator {
      background-color: #20ed20;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.done a:hover .step-indicator {
      background-color: #2d862d;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.current a:hover .step-indicator {
      background-color: #737373;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.editing a:hover .step-indicator {
      background-color: #e60000;
      color: black; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.completed a:hover .step-indicator {
      background-color: #2d862d;
      color: black; }
  
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator {
    padding: 0px 0px 0px 50px; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator {
      padding: 5px 5px 5px 5px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li:not(:last-child):after {
      background-color: #E6E6E6;
      content: '';
      position: absolute;
      left: -25px;
      top: 50px;
      height: calc(100% - 50px);
      width: 0px; }
      :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li:not(:last-child):after {
        left: auto;
        right: -25px; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li a {
      min-height: 50px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
      top: 0;
      left: -50px;
      position: absolute;
      width: 50px;
      height: 50px;
      text-align: center;
      vertical-align: middle;
      line-height: 46px;
      border-radius: 100%;
      border: 2px solid #E6E6E6;
      color: #E6E6E6; }
      :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
        left: auto;
        right: -50px; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.optional .step-indicator {
      border: 2px solid #30457c;
      color: #30457c; }
      /*:host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.done .step-indicator {
      border: 2px solid #30A03E;
      color:#30A03E; }*/
      :host ::ng-deep .red aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
        border: 2px solid red;
        color:red; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.current .step-indicator {
      border: 2px solid white;
      color: white;
      background-color:#30457c ;
        }
      
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.completed .step-indicator {
      border: 2px solid red ;
      color: red; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable a:hover .step-indicator {
      position: absolute;
      width: 50px;
      height: 50px;
      text-align: center;
      vertical-align: middle;
      line-height: 46px;
      border-radius: 100%;
      border: 2px solid #eee ;
      color: #eee; 
    background-color: #30457c;}
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.optional a:hover .step-indicator {
      border: 2px solid #eee;
      color: #eee; 
    background-color: #30457c;}
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.done a:hover .step-indicator {
      border: 2px solid #eee;
      color: #eee;
    background-color: #30457c; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.current a:hover .step-indicator {
      border: 2px solid #eee;
      color: #eee; 
    background-color: #30457c;}
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.editing a:hover .step-indicator {
      border: 2px solid #cc0000;
      color: #cc0000; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.red a:hover .step-indicator {
        border: 2px solid #cc0000;
        color: #cc0000; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.completed a:hover .step-indicator {
      border: 2px solid #267326;
      color: #267326; }
  /*responsive*/
  @media only screen and (max-width: 767px) {
    :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li a .label {
      display:none !important;
    }
    :host ::ng-deep aw-wizard-navigation-bar.vertical {
      width:20% !important;
    }
    :host ::ng-deep aw-wizard.vertical .wizard-steps {
      width: 100% !important;
      max-width: 380px !important;
    }
    :host ::ng-deep aw-wizard-navigation-bar ul{
      background-color: white;
      width: 100px;
      border-radius: 10px;
      height: 75%;}
  }
  
  
  :host ::ng-deep .ui-table-customers {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);
  
    .customer-badge {
        border-radius: 2px;
        padding: .25em .5em;
        text-transform: uppercase;
        font-weight: 700;
        font-size: 12px;
        letter-spacing: .3px;
       
        &.status-qualified {
            background-color: #4CAF50; ;
            color: #FFF;
            padding-left: 12px;
            padding-right: 12px;
        }
  
        &.status-unqualified {
            background-color:#C63720 ;
            color: #FFF;
        }
  
        &.status-negotiation {
            background-color: #FEEDAF;
            color: #8A5340;
        }
  
        &.status-new {
            background-color: #B3E5FC;
            color: #23547B;
        }
  
        &.status-renewal {
            background-color: #ECCFFF;
            color: #694382;
        }
  
        &.status-proposal {
            background-color: #FFD8B2;
            color: #805B36;
        }
    }
  
    .flag {
        vertical-align: middle;
        width: 30px;
        height: 20px;
    }
  
    .ui-multiselect-representative-option {
        display: inline-block;
        vertical-align: middle;
  
        img {
            vertical-align: middle;
            width: 24px;
        }
  
        span {
            margin-top: .125em;
            vertical-align: middle;
            margin-left: .5em
        }
    }
  
    .ui-paginator {
        .ui-dropdown {
            float: left;
        }
  
        .ui-paginator-current {
            float: right;
        }
    }
  
    .ui-progressbar {
        height: 8px;
        background-color: #D8DADC;
  
        .ui-progressbar-value {
            background-color: #00ACAD;
        }
    }
  
    .ui-column-filter {
        display: block;
        font-weight: 500;
        width: 80px;
        border-radius: 2px;
        border:none;
  
        input {
            width: 100%;
            border-radius: 10px;
        }
    }
  
    .ui-table-globalfilter-container {
        float: right;
        font-weight: 300;
        border-radius: 5px;
  
        input {
            width: 200px;
            border-radius: 2px;
  
        }
    }
  
    .ui-datepicker {
        min-width: 25em;
  
        td {
            font-weight: 400;
        }
    }
  
    .ui-table-caption {
        border: 0 none;
        padding: 12px;
        text-align: left;
        font-size: 17px;
    }
  
    .ui-paginator {
        border: 0 none;
        padding: 1em;
    }
  
    .ui-table-thead > tr > th {
        border: 0 none;
        text-align: left;
        font-size: 15px;
    
  
        &.ui-filter-column {
            border-top: 1px solid #c8c8c8;
        }
  
        &:first-child {
            width: 5em;
            text-align: center;
        }
  
        &:last-child {
            width: 8em;
            text-align: center;
        }
    }
  
    .ui-table-tbody > tr > td {
        border: 0 none;
        cursor: auto;
  
        &:first-child {
            width: 3em;
            text-align: center;
        }
  
        &:last-child {
            width: 8em;
            text-align: center;
        }
    }
  
    .ui-dropdown-label:not(.ui-placeholder) {
        text-transform: uppercase;
    }
  
    .ui-table-tbody > tr > td .ui-column-title {
        display: none;
    }
  }
  //
  .red{
    color:red !important;
  }
  .break-long-url{
    word-break: break-word;
    color:#333;
  }