import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/take';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { take } from 'rxjs/operators';


@Injectable()
export class AiJobGeneratorService {
  private aiGeneratorUrl = '';

  constructor(
    private http: HttpClient,
    private privateSharedURL: ExportUrlService
  ) {
    this.privateSharedURL.publicUrl
      .pipe(take(1))
      .subscribe((url: string) => {
        this.aiGeneratorUrl = url +'job-advertisement/ai-generate';
      });
  }

  generateJobDescription(Text: string): Observable<any> {
    return this.http.post(this.aiGeneratorUrl, { 'text': Text });
  }
}

