import { Component, OnInit } from '@angular/core';
import {FormArray, FormControl, FormGroup} from '@angular/forms';
import 'rxjs/add/operator/debounceTime';
import 'rxjs/add/operator/map';
import "rxjs/add/operator/distinctUntilChanged";
@Component({
  selector: 'app-array-storage',
  templateUrl: './array-storage.component.html',
  styleUrls: ['./array-storage.component.css']
})
export class ArrayStorageComponent implements OnInit {
  form;
  constructor() {
    let retrievedObject = localStorage.getItem('test2Object');
    this.form= new FormGroup({
      topics:new FormArray([])
    });

    if(retrievedObject){
      let formObject =JSON.parse(retrievedObject);
      for(let topic of formObject.topics){
        this.Topics.push(new FormControl(topic));
      }

    }

  }

  ngOnInit() {
    this.form.valueChanges.debounceTime(1200)
      .distinctUntilChanged()
      .subscribe(()=>{
        localStorage.setItem('test2Object', JSON.stringify(this.form.value));
      });
  }

  addTopic(Topic:HTMLInputElement){
    this.Topics.push(new FormControl(Topic.value));
    Topic.value='';

  }
  removeTopic(index){
    this.Topics.removeAt(index);

  }

  get Topics(){
    return this.form.get('topics')as FormArray;
  }

}
