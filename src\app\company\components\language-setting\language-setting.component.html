<div class="container-fluid">
    <!--<div class="preloader" *ngIf="employmentTypeOpt.length === 0">
        <div class="infinity">
            <div>
                <span></span>
            </div>
            <div>
                <span></span>
            </div>
            <div>
                <span></span>
            </div>
        </div>
    </div>-->
    <div class="page-navbar page-navbar2">
     <aw-wizard [awNavigationMode] navigateBackward="allow" navigateForward="allow" 
    [navBarLocation]="'left'" >
        <aw-wizard-step >
            <div class="col-sm-1 col-xs-1">
                <ng-template awWizardStepTitle let-wizardStep="wizardStep" 
                
                >  
                    {{ wizardStep.completed ? "Company Profile" : "Company Profile" }}
                    <p style="color:#ccc; font-size: 12px;">Select Company Profile Languages</p>
                  </ng-template>
                    </div>
                    <div class="col-sm-1"></div>
            <div class="col-sm-9 col-xs-11 details">
                <div class="row">
                    <h4 style="color:grey;margin-bottom: 20px"> Company Profile</h4>
                    <p style="color:#bbb; margin-bottom:40px"> description</p>
                    <h4 style="color: black; font-weight: bold;" class="col-sm-4"> Default Language</h4>
                    <p-dropdown class="col-sm-3" [options]="groupedCars" [(ngModel)]="selectedCar" filter="true">
                        <ng-template let-item pTemplate="selectedItem">
                            <img src="assets/showcase/images/demo/car/{{item.label}}.png" style="width:16px;vertical-align:middle" />
                            <span style="vertical-align:middle; margin-left: .5em">{{item.label}}</span>
                        </ng-template>
                        <ng-template let-car pTemplate="item">
                            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <img src="assets/showcase/images/demo/car/{{car.label}}.png" style="width:24px;position:absolute;top:1px;left:5px"/>
                                <div style="font-size:14px;float:right;margin-top:4px">{{car.label}}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                
                
                </div>
                <hr>
                <div class="row">
                    <p style="color:#bbb; margin-bottom:40px"> description</p>
                    <h4 class="col-sm-4" style="color: black; font-weight: bold;"> Other Languages</h4>
                    <p-dropdown class="col-sm-3" [options]="groupedCars" [(ngModel)]="selectedCar" filter="true">
                        <ng-template let-item pTemplate="selectedItem">
                            <img src="assets/showcase/images/demo/car/{{item.label}}.png" style="width:16px;vertical-align:middle" />
                            <span style="vertical-align:middle; margin-left: .5em">{{item.label}}</span>
                        </ng-template>
                        <ng-template let-car pTemplate="item">
                            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <img src="assets/showcase/images/demo/car/{{car.label}}.png" style="width:24px;position:absolute;top:1px;left:5px"/>
                                <div style="font-size:14px;float:right;margin-top:4px">{{car.label}}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div class="col-xs-1  custom-col-1">
                        <button class="btn btn-primary" >
                          <i class="fa fa-plus" aria-hidden="true"></i>
                        </button>
                      </div>
                </div>
                </div>    
                </aw-wizard-step>
        <aw-wizard-step >
            <div class="col-sm-1 col-xs-1">
                <ng-template awWizardStepTitle let-wizardStep="wizardStep" 
                
                >  
                    {{ wizardStep.completed ? "Advertisements " : "Advertisements" }}
                    <p style="color:#ccc; font-size: 12px;">Select Advertisement Languages</p>
                  </ng-template>
                    </div>
                    <div class="col-sm-1"></div>

            <div class="col-sm-9 col-xs-11 details">
                <div class="row">
                    <h4 style="color:grey;margin-bottom: 20px"> Advertisements</h4>
                    <p style="color:#bbb; margin-bottom:40px"> description</p>
                    <h4 style="color: black; font-weight: bold;" class="col-sm-4"> Default Language</h4>
                    <p-dropdown class="col-sm-3" [options]="groupedCars" [(ngModel)]="selectedCar" filter="true">
                        <ng-template let-item pTemplate="selectedItem">
                            <img src="assets/showcase/images/demo/car/{{item.label}}.png" style="width:16px;vertical-align:middle" />
                            <span style="vertical-align:middle; margin-left: .5em">{{item.label}}</span>
                        </ng-template>
                        <ng-template let-car pTemplate="item">
                            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <img src="assets/showcase/images/demo/car/{{car.label}}.png" style="width:24px;position:absolute;top:1px;left:5px"/>
                                <div style="font-size:14px;float:right;margin-top:4px">{{car.label}}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                
                
                </div>
                <hr>
                <div class="row">
                    <p style="color:#bbb; margin-bottom:40px"> description</p>
                    <h4 class="col-sm-4" style="color: black; font-weight: bold;"> Other Languages</h4>
                    <p-dropdown class="col-sm-3" [options]="groupedCars" [(ngModel)]="selectedCar" filter="true">
                        <ng-template let-item pTemplate="selectedItem">
                            <img src="assets/showcase/images/demo/car/{{item.label}}.png" style="width:16px;vertical-align:middle" />
                            <span style="vertical-align:middle; margin-left: .5em">{{item.label}}</span>
                        </ng-template>
                        <ng-template let-car pTemplate="item">
                            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                <img src="assets/showcase/images/demo/car/{{car.label}}.png" style="width:24px;position:absolute;top:1px;left:5px"/>
                                <div style="font-size:14px;float:right;margin-top:4px">{{car.label}}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div class="col-xs-1  custom-col-1">
                        <button class="btn btn-primary" >
                          <i class="fa fa-plus" aria-hidden="true"></i>
                        </button>
                      </div>
                </div>
                </div>       
             </aw-wizard-step>
        
      </aw-wizard>
    </div>
    <!--
<div class="col-sm-3">
    <div class="page-navbar page-navbar2">
    <ul id="page-navbar-list">
        <li style="cursor: pointer;">
            <a > <span>Company Profile</span></a>
        </li>
        <li>
            <a routerLink=''><span>Advertisements</span></a>
        </li>
        
        </ul>
        </div>
</div>
<div class="col-sm-9 details">
<div class="row">
    <p style="color:#bbb; margin-bottom:40px"> description</p>
    <h4 style="color: black; font-weight: bold;" class="col-sm-4"> Default Language</h4>
    <p-dropdown class="col-sm-3" [options]="groupedCars" [(ngModel)]="selectedCar" filter="true">
        <ng-template let-item pTemplate="selectedItem">
            <img src="assets/showcase/images/demo/car/{{item.label}}.png" style="width:16px;vertical-align:middle" />
            <span style="vertical-align:middle; margin-left: .5em">{{item.label}}</span>
        </ng-template>
        <ng-template let-car pTemplate="item">
            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                <img src="assets/showcase/images/demo/car/{{car.label}}.png" style="width:24px;position:absolute;top:1px;left:5px"/>
                <div style="font-size:14px;float:right;margin-top:4px">{{car.label}}</div>
            </div>
        </ng-template>
    </p-dropdown>


</div>
<hr>
<div class="row">
    <p style="color:#bbb; margin-bottom:40px"> description</p>
    <h4 class="col-sm-4" style="color: black; font-weight: bold;"> Other Languages</h4>
    <p-dropdown class="col-sm-3" [options]="groupedCars" [(ngModel)]="selectedCar" filter="true">
        <ng-template let-item pTemplate="selectedItem">
            <img src="assets/showcase/images/demo/car/{{item.label}}.png" style="width:16px;vertical-align:middle" />
            <span style="vertical-align:middle; margin-left: .5em">{{item.label}}</span>
        </ng-template>
        <ng-template let-car pTemplate="item">
            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                <img src="assets/showcase/images/demo/car/{{car.label}}.png" style="width:24px;position:absolute;top:1px;left:5px"/>
                <div style="font-size:14px;float:right;margin-top:4px">{{car.label}}</div>
            </div>
        </ng-template>
    </p-dropdown>
    <div class="col-xs-1  custom-col-1">
        <button class="btn btn-primary" >
          <i class="fa fa-plus" aria-hidden="true"></i>
        </button>
      </div>
</div>
</div>-->
</div>