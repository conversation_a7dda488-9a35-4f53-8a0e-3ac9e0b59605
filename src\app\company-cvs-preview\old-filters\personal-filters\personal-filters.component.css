/* start of common filter styles */
 /* start custom p-multiselect styles */
:host ::ng-deep .cust-p-multiselect {
    border: 0;
    border-bottom: 1px solid #ccc;
    border-radius: 0;
    width: 100%;
    background: transparent;
}
:host ::ng-deep .cust-p-multiselect .ui-corner-right {
    border: 0;
}
:host ::ng-deep .cust-p-multiselect .ui-multiselect-label {
    color: #808080;
}
:host ::ng-deep .cust-p-multiselect:not(.ui-state-disabled):hover {
    border-color: #ccc;
}

:host ::ng-deep .cust-p-multiselect:focus {
    border: 3px solid #4f94df;
}

 /* end custom p-multiselect styles */
 /* unify placeholer style for all different controls */
::placeholder,
 :host ::ng-deep .ui-dropdown label.ui-dropdown-label,
:host ::ng-deep .ui-autocomplete ::placeholder {
    /* ::placeholder Chrome, Firefox, Opera, Safari 10.1+ */
    color: #808080;
    opacity: 1;
    /* Firefox */
    font-weight: normal;
    font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
    font-size: 15px !important;
}

.tag-label {
    color: #808080;
    background-color: #f2f2f2;
    padding: 3px;
    margin-left: 5px !important;
}
.equal-height-row-cols{
    display: flex;
    flex-flow: row wrap;
}
.label-fixed{
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    color:#4f94df;
    font-weight: normal;
}
.label-fixed-dd{
    align-items: center;
}
.label-fixed-checkbox{
    align-items: flex-start;
}
:host ::ng-deep .ng5-slider{
    margin:0;
    z-index: 1;
}
:host ::ng-deep.ng5-slider-bubble{
    font-size: 12px !important;
}
:host ::ng-deep.ng5-slider .ng5-slider-pointer{
    width: 15px;
    height: 15px;
    top: -5px; /* to remove the default positioning */
    bottom: 0px;
}
:host ::ng-deep.ng5-slider .ng5-slider-pointer-min::after{
    top:4.3px;
    left:2.9px;
}
:host ::ng-deep.ng5-slider .ng5-slider-pointer-max::after{
    top:4.3px;
    left:2.9px;
}
@media screen and (min-width:768px){
    .ng5-slider-div{
        min-height:0;
    }
}
@media screen and (max-width:767px){
    .focus-no-padding{
        padding-left: 15px !important;
    }
    .label-fixed{
        justify-content: flex-start;
    }
    :host ::ng-deep .ng5-slider{
        margin-top:35px;
    }
    .label-fixed-checkbox{
        margin-bottom: 5px;
    }
}
/* end of common filter styles */
.check-block{
    margin-bottom: 5px;
}
.check-block input{
    margin:0 5px 0 0;
    width: 15px;
    height: 15px;
    cursor: pointer;
}
.check-block label{
    margin:0;
    font-size:15px;
    cursor: pointer;
}
.checkbox-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.filters-form {
    padding: 2rem;
}

/* .form-group label {
    margin: 1rem;
} */

.ng5-slider {
    height: 1px;
    width: 70%;
}

.filter-multiselect {
    width: 65% !important;
}

.filter-text-field {
    width: 60%;
    border: none;
    border-bottom: 1px solid #0db9f0;
}