.search-div{
    display:inline-block;
}
:host ::ng-deep .search-input{
    width:98%;
    padding-right:0;
    height:32px;
}
.search-input {
    width: calc(100% - 80px);
    font-size: 15px;
    color: #686F7A;
    border-radius: 1px;
    border: 1px solid #c5c5c5;
    box-sizing: border-box;
    margin: 8px 0 8px 15px;
    height: 32px;
    /* padding-left: 20px;
    padding-right: 20px; */
    -webkit-appearance: none;
    font-weight: normal;
    transition: border .12s ease-in-out;
    background-color: white;
    display:inline-block;
  }
.search-btn , .search-btn:active , .search-btn:focus , .search-btn:hover{
    display:inline-block;
    background: transparent;
    outline: 0;
    box-shadow: none;
}
.search-btn{
    margin-bottom: 0;
    padding-bottom: 0;
    padding-top: 0;
}
.search-btn i{
    color:#fff;
    font-size: 24px;
}
:host ::ng-deep .ng-select-container .ng-placeholder {
    font-size:14px;
}
@media screen and (min-width: 992px){
    .search-div {
        width: 447px  !important;
    }
}
@media screen and (max-width: 991px){
    .search-input {
      width: calc(100% - 112px);
    }
    .search-div{
        width:250px;
    }
}
@media screen and (max-width: 767px) {
  .search-input{
    margin-left:0;
  }
  .search-div{
    display:inline-block;
    width:240px;
}
}
@media screen  and (max-width:470px){
    :host ::ng-deep .search-input .ng-dropdown-panel{
        width:150%;
    }
    .search-div{
        display:inline-block;
        width:210px;
    }
    .search-btn{
        padding-left: 5px;
        padding-right: 5px;
    }
    .search-btn i{
        font-size:20px;
    }
}
