import { Component, OnInit, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { ContactService } from 'app/admin/services/contact.service';
import { Table } from 'primeng/table';
import { FilterUtils } from 'primeng/utils';
import { Calendar } from 'primeng/calendar';
declare var $: any;

@Component({
  selector: 'app-message-archive',
  templateUrl: './message-archive.component.html',
  styleUrls: ['./message-archive.component.css']
})
export class MessageArchiveComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  @ViewChild('cl') calendar: Calendar;
  rangeDates: Date[];
  filteredMessages = [];
  displayDeleteModal = false;
  private ngUnsubscribe: Subject<any> = new Subject();
  messages = [];
  opperationNum;
  mainCats: any = [];
  subCats: any = [];
  msgIdToDelete: number;
  displayMsgModal: boolean;
  msgToPreview;
  admins = [];
  statuses = [
    {'label': '', 'value': '' },
    {'label': 'new', 'value': 'new' },
    {'label': 'opened', 'value': 'opened' },
    {'label': 'commented', 'value': 'commented' },
    {'label': 'done', 'value': 'done' },
    {'label': 'replied', 'value': 'replied' },
    {'label': 'deleted', 'value': 'deleted' }
  ];
  loading = true;
  main_cat;
  sub_cat;
  admin;
  constructor(private contact: ContactService) {
    FilterUtils['isBetween'] = (value: any, filter: any): boolean => {
      let newDate = new Date(value);
      console.log(newDate);
      filter = new Date(filter);
      if (this.rangeDates['1'] === null) {
          if (newDate === filter) {
            let startDate =new Date(this.rangeDates['0']);
            if (startDate <= newDate ) {
              return true;
             }
          }
      } else {
             let startDate =new Date(this.rangeDates['0']);
             let finishDate =new Date(this.rangeDates['1']);
             console.log(startDate, finishDate);
                  if (startDate <= newDate && finishDate >= newDate) {                                                 {
                       return true;
                  }
      }
    }
  };

  }

  ngOnInit() {
   this.getMessages();
   this.getAdmins();
  }

  getMessages() {
   this.contact.getMsgArchive().takeUntil(this.ngUnsubscribe).subscribe(res => {
     console.log('res', res);
     let temp  = res['messages'];
     let temp2 = res['contactMainCat'];
     let temp3 = res['contactSubCat'];
     let comments = [], replies = [], assign_log = [];
     for (let msg of temp) {
       comments   = msg.admin_comment;
       replies    = msg.admin_replied;
      //  assign_log = msg.admin_email_assign_log;
          this.messages.push({
            'id'         : msg.id,
            'year'       :msg.created_at.substr(0, 4), 
            'name'       : (msg.user === null) ? '.....' : msg.user.display_name,
            'type'       : (msg.user === null) ? 'ROLE_VISITOR' : msg.user.role,
            'message'    : msg.message,
            'main_cat_id': msg.contact_main_cat_id,
            'main_cat'   : msg.contact_main_cat,
            'sub_cat_id' : msg.contact_sub_cat_id,
            'sub_cat'    : (msg.contact_sub_cat === null) ? '....' : msg.contact_sub_cat,
            'email'      : msg.email,
            'langId'     : msg.translated_languages_id,
            'language'   : msg.translated_languages,
            'date'       : msg.created_at,
            // 'assign_log' : assign_log,
            'comments'   : comments,
            'replies'    : replies,
            'last_reply' : msg.last_reply,
            'handled_by' : msg.handled_by,
            'status'     : (msg.last_action_name === null ) ? 'new' : msg.last_action_name.toLowerCase(),
            'display'    : false
          });

     }
      // this.filteredMessages = this.messages;
      this.table.filter(1, 'id', 'startsWith');
      this.table.filter(null, 'id', 'startsWith');
      this.loading = false;
      for (let main of temp2) {
        if (main.contact_main_cat_translation.length !== 0) {
          this.mainCats.push({
            'label': main.contact_main_cat_translation[0].name,
            'value': main.contact_main_cat_translation[0].contact_main_cat_id
          });
        }
      }
      this.mainCats.unshift({ 'label': '', 'value': null });
      for (let sub of temp3) {
        if (sub.contact_sub_cat_translation.length !== 0) {
          this.subCats.push({
            'label'      : sub.contact_sub_cat_translation[0].name,
            'value'      : sub.contact_sub_cat_translation[0].contact_sub_cat_id,
            'main_cat_id': sub.contact_main_cat_id
          });
        }
      }
      this.subCats.unshift({ 'label': '', 'value': null, 'main_cat_id': null });
      console.log('archived msg', this.messages);
      console.log('main cats', this.mainCats);
      console.log('sub cats', this.subCats);

   });

  }

  getAdmins() {
    this.contact.getAdmins().takeUntil(this.ngUnsubscribe).subscribe(res => {
     console.log('adm res', res);
     for (let admin of res['data']) {
       this.admins.push({
         'id'   : admin.id,
         'value': admin.display_name,
         'label': admin.display_name
       });
     }
     this.admins.unshift({'value': null, 'label': '' });
     console.log('adms', this.admins);
    });
  }


  displayModal(msg) {
    this.displayMsgModal = true;
    this.msgToPreview = {
      'id'         : msg.id,
      'year'       :msg.year, 
      'name'       : msg.name,
      'type'       : msg.type,
      'message'    : msg.message,
      'main_cat_id': msg.main_cat_id,
      'main_cat'   : msg.main_cat,
      'sub_cat_id' : msg.sub_cat_id,
      'sub_cat'    : msg.sub_cat,
      'email'      : msg.email,
      'langId'     : msg.langId,
      'language'   : msg.language,
      'date'       : msg.date,
      // 'assign_log' : msg.assign_log,
      'comments'   : msg.comments,
      'replies'    : msg.replies,
      'last_reply' : msg.last_reply,
      'handled_by' : msg.handled_by,
      'status'     : msg.status

    };
    console.log('msg', this.msgToPreview);
  }


  closeModal() {
    this.displayMsgModal = false;
  }

  displayDeleteAlert( num, msgId?: number) {
    this.opperationNum = num;
    if (msgId) {
      this.msgIdToDelete = msgId;
    }
    this.displayDeleteModal = true;
  }

  restoreMsg(message) {
    this.contact.restoreMultiMsgs({ 'received_messages_ids': [ message.id] }).subscribe((res: number[]) => {
      console.log('restore res', res);
      let index = this.getMsgIndex(res[0]);
      this.messages.splice(index, 1);
      this.table._totalRecords = this.messages.length;

      console.log(index);
    });
  }

  restoreMsgs() {
    let msgsId = [];
    for (let msg of this.filteredMessages) {
      msgsId.push(msg.id);
    }
    this.contact.restoreMultiMsgs({ 'received_messages_ids':  msgsId }).subscribe((res: number[]) => {
      console.log('restore res', res);
      for (let i = 0; i < this.messages.length; i++) {
        for (let id of msgsId) {
          if (this.messages[i].id === id) {
            this.messages.splice(i, 1);
          }
        }
      }
      this.table._totalRecords = this.messages.length;
      this.filteredMessages = [];
    });
  }


  getMsgIndex(msgId) {
    for (let i = 0; i < this.messages.length; i++) {
      if (this.messages[i].id === msgId) {
        console.log('index', i);
        return i;
      }
    }
  }

  addToSelected(message) {
    console.log('filtered msg', this.filteredMessages);
  }

  closeDModal() {
    this.displayDeleteModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
  }

  deleteMsg() {
    this.contact.deleteMultiArchivedMsgs({ 'received_messages_ids':  [this.msgIdToDelete] }).subscribe(res => {
      console.log('delete res', res);
      if ((res['success'] as string) === 'false' ) {
        alert(res['messages']);
      } else {
      let index = this.getMsgIndex(this.msgIdToDelete);
      this.messages.splice(index, 1);
      console.log(this.msgIdToDelete);
        this.table._totalRecords = this.messages.length;
        this.filteredMessages = [];
      this.closeDModal();
      }
    });
  }

  deleteMultiMsgs() {
    let msgsId = [];
    for (let msg of this.filteredMessages) {
      msgsId.push(msg.id);
    }
    this.contact.deleteMultiArchivedMsgs({ 'received_messages_ids':  msgsId }).subscribe(res => {
      console.log('delete res', res);
      this.closeDModal();
      if ((res['success'] as string) === 'false' ) {
        alert(res['messages']);
      } else {
      // let index = this.getMsgIndex(res['data'].id);
      for (let i = 0; i < this.messages.length; i++) {
        for (let id of msgsId) {
          if (this.messages[i].id === id) {
            this.messages.splice(i, 1);
          }
        }
      }
      console.log(msgsId);
      this.table._totalRecords = this.messages.length;
      this.filteredMessages = [];
    }
    });
  }

  onDateSelect(value) {
    this.table.filter(this.formatDate(value), 'date', 'contains');
  }

  formatDate(date) {
    let month = date.getMonth() + 1;
    let day = date.getDate();

    if (month < 10) {
        month = '0' + month;
    }

    if (day < 10) {
        day = '0' + day;
    }

    return date.getFullYear() + '-' + month + '-' + day;
}

clearAll() {
  this.table.filter(null, 'id', 'startsWith');
  this.table.filter(null, 'year', 'startsWith');
  this.table.filter(null, 'email', 'contains');
  this.table.filter('', 'date', 'contains');
  this.table.filter('', 'main_cat_id', 'equals');
  this.table.filter('', 'sub_cat_id', 'equals');
  this.table.filter('', 'handled_by', 'equals');
  this.table.filterGlobal(null, 'contains');
  $('span.ui-column-filter.ui-calendar input').val(null);
  $('.ui-table-globalfilter-container input').val(null);
  $('.ui-column-filter').val(null);
   this.main_cat = '';
   this.sub_cat = '';
   this.admin = '';
   this.rangeDates = [new Date(''), new Date('')];
   this.calendar.onClearButtonClick('');
 }


  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }



}


