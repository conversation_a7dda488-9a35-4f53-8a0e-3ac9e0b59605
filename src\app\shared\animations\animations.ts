import {
    trigger,
    state,
    style,
    animate,
    transition,
    keyframes,
  } from '@angular/animations';

export let fade =  trigger('fade',[
    state('void', style({opacity:0}) ),
    transition(':enter, :leave', [ // void <=> *
      animate('1000ms 1000ms ease-in')
    ])
  ]);

  export let slide =  trigger('slide',[
    transition(':enter', [ 
      style({transform: 'translateX(-100%'}),
      animate('1000ms')  // timing,delay,easing
    ]),

    transition(':leave',[
        animate('0.5s ease-in',style({transform: 'translateX(-100%)'}))
    ])
  ]);

  export let UpDown =  trigger('UpDown',[
    transition(':enter', [ 
      animate('2000ms 1000ms ease-in-out', keyframes([
          style({
            offset:.5,
            top:'-250px',
          //  transform: 'translateY(-250px)'
          }),
          style({
            offset:1,
            top:0,
         //   transform: 'translateY(0)'
        }),
      ])) 
    ])
  ]);

  export let lightSpeedInLeft =  trigger('lightSpeedInLeft',[
    transition(':enter', [ 
      animate('1000ms 0.5s ease-out', keyframes([
          style({
            offset:0,
            transform: 'translate3d(-100%, 0, 0) skewX(30deg)',
            opacity: 0
          }),
          style({
            offset:0.6,
            transform: 'skewX(-20deg)',
            opacity: 1,
          }),
          style({
            offset:0.8,
            transform: 'skewX(5deg)'
          }),
          style({
            offset:1,
            transform: 'translate3d(0, 0, 0)'
          }),
      ])) 
    ])
  ]);

  export let backInLeft =  trigger('backInLeft',[
    transition(':enter', [ 
      animate('1000ms 0.5s', keyframes([
          style({
            offset:0,
            transform: 'translateX(-2000px) scale(0.7)',
            opacity: 0.7
          }),
          style({
            offset:0.8,
            transform: 'translateX(0px) scale(0.7)',
            opacity: 0.7
          }),
          style({
            offset:1,
            transform: 'scale(1)',
            opacity: 1
          }),
      ])) 
    ])
  ]);

  export let heartBeat =  trigger('heartBeat',[
    transition(':enter', [ 
      animate('1500ms 1000ms', keyframes([
          style({
            offset:0,
            transform: 'scale(1)'
          }),
          style({
            offset:0.14,
            transform: 'scale(1.3)'
          }),
          style({
            offset:0.28,
            transform: 'scale(1)'
          }),
          style({
            offset:0.42,
            transform: 'scale(1.3)'
          }),
          style({
            offset:0.7,
            transform: 'scale(1)',
          }),
      ])) 
    ])
  ]);

  export let swing =  trigger('swing',[
    transition(':enter', [ 
      animate('1500ms 1000ms', keyframes([
          style({
            offset:0.2,
            transform: 'rotate3d(0, 0, 1, 15deg)'
          }),
          style({
            offset:0.4,
            transform: 'rotate3d(0, 0, 1, -10deg)'
          }),
          style({
            offset:0.6,
            transform: 'rotate3d(0, 0, 1, 5deg)'
          }),
          style({
            offset:0.8,
            transform: 'rotate3d(0, 0, 1, -5deg)'
          }),
          style({
            offset:1,
            transform:' rotate3d(0, 0, 1, 0deg)'
          }),
      ])) 
    ])
  ]);

  // export let UpDown =  trigger('UpDown',[
  //   transition(':enter', [ 
  //     animate('1000ms ease-in-out', keyframes([
  //         style({
  //           offset:.5,
  //           top:'-250px',
  //           left:'-250'
  //         }),
  //         style({
  //           offset:1,
  //           top:0
  //       }),
  //     ])) 
  //   ])
  // ]);

  export let zoom =  trigger('zoom',[
    transition(':enter', [ 
      animate('1000ms 700ms ease-in-out', keyframes([
          // style({
          //   offset:0,
          //   transform: 'scale(1)'
          // }),
          style({
            offset:0.5,
            transform: 'scale(1.3)'
          }),
          style({
            offset:1,
            transform: 'scale(1)'
          }),
      ])) 
    ])
  ]);

  // export let sectionItems = trigger('sectionItems',[
  //   transition(':enter', [ 
  //     query('.gold-partenrs-title-div',[
  //       style({transform: 'translateX(-100%)'}),
  //       animate('1000ms 2s')  
  //     ])
  //     // query('.goldCompanies',[
  //     //   stagger(200, [
  //     //     style({transform: 'translateX(-100%)'}),
  //     //     animate('1000ms')  
  //     //   ])
  //     // ])
  //   ]),

  // ]);

  // export let sectionItems = trigger('sectionItems',[
  //   transition(':enter', [ 
  //     query('.company-logo-div',[
  //       stagger(200, [
  //         style({transform: 'translateX(-100%)'}),
  //         animate('1000ms')  
  //       ])
  //     ])
  //   ]),

    
  // ]);