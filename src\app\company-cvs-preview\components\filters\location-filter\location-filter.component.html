<form #formLocationFilters="ngForm" [formGroup]="locationForm" 
class="form-horizontal location-filter-interface validate-form">
    <div class="container-fluid form-padding">
        <div class="row group-mar">
            <div class="col-xs-12">
                <div class="form-group equal-height-row-cols">
                    <div class="col-sm-2 col-xs-12 label-fixed">
                        <span>Location</span>
                    </div>
                    <div class="col-sm-8 col-xs-12 focus-no-padding">
                        <input formControlName="location" placeholder=" " type="text" class="form-control" #googlelocationplaceLocation>
                        <span class="custom-underline"></span>
                    </div>
                </div>

                <div class="form-group equal-height-row-cols">
                    <div class="col-sm-2 col-xs-12 label-fixed">
                        <span>Distance Range</span>
                    </div>
                    <div class="col-sm-3 col-xs-12 focus-no-padding">
                        <input formControlName="distance" type="number" class="form-control"  style="height:100%;">
                        <span class="custom-underline"></span>
                    </div>
                    <div class="col-sm-2 col-xs-12 label-fixed mar-top-mob">
                        <span>Distance Unit</span>
                    </div>
                    <div class="col-sm-3 col-xs-12 focus-no-padding">
                        <p-dropdown [options]="distanceTool" optionLabel="label"
                            formControlName="unit_distance" placeholder="Distance Unit"
                            styleClass="unit_distance">
                        </p-dropdown>
                    </div>
                </div>

                <div class="alert alert-info" role="alert">
                        <p style="text-align:justify"><span style="font-weight:bold">Note:</span> If you want to filter based on location field, please choose both distance range and distance unit fields to get the required results</p>
                </div>
                
            </div>
        </div>


        <!-- <p>{{ locationForm.value | json }}</p> -->
        <div class="row">
            <div class="col-sm-12 text-center">
                <button pButton style="background-color: #3bb34b !important; color:white !important" icon="pi pi-check" type="submit" label="Apply" class="p-button-text" (click)="sendFilters()"></button>
            </div>
        </div>
    </div>
</form>
