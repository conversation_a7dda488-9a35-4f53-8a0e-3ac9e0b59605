import { Component, OnInit } from '@angular/core';
import { GeneralService } from '../../../general/services/general.service';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.css']
})
export class MainComponent implements OnInit {
  firstLoad: boolean = true;
  foldersCollapsed = window.innerWidth <= 1000;
  // fixPosition = false;
  constructor(private generalService:GeneralService) { }

  ngOnInit(): void {
    if(window.innerWidth > 1180){
      this.foldersCollapsed = false;
    }
    else{
      this.foldersCollapsed = true;
    }

    this.generalService.internalMessage.subscribe( (data) => {
      //when cvs table data ready, stop page loader
      if (data['message'] === 'stop-loader' && data['src'] === 'cvs-table') {
        this.firstLoad = data['mData'].firstLoad;
      }

      // to check if mobile and folders not collapsed state , then collapse it after click
      if (data['message'] === 'collapseFolders' && data['src'] === 'folder-list') {
        this.foldersCollapsed = true;
        this.generalService.notify('expandedChanged' , 'main' , 'folder-list' , {'expanded' : false}) ;
      }

      //in mobile we get toggle event from page-navbar
      if (data['message'] === 'toggleFolders' && data['src'] === 'filters-wrapper') {
        this.toggleFolders();
      }
      // if (data['message'] === 'toggleFolders' && data['src'] === 'search-bar') {
      //   this.toggleFolders();
      // }
    });
  //  window.addEventListener('scroll', this.sticky_relocate, true); //third parameter
  }

  toggleFolders() {
    this.foldersCollapsed = !this.foldersCollapsed;
    if(this.foldersCollapsed === true){
      this.generalService.notify('expandedChanged' , 'main' , 'folder-list' , {'expanded' : false}) ;
    }
  }

  // sticky_relocate = (event): void => {
  //   var window_top = $(window).scrollTop();
  //   var footer_top = $(".footer").offset().top;
  //   var div_height = $(".folders-section").height();

  //   if (window_top + div_height > footer_top ){   
  //     this.fixPosition = true;
  //   }
  //   else{
  //     this.fixPosition = false;
  //   }
  // };

  // ngOnDestroy() {
  //   window.removeEventListener('scroll', this.sticky_relocate, true);
  // }

}
