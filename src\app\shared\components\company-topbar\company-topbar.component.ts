import 'rxjs/add/operator/takeUntil';

import { Component, OnDestroy, OnInit, Input, HostListener } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { AuthService } from 'shared/shared-services/auth-service';
import { UserAccountService } from '../../../user/cv-services/user-account.service';
import { GeneralService } from '../../../general/services/general.service';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'company-topbar',
  templateUrl: './company-topbar.component.html',
  styleUrls: ['./company-topbar.component.css']
})
export class CompanyTopbarComponent implements OnInit, OnDestroy {
  username;
  display = false;
  searchActive = false;
  profilePicture;
  companyName = '';
  companyHaveProfile = '';
  country='';
  @Input("inAdvrsInterface") inAdvrsInterface : boolean;
  @Input('inHomePage') inHomePage: boolean;
  isScrolled = false;
  private ngUnsubscribe: Subject<any> = new Subject();

  constructor(
    private route: ActivatedRoute,
    public authService:AuthService,
    private userAccountService:UserAccountService,
    private imageProcessingService : ImageProcessingService,
    private generalService: GeneralService,
  ) { }



  displayModal() {
    this.display = !this.display;
  }

  closeModal() {
    this.display = false;
  }

  ngOnInit() {
    // get country for Homepage link
    if(localStorage.getItem('country'))
      this.country = localStorage.getItem('country');

    this.userAccountService.sharedcompanyUsername.subscribe(username => {
      this.username = username;
    });
    this.route.params.subscribe(res => {
      this.username = res['username'];
    });
    if(this.username == null){
      this.username = localStorage.getItem("username");
    }
    if(localStorage.getItem("company_name")){
      this.companyName = localStorage.getItem("company_name");
    }
    if(localStorage.getItem("have_profile")){
      this.companyHaveProfile = localStorage.getItem("have_profile");
    }
    this.profilePicture = localStorage.getItem("pic");
    if(this.profilePicture !== "none"){
      this.profilePicture = this.imageProcessingService.getImagePath ('companyLogo','small_thumbnail', this.profilePicture);
    }

    // get changes in profile picture from company form
    this.generalService.internalMessage.subscribe( (data) => {
      if(data['src'] === 'companyProfile' && data['dist'] === 'navbar'){
        if(data['message'] === 'profilePictureRemoved'){
          this.profilePicture = "none";
        }
        else if(data['message'] === 'profilePictureChanged'){
          this.profilePicture = this.imageProcessingService.getImagePath ('companyLogo','small_thumbnail', data['mData'].profile_picture);
        }
      }
    });


    if(this.inAdvrsInterface){
      this.activateSearch();
    }
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Works for most browsers
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;

    this.isScrolled = scrollTop > 300;
  }

  activateSearch(){
    if(this.searchActive === false){
      this.searchActive = true;
    }
  }
  deactivateSearch(){
    if(this.searchActive === true){
      this.searchActive = false;
    }
  }

  logout(){
    this.authService.logout();
  }
  isLoggedIn(){
    if(this.authService.loggedIn()){
      return true;
    }
    else{
      return false;
    }

  }

  displayContactModal() {
    this.generalService.notify('display Contact Modal' , 'topbar' , 'contact-us' , {'displayContactModal':true}) ;
  }

  HideCvPreviewMode() {
    this.generalService.notify('HideCvPreviewMode' , 'topbar' , 'cvs-table' , {'cvPreviewMode':false }) ;
  }
  
  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
