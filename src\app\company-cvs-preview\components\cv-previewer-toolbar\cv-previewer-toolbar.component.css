.cv-actions-toolbar{
    padding: 1rem;
    background: #cccccc29;
}

.cv-actions-toolbar i{
    font-size: 20px; 
    cursor:pointer;
    padding: 5px 10px; 
}
.cv-actions-toolbar img{
    width: 23px;
    margin: 0 5px 5px 10px;
    cursor:pointer;
}
.cv-actions-toolbar i:hover{
    box-shadow: 1px 1px 6px 1px #eaeef3;
}
.switchCveek-container{
    margin:0 16px;
    display: inline-block;
}
.switchCveek{
    border:1px solid #3D7BCE;
    background:#fff;
    padding: 5px;
    cursor: pointer;
}
.cveekActive{
    background:#3D7BCE;
    color:#fff;
}

.folders-tags{
    padding: 5px 10px;
    margin-bottom:20px;
}
.tag-label{
    color: #808080; 
    background-color:#f2f2f2; 
    padding:3px; 
    margin-left: 5px !important;
}
.tag-separator{
    display: inline-block;
    padding: 0 10px;
    margin: 12px 0;
    color: #30457c;
    font-size: 24px;
}
.delete-tag {
    border:none;
    filter: alpha(opacity=20);
}
.delete-tag:hover{
    color: red;
}

@media screen and (max-width:767px){
    .switchCveek-container{
        margin:0 0 0 7px;
        font-size: 13px;
    }
    .cv-actions-toolbar i{
        padding: 5px;
    }
}
