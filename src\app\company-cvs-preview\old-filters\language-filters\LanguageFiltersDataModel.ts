export class LanguageFiltersDataModel {
    languages = []  ;
    allMandatory = false;
    ///
    ///
    filters = {};

    firstLang = {
         lang_id  : '',
         level: ''
    };

    setFilters() {

        const languageValues = [] ;
        this.languages.forEach(ele => {
             if (ele['lang_id']) {
                var  obj = {};
                obj['lang_id'] = ele['lang_id']['id'] ;
                obj['level'] = ele['level']['self_assessment_id'] ;
                languageValues.push(obj);
             }
        });

        this.filters = {/// *
             'all_mandatory' : this.allMandatory,
             'values' : languageValues
        };
    }

    constructor() {
    }
}
