import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CompanyCvsPreviewRoutingModule } from './company-cvs-preview-routing.module';
import { MainComponent } from './components/main/main.component';
import { SharedModule } from 'shared/shared.module';
import { CompanyFormService } from 'app/company/services/company-form.service';
import { CvsTableComponent } from './components/cvs-table/cvs-table.component';
import { FoldersListComponent } from './components/folders-list/folders-list.component';
import { ColsManagerComponent } from './components/cols-manager/cols-manager.component';
import { FiltersWrapperComponent } from './components/filters-wrapper/filters-wrapper.component';
import { PaginatorComponent } from './components/paginator/paginator.component';
import { CvPreviewerToolbarComponent } from './components/cv-previewer-toolbar/cv-previewer-toolbar.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { SearchBarComponent } from './components/search-bar/search-bar.component';
import {TreeModule} from 'primeng/tree';
import { DragDropModule } from "@angular/cdk/drag-drop";
import { CvsFoldersComponent } from './components/cvs-folders/cvs-folders.component';
import { MoveCvModalComponent } from './components/move-cv-modal/move-cv-modal.component';
import { OthersFiltersComponent } from './old-filters/others-filters/others-filters.component';
import { PersonalFiltersComponent } from './old-filters/personal-filters/personal-filters.component';
import { EducationFiltersComponent } from './old-filters/education-filters/education-filters.component';
import { WorkExperienceFiltersComponent } from './old-filters/work-experience-filters/work-experience-filters.component';
import { SkillFiltersComponent } from './old-filters/skill-filters/skill-filters.component';
import { LanguageFiltersComponent } from './old-filters/language-filters/language-filters.component';
import { AllFiltersComponent } from './components/filters/all-filters/all-filters.component';
import { NameFilterComponent } from './components/filters/name-filter/name-filter.component';
import { LocationFilterComponent } from './components/filters/location-filter/location-filter.component';
import { CountriesFilterComponent } from './components/filters/countries-filter/countries-filter.component';
// import {SliderModule} from 'primeng/slider';

@NgModule({
  declarations: [
    MainComponent,
    CvsTableComponent,
    FoldersListComponent,
    ColsManagerComponent,
    FiltersWrapperComponent,
    PersonalFiltersComponent,
    EducationFiltersComponent,
    WorkExperienceFiltersComponent,
    SkillFiltersComponent,
    LanguageFiltersComponent,
    PaginatorComponent,
    OthersFiltersComponent,
    CvPreviewerToolbarComponent,
    SearchBarComponent,
    CvsFoldersComponent,
    MoveCvModalComponent,
    AllFiltersComponent,
    NameFilterComponent,
    LocationFilterComponent,
    CountriesFilterComponent,
  ],
  imports: [
    SharedModule,
    CompanyCvsPreviewRoutingModule,
    PdfViewerModule,
    TreeModule,
    DragDropModule,
    // SliderModule
  ],
  exports:[
    PaginatorComponent,
    CvPreviewerToolbarComponent,
  //  SearchBarComponent,
    FiltersWrapperComponent,
    MoveCvModalComponent,
  ],
  providers: [
    CompanyFormService,
  ]
})
export class CompanyCvsPreviewModule { }
