<!-- <div class="header container-fluid page-navbar">
    <div class="row search-row">
        <div class="col-xs-1">
            <div class="toggle-folders-btn">
                <i class="fa fa-bars" (click)="toggleFolders()"></i>
            </div>
        </div>
        <div class="col-md-5 col-sm-9 col-xs-12">
            <app-name-filter [pageType]="pageType"></app-name-filter>
            
        </div>
    </div>
</div> -->

<div class="filters-container page-navbar">
    <div class="toggle-folders-btn">
        <i class="fa fa-bars" (click)="toggleFolders()"></i>
    </div>
    <div class="name-filter-container">
        <app-name-filter [pageType]="pageType"></app-name-filter>
    </div>
    <div class="filter-btn country-btn" style="position:relative;" dropdown>
        <img src="./assets/images/secondbar/search-job/location-01.svg" class="filter-btn-icon">
        <span class="filter-btn-label">Countries</span>
        <div style="display: inline-block;position:absolute;left:15px;">
            <app-countries-filter></app-countries-filter>
        </div>
    </div>
    
    <!-- <div class="filter-btn">
        <span (click)="showFiltersModal('name_filter')">
            <img src="./assets/images/secondbar/search-job/All-Filters.svg" class="filter-btn-icon">
            <span class="filter-btn-label">Name</span>
        </span>
    </div> -->
    
    <!-- <div class="filter-btn">
        <span (click)="showFiltersModal('location_filter')">
            <img src="./assets/images/secondbar/search-job/location-01.svg" class="filter-btn-icon">
            <span class="filter-btn-label">Location</span>
        </span>
    </div> -->
    <div class="filter-btn">
        <span (click)="showFiltersModal('all_filters')">
            <img src="./assets/images/secondbar/search-job/All-Filters.svg" class="filter-btn-icon">
            <span class="filter-btn-label">All Filters</span>
        </span>
    </div>

    <div class="dropdown filter-btn" dropdown>
        <div class="nav-link dropdown-toggle top-navbar-profile" id="navbarDropdownMenuLink" aria-haspopup="true" aria-expanded="false" dropdownToggle> 
            <img src="./assets/images/secondbar/search-job/actions-icon.svg" class="filter-btn-icon">
            <span class="filter-btn-label">Actions</span>
        </div>
        <div class="dropdown-menu actions-dropdown-menu" aria-labelledby="navbarDropdownMenuLink" *dropdownMenu>
            <a  class="dropdown-item" (click)="notifySelectedAction('delete')">Delete Selected cvs</a> <br>
            <a  class="dropdown-item" (click)="notifySelectedAction('move')">Move Selected cvs to folders</a><br>
            <a class="dropdown-item" (click)="notifySelectedAction('read')">Mark selected cvs as read</a><br>
        </div>
    </div>

</div> 

<div class="modal fade" id="filtersModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="onModalCancel()">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModal2Label">
                    {{dataModel.filtersTitle}}
                </h4>
            </div>
            <div class="filters-form">
                <!-- <app-name-filter [pageType]="pageType" [ngStyle]="dataModel.filterSection  === 'name_filter'?{'display': 'block'} : {'display': 'none'}"></app-name-filter> -->

                <app-location-filter [ngStyle]="dataModel.filterSection  === 'location_filter'?{'display': 'block'} : {'display': 'none'}"></app-location-filter>
                <app-all-filters [ngStyle]="dataModel.filterSection  === 'all_filters'?{'display': 'block'} : {'display': 'none'}"></app-all-filters>

                <!-- <app-name-filter [pageType]="pageType" *ngIf="dataModel.filterSection  === 'name_filter'"></app-name-filter>
                <app-all-filters *ngIf="dataModel.filterSection  === 'all_filters'"></app-all-filters> -->
                <!-- <app-personal-filters [filterData]="dataModel.filterData" *ngIf="dataModel.filterSection  === 'personal_info'"></app-personal-filters>
                <app-education-filters [filterData]="dataModel.filterData" *ngIf="dataModel.filterSection  === 'education'"></app-education-filters>
                <app-work-experience-filters [filterData]="dataModel.filterData" *ngIf="dataModel.filterSection  === 'work_experience'"></app-work-experience-filters>
                <app-skill-filters [filterData]="dataModel.filterData" *ngIf="dataModel.filterSection  === 'skills'"></app-skill-filters>
                <app-language-filters [filterData]="dataModel.filterData" *ngIf="dataModel.filterSection  === 'languages'"></app-language-filters> -->
                <!-- <app-others-filters [filterData]="dataModel.filterData" *ngIf="dataModel.filterSection  === 'others'"></app-others-filters> -->
            </div>
        </div>
    </div>
</div>