import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { FormBuilder } from "@angular/forms";
import { Subject } from "rxjs";
 import { DataMap } from 'shared/Models/data_map';

@Component({
  selector: 'app-cv-search',
  templateUrl: './cv-search.component.html',
  styleUrls: ['./cv-search.component.css']
})
export class CvSearchComponent implements OnInit {
  username: any;
  searchForm;
  country;
  countryCode;
  city;
  locationService = new DataMap();
  currentSub: Subject<any> = new Subject();
  @ViewChild('googlelocationplace') googlelocationplace: any;
  @ViewChild('googlelocationplace') public googlelocationplaceRef: ElementRef;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
     ) {
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];

    });
    this.searchForm = this.fb.group({
      country: [''],
      country_code: [''],
      city: [''],
      location: [''],
    });

   }

   notifyCurrentMap(val) {

      this.currentSub.next(val);
  }

  private getLocationPlaceAutocomplete() {
    const autocomplete = new google.maps.places.Autocomplete(this.googlelocationplaceRef.nativeElement,
      {
        types: ['geocode']  // 'establishment' / 'address' / 'geocode'
      });
    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      this.getLocationAddress(place);
    });

  }

  getLocationAddress(place: object) {
    let newPlace = this.locationService.getLocationDataFromGoogleMap(place);
    this.city = newPlace.city;
    this.country = newPlace.country;
    this.countryCode = newPlace.country_code;
    this.searchForm.controls['country'].setValue(this.country);
    this.searchForm.controls['country_code'].setValue(this.countryCode);
    this.searchForm.controls['city'].setValue(this.city);
    this.searchForm.controls['location'].setValue(newPlace.fullAddress);

  }


  // setFullLocation() {
  //   let country = this.searchForm.controls['country'].value;
  //   let city = this.searchForm.controls['city'].value;
  //   if (country !== '' && city !== '') {
  //     return city + ', ' + country;
  //   } else if (country !== '' ) {
  //     return country;
  //   } else if (city !== '') {
  //     return city;
  //   } else {
  //     return '';
  //   }
  // }


  ngOnInit() {
    this.getLocationPlaceAutocomplete();
  }

}
