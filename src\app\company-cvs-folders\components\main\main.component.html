<div class="row">
    <div class="col-md-12">
        <div class="filters-wrapper">
            <app-filters-wrapper [pageType]="'cv_folders'"></app-filters-wrapper>
            <!-- <app-search-bar></app-search-bar> -->
        </div>
    </div>
</div>

<app-pre-loader [show]="firstLoad"></app-pre-loader>

<!-- 
    class="recieve-cvs-container" 
     -->
<div 
    cdkDropListGroup
    [hidden]="firstLoad"
    [ngClass]="{'expand-folders': foldersCollapsed === false , 'collapse-folders' : foldersCollapsed === true}"
    >
    <div class="folders-section">
        <app-cvs-folders></app-cvs-folders>
    </div>
    <div class="folders-toggle" (click)="toggleFolders()">
        <i class="pi" [ngClass]="{'pi-angle-left': foldersCollapsed === false , 'pi-angle-right' : foldersCollapsed === true}"></i>
    </div>

    <div class="table-section">
        <app-cvs-table></app-cvs-table>
    </div>
</div>

