{"sidebar": {"personalInformation": "Personal Information", "summary": "Summary", "objective": "Objective", "contactInformation": "Contact Information", "education": "Education", "languages": "Languages", "drivingLicence": "Driving License", "achievements": "Achievements", "workExperience": "Work Experience", "skills": "Skills", "certification": "Certification", "publications": "Publications", "projects": "Projects", "portfolios": "Portfolios", "training": "Training", "membership": "Memberships", "volunteers": "Volunteers", "conferencesWorkshopSeminar": "Conferences & Workshops & Seminars", "hobbiesInterests": "Hobbies and Interests", "references": "References"}, "shared": {"exit": "Exit", "saveAndExit": "Save and Exit", "save": "Save", "preview": "Preview", "details": "Details", "extraInformation": "Extra Information", "charactersRemaining": "characters remaining", "actions": "Actions", "present": "Present", "edit": "Edit", "cancel": "Cancel", "additionalCvSections": "Additional CV Sections", "myCvSections": "My CV Sections", "add": "Add", "hide": "<PERSON>de", "remove": "Remove", "sectionsManagement": "CV Sections Management", "at": "At", "outOf": "out of", "other": "Other", "by": "by", "apply": "Apply", "change": "Change", "yes": "Yes", "no": "No", "addNewItem": "Add Item", "noItemsFound": "No items found", "next": "Next", "previous": "Previous", "level": "Level", "addInfoEdit": "Add more info & edit", "addMoreValues": "click to add more values", "locationAddedAutomatically": "Location will be added automatically", "locationNotAddedAutomatically": "Location will not be added automatically", "errorSavingWorkExps": "Error saving work experience data. Please try again.", "skip": "<PERSON><PERSON>"}, "personalInformation": {"title": "Personal Information", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "resumeTitle": "Resume Title", "maritalStatus": "Marital Status", "nationality": "Nationality", "dateOfBirth": "Date Of Birth", "birthInfo": "Birth Info", "year": "Year", "placeOfBirth": "Place Of Birth", "country": "Country", "city": "City", "currentLocation": "Current Location", "streetAddress": "Street Address", "postalCode": "Postal Code", "gender": "Gender", "male": "Male", "female": "Female", "age": "Age", "years": "years", "uploadYourPhoto": "UPLOAD YOUR PHOTO"}, "summary": {"title": "Summary", "addYourSummary": "Add Your Summary"}, "objective": {"title": "Objective", "addYourObjective": "Add Your Objective"}, "contactInformation": {"title": "Contact Information", "email": "Email", "contactNumber": "Mobile number", "internetCommunication": "Internet Communication", "socialMedia": "Social Media"}, "education": {"title": "Education", "addCertification": "Add Certification", "degreeLevel": "Degree Level", "institution": "Institution", "educationField": "Education Field", "subEducationField": "Sub Education Field", "major": "Major", "minor": "Minor", "from": "From", "to": "To", "date": "Date", "location": "Location", "yourGrade": "Your Grade", "fullGrade": "Full Grade", "collegeWebsite": "College Website", "description": "Description", "projectTitle": "Title", "editEducation": "Edit Education", "academicResearchProjects": "Academic Research & Projects", "grade": "Grade", "outOf": "out of", "noDataEntered": "No Education entered"}, "languages": {"title": "Languages", "language": "Language", "motherLanguage": "Mother Language", "otherLanguage": "Other Language", "addLanguage": "Add Language", "type": "Type", "selfAssessment": "Self Assessment", "listening": "Listening", "reading": "Reading", "speaking": "Speaking", "writing": "Writing", "level": "Level", "diplomaOrCertification": "Diploma Or Certification", "nameOfDiploma": "Name Of diploma", "yourGrade": "Your Grade", "fullGrade": "Full Grade", "editLanguage": "Edit Language", "degree": "Degree", "mother": "Mother Language", "mother Language": "Mother Language", "other": "Other Language", "noDataEntered": "No language entered"}, "drivingLicence": {"title": "Driving License", "licenceType": "License Type", "category": "Category", "normalCar": "Cars (Category B)", "allTypes": "Other Types", "mopeds": "Mo<PERSON><PERSON>", "motorcycles": "Motorcycles", "lightVehicles": "Light vehicles", "cars": "Cars", "mediumSizedVehicles": "Medium-sized vehicles", "largeVehicles": "Large vehicles", "minibuses": "Minibuses", "buses": "Buses", "country": "Country", "internationalLicence": "International License", "yes": "Yes", "no": "No", "Yes": "Yes", "No": "No"}, "achievements": {"title": "Achievements", "addAchievement": "Add Achievement", "date": "Date", "enterYourAchievement": "Enter Your Achievement", "description": "Description", "editAchievement": "Edit Achievement", "noDataEntered": "No achievement entered"}, "workExperience": {"title": "Work Experience", "addWorkExperience": "Add Work Experience", "searchCompanyWithGoogle": "Search Company with Google", "addCompanyManually": "Add company manually", "companyName": "Company Name", "location": "Location", "from": "From", "to": "To", "date": "Date", "jobTitle": "Job Title", "name": "Name", "companyIndustry": "Company Industry", "description": "Description", "employmentType": "Job Type", "companySize": "Company Size", "website": "Website", "companyDescription": "Company Description", "editWorkExperience": "Edit Work Experience", "1to9employees": "1-9 Employees", "10to49employees": "10-49 Employees", "50to99employees": "50-99 Employees", "100to499employees": "100-499 Employees", "500employeesOrMore": "500 Employees or More", "experienceField": "Experience Field", "noDataEntered": "No work experience entered"}, "skills": {"title": "Skills", "addSkill": "<PERSON><PERSON>", "editSkill": "<PERSON>", "skillName": "Skill Name", "skillLevel": "Skill Level", "level": "Level", "noDataEntered": "No skill entered"}, "certifications": {"description": "Description", "title": "Certifications", "addCertificate": "Add Certificate", "name": "Name", "organization": "Organization", "acquiringDate": "Acquiring Date", "validFor": "Valid for", "dateOfReceipt": "Date of Receipt", "editCertificate": "Edit Certificate", "enterYourDescription": "Enter Your Description", "validityPeriod": "Validity period", "noDataEntered": "No certification entered"}, "publications": {"title": "Publications", "addPublication": "Add Publication", "date": "Date", "enterYourPublication": "Enter Your Publication", "description": "Description", "editPublication": "Edit Publication", "noDataEntered": "No publication entered"}, "projects": {"title": "Projects", "addProject": "Add Project", "date": "Date", "enterYourProject": "Enter Your Project", "description": "Description", "editProject": "Edit Project", "noDataEntered": "No project entered"}, "volunteers": {"title": "Volunteers", "addVolunteer": "Add Volunteer", "date": "Date", "enterYourVolunteer": "Enter Your Volunteer", "description": "Description", "editVolunteer": "Edit Volunteer", "noDataEntered": "No volunteer entered"}, "memberships": {"title": "Memberships", "addMembership": "Add Membership", "date": "Date", "enterYourMembership": "Enter Your Membership", "description": "Description", "editMembership": "Edit Membership", "noDataEntered": "No membership entered"}, "portfolios": {"title": "Portfolios", "addPortfolio": "Add Portfolio", "editPortfolio": "Edit Portfolio", "portfolioTitle": "Title", "link": "Link", "noDataEntered": "No portfolio entered"}, "trainings": {"description": "Description", "title": "Trainings", "addTraining": "Add Training", "editTraining": "Edit Training", "name": "Name", "organization": "Organization", "from": "From", "to": "To", "date": "Date", "totalHours": "Total Hours", "website": "Website", "location": "Location", "enterTheDescription": "Enter The Description", "trainingHours": "Training hours", "noDataEntered": "No training entered"}, "conferencesWorkshopSeminar": {"title": "Conferences & Workshops & Seminars", "type": "Type", "date": "Date", "description": "Description", "editConferenceWorkshopSeminar": "Edit Conference, Workshop, Seminar", "enterYourConferenceWorkshopSeminar": "Enter Your Conference, Workshop, Seminar", "attendee": "<PERSON><PERSON><PERSON>", "speaker": "Speaker", "conferences": "Conferences", "workshops": "Workshops", "seminars": "Seminars", "attendedAs": "Attended As", "noDataEntered": "No conference, workshop, seminar entered"}, "hobbiesAndInterests": {"title": "Hobbies and Interests", "choose": "<PERSON><PERSON>", "enterYourHobbiesAndInterests": "Enter Your Hobbies and Interests", "allHobbiesInsertedHereWillBeWithoutIcons": "All Hobbies inserted here will be without icons"}, "references": {"title": "References", "addReference": "Add Reference", "editReference": "Edit Reference", "availableUponRequest": "Available upon request", "name": "Name", "position": "Position", "organization": "Organization", "email": "Email", "mobile": "Mobile", "preferredTimeToCall": "Preferred time to call", "noDataEntered": "No reference entered"}, "uploadCV": {"title": "Upload CV"}, "helper": {"helpTip": "Help Tip"}, "company_form": {"companyName": "Company Name", "companyIndustry": "Company Industry", "companySpecialities": "Company Specialities", "type": "Type", "companyWebsite": "Company Website", "companyDescription": "Company Description", "companySize": "Company Size", "founded": "Founded", "socialMedia": "Social Media", "company_information": "Company Information", "preview": "Preview"}, "company_location": {"AddLocation": "Add Location", "Name": "Name", "Location": "Location", "MainOffice": "Main Office", "Actions": "Actions", "EditLocation": "Edit Location", "noDataEntered": "No location entered"}, "post_advr": {"advr_title": "Advertisement Title", "job_info": "Job Info", "job_title": "Job Title", "exp_field": "Experience Field", "emp_type": "Job Types", "salary": "Salary", "title": "Title", "subTitle": "SubTitle", "from": "From", "to": "To", "more_details": "More Details", "check_values": "Here are extra values to describe your vacancy. if you want to receive only  CVs that exactly match one or more of these values, please click on the small square on the right of the corresponding value(s).", "equivalent_major": "Or any equivalent major", "mandatory": "Mandatory", "job_details": "Job Details", "year_exp": "Years of experience", "gender": "Gender", "age": "Age", "nationality": "Nationality", "job_seeker_location": "Job Seeker Location", "degree_level": "Degree Level", "languages": "Langauges", "skills": "Skills", "certification": "Certification", "driving_license": "Driving License", "company_info": "Company Info", "location_name": "Location Name", "show_hide": "Show / Hide", "add_job_application": "Add Job Application"}, "advr_preview": {"post_since": "Posted Since", "job_description": "Job Description", "about_company": "About Company", "save_as_template": "Save as template", "save_as_draft": "Save as draft", "publish": "Publish", "confidential_company": "Confidential Company"}, "manage_advs": {"title": "Title", "id": "ID", "remaining_days": "Remaining days", "status": "Status", "statistics": "Statistics", "actions": "Actions", "active": "Active", "deactive": "Inactive", "activate_deactivate": "Activate / deactivate", "edit": "Edit", "end_work_flow": "End work flow", "expiration_date": "Expiration date", "delete": "Delete", "create_date": "Create Date", "last_update": "Last Update", "temp_id": "Template ID", "choose_language": "Choose another language for other translated Advertisement", "choose_language_others": "Choose another language for other translated "}, "validationMessages": {"required": "Required", "namePattern": "You can only use letters and numbers", "YouShouldProvideMobileNumber": "You should provide mobile number", "YouShouldProvidePhoneNumber": "You should provide phone number", "notValidNumber": "Not a valid Number", "invalidEmail": "In<PERSON>id Email Address", "invalidUrl": "Invalid URL", "invalidDateRange": "Invalid Date Range", "invalidDataRange": "Invalid Data Range", "mismatchType": "Mismatch Type", "yearRequired": "Year Required", "pleaseChooseValidSMProviderValue": "Please choose a valid social media provider value", "pleaseEnterValidCProviderValue": "Please Enter a valid communication provider value", "pleaseChooseProvider": "Please choose a provider", "numberCantContainSpace": "Number can't contain space", "numberIsTooLong": "Number is too long", "numberIsTooShort": "Number is  too short", "pleaseFillValidEducationFieldValue": "Please fill a valid education field value", "youShouldProvideCountryCode": "You should provide country code", "pleaseFillTheForm": "Please fill the form", "tooLongMaxNumber400": "Too Long max number 400", "tooLongText": "The text is too long", "duplicatedNationality": "Duplicated nationality", "duplicatedSkill": "Duplicated skill", "duplicatedLanguage": "Duplicated Language", "imageSizeBig": "Too big,Max size 1MB", "fileSizeBig": "Too big,Max size 1MB", "invalidImageType": "Please upload jpeg, jpg or png images", "invalidFileType": "Please upload pdf files only", "ChooseAutoCompleteSuggestionsError": "Please, choose from autocomplete suggestions", "ChooseCountryCityGoogleSuggestionsError": "Please choose (country & city) from google suggestions", "googleVerifiedCompanyAutoCompleteError": "Please, choose from autocomplete suggestions", "languageLevel": "Language Level Required", "language": "Language Required", "skill": "<PERSON><PERSON> Required", "skillLevel": "Skill Level Required", "countryRequired": "Country Required", "cityRequired": "City Required", "categoryRequired": "Category Required", "pleaseCompleteAllFields": "Please complete all required fields."}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "faqs": {"FrequentlyAskedQuestions": "Frequently Asked Questions", "Employer": "Employer", "JobSeeker": "<PERSON>", "Type": "Type", "User": "User", "Company": "Company", "Category": "Category", "Question": "Question", "Answer": "Answer", "Order": "Order", "Activation": "Activation", "Save": "Save", "Cancel": "Cancel", "labels": {"ManageFAQs": "Manage FAQs", "Edit": "Edit", "AddNewFAQ": "Add New FAQ", "Preview": "Preview", "Delete": "Delete", "Close": "Close", "Yes": "Yes", "ShowMore": "show more", "ShowLess": "show less"}, "errorMessages": {"TypeRequired": "Type is required.", "CategoryRequired": "Category is required.", "QuestionRequired": "Question is required.", "AnswerRequired": "Answer is required.", "OrderRequired": "Order is required.", "DeleteConfirm": "Are you sure you want to delete this question?"}, "notes": {"QuestionNote": "We'll never share this question until you check the activation box below.", "OrderNote": "put the order of the question according to its importance. ex: 1 is the most important."}, "languages": {"English": "English", "Arabic": "Arabic"}}, "help": {"SearchResult": "Search Results", "HelpCenterTitle": "How may we help you?", "HelpHome": "Help Home ", "EmptySubCat": "There is no help topic yet under this category.", "NoMatchesWasFound": "No Matches Was Found", "SectionName": "Section Name", "FieldName": "Field Name", "SectionID": "Section ID", "FieldID": "Field ID", "CompanyHelp": "Company Help", "UserHelp": "User Help", "MainCategories": "Main Categories", "SubCategories": "Sub Categories", "MainCategory": "Main Category", "SubCategory": "Sub Category", "HelpTopics": "Help Topics", "HelpTips": "Help Tips", "Employer": "Employer", "JobSeeker": "<PERSON>", "Type": "Type", "User": "User", "Company": "Company", "Category": "Category", "Title": "Title", "Description": "Description", "Order": "Order", "Activation": "Activation", "Slug": "Slug", "PageTitle": "Page Title", "MetaDescription": "Meta Description", "MetaKeywords": "Meta Keywords", "Save": "Save", "Cancel": "Cancel", "Next": "Next", "Previous": "Previous", "labels": {"ManageHelpTopics": "ManageHelp Topics", "ManageHelpTips": "ManageHelp Tips", "Edit": "Edit", "AddNewHelpTopic": "Add New Help Topic", "AddNewHelpTip": "Add New Help Tip", "Preview": "Preview", "Delete": "Delete", "Close": "Close", "Yes": "Yes", "ShowMore": "show more", "ShowLess": "show less"}, "errorMessages": {"TypeRequired": "Type is required.", "MainCategoryRequired": "Main Category is required.", "SubCategoryRequired": "Sub Category is required.", "TitleRequired": "Title is required.", "DescriptionRequired": "Description is required.", "OrderRequired": "Order is required.", "SlugRequired": "Slug Required", "PageTitleRequired": "Page Title is required.", "SectionNameRequired": "Section Name is required.", "fieldnNameRequired": "Field Name is required.", "DeleteConfirm": "Are you sure you want to delete this question?"}, "languages": {"English": "English", "Arabic": "Arabic"}}, "verification": {"Name": "Name", "errorMessages": {"NameRequired": "Name is required"}, "languages": {"English": "English", "Arabic": "Arabic"}}, "contactUs": {"MsgSentSuccessfully": "Your Message Sent Successfully!", "FailToSendMsg": "Failed To Send The Message, Please Try Again.", "ContactUs": "Contact Us!", "Email": "Email", "Message": "Message", "MainCategory": "Main Category", "SubCategory": "Sub Category", "Type": "Type", "JobSeeker": "I'm a JobSeeker", "Employer": "I'm an Employer", "Send": "Send", "errorMessages": {"Required": "this field is required", "InvalidEmail": "InvalidEmail"}, "languages": {"English": "English", "Arabic": "Arabic"}}, "confirm": {"deleteProfilePic": "Are you sure you want to delete your profile picture?", "deleteLogo": "Are you sure you want to delete the logo?", "deleteSection": "Do you want to hide these section(s)?", "checkSecForDel": "Please choose the sections to want to hide", "addExtraSections": "Please choose the sections you want to add to your cv", "useAddHideBtns": "Please use Add / Hide buttons to move selected sections before save"}}