<app-pre-loader [show]="accountData == null"></app-pre-loader>

<page-navbar [navType]="'empty'"></page-navbar>
<div style="margin-top: 40px;" class="container-fluid" *ngIf="accountData">
    <p-toast position="bottom-right"></p-toast>
    <p-toast key="company_installed_plugin" position="bottom-right" baseZIndex="1" >
        <ng-template let-message pTemplate="message">
            <span>{{message.detail}}</span> &nbsp;
            <a href="{{message.data}}"  target="_blank" class="warning-msg-link">Website Link</a>
        </ng-template>
    </p-toast>
    <div class="CForm">
        <form
        #form="ngForm" 
        [formGroup]="companyAccountForm"
        >        
            <div>  <!--start of slide forms -->
                    <div id="mySidenav" class="sidenav row">                      
                        <div formGroupName="email_group" class="container con">
                            <div class="row div-margin-bo-20">                                
                                <a href="javascript:void(0)" (click)="closeNav()">
                                    <i class="fa fa-arrow-left" aria-hidden="true"></i>
                                </a>
                                <span class="section-title"> Edit Email:</span>
                            </div>                          
                            <div class="slide">
                                <div class="row">
                                    <label class="col-sm-4 col-xs-12">New email</label>
                                    <div class="col-lg-6 col-sm-8 col-xs-12">
                                        <input type="email" formControlName="email" class="form-control"  (keydown)="emailErrorMessage = null">
                                        <div *ngIf="emailGroup['controls'].email.pending" style="margin-top: 10px;">Checking if email is taken ...</div>
                                        <div class="error-message">               
                                            <span *ngIf="emailErrorMessage">{{emailErrorMessage}}</span>
                                            <span *ngIf="emailGroup['controls'].email.touched && emailGroup['controls'].email.errors?.invalidEmailError">Please insert a valid email address</span>
                                            <!-- <span *ngIf="emailGroup['controls'].email.touched && emailGroup['controls'].email.errors?.email">Please insert a valid email address</span> -->
                                            <span *ngIf="emailGroup['controls'].email.errors?.emailTaken">Some one is already using this email</span>
                                        </div>                                       
                                    </div>
                                    <div class="col-xs-12 text-center">
                                        <button class="btn btn-success btn-block" (click)="verifyEmail()"> verify</button>
                                    </div>                                                                                                 
                                </div>  
                                
                                <div class="row">
                                    <label class="col-sm-4 col-xs-12" for="lname">Verification code</label>
                                    <div class="col-lg-6 col-sm-8 col-xs-12">
                                        <input  type="text" formControlName="code" class="form-control"  (keydown)="emailConfirmErrorMessage = null">                                       
                                        <div class="error-message">
                                            <span *ngIf="emailConfirmErrorMessage">{{emailConfirmErrorMessage}}</span>
                                        </div>    
                                        <div style="margin-top: 20px;">
                                            <span class="resend-btn" (click)="resendEmailCode()">Resend Code</span>
                                        </div>                                                   
                                    </div>                                    
                                    <div class="col-xs-12 text-center">                                      
                                        <button class="btn btn-success btn1" (click)="confirmEmail()">Confirm</button>
                                    </div>                                                     
                                </div>                             
                            </div>
                          </div>
                          
                      </div>
                      <div id="mySidenav1" class="sidenav row">                      
                        <div formGroupName="password" class="container con">
                            <div class="row div-margin-bo-20">                                   
                                <a href="javascript:void(0)" (click)="closeNav1()">
                                    <i class="fa fa-arrow-left" aria-hidden="true"></i>
                                </a>
                                <span class="section-title"> Edit Password:</span>
                            </div>
                           
                            <div  class="slide">
                                <div class="row">
                                    <label class="col-sm-4 col-xs-12">Old password</label>
                                    <div class="col-lg-6 col-sm-8 col-xs-12">  
                                        <input type="{{ typeOldPassword }}" formControlName="old_password" (keydown)="passwordErrorMessage = null" class="form-control div-margin-bo-15">
                                        <span  class="fa fa-fw password-eye-icon"  
                                            [ngClass]="{
                                                'fa-eye' : showOldPassword,
                                                'fa-eye-slash' : !showOldPassword
                                            }"
                                            (click)="toggleShowPassword('old')"
                                        ></span>                                                  
                                    </div>                                                                                                              
                                </div>  
                                <div class="row div-margin-bo-20">
                                    <div class="col-sm-offset-4 col-lg-6 col-sm-8 col-xs-12">  
                                        <div class="error-message">               
                                            <span  *ngIf="password['controls'].old_password.touched && password['controls'].old_password.errors?.minlength">Too short password</span>
                                            <span  *ngIf="password['controls'].old_password.touched && password['controls'].old_password.errors?.maxlength">Too long password</span>
                                        </div>  
                                    </div>
                                </div>
                                <div class="row">
                                    <label class="col-sm-4 col-xs-12">New password</label>
                                    <div class="col-lg-6 col-sm-8 col-xs-12">  
                                        <input type="{{ typeNewPassword }}" formControlName="new_password" (keydown)="passwordErrorMessage = null" class="form-control div-margin-bo-15">
                                        <span class="fa fa-fw password-eye-icon"  
                                            [ngClass]="{
                                            'fa-eye' : showNewPassword,
                                            'fa-eye-slash' : !showNewPassword
                                            }"
                                            (click)="toggleShowPassword('new')"
                                        ></span>                                                                      
                                    </div>                                                                                                                
                                </div>  
                                <div class="row">
                                    <div class="col-sm-offset-4 col-lg-6 col-sm-8 col-xs-12"> 
                                        <password-strength-meter [password]="password['controls'].new_password.value" enableFeedback="true"></password-strength-meter>            
                                        <div class="error-message">               
                                            <span *ngIf="password['controls'].new_password.touched && password['controls'].new_password.errors?.minlength">Too short password</span>
                                            <span *ngIf="password['controls'].new_password.touched && password['controls'].new_password.errors?.maxlength">Too long password</span>                    
                                            <span *ngIf="passwordErrorMessage">{{passwordErrorMessage}}</span> 
                                        </div>   
                                    </div>
                                    <div class="col-xs-12 text-center">                                      
                                        <button class="btn btn-success btn1" (click)="savePassword()">Save</button>
                                    </div> 
                                </div>
                            </div>                         
                        </div>
                      </div>

                      <div id="mySidenav2" class="sidenav row">                       
                        <div class="container con">
                            <div class="row div-margin-bo-20">                                   
                                <a href="javascript:void(0)" (click)="closeNav2()">
                                    <i class="fa fa-arrow-left" aria-hidden="true"></i>
                                </a>
                                <span class="section-title">Edit Mobile Number:</span>
                            </div>
                            <div  class="slide" formGroupName="contact_number">
                                <div class="row">
                                    <label class="col-sm-4 col-xs-12">Mobile</label>
                                    <div class="col-lg-6 col-sm-8 col-xs-12">
                                        <div class="row flex-container">
                                            <div class="col-xs-6 equal-height-cols">
                                                <p-dropdown
                                                    [options]="countryCodeOptions"
                                                    optionLabel="name"
                                                    formControlName="country_code_options"
                                                    [filter]="true"
                                                    filterBy="label,value.name"
                                                    filterMatchMode="startsWith"
                                                    #countryCodee
                                                    (onChange)="CountryCodeChange(countryCodee)"
                                                    (keydown)="MobileErrorMessage = null"
                                                >
                                                    <ng-template let-it pTemplate="selectedItem">
                                                    <img *ngIf="it.label!=''" src="./assets/images/CountryCode/{{it.value.country_code}}.gif"
                                                            class="countryimg" style=" vertical-align:middle; float:right;"/>
                                                    <span class="countrycode" style=" vertical-align:middle; float:left;">{{it.value.country_code}}</span>
                                                    </ng-template>
                                                    <ng-template let-code pTemplate="it">
                                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                                        <div style="margin-top:4px ;float: left;width:271px;">
                                                        <span style=" width: 55px; display: inline-block;">{{code.value.country_code }}</span>
                                                        <span style="width:200px;display: inline-block;">
                                                            {{ (code.value.name.length>25)? ( code.value.name | slice:0:25)+'...': code.value.name }}                        
                                                        </span>
                                                        <img *ngIf="code.label!=''" src="./assets/images/CountryCode/{{code.value.country_code}}.gif" style="display: inline-block;width:16px;"/>
                                                        </div>
                                                    </div>
                                                    </ng-template>
                            
                                                </p-dropdown>
                                            </div>
                                            <div class="col-xs-6 equal-height-cols">
                                                <input type="tel" class="form-control input-height" formControlName="mobile_number" (keydown)="MobileErrorMessage = null"  onkeypress="return (event.key.charCodeAt(0) == 8 || event.key.charCodeAt(0) == 0 || event.key.charCodeAt(0) == 13) ? null : event.key.charCodeAt(0) >= 48 && event.key.charCodeAt(0) <= 57" >
                                            </div>
                                        </div> 
                                        <div class="row">
                                            <div class="col-xs-12">
                                                <div class="error-message">
                                                    <span *ngIf="MobileErrorMessage">{{MobileErrorMessage}}</span>
                                                    <span *ngIf="(contactNumber.controls['mobile_number'].touched || contactNumber.controls['country_code'].touched) && contactNumber.hasError('fullNumberError')" translate> {{ contactNumber.errors?.fullNumberError }}</span>
                                                    <span *ngIf="(contactNumber.controls['mobile_number'].touched || contactNumber.controls['country_code'].touched) && contactNumber.hasError('fullNumberError') && contactNumber.controls['mobile_number'].hasError('contactNumberError')">, </span>
                                                    <span *ngIf="contactNumber.controls['mobile_number'].touched && contactNumber.controls['mobile_number'].hasError('contactNumberError')" translate>{{ contactNumber.controls['mobile_number'].errors?.contactNumberError }}</span>
                                                </div>
                                            </div> 
                                        </div>                               
                                    </div>
                                    <div class="col-xs-12 text-center">
                                        <button class="btn btn-success btn1" (click)="verifyMobileNumber()">
                                            <!-- <i style="font-size: 16px;" class="fa fa-whatsapp" aria-hidden="true"></i>  -->
                                            Save
                                        </button>
                                    </div>                                                                                             
                                </div> 
                                                           
                              <!-- <div class="row">
                                <label class="col-sm-4 col-xs-12">Verification code</label>
                                <div class="col-lg-6 col-sm-8 col-xs-12" style="padding-right: 45px;">
                                    <input type="text" formControlName="code" (keydown)="MobileConfirmErrorMessage = null"  class="form-control">
                                    <div class="error-message">               
                                        <span *ngIf="MobileConfirmErrorMessage">{{MobileConfirmErrorMessage}}</span>
                                    </div>  
                                    <div style="margin-top: 20px;">
                                        <span class="resend-btn" (click)="resendMobileCode()">Resend Code</span>
                                    </div>                                      
                                </div>
                                <div class="col-xs-12 text-center">
                                    <button class="btn btn-success btn1" (click)="confirmMobileNumber()">Confirm</button>
                                </div>                                                                                                 
                            </div>   -->
                                                    
                            </div>
                          </div>
                          
                      </div>

                      <div id="mySidenav3" class="sidenav row">                       
                        <div class="container con">
                            <div class="row div-margin-bo-20">
                                <a href="javascript:void(0)" (click)="closeNav3()">
                                    <i class="fa fa-arrow-left" aria-hidden="true"></i>
                                </a>
                                <span class="section-title">Edit Profile Url:</span>
                            </div>    
                            <div class="slide">
                                <div class="row">
                                    <label class="col-sm-4 col-xs-12">New url</label>
                                    <div class="col-lg-6 col-sm-8 col-xs-12">
                                        <input type="text" formControlName="user_name" (keydown)="profileUrlErrorMessage = null" class="form-control"> 
                                        <br>
                                        <p>
                                            eg: If you enter cveek it will be cveek.com/i/c/cveek
                                        </p>
                                        <p>You can change it only three times.</p>
                                        <div class="error-message">               
                                            <span *ngIf="profileUrlErrorMessage">{{profileUrlErrorMessage}}</span>
                                            <span *ngIf="companyAccountForm['controls'].user_name.touched && companyAccountForm['controls'].user_name.errors?.pattern">You can use letters, numbers or -</span>
                                        </div>                                       
                                    </div>
                                    <div class="col-xs-12 text-center">
                                        <button class="btn btn-success btn-block" (click)="openProfileUrlModal()"> Save</button>
                                    </div>                                                                                                 
                                </div>  
                            </div>
                          </div>
                          
                      </div>

                    </div>  <!-- end of slide forms -->

                    <div class="col-sm-3 lan">
        
                    </div>
                    <div class="col-sm-9 form-horizontal validate-form details" style="background-color: white;">
                        <div class="row clearfix flex-row info1">       
                            <div class="col-sm-8 col-xs-12 flex-order-sm-2">                                                                                   
                                <div class="custom-row clearfix">
                                    <div class="col-sm-5 col-xs-4 left-col-preview-alignright">
                                        <p class="info" translate>Email</p>            
                                    </div>
                                    <div class="col-sm-7 col-xs-8 preview1" >
                                        <div class="row">
                                            <div class="preview col-xs-10"> {{email}} </div>&nbsp;
                                            <div class="edit col-xs-1">
                                                <i class="fa fa-pencil-square-o" aria-hidden="true" (click)="openNav()"></i>
                                            </div>
                                        </div>        
                                    </div>
                                </div>
                
                                <div class="custom-row clearfix">
                                    <div class="col-sm-5 col-xs-4 left-col-preview-alignright">
                                        <p class="info" translate>Password</p>   
                                    </div>
                                    <div class="col-sm-7 col-xs-8 preview1">
                                        <div class="row">
                                            <div class="preview col-xs-10">{{passwordChangeMesg}}</div>
                                            <div class="edit col-xs-1">
                                                <i class="fa fa-pencil-square-o"  aria-hidden="true" (click)="openNav1()"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                
                                <div class="custom-row clearfix">
                                    <div class="col-sm-5 col-xs-4 left-col-preview-alignright">
                                        <p class="info" translate>Mobile Number</p>
                                    </div>
                                    <div class="col-sm-7 col-xs-8 preview1">
                                        <div class="row">
                                            <div class="preview col-xs-10" *ngIf="mobile.mobile_number != ''">({{mobile.country_code}}) {{mobile.mobile_number}}</div>
                                            <div class="preview col-xs-10" *ngIf="mobile.mobile_number == ''">add your number</div>
                                            <!-- <div class="preview col-xs-10" *ngIf="mobile.mobile_number == '' && newMobile.mobile_number == ''">add your number</div>
                                            <div class="preview col-xs-10" *ngIf="mobile.mobile_number == '' && newMobile.mobile_number != ''">Verify your number</div> -->
                                            <div class="edit col-xs-1">
                                                <i class="fa fa-pencil-square-o"  aria-hidden="true" (click)="openNav2()"></i></div>
                                        </div>
                                    </div>
                                </div>
                
                                <div class="custom-row clearfix">
                                    <div class="col-sm-5 col-xs-4 left-col-preview-alignright ">
                                        <p class="info" translate>Profile Url</p>
                                    </div>
                                    <div class="col-sm-7 col-xs-8 preview1">
                                        <div class="row">
                                            <div class="preview col-xs-10">{{profileUrl}}</div>
                                            <div class="edit col-xs-1">
                                                <i class="fa fa-pencil-square-o"  aria-hidden="true"  (click)="openNav3()"></i>
                                            </div>
                                        </div>          
                                    </div>
                                </div>

                                <div class="custom-row clearfix">
                                    <div class="col-sm-5 col-xs-4 left-col-preview-alignright ">
                                        <p class="info" translate>Delete account</p>
                                    </div>
                                    <div class="col-sm-7 col-xs-8 preview1">
                                        <button type="button" class="btn editStyle delete-btn" 
                                        data-toggle="modal" data-target="#deleteAccountModal">Delete Account </button>       
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        
          
              <!-- Modal -->
              <div class="modal fade" id="deleteAccountModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                  <div class="modal-content">   
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Delete account</h4>
                    </div>     
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-xs-12">
                                <p>
                                    Note that by deleting your account, you will not be able to access your account again and all information
                                    will be lost . This action cannot undone!
                                </p>
                                <p>
                                    Please tell us why you are deleting your account?
                                </p>
                            </div>
                            <div class="col-md-8 col-xs-12">
                                <p-dropdown 
                                    [options]="reasonsDeleteAccount"
                                    optionLabel="name"
                                    formControlName="delete_account_options"
                                    [filter]="true"
                                    filterMatchMode="startsWith"  
                                    placeholder="Delete Reason" 
                                    #reason
                                    (onChange)="deleteReasonChange(reason)" 
                                    (keydown)="deleteAccountErrorMessage = null"                                                   
                                    >
                                    <ng-template let-reason pTemplate="item">
                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                        <div style="font-size:14px;float:left;margin-top:4px">{{reason.label}}</div>
                                    </div>
                                    </ng-template>
                                </p-dropdown>    
                                <div class="error-message">
                                    <span *ngIf="deleteAccountErrorMessage">{{deleteAccountErrorMessage}}</span> 
                                </div> 
                                <div formGroupName="delete_account" class="div-margin-top-20">
                                    <textarea formControlName="comment" class="form-control" placeholder="comment">
    
                                    </textarea>
                                </div>  
                            </div>
                            <div class="col-xs-12 text-center div-margin-top-30 div-margin-bo-10">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>&nbsp;&nbsp;
                                <button type="button" class="btn btn-success" (click)="deleteUserAccount()">Confirm</button>
                            </div> 
                        </div>                    
                    </div>                    
                  </div>
                </div>
              </div>
            
              <div class="modal fade" id="updateProfileUrlModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">   
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">Confirm update profile url</h4>
                        </div>      
                        <div class="modal-body">
                            <div>
                                <p>
                                    Please note you can change your url only 3 times , 
                                    and after first change you can't change it before 60 days.                  
                                </p>      
                                <div class="row">
                                    <div class="col-xs-12 text-center div-margin-top-15 div-margin-bo-10">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>&nbsp;&nbsp;
                                        <button type="button" class="btn btn-success" (click)="updateProfileUrl()" data-dismiss="modal">Change</button>
                                    </div>
                                </div>                                   
                            </div>                    
                        </div>                    
                    </div>
                </div>
                </div>
                        
                
            </div>  
            <!-- <p>  {{ form.value | json }} </p> -->
        </form>
        
    </div>
</div>
    