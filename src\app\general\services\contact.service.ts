import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable()
export class ContactService {


  url = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data ;
      this.url     = data + '/admin/contact';
    });

  }

  sendMessage(message) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url , message , {headers});
  }


  getAllCategories(langId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/category/' + langId, {headers});
  }


}
