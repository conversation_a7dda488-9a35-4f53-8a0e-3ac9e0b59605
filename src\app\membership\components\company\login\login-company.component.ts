import { Component, OnInit, NgZone, Renderer2 } from '@angular/core';
import {AuthService} from 'shared/shared-services/auth-service';
import {Router} from '@angular/router';
import {FormBuilder, Validators} from '@angular/forms';
import {AuthService as SocialAuthService, FacebookLoginProvider} from 'angular5-social-login';
import { OAuthService } from 'angular-oauth2-oidc';
import 'rxjs/add/operator/switchMap';
import { Errors } from 'app/membership/models/errors';
import 'rxjs/add/observable/empty' 
import { Observable } from 'rxjs';
import { EmptyObservable } from 'rxjs/observable/EmptyObservable';
import { SocketioService } from 'shared/shared-services/socketio.service';
import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from '../../../../general/services/general.service';
import { EmailValidator } from 'shared/validators/email.validators';
import { CredentialResponse} from 'google-one-tap';
import { environment } from 'environments/environment.prod';
import { ScriptService } from 'shared/shared-services/script.service';

@Component({
  selector: 'app-login',
  templateUrl: './login-company.component.html',
  styleUrls: ['./login-company.component.css']
})
export class LoginCompanyComponent implements OnInit {

 // emailPattern = '^[a-z0-9._%+-]+@[a-z0-9.-]+[.]+[a-z]{2,4}$';
  loginForm;
  resetMessage;
  helpLink;
  clickForgetPassword: boolean = false;
  type= 'password';
  show = false;
  username='';
  loader = false;
  constructor(private authService: AuthService,
              private router: Router,
              private  fb: FormBuilder,
              private socialAuthService: SocialAuthService,
              private oauthService: OAuthService,
              private socketSerivce: SocketioService,
              private title: Title,
              private meta:Meta,
              private generalService: GeneralService,
              private ngZone: NgZone,
              private renderer: Renderer2,
              private scriptService: ScriptService
  ) {
    this.loginForm = this.fb.group({
     // email : ['', [Validators.required , Validators.email]],
      email : ['', [Validators.required , EmailValidator.isValidEmailFormat]],
      password : ['', [Validators.required , Validators.minLength(6), Validators.maxLength(20)]],
    });
  }

  ngOnInit() {
    this.title.setTitle('CVeek Website | Employer Sign in ');
    this.meta.updateTag({ name: 'description', content: 'Sign in to your employer account to post Jobs, manage advertisements and job applications.'});

    if(sessionStorage.getItem("expired")){
      this.resetMessage = Errors.sessionTerminated;
      sessionStorage.removeItem("expired");
    }

    this.authService.currentError.subscribe(errorMessage => this.resetMessage = errorMessage);

    this.initializeGoogleLogin();
  }

  isInvalid(controlName: string) {
    return this.loginForm.controls[controlName].hasError('required');
  }

  isInvalidSyn(controlName: string) {
    return this.loginForm.controls[controlName].hasError('invalidEmailError');
  //  return this.loginForm.controls[controlName].hasError('email');
  }

  isInvalidMin(controlName: string) {
    return this.loginForm.controls[controlName].hasError('minlength');
  }

  isInvalidMax(controlName: string) {
    return this.loginForm.controls[controlName].hasError('maxlength');
  }

  login() {
    this.authService.logoutIfAlreadyLoggedin();

    const data = {
      'email' : this.loginForm.get('email').value,
      'password' : this.loginForm.get('password').value,
    } ;

    if (this.loginForm.valid) {
      this.loader = true;
      this.oauthService.fetchTokenUsingPasswordFlow(data.email,data.password).then(
        (resp) => {
          if (resp['error'] && resp['type']=== 'UnverifiedEmailLogin'){

            // if user orginaly comes from verification page with senario he visit verification page from another browser
            // we navigate the user to login page to get his email then navigate him to verification page but without 
            // displaying error about verify in this case
            if(!localStorage.getItem('fromVerification')){
              this.authService.changeError(Errors.emailVerification);
            }
            else{
              localStorage.removeItem('fromVerification');
              this.authService.changeError("");
            }

            return Promise.reject('email verification');  
          }
          else if(resp['error']){
            this.authService.changeError(resp['error']);
            return Promise.reject(resp['error']);   
          }
          else{
             // Loading data about the user
            return this.oauthService.loadUserProfile();
          }
      },
      (err) => {
      //  console.log(err);
        this.loader = false;
        if (err['error']['error'] == 'Unauthorized'){
          this.resetMessage = Errors.InvalidEmailOrPassword;
          this.resetHelpLink();
        }
      }
      ).catch(error => {
        this.loader = false;
        if(error === 'email verification'){
          if(!localStorage.getItem('newUser')){
            localStorage.setItem('newUser', data.email);
            localStorage.setItem('futureRole', 'ROLE_EMPLOYER');
          }
          this.router.navigate(['/m/company/verification']);
        }
      })
      .then(() => {
            // Using the loaded user data
            let claims = this.oauthService.getIdentityClaims();
            this.loader = false;
            if (claims) {
              if(claims['user_info'].roles[0].name === 'ROLE_EMPLOYER'){
                localStorage.setItem('role',claims['user_info'].roles[0].name);
                localStorage.setItem("username",claims['user_info'].user_name);
                this.username = claims['user_info'].user_name;
                localStorage.setItem('company_id',claims['company_id']);
                localStorage.setItem("email",claims['user_info'].email);
                localStorage.setItem('company_name',claims['company_name']);
                //company logo
                if(claims['user_info'].profile_picture){
                  localStorage.setItem("pic",claims['user_info'].profile_picture);
                }
                else {
                  localStorage.setItem("pic","none");
                }

                this.generalService.notify(
                  'roleChanged' , 'membership','contact' , 
                  {'role':claims['user_info'].roles[0].name , 'email':claims['user_info'].email}
                );
                //if the user logged in in different rule like user , the guard will stop him and clear 
                // access token , so he won't access company route 
                // this.socketSerivce.storeUser(this.username);
                localStorage.setItem("have_profile",claims['have_profile']);
                if(claims['have_profile'] === 0){
                  this.router.navigate(['c/',this.username,'profile','new']);
                }
                else{
                  this.router.navigate(['c/',this.username,'manage_advs','published']);
                }
              }
              // account is job seeker and trying to login from employer login page 
              else{
                this.authService.logout(false);
                this.resetMessage =  Errors.loginWithWrongRuleEmployer
                this.helpLink = Errors.jobSeekerLoginLink;
              }
            }  // end claims

      });
      // this.oauthService.fetchTokenUsingPasswordFlowAndLoadUserProfile(data.email,data.password).then((res) => {
      //   console.log("in response",res);
      //   let claims = this.oauthService.getIdentityClaims();
      //       if (claims) {
      //         console.log("done");
      //         localStorage.setItem('role',claims['user_info'].roles[0].name);
      //         if(localStorage.getItem("role")=== 'ROLE_JOB_SEEKER'){
      //           this.resetMessage = this.loginWithWrongRule;
      //         }
      //         localStorage.setItem('company_id',claims['company_id']);
      //         this.router.navigate(['company/company-wrapper']);
      //       }
      // }
      // ,(err) => {
      //   console.log(err);
      //   if (err['error']['error'] == 'Unauthorized'){
      //     this.resetMessage = 'Invalid Email or Password';
      //   }
      // }
      // )
      // .catch(error => console.log("hello",error));
    }
    
    // if (this.loginForm.valid) {
    //   this.authService.loginCompany(data).subscribe(data => {
    //     if (data['response'] == 'error') {
    //       this.resetMessage = 'Invalid Email or Password';
    //     } else {
    //       localStorage.setItem('token', data['access_token']);
    //       this.router.navigate(['company/company-wrapper']);
    //     }
    //   });
    // }
  }

  // forgetPassword() {
  //   this.clickForgetPassword = true;
  //   const data = {'email' : this.loginForm.get('email').value};
  //   if (!this.isInvalid('email') && !this.isInvalidSyn('email')) {
  //     localStorage.setItem('oldUser', JSON.stringify(data));
  //     this.authService.forgetPasswordCompany(data).subscribe(res => {
  //       if (res['error']) {
  //         this.resetMessage = res['error'];
  //         this.resetHelpLink();

  //         // if user trying to reset password but its account is not verified yet, we navigate the user to verification page
  //         if(res['type']==='UnverifiedEmailRest'){
  //           this.authService.changeError(res['error']);
  //           this.router.navigate([res['help']]);
  //         }
  //       }
  //       if (res['data']) {
  //         this.router.navigate(['/m/company/reset-password-verification']);
  //       }
  //     });
  //   }
  // }

  public facebookLogin() {
    this.loader = true;

    this.authService.logoutIfAlreadyLoggedin();

    let socialPlatformProvider = FacebookLoginProvider.PROVIDER_ID;
    this.socialAuthService.signIn(socialPlatformProvider).then(
      (userData) => {
        this.authService.loginWithFacebookCompany(userData).switchMap(
          data => {
            if(data['success']){
              localStorage.setItem('access_token',data['token'].access_token);
              return this.authService.getUserInfo();
            }
            else if(data['type']=== 'noEmailFacebookAccount'){
              this.loader = false;
              this.authService.changeError(data['error']);
              this.router.navigate([data['help']]);
              return new EmptyObservable<Response>();
            }
            else{
              this.loader = false;
              if(data['error'])
                this.resetMessage = data['error'];
                this.resetHelpLink();
                if(data['help'])
                  this.helpLink = data['help'];
              // if(data['error']=== "There is no permission with Employer."){
              //   this.resetMessage = Errors.loginWithWrongRuleEmployer;
              // }
              return new EmptyObservable<Response>();
            } 
        })
        .subscribe(data => {
          localStorage.setItem("role",data['user_info'].roles[0].name);
          localStorage.setItem('company_id',data['company_id']);
          localStorage.setItem("username",data['user_info'].user_name);
          localStorage.setItem('company_name',data['company_name']);
          localStorage.setItem("email",data['user_info'].email);
          if(data['user_info'].profile_picture){
            localStorage.setItem("pic",data['user_info'].profile_picture);
          }
          else {
            localStorage.setItem("pic","none");
          }
          this.username = data['user_info'].user_name;
          this.generalService.notify(
            'roleChanged' , 'membership','contact' , 
            {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
          );
          this.loader = false;
        //  this.router.navigate(['c/',this.username]);
          localStorage.setItem("have_profile",data['have_profile']);
          if(data['have_profile'] === 0){
            this.router.navigate(['c/',this.username,'profile','new']);
          }
          else{
            this.router.navigate(['c/',this.username,'manage_advs','published']);
          }
        });
      }
    );
  }

  // old sign in with google
  // public signinWithGoogle () {
  //   this.loader = true;

  //   this.authService.logoutIfAlreadyLoggedin();
    
  //   let socialPlatformProvider = GoogleLoginProvider.PROVIDER_ID;

  //   this.socialAuthService.signIn(socialPlatformProvider)
  //     .then((userData) => {
  //       this.authService.loginWithGoogleCompany(userData).switchMap(
  //         data => {
  //           if(data['success']){
  //             localStorage.setItem('access_token',data['token'].access_token);
  //             return this.authService.getUserInfo();
  //           }
  //           else{
  //             this.loader = false;
  //             if(data['error']=== "There is no permission with Employer."){
  //               this.resetMessage = Errors.loginWithWrongRuleEmployer;
  //             }
  //           return new EmptyObservable<Response>();
  //           } 
  //       })
  //       .subscribe(data => {
  //         localStorage.setItem("role",data['user_info'].roles[0].name);
  //         localStorage.setItem('company_id',data['company_id']);
  //         localStorage.setItem("username",data['user_info'].user_name);
  //         localStorage.setItem('company_name',data['company_name']);
  //         if(data['user_info'].profile_picture){
  //           localStorage.setItem("pic",data['user_info'].profile_picture);
  //         }
  //         else {
  //           localStorage.setItem("pic","none");
  //         }
          
  //         this.username = data['user_info'].user_name;
  //         this.generalService.notify(
  //           'roleChanged' , 'membership','contact' , 
  //           {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
  //         );
  //         this.loader = false;

  //         localStorage.setItem("have_profile",data['have_profile']);
  //         if(data['have_profile'] === 0){
  //           this.router.navigate(['c/',this.username,'profile','new']);
  //         }
  //         else{
  //           this.router.navigate(['c/',this.username,'manage_advs','published']);
  //         }
  //       });
  //     });
  // }

  // new sign in with google
  initializeGoogleLogin(){
    const SCRIPT_PATH = 'https://accounts.google.com/gsi/client';
    const scriptElement = this.scriptService.loadJsScript(this.renderer, SCRIPT_PATH);
    scriptElement.onload = () => {
      // @ts-ignore
      google.accounts.id.initialize({
        client_id: environment.clientId,
        callback: this.handleGoogleLoginCredentialResponse.bind(this),
        auto_select: false,
        cancel_on_tap_outside: true
      });
      // @ts-ignore
      google.accounts.id.renderButton(
      // @ts-ignore
      document.getElementById("googleLoginButtonDiv"),
        {theme: "outline", size: "medium" , text:"signin_with", type:"standard",locale:"en-us"}
      );
    };
  }

  async handleGoogleLoginCredentialResponse(response: CredentialResponse) {
    this.ngZone.run( () => { 
      this.loader = true;
      let idToken = {
        "idToken":response.credential
      }
      this.authService.loginWithGoogleCompany(idToken).switchMap(
        data => {
          if(data['success']){
            localStorage.setItem('access_token',data['token'].access_token);
            return this.authService.getUserInfo();
          }
          else{
            this.loader = false;
            if(data['error'])
              this.resetMessage = data['error'];
              this.resetHelpLink();
              if(data['help'])
                this.helpLink = data['help'];
            // if(data['error']=== "There is no permission with Employer."){
            //   this.resetMessage = Errors.loginWithWrongRuleEmployer;
            // }
          return new EmptyObservable<Response>();
          } 
      })
      .subscribe(data => {
        localStorage.setItem("role",data['user_info'].roles[0].name);
        localStorage.setItem('company_id',data['company_id']);
        localStorage.setItem("username",data['user_info'].user_name);
        localStorage.setItem('company_name',data['company_name']);
        localStorage.setItem("email",data['user_info'].email);
        if(data['user_info'].profile_picture){
          localStorage.setItem("pic",data['user_info'].profile_picture);
        }
        else {
          localStorage.setItem("pic","none");
        }
        
        this.username = data['user_info'].user_name;
        this.generalService.notify(
          'roleChanged' , 'membership','contact' , 
          {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
        );
        this.loader = false;

        localStorage.setItem("have_profile",data['have_profile']);
        if(data['have_profile'] === 0){
          this.router.navigate(['c/',this.username,'profile','new']);
        }
        else{
          this.router.navigate(['c/',this.username,'manage_advs','published']);
        }
      //  this.router.navigate(['c/',this.username]);
      //  this.router.navigate(['company/company-wrapper']);
      });
    });
  }

  resetHelpLink(){
    this.helpLink = '';
  }

  toggleShowPassword() {
    this.show = !this.show;
    if (this.show) {
        this.type = 'text';
    } else {
        this.type = 'password';
    }
  }

  public signout () {
    this.socialAuthService.signOut();
  }

}
