import {<PERSON><PERSON>iew<PERSON>nit, Component, ComponentFactoryResolver, OnDestroy, OnInit, ViewChild, ViewContainerRef} from '@angular/core';
import {Subscription} from 'rxjs/Subscription';
import { SharedService } from "app/company/components/google-form/form-services/shared.service";
import { QuestionService } from "app/company/components/google-form/form-services/question.service";
import { Question } from "app/company/components/google-form/Models/question";
import { FormCustomerComponent } from "app/company/components/google-form/form-customer/form-customer.component";
import { PostJobService } from "app/company/services/post-job.service";
import { ActivatedRoute } from '@angular/router';
import { FormBuilder, Validators } from "@angular/forms";
import { FormJobService } from "app/company/components/google-form/form-services/form-job.service";



@Component({
  selector: 'full-form-customer-generator',
  templateUrl: './full-form-customer-generator.component.html',
  styleUrls: ['./full-form-customer-generator.component.css']
})
export class FullFormCustomerGeneratorComponent implements AfterViewInit, OnInit, OnDestroy {

  @ViewChild('dynamicInsert', {read: ViewContainerRef}) dynamicInsert: ViewContainerRef;
  components = [];
  subscribe: Subscription;
  subscribe2:Subscription;
  Active:boolean;
  itsTemp:boolean = false;
  companyAdvId = '';
  username = '';
  job_advertisement_id;
  job_temp_id;
  formAdded:boolean= true;
  postForm;
  formId;
  constructor(private componentFactoryResolver: ComponentFactoryResolver,
              private data: SharedService,
              private questionService: QuestionService,
              private postJobService: PostJobService,
              private route: ActivatedRoute,
              private fb: FormBuilder,
              private formJobService: FormJobService
  ) {

    this.postForm = this.fb.group({
      job_adv_id:[''],
      header:['',[Validators.required,
        Validators.minLength(2),
        Validators.maxLength(40)]],
      description:['',[Validators.required,
        Validators.minLength(2),
        Validators.maxLength(40)]],
      template: [''],
    });
    

    this.setRoutingParams();



    this.subscribe = this.data.currentValue.subscribe(refChild => {
      let index = this.components.indexOf(refChild);
      this.components.splice(index, 1);
      let i = 1;
      let fullOrderMap = [];
      if (this.components.length !== 0) {
        for (let c of this.components) {
          c.instance.order = i;
          let orderMap: { [k: string]: any } = {};
          orderMap.id = c.instance.quest_id;
          orderMap.order = c.instance.order;
          fullOrderMap.push(orderMap);
          i++;
        }
        this.formJobService.reOrderQuestion(fullOrderMap).subscribe(data => {});
      }
    });

    this.subscribe2=this.data.cssValue.subscribe(refChild=>{
      let index=this.components.indexOf(refChild);
      for (let c of this.components ){
       let cIndex= this.components.indexOf(c);
        if(index !==cIndex){
           c.instance.Active=false;
        }
      }
      this.Active=false;
    })


  }


  setRoutingParams(){
    this.route.params.subscribe(res => {
      this.companyAdvId = res['advId'];
    });
    /* this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    }); */
  }


  getTempId() {
    this.postJobService.currenttempId.subscribe(res => {
 
      if(res.length) {
        
        this.itsTemp = true;
        this.job_temp_id = res[0].Temp_id;
      } else {
        this.itsTemp = false
      } 
    });
  }

  getAdvId() {
    let advType = 'other';
    if(this.itsTemp)
      advType='template';
    this.postJobService.getGlobalAdvId(this.companyAdvId,advType).subscribe(res => {
     
      this.job_advertisement_id = res['data'].id;
     
    });
  }

  addForm(form) {
    if(this.postForm.valid) {
      let sendData = this.postForm.value;
     
      this.formAdded = false;
     
      if(this.itsTemp) {
        sendData.job_adv_id = this.job_temp_id;
        sendData.template = this.itsTemp
      } else {
        sendData.job_adv_id = this.job_advertisement_id;
        sendData.template = this.itsTemp
      }
      
      this.formJobService.AddForm(sendData).toPromise().then(
        (res) => {
        
          this.formId = res['data'].id;
         
        }
      )

    }
    
  }


  

  ngOnInit() {
    this.Active=true;
    this.getTempId();
    this.getAdvId();
    /* this.formJobService.getAllQuestions(this.formId)
      .subscribe(dataComp => {
        console.log('dataComp',dataComp);
        for (let i = 0; i < dataComp['questions'].length; i++) {
          let question: Question = dataComp['questions'][i];
          const componentFactory = this.componentFactoryResolver.resolveComponentFactory(FormCustomerComponent);
          const dynamicComponent = this.dynamicInsert.createComponent(componentFactory);
          dynamicComponent.instance._objRef = dynamicComponent;
          this.components.push(dynamicComponent);
          dynamicComponent.instance.order = this.components.length;
          dynamicComponent.instance.addQuestion(question);
        }
      }
    ); *//* this.formJobService.getAllQuestions(this.formId)
      .subscribe(dataComp => {
        console.log('dataComp',dataComp);
        for (let i = 0; i < dataComp['data'].length; i++) {
          let question: Question = dataComp['data'][i];
          const componentFactory = this.componentFactoryResolver.resolveComponentFactory(FormCustomerComponent);
          const dynamicComponent = this.dynamicInsert.createComponent(componentFactory);
          dynamicComponent.instance._objRef = dynamicComponent;
          this.components.push(dynamicComponent);
          dynamicComponent.instance.order = this.components.length;
          dynamicComponent.instance.addQuestion(question);
        }
      }
    ); */
  }

  ngAfterViewInit() {


  }

  
  


  addComponent() {
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(FormCustomerComponent);
    const dynamicComponent = this.dynamicInsert.createComponent(componentFactory);
    dynamicComponent.instance._objRef = dynamicComponent;
    this.components.push(dynamicComponent);
    dynamicComponent.instance.order = this.components.length;
    dynamicComponent.instance.form_id = this.formId;
    this.makeComponetsInActive();
    dynamicComponent.instance.Active=true;
    dynamicComponent.instance.addQuestion();
  }

  makeActive(){
    if(this.Active!==true) {
      this.Active = !this.Active;
      this.makeComponetsInActive();
    }
  }

  makeComponetsInActive(){
    for (let c of this.components ){
      c.instance.Active=false;
    }
  }

  ngOnDestroy() {
    this.subscribe.unsubscribe();
    this.subscribe2.unsubscribe();
  }

}
