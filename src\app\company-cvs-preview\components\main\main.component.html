<div class="row">
    <div class="col-md-12">
        <div class="filters-wrapper">
            <!-- <app-search-bar></app-search-bar> -->
            <!-- <page-navbar class="empty-pagenavbar" [navType]="'empty'"></page-navbar>
            <page-navbar class="mobile-pagenavbar"  [navType]="'recieveCV'"></page-navbar> -->
            <app-filters-wrapper [pageType]="'inbox'"></app-filters-wrapper>
        </div>
    </div>
</div>

<!-- [ngClass]="{'expand-folders': foldersCollapsed === false , 'collapse-folders' : foldersCollapsed === true , 'fix-position':fixPosition === true , '':fixPosition === false}" -->

<app-pre-loader [show]="firstLoad"></app-pre-loader>
<div cdkDropListGroup
    [hidden]="firstLoad"
    class="recieve-cvs-container" 
    [ngClass]="{'expand-folders': foldersCollapsed === false , 'collapse-folders' : foldersCollapsed === true}">
    <div class="folders-section">
        <app-folders-list></app-folders-list>
        <hr>
        <app-cvs-folders></app-cvs-folders>
    </div>
    <div class="folders-toggle" (click)="toggleFolders()">
        <i class="pi" [ngClass]="{'pi-angle-left': foldersCollapsed === false , 'pi-angle-right' : foldersCollapsed === true}"></i>
    </div>

    <div class="table-section">
        <app-cvs-table></app-cvs-table>
    </div>
</div>
<!-- <div class="row">
    <div class="col-md-2">
        <app-folders-list></app-folders-list>
    </div>
    <div class="col-md-10">
        <app-cvs-table></app-cvs-table>
    </div>
</div> -->