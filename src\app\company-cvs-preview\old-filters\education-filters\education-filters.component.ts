import { Component, DoCheck, Input, OnInit } from '@angular/core';
import { EducationFiltersDataModel } from './EducationFiltersDataModel';
import { KeyValueChanges, KeyValueDiffer, KeyValueDiffers } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
declare var $: any;
@Component({
  selector: 'app-education-filters',
  templateUrl: './education-filters.component.html',
  styleUrls: ['./education-filters.component.scss']
})
export class EducationFiltersComponent implements OnInit {
  public dataModel: EducationFiltersDataModel;
  private dataModelDiffer: KeyValueDiffer<string, any>;
  public temp: any;

  @Input() filterData;
  constructor(private generalService: GeneralService, private differs: KeyValueDiffers) {
    this.dataModel = new EducationFiltersDataModel();
  }

  ngOnInit(): void {
     // this.dataModelDiffer = this.differs.find(this.dataModel).create();
    this.sendInitStateToWarapper();

    this.temp = {
      'majors' : [],
      'minors': [],
   };
   ////////////
   this.temp['majors'] = this.filterData['majors'];
   this.temp['minors'] = this.filterData['minors'];
  }
  ///////////
  filterArray(event, arr) {
    this.temp[arr] = this.filterData[arr].filter(e => e.name.toLowerCase().includes(event.query.toLowerCase()));
  }


  // ngDoCheck(): void {
  //   const changes = this.dataModelDiffer.diff(this.dataModel);
  //   if (changes) {
  //     this.dataModel.setFilters();
  //     //  *  here  we set src of   this.generalService.notify equals to 'education' which it's not
  //     // the same of component name  "education-filters" please see filters-wrapper component

  //     this.generalService.notify('filters-changes',
  //       'education', 'filters-wrapper', this.dataModel.filters);
  //   }
  // }
////////////
  sendFilters() {
    this.dataModel.setFilters();
   this.generalService.notify('filters-changes',
     'education', 'filters-wrapper', this.dataModel.filters);
     $('#filtersModal').modal('hide');
 }


 sendInitStateToWarapper() {
  this.dataModel.setFilters();
  this.generalService.notify('init-filters',
    'education', 'filters-wrapper', this.dataModel.filters);
}

 hideModal() {
  $('#filtersModal').modal('hide');
}

}
