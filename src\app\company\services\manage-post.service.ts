import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from "rxjs";

@Injectable()
export class ManagePostService {
  url = '';
  reOrderUrl = '';
  reNewUrl = '';
  activeAdvUrl = '';
  refreshAdvUrl = '';
  endWorkFolwAdvUrl = '';
  saveAsTemplateAdvrUrl = '';
  deleteExpiredAdvrUrl = '';
  deleteTemplateAdvrUrl = '';
  saveAsPublishAdvrUrl = '';
  saveTemplateAsPublishAdvrUrl = '';
  editAdvr = '';
  editAdvrTemplate = '';
  getAdvrData = '';
  getAdvrTempData = '';
  getResumesData = '';
  urlHeader = '';
  getAdvrLogData = '' ;
  getAdvrTempLogData = '';
  private ok = new BehaviorSubject([]);
  Data = this.ok.asObservable();
  private advLog = new BehaviorSubject([]);
  advLogData = this.advLog.asObservable();
  constructor(private http: HttpClient ,
    private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.urlHeader = data;
      //this.url = data + 'manage_advertisements/by_type';
      this.url = data + 'manage_advs/by_type';
      this.reOrderUrl = data + 'jobAdvertisement/order';
     // this.reNewUrl = data + 'manage_advertisements/renew';
      this.reNewUrl = data + 'manage_advs/renew';
      // this.activeAdvUrl = data + 'manage_advertisements/activate';
     this.activeAdvUrl = data + 'manage_advs/activate';
      // this.refreshAdvUrl = data + 'manage_advertisements/refresh_listing';
      this.refreshAdvUrl = data + 'manage_advs/refresh_listing';
      //this.endWorkFolwAdvUrl = data + 'manage_advertisements/expired_advertisement';
      this.endWorkFolwAdvUrl = data + 'manage_advs/expired_advertisement';
      //this.saveAsTemplateAdvrUrl = data + 'manage_advertisements/save_template_for_adv';
      this.saveAsTemplateAdvrUrl = data + 'manage_advs/save_template_for_adv';
      // this.saveTemplateAsPublishAdvrUrl = data + '/manage_advertisements/save_publish_adv_from_template';
      this.saveTemplateAsPublishAdvrUrl = data + 'manage_advs/save_publish_adv_from_template';
      this.deleteExpiredAdvrUrl = data + 'jobAdvertisement';
      this.deleteTemplateAdvrUrl = data + 'job_advertisement_template'
      this.saveAsPublishAdvrUrl = data + 'save_as_publish'
      this.getAdvrData = data + 'jobAdvertisement/all_languages';
      this.getAdvrTempData = data + 'adv_template/all_languages';
      this.editAdvr = data + 'jobAdvertisement';
      this.editAdvrTemplate = data + 'job_advertisement_template';
      this.getResumesData = data + 'emp_application';
      this.getAdvrLogData = data + 'job_advertisement_log'
      this.getAdvrTempLogData = data + 'show_template_log'
     });
   }

   send_folder_id(value) {
    this.ok.next(value);
  }

  send_adv_id_log(value) {
    this.advLog.next(value)
  }

   getAdverByType(company_id,stat,page_num,page_SIZE,langId) {
    const params = new  HttpParams().set('status', stat)
    .set('pgnum',page_num)
    .set('pgsize',page_SIZE)
    .set('language_id',langId);

    return this.http.get(this.url + '/' + company_id,{ params });
   }

   orderCompanyAdvertisement(companyId,orderdAdvs) {
    return this.http.post(this.reOrderUrl + '/' + companyId, orderdAdvs);
   }

   renewCompanyAdvertisement(AdvrId) {
    return this.http.post(this.reNewUrl + '/' + AdvrId,'')

   }

   activateCompanyAdvertisement(AdvrId,status) {
    return this.http.post( this.activeAdvUrl + '/' + AdvrId,status)

   }

   refreshListingCompanyAdvertisement(AdvrId) {
    return this.http.post( this.refreshAdvUrl + '/' + AdvrId,'')

   }

   endWorkFlowCompanyAdvertisement(AdvrId){
    return this.http.post( this.endWorkFolwAdvUrl + '/' + AdvrId,'')
   }

   saveAsTemplateCompanyAdvertisement(AdvrId){
    return this.http.post( this.saveAsTemplateAdvrUrl + '/' + AdvrId,'')
   }

   deleteCompanyAdvertisement(AdvrId) {

    return this.http.delete( this.deleteExpiredAdvrUrl + '/' + AdvrId)
   }

   saveAsPublishCompanyAdvertisement(AdvrId){
    return this.http.post( this.saveAsPublishAdvrUrl + '/' + AdvrId,'')
   }

   saveTemplateAsPublishCompanyAdvertisement(AdvrId){
         return this.http.post( this.saveTemplateAsPublishAdvrUrl + '/' + AdvrId,'')
   }

   getCompanyAdvertisementData(AdvrId){
    return this.http.get( this.getAdvrData + '/' + AdvrId)
   }

   getCompanyTemplateAdvertisementData(AdvrId){
    return this.http.get( this.getAdvrTempData + '/' + AdvrId)
   }

   deleteCompanyAdvertisementTemplate(AdvrTempId) {

    return this.http.delete( this.deleteTemplateAdvrUrl + '/' + AdvrTempId)
   }

   editCompanyAdvr(AdvrId,data){
    return this.http.put( this.editAdvr + '/' + AdvrId,data)
   }

   editCompanyAdveTemplate(AdvrTempId,data) {
    return this.http.put( this.editAdvrTemplate + '/' + AdvrTempId, data)
   }

   getResumes(adv_id) {

    return this.http.get( this.getResumesData + '/' + adv_id)

   }

   getAdvrDataLog(adv_id,advType) {
     if(advType === 'template') {
      return this.http.get(this.getAdvrTempLogData + '/' + adv_id)

     } else {
      return this.http.get(this.getAdvrLogData + '/' + adv_id)
     }


   }



}
