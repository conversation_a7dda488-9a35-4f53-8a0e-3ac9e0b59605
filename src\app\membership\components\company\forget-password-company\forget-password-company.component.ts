import { Component, OnInit } from '@angular/core';
import { AuthService } from 'shared/shared-services/auth-service';
import { Router } from '@angular/router';
import { FormBuilder, Validators } from '@angular/forms';
import { Title, Meta } from '@angular/platform-browser';
import { EmailValidator } from 'shared/validators/email.validators';


@Component({
  selector: 'app-forget-password-company',
  templateUrl: './forget-password-company.component.html',
  styleUrls: ['./forget-password-company.component.css']
})
export class ForgetPasswordCompanyComponent implements OnInit {
  forgetPasswordForm;
  resetMessage;
  constructor(
    private authService: AuthService,
    private router: Router,
    private  fb: FormBuilder,
    private title: Title,
    private meta:Meta) 
    { }

  ngOnInit(): void {
    this.title.setTitle('CVeek Website | Forget password ');
    this.meta.updateTag({ name: 'robots', content: 'noindex' });

    this.forgetPasswordForm = this.fb.group({
      email: ['', [Validators.required, EmailValidator.isValidEmailFormat] ],
    });
  }

  forgetPassword() {
    if(this.forgetPasswordForm.valid){
      const data = {'email' : this.forgetPasswordForm.get('email').value};
        localStorage.setItem('oldUser', JSON.stringify(data));
        this.authService.forgetPasswordCompany(data).subscribe(res => {
          if (res['error']) {
            this.resetMessage = res['error'];
  
            // if user trying to reset password but its account is not verified yet, we navigate the user to verification page
            if(res['type']==='UnverifiedEmailRest'){
              this.authService.changeError(res['error']);
              this.router.navigate([res['help']]);
            }
          }
          if (res['data']) {
            // this.resetMessage = res['data'];
            this.router.navigate(['/m/company/reset-password-verification']);
          }
        });
    }
  }

  
}
