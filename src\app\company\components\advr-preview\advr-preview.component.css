.container-fluid{
    background-color: #eee;
    padding-top: 30px;
    padding-bottom: 30px;
    font-family: 'Exo2-Regular', sans-serif !important;
 }
.page-title {
    color: #276ea4;
    font-weight: bold;
    
}
.page-title h1{
    font-size: 25px;
}
a{
    text-decoration:none;
}
.page-title2{
    color: #276ea4;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 10px;
}
.modal.in .modal-dialog {
    width: auto;
    height: auto
  }
  .modal-dialog {
    margin-left: 8px;
    padding: 17px;
}
.company-name{
    text-decoration: underline;
}
.CForm{
    padding: 30px;
    background-color: white;
    border-radius: 10px;
    font-family: 'Exo2-Regular', sans-serif !important;
    width: 75%;
    margin: auto;
}
.title{
    color: #276ea4;
    font-size: 18px;
    font-weight: bold;
}
.user-pro-pic{
    text-align: right;
}
.user-pro-pic img {
    margin-left: auto;
    width: 110px;
}
.left-col-preview-alignleft-1 p{
    color: #32363a;
    font-size: 21px;
}
.left-col-preview-alignleft-2{
    color: #999;
    font-size: 18px;
}
.left-col-preview-alignleft-3 {
    color: #999;
    font-size: 16px;
}
.left-col-preview-alignleft-1 .left-col-preview-alignleft-2{
    color: #999;
    font-size: 18px;
}
.con{
    background-color:#eee;
    width: 100%;
    border: white 1px solid;
    margin:auto;
    padding-top: 9px;
    padding-bottom: 5px;
}
.con1{
    display: flex;
    flex-wrap: wrap;
}
.con1 span{
    font-size: 16px;
}
.con2{
    display: flex;
    flex-wrap: wrap;
    background-color: white;
    margin-top: 10px;
}
.con2 div , .con1 div{
    margin-bottom: 10px;
}
.divder{
    border-right: 1px red solid;
    width: 5px;
}
.blauen {
    color: #276ea4;
    cursor: pointer;
}

.middle_divs {
    padding-bottom: 0px;
}
.container {
    font-size: 16px;
    line-height: 19px;
    height: 34px;
    overflow: hidden;
}
.show {
    overflow: visible;
    height: auto;
}

/* Dropdown Button */
.btn {
    /* background-color: #276ea4; */
    color: white;
    padding: 16px;
    font-size: 16px;
    border: none;
    outline: none;
  }
  a.disabled {
    pointer-events: none;
    cursor: default;
    color:#999;
  }
  :host ::ng-deep .tooltip .farah{
    background-color: red !important;
    color: #fff;
    width:10px;
}
:host ::ng-deep .ui-tooltip .ui-tooltip-text {
    background-color: red !important;
    color: #fff;
    width:10px;
}
.float-publish-btn{
    position:fixed;
    top:137px;
    right:46px;
    z-index:5;
    padding: 7px;
}
.finish{
    background-color: #30A03E;
     border: #30A03E solid 1px;
     padding: 10px;
     padding-left:6px;
     width: 60%;
     float: left;
     border-bottom-right-radius: 0px; border-top-right-radius: 0px;
}
.finish1{
    background-color: #30A03E;
    border: #30A03E solid 1px;
    padding: 10px;
    width: 40px;
    border-bottom-left-radius: 0px; border-top-left-radius: 0px;
    border-left:1px solid white;
}
.apply-div{
    margin-top: 50px;
    /* margin-bottom: 40px; */
}
.apply{
    background-color: #30A03E;
    border: #30A03E solid 1px;
    padding: 10px;
    padding-left:6px;
    min-width: 120px;
    border-bottom-right-radius: 0px; border-top-right-radius: 0px;
}
.applied{
    background-color: #b8beb9;
    border: #b8beb9 solid 1px;
    padding: 10px;
    padding-left:6px;
    min-width: 120px;
    border-bottom-right-radius: 0px; border-top-right-radius: 0px;
}
.show-less {
    height: 3rem;
    overflow: hidden;
}
.img-responsive{
    max-width: 100%;
}
.adv-actions a{
    display:flex;
    align-items: center;
}

.editor-description{
    font-size: 16px; 
    text-align: justify !important; 
}
.editor-description h1, .editor-description h2{
    font-size:16px !important;
}
.adv-header-mobile, .published-mobile{
    display:none;
}
.verified-status{
    width:20px;
}
.adv-footer{
    border-top: 1px solid #eee;
    padding-top:10px;
    color:#bbb;
    text-align: justify;
    font-size:13px;
}
.about-company .adv-header-desktop, .about-company .adv-header-mobile{
    margin-bottom:15px;
}
.contact{
    text-decoration:none;
    cursor: pointer;
    font-weight: bold;
}
.verification-status{
    display:inline-block;
}
.verification-status img{
    width:20px;
}
.adv-separator{
    margin-top: 40px;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}
.adv-separator span{
    color: #bbb;
    font-size:13px;
}
/* start responsive styles */
@media screen and (max-width:547px), (min-width:770px) and (max-width:844px) {
    #draft {
        margin: 5px 0px 0px 22px;
    }
}
@media screen and (max-width:1010px) {
    .CForm{
        width:100%;
    }
    .left-col-preview-alignleft-3 {
        width: 100%;
    }
    /* .con1 div {
        margin-bottom: 6px;
    } */
}
@media screen and (max-width:767px) {
    .CForm {
        padding: 15px 15px 20px 15px;
    }
    .page-title h1{
        font-size:20px;
        line-height: 24px;
    }
    .user-pro-pic{
        text-align: center;
    }
    .user-pro-pic img {
        margin: auto;
    }
    .left-col-preview-alignleft-1 p {
        font-size: 19px;
    }
    .left-col-preview-alignleft-1 .left-col-preview-alignleft-2 {
        font-size: 15px;
    }
    /* .con1 div {
        margin-bottom: 6px;
    } */
    .adv-statistics{
        margin-bottom:30px;
    }
    .float-publish-btn{
        top:108px;
        right: 14px;
    }
    
    .adv-header-desktop{
        display:none;
    }
    .adv-header-mobile{
        display:block;
    }
    .adv-header-mobile .logo{
        width: 80px;
    }
    .adv-header-mobile .logo img{
        width: 80px;
    }
    .company-div{
        display:flex;
    }
    .adv-header-mobile .company-info{       
        width: calc(100% - 80px);
        padding-left:10px;
    }
    .adv-header-mobile .company-info .company-name{
        font-size:18px;
    }
    .adv-header-mobile .company-info .left-col-preview-alignleft-2{
        font-size:16px;
    }
    .published-mobile{
        display:inline-block;
        position:absolute;
        top:-8px;
        right:5px;
    }
}