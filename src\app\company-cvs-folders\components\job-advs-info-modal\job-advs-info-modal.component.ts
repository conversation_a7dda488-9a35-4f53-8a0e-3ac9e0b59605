import { Component, OnInit, Input, SimpleChanges } from '@angular/core';
import { Cv } from '../../../company-cvs-preview/components/cvs-table/DataModel';
import { CvsTableService } from '../../services/cvs-table.service';

@Component({
  selector: 'app-job-advs-info-modal',
  templateUrl: './job-advs-info-modal.component.html',
  styleUrls: ['./job-advs-info-modal.component.css']
})
export class JobAdvsInfoModalComponent implements OnInit {
  @Input('currentCv') currentCv: Cv;
  jobAdvsInfo = [];
  loading = true;
  constructor(private cvsTableService:CvsTableService) { }

  ngOnInit(): void {
    this.getFoldersForResume();
  }

  ngOnChanges(changes: SimpleChanges) {
    console.log(changes);
    if(changes['currentCv'] && changes['currentCv'].currentValue !==undefined){
      this.currentCv = changes['currentCv'].currentValue;
      this.getFoldersForResume();
    }
  }

  getFoldersForResume(){
    if(this.currentCv !== undefined){
      this.loading=true;
      this.jobAdvsInfo = [];
      this.cvsTableService.getResumeAppFolders(this.currentCv.resume_id).subscribe(res=>{
        this.loading=false;
        this.jobAdvsInfo = res['resume_app_folders'];
      });
    }
  }

}
