import { Val<PERSON><PERSON>, FormBuilder } from '@angular/forms';
import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { ContactService } from 'app/admin/services/contact.service';
import { Subject } from 'rxjs/Subject';
import { Table } from 'primeng/table';
import { FilterUtils } from 'primeng/utils';
import { Calendar } from 'primeng/calendar';
declare var $: any;

@Component({
  selector: 'app-messages',
  templateUrl: './messages.component.html',
  styleUrls: ['./messages.component.css']
})
export class MessagesComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  @ViewChild('cl') calendar: Calendar;
  rangeDates: Date[ ];
  // displayActions = [];
  filteredMessages = [];
  admins: {'value': number, 'label': string}[] = [];
  msgToPreview;
  operationType;
  displayMsgModal = false;
  displayAddModal = false;
  status;
  type;
  mainCats: {'value': number, 'label': string}[] = [];
  subCats: {'value': number, 'label': string, 'main_cat_id': number }[] = [];
  main_cat;
  sub_cat;
  private ngUnsubscribe: Subject<any> = new Subject();
  messages = [];
  types = [
        {'label': '', 'value': '' },
        // {'label': 'Visitor', 'value': 'ROLE_VISITOR' },
        // {'label': 'Admin', 'value': 'ROLE_ADMIN' },
        {'label': 'Job Seeker', 'value': 'ROLE_JOB_SEEKER' },
        {'label': 'Employer', 'value': 'ROLE_EMPLOYER' }
        ];
  statuses = [
    {'label': '', 'value': '' },
    {'label': 'new', 'value': 'new' },
    {'label': 'opened', 'value': 'opened' },
    {'label': 'commented', 'value': 'commented' },
    {'label': 'done', 'value': 'done' },
    {'label': 'not-done', 'value': 'not-done' },
    {'label': 'replied', 'value': 'replied' },
    {'label': 'assigned', 'value': 'assigned' },
    {'label': 'restored', 'value': 'restored' }
  ];
  opperationNum: number;
  commentForm: any;
  assignForm: any;
  msgIdToDelete: number;
  newCommentDetails: { comment: any; created_at: any; };
  msgId: number;
  mode: string;
  msgLog = [];
  templates = [];
  templateTitles = [];
  loading = false;

  constructor(private contact: ContactService, private fb: FormBuilder) {

    FilterUtils['isBetween'] = (value: any, filter: any): boolean => {
      // value = value.substring(0, 10);
      let newDate = new Date(value);
      console.log(newDate);
      // filter = this.formatDate(filter);
      filter = new Date(filter);
      if (this.rangeDates['1'] === null) {
           if (newDate === filter) {
            // let startDate = this.formatDate(this.rangeDates['0']);
            let startDate =new Date(this.rangeDates['0']);
            if (startDate <= newDate ) {
              return true;
             }
            }
      } else {
            //  let startDate = this.formatDate(this.rangeDates['0']);
            //  let finishDate = this.formatDate(this.rangeDates['1']);
             let startDate =new Date(this.rangeDates['0']);
             let finishDate =new Date(this.rangeDates['1']);
             console.log(startDate, finishDate);
                  if (startDate <= newDate && finishDate >= newDate) {                                                 {
                       return true;
                  }
      }
    }
  };

}

initAndRefreshTable(){
  this.loading = true;
  this.messages=[];
  this.admins = [];
  this.templateTitles = [];
  this.getMessages();
   this.getAdmins();
   this.getTemplates();
   this.loading = false;
}
  ngOnInit(): void {
    this.initAndRefreshTable();
  }

  getAdmins() {
    this.contact.getAdmins().takeUntil(this.ngUnsubscribe).subscribe(res => {
     console.log('adm res', res);
     for (let admin of res['data']) {
       this.admins.push({
         'value': admin.id,
         'label': admin.display_name
       });
     }
     this.admins.unshift({'value': null, 'label': '' });
     console.log('adms', this.admins);
    });
  }

  getMessages() {
   this.contact.getMessages().takeUntil(this.ngUnsubscribe).subscribe(res => {
     console.log('res', res);
     let temp = res['data'];
     let comments = [], replies = [], assign_log = [];
     for (let msg of temp) {
       comments = msg.admin_comment;
       replies = msg.admin_replied;
       assign_log = msg.admin_email_assign_log;
          this.messages.push({
            'id'         : msg.id,
            'year'       :msg.created_at.substr(0, 4), 
            'name'       : ((msg.user === null) || (msg.user.display_name.length === 0)) ? '------' : msg.user.display_name,
            'type'       : (msg.user === null) ? 'ROLE_VISITOR' : msg.user.role,
            'message'    : msg.message,
            'main_cat_id': msg.contact_main_cat_id,
            'main_cat'   : msg.contact_main_cat,
            'sub_cat_id' : msg.contact_sub_cat_id,
            'sub_cat'    : (msg.contact_sub_cat === null) ? '....' : msg.contact_sub_cat,
            'email'      : msg.email,
            'langId'     : msg.translated_languages_id,
            'language'   : msg.translated_languages,
            'date'       : msg.created_at,
            'assign_log' : assign_log,
            'comments'   : comments,
            'replies'    : replies,
            'last_reply' : msg.last_reply,
            'handled_by' : msg.handled_by,
            'status'     : (msg.last_action_name === null ) ? 'new' : msg.last_action_name.toLowerCase(),
            'display'    : false,
            'images'     : msg.images
          });

      //  this.displayActions.push(false);
     }
     this.table.filter(null, 'id', 'startsWith');
     // this.filteredMessages = this.messages;
     console.log('messages', this.messages);
     this.loading = false;
   });


  }


  getLangAbbrev(lang: string) {
    let langAbbrev;
    switch (lang.toLocaleLowerCase()) {
      case 'english':
         langAbbrev = 'en';
         break;
      case 'arabic':
         langAbbrev = 'ar';
         break;
      default:
        langAbbrev = undefined;
    }
    return langAbbrev;

  }

  getTemplates() {
    this.contact.getMsgTemplates().takeUntil(this.ngUnsubscribe).subscribe(res => {
       console.log('res', res);
       let temp = res['template_emails'];
       let temp2 = res['template_titles'];
       let temp3 = res['ContactMainCat'];
       let temp4 = res['ContactSubCat'];
       for (let t of temp) {
           this.templates.push({
             'id'         : t.id,
             'title'      : (t.user === null) ? '.....' : t.template_title,
             'email_title': t.email_title,
             'main_cat_id': t.contact_main_cat_id,
             'main_cat'   : t.contact_main_cat,
             'sub_cat_id' : t.contact_sub_cat_id,
             'sub_cat'    : (t.contact_sub_cat === null) ? '....' : t.contact_sub_cat,
            // 'short_reply'   : t.short_reply,
             'detailed_reply': t.detailed_reply,
           });

       }
       console.log('template msg', this.templates);
       for (let title of temp2) {
         this.templateTitles.push({
           'label': title.template_title,
           'value': title.id,
          //  'id'   : title.id
         });
       }
       this.templateTitles.unshift({ 'label': '', 'value': null});
       for (let main of temp3) {
        this.mainCats.push({
          'label': main.name,
          'value': main.id
        });
      }
      this.mainCats.unshift({ 'label': '', 'value': null });
      for (let sub of temp4) {
        this.subCats.push({
          'label'      : sub.name,
          'value'      : sub.id,
          'main_cat_id': sub.contact_main_cat_id
        });
      }
      this.subCats.unshift({ 'label': '', 'value': null, 'main_cat_id': null });

     });
   }



  addToSelected(msg) {
    console.log('selected', this.filteredMessages);
  }


  displayModal(msg) {
    // this.resetStyle();
    this.displayMsgModal = true;
    this.mode = 'reply_form';
    this.msgToPreview = {
      'id'         : msg.id,
      'name'       : msg.name,
      'type'       : msg.type,
      'message'    : msg.message,
      'main_cat_id': msg.main_cat_id,
      'main_cat'   : msg.main_cat,
      'sub_cat_id' : msg.sub_cat_id,
      'sub_cat'    : msg.sub_cat,
      'email'      : msg.email,
      'langId'     : msg.langId,
      'language'   : msg.language,
      'date'       : msg.date,
      'year'       : msg.year,
      'assign_log' : msg.assign_log,
      'comments'   : msg.comments,
      'replies'    : msg.replies,
      'last_reply' : msg.last_reply,
      'status'     : msg.status,
      'images'     : msg.images

    };
    console.log('msg', this.msgToPreview);
    if (msg.status === 'new') {
      this.contact.setAsOpen(msg.id).subscribe(res => {
        console.log('res', res);
        let i = this.getMsgIndex(msg.id);
        this.messages[i].status = 'opened';
        this.msgToPreview.status = 'opened';
      });
    }


  }

  displayComments(msg) {
    // this.applySpecificStyle();
    this.displayMsgModal = true;
    this.mode = 'preview_comments';
    this.msgToPreview = {
      'id': msg.id,
      'comments': msg.comments,
      'status'  : msg.status
    };

  }

  displayReplys(msg) {
    // this.applySpecificStyle();
    this.displayMsgModal = true;
    this.mode = 'preview_replys';
    this.msgToPreview = {
      'replies'    : msg.replies,
    };
  }

  // applySpecificStyle() {
  //   $('#msgModal .modal-dialog').css('width', '50%');
  // }

  // resetStyle() {
  //   $('.modal-dialog').css('width', 'inherit');
  // }

  closeModal() {
    this.displayMsgModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
    this.msgLog = [];
  }


  closeAddModal() {
    this.displayAddModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
    }


  addNewComment(event) {
    console.log('event', event);
    let i = this.getMsgIndex(this.msgToPreview.id);
    let newComment = event['data'].admin_comment[event['data'].admin_comment.length - 1];
    console.log('new comment', newComment);
    this.messages[i].comments.push(newComment);
    this.messages[i].status = 'commented';
    this.msgToPreview.status = 'commented';
    // this.msgToPreview.comments.push(newComment);
  }

  assignTo(event) {
    console.log('event', event);
    let i = this.getMsgIndex(this.msgToPreview.id);
    this.messages[i].assign_log = event['data'].admin_email_assign_log;
    this.messages[i].status = 'assigned';
    this.msgToPreview.status = 'assigned';
    this.msgToPreview.assign_log = event['data'].admin_email_assign_log;
  }

  displayMsgLog(msg) {
    this.contact.getMsgLog(msg.id).subscribe(res => {
     console.log('res', res);
     this.msgLog = res as Array<Object>;
     console.log('msg log', this.msgLog);
    });
    this.displayMsgModal = true;
    this.mode = 'log_mode';


  }

  updateMsgStatus(event) {
    console.log('event', event);
    let status = event['status'];
    let msgId = event['msgId'];
    let i = this.getMsgIndex(msgId);
    this.messages[i].status = status;
    this.msgToPreview.status = status;
    console.log('new status', this.messages[i].status) ;
  }

  addReplyResult(event) {
    let msg = event['msg'];
    let index = this.getMsgIndex(msg.id);
    this.messages[index].replies = msg.admin_replied;
    this.closeModal();
  }


  displayDeleteAlert(num, msgId?) {
    this.opperationNum = num;
    if (msgId) {
      this.msgIdToDelete = msgId;
    }
    this.displayAddModal = true;
  }

  deleteMultiMsgs() {
   let msgsIds = [];
   for (let msg of this.filteredMessages) {
      msgsIds.push(msg.id);
   }
   this.contact.deleteMultiMsgs({ 'received_messages_ids': msgsIds}).subscribe(res => {
      console.log('res', res);
      this.closeAddModal();
      if ((res['success'] as string) === 'false' ) {
        alert(res['messages']);
      } else {
        for (let i = 0; i < this.messages.length; i++) {
          for (let id of msgsIds) {
            if (this.messages[i].id === id) {
              this.messages.splice(i, 1);
            }
          }
        }
        this.table._totalRecords = this.messages.length;
        this.filteredMessages = [];
      }
    });

  }

  deleteMsg() {
    this.contact.deleteMultiMsgs({ 'received_messages_ids': [this.msgIdToDelete] }).subscribe(res => {
      console.log('res', res);
      this.closeAddModal();
      if ((res['success'] as string) === 'false') {
        alert(res['messages']);
      } else {
        let i = this.getMsgIndex(this.msgIdToDelete);
        this.messages.splice(i, 1);
        this.table._totalRecords = this.messages.length;
      }
    });

  }

  getMsgIndex(msgId) {
    for (let i = 0; i < this.messages.length; i++) {
      if (this.messages[i].id === msgId) {
        console.log('index', i);
        return i;
      }
    }
  }

  onDateSelect(value) {
    console.log('value', value);
    console.log('range dates', this.rangeDates);
     this.table.filter(this.rangeDates, 'date', 'dateFilter');
    // let res = [];
    // res = this.formatDate(this.rangeDates);
    // // this.table.filter(res[0], 'date', 'gte');
    // // this.table.filter(res[res.length - 1], 'date', 'lte');
    // console.log('value', value);
    // console.log('range dates', this.rangeDates);
  }



  formatDate(date): Date {
    if (date[1] !== null) {
    let month = date.getMonth() + 1;
    let day = date.getDate();

    if (month < 10) {
        month = '0' + month;
    }

    if (day < 10) {
        day = '0' + day;
    }

    return new Date(date.getFullYear() + '-' + month + '-' + day);
    }
  }

//   formatDate(date) {
//    //  console.log('date', date);
//     if (date[1] !== null) {
//       let res  = [];
//       for (let i = 0; i < date.length; i++) {
//         let month = date[i].getMonth() + 1;
//         let day = date[i].getDate();

//         if (month < 10) {
//             month = '0' + month;
//         }

//         if (day < 10) {
//             day = '0' + day;
//         }
//         res.push(date[i].getFullYear() + '-' + month + '-' + day);
//         // console.log('res', res);
//         // if (i === 0) {
//         //   this.table.filter(res[0], 'date', 'gte');
//         // }

//         // if (i !== 0 && (i === (date.length - 1))) {
//         //   this.table.filter(res[res.length - 1], 'date', 'lte');
//         // }
//         if (i !== (date.length - 1)) {
//           let item = date[i];
//           // console.log(item);
//           let nextDay: any = item.getDate() + 1;
//           while (nextDay <= date[date.length - 1].getDate()) {
//             item.setDate(nextDay);
//             if (nextDay < 10) {
//               nextDay = '0' + nextDay;
//             }
//             res.push(item.getFullYear() + '-' + month + '-' + nextDay);
//             //  item = date[i + 1];
//            nextDay = item.getDate() + 1;
//         }
//       }
//       console.log('date arr', res);
//       return res;
//     }
//   }
//  }



 clearAll() {
  this.table.filter(null, 'id', 'startsWith');
  this.table.filter(null, 'year', 'startsWith');
  this.table.filter(null, 'email', 'contains');
  this.table.filter(null, 'type', 'equals');
  this.table.filter('', 'date', 'contains');
  this.table.filter(null, 'language', 'contains');
  this.table.filter(null, 'main_cat_id', 'equals');
  this.table.filter(null, 'sub_cat_id', 'equals');
  this.table.filter(null, 'status', 'equals');
  this.table.filterGlobal(null, 'contains');
  $('span.ui-column-filter.ui-calendar input').val(null);
  $('.ui-table-globalfilter-container input').val(null);
  console.log($('.ui-column-filter').val());
  $('.ui-column-filter').val(null);
   this.status = '';
   this.type = '';
   this.main_cat = '';
   this.sub_cat = '';
   this.rangeDates = [new Date(''), new Date('')];
   this.calendar.onClearButtonClick('');
 }


  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}



























// changeStyle() {
//   $('.ui-table-messages .ui-table-tbody .fa').css('zindex', '101');
//   // $('.ui-table-messages .ui-table-tbody .fa').css('position', 'absolute');
//   // $('.ui-table-messages .ui-table-tbody .ui-selectable-row').css('position', 'relative');

// }

// resetStyle() {
//   $('.ui-table-messages .ui-table-tbody .fa').css('zindex', 'unset');
//   // $('.ui-table-messages .ui-table-tbody .fa').css('position', 'unset');
//   // $('.ui-table-messages .ui-table-tbody .ui-selectable-row').css('position', 'unset');

// }


// paginate(event) {
//   //event.first = Index of the first record
//   //event.rows = Number of rows to display in new page
//   //event.page = Index of the new page
//   //event.pageCount = Total number of pages
// }




