<h3 class="verf-heading"> Manage Values Verification</h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ transactionsArray.length }}</span></p>
<!-- <p>filtered: <span class="badge badge-primary badge-pill">{{ filteredTransactions.length }}</span></p> -->


<p-table #dt [value]="transactionsArray" [(selection)]="filteredTransactions" dataKey="id"
  styleClass="ui-table-questions" [rowHover]="true" [rows]="10" [showCurrentPageReport]="true"
  [rowsPerPageOptions]="[10,25,50]" [loading]="loading" [paginator]="transactionsArray.length"
  currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [filterDelay]="0"
  [globalFilterFields]="['id', 'trans_id', 'date', 'time','type', 'handled_by', 'admin_time', 'duration']">
  <ng-template pTemplate="caption">
    <i class="fa fa-remove" title="clear All" (click)="clearAll()"></i>
    <div class="ui-table-globalfilter-container">
      <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
        placeholder="Global Search" />
    </div>
  </ng-template>
  <ng-template pTemplate="header">
    <tr>
      <th pSortableColumn="cv_id" style="width:70px;">CV ID <p-sortIcon field="cv_id"></p-sortIcon>
      </th>
      <th pSortableColumn="adv_id" style="width:70px;">Adv ID <p-sortIcon field="adv_id"></p-sortIcon>
      </th>
      <th pSortableColumn="trans_id" style="width:80px;">Transaction ID <p-sortIcon field="trans_id"></p-sortIcon>
      </th>
      <th pSortableColumn="name" style="width:130px;">name <p-sortIcon field="neme"></p-sortIcon>
      </th>
      <th pSortableColumn="date" style="width:100px;">Date <p-sortIcon field="date"></p-sortIcon>
      </th>
      <!-- <th pSortableColumn="time" style="width:100px;">Time <p-sortIcon field="time" ></p-sortIcon></th> -->
      <th pSortableColumn="type" style="width:100px;">Type <p-sortIcon field="type"></p-sortIcon>
      </th>
    </tr>
    <tr>
      <!-- <th>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected questions" (click)="displayDeleteAlert(4)"></i>
            </th> -->
      <th>
        <input pInputText type="text" (input)="dt.filter($event.target.value, 'cv_id', 'contains')" placeholder=""
          class="ui-column-filter" style="width:80px;" >
      </th>
      <th>
        <input pInputText type="text" (input)="dt.filter($event.target.value, 'adv_id', 'contains')" placeholder=""
          class="ui-column-filter"  style="width:80px;">
      </th>
      <th>
        <input pInputText type="text" (input)="dt.filter($event.target.value, 'trans_id', 'contains')" placeholder=""
          class="ui-column-filter"  style="width:130px;">
      </th>
      <th>
        <input pInputText type="text" (input)="dt.filter($event.target.value, 'name', 'contains')" placeholder=""
          class="ui-column-filter">
      </th>
      <th>
        <p-calendar #cl [(ngModel)]="rangeDates" (onSelect)="dt.filter(rangeDates, 'date', 'isBetween')"
          (onClearClick)="dt.filter('', 'date', 'contains')" [showButtonBar]="true" yearRange="2020:2030"
          selectionMode="range" styleClass="ui-column-filter" placeholder="" [readonlyInput]="true"
          dateFormat="yy-mm-dd"></p-calendar>
      </th>

      <!-- <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'time', 'contains')" placeholder="" class="ui-column-filter">
            </th> -->
      <th>
        <p-dropdown [options]="types" (onChange)="dt.filter($event.value, 'type', 'equals')"
          styleClass="ui-column-filter" placeholder="" [(ngModel)]="type" [showClear]="true">
          <ng-template let-option pTemplate="item">
            <span [class]="'question-badge status-' + option.value">{{option.label}}</span>
          </ng-template>
        </p-dropdown>
      </th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-trans>
    <tr class="ui-selectable-row" data-toggle="modal" data-target="#transModal" style="cursor: pointer;"
      (click)="displayTModal(trans)">
      <!-- <td>
                <p-tableCheckbox [value]="trans" (click)="addToSelected(trans)"></p-tableCheckbox>
            </td> -->
      <td class="text-center">
        {{ trans.cv_id }}
      </td>
      <td class="text-center">
        {{ trans.adv_id }}
      </td>
      <td class="text-center">
        {{ trans.trans_id }}
      </td>
      <td class="text-center">
        {{ trans.name }}
      </td>
      <td class="text-center">
        {{ trans.date }}
      </td>
      <!-- <td>
              {{ trans.time }}
            </td> -->
      <td class="text-center">
        <span [class]="'question-badge status-'+ trans.type ">{{ trans.type }} </span>
      </td>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptytrans">
    <tr>
      <td colspan="9" style="text-align:center;padding:15px;">No transactions found.</td>
    </tr>
  </ng-template>
</p-table>

<p style="text-align:center;padding:15px;">{{ message }} </p>



<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayModal" id="transModal" tabindex="-1" role="dialog"
  aria-labelledby="createModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
        <h3 *ngIf="transactionToPreview.type === 'UNIVERSITY'"> University Verification </h3>
        <h3 *ngIf="transactionToPreview.type === 'SKILL'"> Skill Verification </h3>
        <h3 *ngIf="transactionToPreview.type === 'MAJOR'"> Major Verification </h3>
        <h3 *ngIf="transactionToPreview.type === 'MINOR'"> Minor Verification </h3>
        <h3 *ngIf="transactionToPreview.type === 'JOB_TITLE'"> Job Title Verification </h3>
        <h3 *ngIf="transactionToPreview.type === 'JOB_TITLE_SYNONYMS'"> Job Title Synonyms Verification </h3>
      </div>
      <!-- [majorParentArr]="majorParentArr" -->
      <app-transaction [userInput]="transactionToPreview" [languagesArray]="languagesArray"
        (closeModal)="removeTransFromTable($event)" [skillParentArr]="skillParentArr" [skillCatsArr]="skillCatsArr"
         [majorArr]="majorArr" [experienceFields]="experienceFieldsArr"
        [jobTitles]="jobTitlesArr" (verifyMajorClicked)="openMajorVerify()"
        (verifyJobTitleClicked)="openJobTitleVerify()" (openAddParentModal)="addMajorParent()"
        (openAddCatModal)="addSkillCategory()" (openAddExpFieldModal)="addExpField()"></app-transaction>

      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>
<!-- end of  modal-->





<!-- add  modal-->
<div class="modal fade" *ngIf="displayAddModal" id="addValueModal" tabindex="-2" role="dialog"
  aria-labelledby="addModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeAddModal()" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
        <h3>Add new Value </h3>
      </div>
      <div class="modal-body">
        <ul class="nav nav-tabs">
          <li *ngFor="let lang of languagesArray" role="presentation" class="btn"
            [class.active]="lang.id === currentLangId" (click)="changeLang(lang.id)">{{lang.name}}</li>
        </ul>

        <form [formGroup]="majorForm" *ngIf="opperationNum === 1">

          <div formArrayName="major_parent_trans" *ngFor="let item of languagesArray; let i= index">
            <div [formGroupName]="i" *ngIf="item.id === currentLangId ">

              <div class="form-group">
                <label for="name">Name
                  <span
                    *ngIf="major_parent_trans.controls[i].controls.name.touched && major_parent_trans.controls[i].controls.name.invalid"
                    class="required">**</span>
                </label>
                <input formControlName="name" name="name" type="text" class="form-control" id="name">
                <div
                  *ngIf="major_parent_trans.controls[i].controls.name.touched && major_parent_trans.controls[i].controls.name.invalid">
                  <div *ngIf="major_parent_trans.controls[i].controls.name.errors.required" class="alert alert-danger">
                    Name is Required</div>
                </div>
              </div>
              <button type="submit" class="btn btn-success" [disabled]="majorForm.invalid" (click)="addNewValue()">Add
              </button>
            </div>
          </div>
          <p>{{ majorForm.value | json }}</p>
        </form>

        <form [formGroup]="skillForm" *ngIf="opperationNum === 2">
          <div formArrayName="skill_category_trans" *ngFor="let item of languagesArray; let i= index">
            <div [formGroupName]="i" *ngIf="item.id === currentLangId ">

              <div class="form-group">
                <label for="name">Name
                  <span
                    *ngIf="skill_category_trans.controls[i].controls.name.touched && skill_category_trans.controls[i].controls.name.invalid"
                    class="required">**</span>
                </label>
                <input formControlName="name" name="name" type="text" class="form-control" id="name">
                <div
                  *ngIf="skill_category_trans.controls[i].controls.name.touched && skill_category_trans.controls[i].controls.name.invalid">
                  <div *ngIf="skill_category_trans.controls[i].controls.name.errors.required"
                    class="alert alert-danger">Name is Required</div>
                </div>
              </div>
              <button type="submit" class="btn btn-success" [disabled]="skillForm.invalid" (click)="addNewValue()">Add
              </button>
            </div>
          </div>
          <p>{{ skillForm.value | json }}</p>
        </form>

        <form [formGroup]="expFieldForm" *ngIf="opperationNum === 3">
          <div formArrayName="experience_fields_trans" *ngFor="let item of languagesArray; let i= index">
            <div [formGroupName]="i" *ngIf="item.id === currentLangId ">

              <div class="form-group">
                <label for="name">Name
                  <span
                    *ngIf="experience_fields_trans.controls[i].controls.name.touched && experience_fields_trans.controls[i].controls.name.invalid"
                    class="required">**</span>
                </label>
                <input formControlName="name" name="name" type="text" class="form-control" id="name">
                <div
                  *ngIf="experience_fields_trans.controls[i].controls.name.touched && experience_fields_trans.controls[i].controls.name.invalid">
                  <div *ngIf="experience_fields_trans.controls[i].controls.name.errors.required"
                    class="alert alert-danger">Name is Required</div>
                </div>
              </div>
              <button type="submit" class="btn btn-success" [disabled]="expFieldForm.invalid"
                (click)="addNewValue()">Add </button>
            </div>
          </div>
          <p>{{ expFieldForm.value | json }}</p>
        </form>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->