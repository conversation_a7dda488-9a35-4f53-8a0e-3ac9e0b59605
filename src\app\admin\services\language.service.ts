import { MenuItem } from 'primeng/api';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
declare var $: any;
@Injectable()
export class LanguageService {
  url = '';
  baseUrl = '';

  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.url     = data + 'admin/faq';
          this.baseUrl = data;
    });
  }

  getLanguages() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + 'translated_languages', { headers });

  }

  getLangAbbrev(langId: number) {
    let langAbbrev;
    switch (langId) {
      case 1:
         langAbbrev = 'en';
         break;
      case 2:
         langAbbrev = 'ar';
         break;
      default:
        langAbbrev = undefined;
    }
    return langAbbrev;
  }

  changeDirection(component:string, langId: number, items: string[]) {
    if (langId === 2) {
      $('html').attr('lang', 'ar');
      for (let item of items) {
        $('' + item + '').css('direction', 'rtl');
        console.log(item);
      }
      console.log(items);
     $('input').css('direction', 'rtl');
     $('textarea').css('direction', 'rtl');
    //  $('ul').css('direction', 'rtl');
    //  $('li').css('direction', 'rtl');
    } else {
      $('html').attr('lang', this.getLangAbbrev(langId));
      for (let item of items) {
        $(item).css('direction', 'ltr');
      }
      $('input').css('direction', 'ltr');
      $('textarea').css('direction', 'ltr');
      // $('ul').css('direction', 'ltr');
      // $('li').css('direction', 'ltr');
    }
  }
}
