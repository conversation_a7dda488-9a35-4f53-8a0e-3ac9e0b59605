<un-auth-top-navbar *ngIf="role === 'unauth'" [inHomePage]="inHomePage"></un-auth-top-navbar>
<!-- <top-navbar *ngIf="role ==='ROLE_JOB_SEEKER'"></top-navbar> -->
<user-topbar *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_ADMIN'|| role === 'ROLE_CONTACT_ADMIN'" [inHomePage]="inHomePage"></user-topbar>
<company-topbar *ngIf="role === 'ROLE_EMPLOYER'" [inHomePage]="inHomePage"></company-topbar>

<page-navbar [navType]="'home'"></page-navbar>

<confirm-message *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_EMPLOYER'"></confirm-message>

<app-pre-loader [show]="showLoader"></app-pre-loader>

<!-- add it to following div to lazyload youtube video
appResourcesLazyload -->

<div class="home-page-content"  [ngClass]="{'home-rtl':pageInfo.direction === 'rtl'}">

    <p-toast position="bottom-right"></p-toast>

    <!-- <img  src="./assets/images/home/<USER>" class="background" alt="cveek-launch-soon" (load)="showLoader = false;"> -->
    <div style="background-color:#E3B336;text-align: center;">
      <img  src="./assets/images/home/<USER>" class="background" alt="welcome-to-cveek" (load)="showLoader = false;">
    </div>
    
    <!-- <div *ngIf="sliderLoader === true" style="font-size:70px;">Loading</div>
    <div *ngIf="sliderLoader === true" style="min-height: 200px;margin:auto;">
      <p-progressSpinner></p-progressSpinner>
    </div>
    <img src="./assets/images/slider/cv.jpg" class="background" alt="CVeek" (load)=" hideLoader()"> -->


    <!-- <div class="slide">
      <img src="./assets/images/slider/cv.jpg" class="background" alt="cevast">
    </div> -->

    <!-- <div class="slide">
      <img src="./assets/images/slider/slide.jpg" class="background" alt="cevast">
      <div class="your">It's your</div>
      <div class="day">Day</div>
      <div class="because">Because you are here!</div>
      <div class="colleagues">your new colleagues are excited to know you</div>
      <div class="deal">We are here to help you deal with whatever assignment you are s copy</div>
    </div> -->

    <!-- <div class="slide">
      <img src="./assets/images/slider/bg.png" class="background" alt="cevast">
      <img src="./assets/images/slider/lucky.png" class="lucky" alt="lucky">
    </div>
     -->

  <!-- class="carousel-fade"  -->
    <!-- <div id="carousel-example-generic" class="carousel slide" data-ride="carousel" data-interval="5000">
        <ol class="carousel-indicators">
          <li data-target="#carousel-example-generic" data-slide-to="0" class="active"></li>
          <li data-target="#carousel-example-generic" data-slide-to="1"></li>
          <li data-target="#carousel-example-generic" data-slide-to="2"></li>
        </ol>

        <div class="carousel-inner" role="listbox">
          <div class="item active">
            <img src="./assets/images/slider/bg.png" alt="cevast">

            <img src="./assets/images/slider/lucky.png" class="lucky" alt="lucky">

            <div class="carousel-caption">
                <h3>caption slide1</h3>
                <p>...</p>
            </div>
          </div>
        </div>

        <a class="left carousel-control" href="#carousel-example-generic" role="button" data-slide="prev">
          <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
          <span class="sr-only">Previous</span>
        </a>
        <a class="right carousel-control" href="#carousel-example-generic" role="button" data-slide="next">
          <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
          <span class="sr-only">Next</span>
        </a>
      </div> -->

      <!-- End of Slider Section -->

    <div class="home-sections" *ngIf="!showLoader">
      <div class="section text-center center-p about-section" style="margin-top: 20px;" (deferLoad)="lazyloadItems[0]=true">
        <div *ngIf="pageInfo.direction === 'rtl'; else section1_ltr">
          <h2 class="centered-heading">ما هو موقع سيفيك CVeek</h2>
          <p class="section-paragraph"> تم تطوير موقع سفيك CVeek ليكون موقعاً رائداً لانشاء سيرة ذاتية احترافية مجاناً ( يتم ذلك من خلال كتابة السيرة الذاتية اون لاين في الموقع، أو من خلال تحميل السيرة الذاتية كملف PDF إلى الموقع) والبحث عن فرص عمل مميزة في العديد من الدول.</p>
          <br><br>
          <h3 class="centered-heading">رؤيتنا</h3>
            <p class="important-txt">أن نكون أفضل موقع لإنشاء السيرة الذاتية مجاناً</p>
            <p class="important-txt">أحد أفضل مواقع التوظيف في الخليج وعموم الشرق الأوسط والعالم</p>
        </div>
        <ng-template #section1_ltr>
          <div class="row">
            <div class="col-sm-6 sec-mar-bot-mob">
              <div>
                <img *ngIf="lazyloadItems[0]" class="about-img" src="./assets/images/home/<USER>" alt="about-cveek">
              </div>

              <div>
                <h2 class="centered-heading">About CVeek</h2>
                <p class="section-paragraph">
                  CVeek is developed to be a leading site for <strong>creating free professional CVs online </strong>
                  creating free professional CVs online <strong>best job opportunities </strong>for all professions in the world.
                </p>
              </div>
            </div>
            <div class="col-sm-6">
              <div>
                <img *ngIf="lazyloadItems[0]" class="vision-img" src="./assets/images/home/<USER>" alt="cveek-vision">
              </div>
    
              <div>
                <h2 class="centered-heading">Our vision</h2>
                <p>To be the Best website to create best free CV online.</p>
                <p>To be the best Job Hunting site in the Arabic Gulf, Middle East, and the world.</p>
              </div>
            </div>
          </div>
        </ng-template>  
      </div>

      <div class="section write-cv" (deferLoad)="lazyloadItems[1]=true">
        <div class="row equal center-vertically">
            <!-- flex-parent-text flex-center-horizontal order-1 -->
            <!-- order-md-2 -->
            <div class="col-md-4 col-xs-12 text-center sec-mar-bot-md">
              <div class="man-div">
                  <img 
                  *ngIf="lazyloadItems[1]"
                  class="img-responsive" 
                  style="margin:auto;"
                  src="./assets/images/home/<USER>"
                  alt="writing-cv-on-cveek"
                  @UpDown
                  >
              </div>
            </div>
            <div class="col-md-8 col-xs-12 create-cv-online">
              <div class="create-cv-bg-abs"></div>
              <div class="text-content text-center center-p create-cv-bg-mob">
                <div *ngIf="pageInfo.direction === 'rtl'; else section2_ltr">
                    <h2>كتابة السيرة الذاتية في موقع سيفيك CVeek</h2>
                    <p>
                        لا بد و أنك تبحث عن أفضل المواقع لكتابة سيرة ذاتية بالعربي مجانا ، حيث يقضي الكثير من الأشخاص وقتهم في البحث على مواقع متخصصة في انشاء سيرة ذاتية، يحاولون اختيار العبارات المناسبة مثل ( كتابة سيرة ذاتية / انشاء سيرة ذاتية / انشاء سيرة ذاتية بالعربي مجاناً / انشاء سيرة ذاتية باللغة العربية مجاناً / انشاء سيرة ذاتية احترافية مجاناً / كتابة سيرة ذاتية أو غيرية/ ..) وغيرها من العبارات، من أجل الوصول إلى موقع كتابة السيرة الذاتية المناسب.
                    </p>
                    <p>
                        وعند وصولهم إلى موقع متخصص يحتوي على قوالب سيرة ذاتية مميزة ، يقومون بالانطلاق في العمل، وقضاء الوقت في إدخال بيانات السيرة الذاتية، ثم يقومون بالبحث عن قوالب سيرة ذاتية احترافية، ثم يختارون قالب مميز  للسيرة الذاتية،  وبعد الانتهاء يكتشفون أن عليهم الاشتراك في الموقع، ودفع الرسوم قبل طباعة أو تصدير السيرة الذاتية.
                    </p>
                    <p class="important-txt">ثق تماماً أنت في المكان المناسب لإنشاء سيرة الذاتية مجاناُ</p>
                </div>
                <ng-template #section2_ltr>
                  <h2>Creating best CV online free!</h2>
                  <p class="important-txt">
                    How a resume should look like? &nbsp;
                    What resume format do employers prefer?
                  </p>
                  <!-- <p class="important-txt"><strong>What resume format do employers prefer?</strong></p> -->
                  <p class="important-txt">
                    Where to put skills in a resume? &nbsp;
                    Which is the best resume builder?
                  </p>
                  <!-- <p class="important-txt"><strong>Which is the best resume builder?</strong></p> -->
                  <p><strong>Well, you are at the right place!</strong></p>
                  <p>CVeek has a very <strong>simple</strong>, user-friendly <strong>CV builder</strong> which will make</p>
                  <p><strong>writing your CV easier</strong> than ever 👌</p>
                  <p>You don't have to worry about how to organize your information or <strong>where to put</strong> them <strong>in</strong> your <strong>CV</strong>.</p>
                  <p>CVeek handles all that for you! 👍</p>
                  <p>and it doesn't end there! CVeek also provides you with a wide</p>
                  <p>range of <strong>professional CV templates</strong>, and it's all for <strong>free!</strong></p>
                  <p>Do you already have a <strong>CV pdf</strong> on your device? 🤔</p>
                  <p>then guess what! you can <strong>upload</strong> your <strong>CV</strong> pdf <strong>file</strong> to CVeek and that's it!</p>
                  <p>you are all set up to <strong>find</strong> your dream <strong>job</strong> on our <strong>job</strong> search page! 🤩</p>
                  <p><strong>No subscription, No fees</strong> for additional features, NO TRICKS!</p>
                  <p>we got it all ready for you! 😎</p>
                </ng-template>  
              </div>
            </div>
        
          </div>
      </div>

      <div class="section text-center center-p templates-section" (deferLoad)="lazyloadItems[2]=true">
        <a class="cust-anchor" id="templates-section"></a>
        <div class="templates-number">
          <span class="plus">+</span>
          <span class="thirty">30</span>
        </div>
        <div class="row">
          <div class="col-md-7 col-xs-12 sec-mar-bot-md">
            <div *ngIf="pageInfo.direction === 'rtl'; else section3_ltr">
              <h2 class="section-title">قوالب سيرة ذاتية عربي انكليزي مجانية</h2>
              <p>بعد الانتهاء من كتابة السيرة الذاتية يمكنك اختيار القالب المناسب، من بين العديد من  قوالب السيرة الذاتية الجاهزة مجاناً.</p>
              <p>
                لديك حزمة  قوالب سي في CV احترافية، كي تساعدك في أن تكون سيرتك الذاتية مميزة ، نود الإشارة إلى أن قوالب السيرة الذاتية باللغة العربية والإنكليزية.
              </p>
            </div>
            <ng-template #section3_ltr>
                <h2 class="section-title">Free CV Templates (English and Arabic)</h2>
                <p>Make your CV stand out with our professional CV template designs.</p>
                <p>CVeek offers you a broad collection of CV templates in different languages, (Currently Arabic and English available, and we are working to add more languages)</p>
                <p>Easily choose the template that most matches your CV, then apply it.</p>
                <p>You can keep your CV on your CVeek account or even download it as a pdf file to your device for free. </p>
                <p>As simple as that!</p>
            </ng-template> 
          </div>
          <div class="col-md-5 col-xs-12">
            <div class="templates-div" *ngIf="lazyloadItems[2]">
              <img class="template template1" src="./assets/images/home/<USER>" alt="cveek-templates">
              <img class="template template2" src="./assets/images/home/<USER>" alt="cveek-templates">
              <img class="template template3" src="./assets/images/home/<USER>" alt="cveek-templates">
            </div>
          </div>
        </div>

        <!-- <div style="direction:ltr;margin: 0 -20px;" *ngIf="lazyloadItems[1]">
          <p-carousel
          [value]="templates" [numVisible]="6" [numScroll]="1"
          [responsiveOptions]="responsiveOptions"
          [autoplayInterval]="3000" [circular]="true">
          <ng-template let-template pTemplate="item">
              <img [src]="templates[templates.indexOf(template)]" style="width:80%">
          </ng-template>
        </p-carousel>
        </div> -->
      </div>

      <div class="upload-cv-pdf" (deferLoad)="lazyloadItems[3]=true">
        <div class="section">
          <div class="row equal text-center center-p">
              <div class="col-sm-4 col-xs-12 order-2">
                <img *ngIf="lazyloadItems[3]" src="./assets/images/home/<USER>" alt="upload-cv-as-pdf">
              </div>
              <div class="col-sm-8 col-xs-12  flex-parent-text order-1">
                <div class="text-content">
                  <div *ngIf="pageInfo.direction === 'rtl'; else section4_ltr">
                      <h2>ادخال السيرة الذاتية من ملف pdf</h2>
                      <p>
                        هل تملك سيفي بملف pdf جاهز مُسبقاً؟! إذاً قم بتحميل هذا الملف بخطوات سريعة بدلا من إعادة كتابة السيرة الذاتية بشكل كامل.
                      </p>
                  </div>
                  <ng-template #section4_ltr>
                      <h2>Upload your CV pdf file</h2>
                      <p>Do you already have your <strong>resume pdf file</strong> on your device?</p>
                      <p>you don't have to start from scratch!</p>
                      <p><strong>upload your cv pdf file from your device</strong> within seconds and get going to CVeek <strong>jobs</strong> section to <strong>find</strong> your perfect <strong>job opportunity.</strong></p>
                  </ng-template>
                </div>
              </div>
            </div>
        </div>
      </div>

      <div class="section companies-section text-center center-p">
        <a class="cust-anchor" id="companies-section"></a>
        <div *ngIf="pageInfo.direction === 'rtl'; else section5_ltr">
            <h2 class="centered-heading">نرحب بشركات التوظيف وجميع الشركات</h2>
            <div class="section-paragraph">
                <p>هل ترغب بنشر اعلان وظيفي مجاناً ؟! من خلال عملية تسجيل سريع </p>          
                <p>
                    يقدم لك سيفيك هذه الخدمة مجانا من خلال منصة توظيف متكاملة تساعدك بنشر اعلاناتك الوظيفية ومتابعتها وإدارتها، ثم استقبال السير الذاتية وعرضها بالشكل المطلوب لك كي تختار الشخص المناسب لك.
                </p>
                <div class="text-center">
                    <button class="btn btn-primary">انضم إلينا الآن</button>
                </div>
            </div>  
        </div>
        <ng-template #section5_ltr>
            <h2 class="centered-heading">All companies of all specialties are most welcome!</h2>
            <div class="section-paragraph">
               <p>Trying to <strong>post a job vacancy online for free</strong>?</p>
                <p>welcome aboard CVeek!</p>
                <p>Create your company's profile with the most manageable steps.</p>
                <p><strong>Post jobs without any fees.</strong></p>
                <p><strong>Find the best employees</strong> with just the right <strong>skills</strong> and <strong>experience </strong>and a lot more!</p>
                <div class="text-center">
                    <button class="btn btn-orange" routerLink="/m/company/sign-up">Join us now</button>
                </div>
            </div>
        </ng-template>
      </div>

      <div class="section cveek-partners-section" *ngIf="goldCompanies.length > 0 || silverCompanies.length > 0">
 
      <div class="gold-parterns-section text-center center-p" *ngIf="goldCompanies.length > 0" (deferLoad)="lazyloadItems[4]=true">
          <div class="gold-partenrs-title-div">
            <div *ngIf="pageInfo.direction === 'rtl'; else section6_ltr">
                <h2 class="section-title">شركاء سيفيك الذهبيين</h2>
            </div>
            <ng-template #section6_ltr>
                <h2 class="section-title">CVeek gold partners</h2>
                <!-- <p>We are welcoming our CVeek gold partners</p> -->
            </ng-template> 
            <img @fade *ngIf="lazyloadItems[4]" src="./assets/images/home/<USER>" alt="cveek-gold-partners">
          </div>
          
          
          <div class="partners-div row flex-row-all" *ngIf="lazyloadItems[4]">
            <div class="goldCompanies col-md-3 col-sm-4 col-xs-6" *ngFor="let company of goldCompanies; let i=index;">
              <div class="company-div text-center">
                <div class="company-logo-div" [routerLink]="['/i/c', company.user_name]">
                  <img *ngIf="company.company_logo" [src]="compLogoPath + company.company_logo" alt="{{company.company_name}}">
                  <img *ngIf="!company.company_logo" src="./assets/images/home/<USER>" alt="{{company.company_name}}">
                  <div>{{company.company_name}}</div>
                </div>
                <div *ngIf="company.show_apply_button">
                  <button *ngIf="!company.directly_applied" type="submit" class="btn btn-primary apply-to-company-btn" (click)="applyToCompany(company,'goldenPartner');" [disabled]="role==='ROLE_EMPLOYER'">Apply Now</button>
                  <button *ngIf="company.directly_applied" type="submit" class="btn btn-primary apply-to-company-btn" disabled>Applied</button>
                </div>
              </div>

            </div>
          </div>
        
          <!-- <div style="direction:ltr;margin: 0 -20px;" *ngIf="lazyloadItems[3]">
            <p-carousel
            [value]="goldCompanies" [numVisible]="6" [numScroll]="1"
            [responsiveOptions]="responsiveOptions"
            [autoplayInterval]="3000" [circular]="true">
            <ng-template let-company pTemplate="item">
              <div class="company-logo-div text-center" [routerLink]="['/i/c', company.user_name]">
                  <img *ngIf="company.company_logo" [src]="compLogoPath + company.company_logo">
                  <img *ngIf="!company.company_logo" src="./assets/images/Confidential-icon.png">
                  <div>{{company.company_name}}</div>
              </div>
            </ng-template>
          </p-carousel>
          </div> -->
        </div>
     
        <div class="silver-parterns-section  text-center center-p" *ngIf="silverCompanies.length > 0" (deferLoad)="lazyloadItems[5]=true">
          <div class="silver-partenrs-title-div">
              <div *ngIf="pageInfo.direction === 'rtl'; else section6_ltr">
                  <h2 class="section-title">شركاء سيفيك الفضيين</h2>
              </div>
              <ng-template #section6_ltr>
                  <h2 class="section-title">CVeek Silver Partners</h2>
                  <!-- <p>We are welcoming our CVeek Silver partners</p> -->
              </ng-template> 
              <img @fade *ngIf="lazyloadItems[5]" src="./assets/images/home/<USER>" alt="cveek-silver-partners">
          </div>
          
          
          <div class="partners-div row flex-row-all" *ngIf="lazyloadItems[5]">
            <div class="col-md-3 col-sm-4 col-xs-6" *ngFor="let company of silverCompanies; let i=index;">
              <div class="company-div text-center">
                <div class="company-logo-div" [routerLink]="['/i/c', company.user_name]">
                  <img *ngIf="company.company_logo" [src]="compLogoPath + company.company_logo" alt="{{company.company_name}}">
                  <img *ngIf="!company.company_logo" src="./assets/images/home/<USER>" alt="{{company.company_name}}">
                  <div>{{company.company_name}}</div>
                </div>
                <div *ngIf="company.show_apply_button">
                  <button *ngIf="!company.directly_applied" type="submit" class="btn btn-primary apply-to-company-btn" (click)="applyToCompany(company,'silverPartner');" [disabled]="role==='ROLE_EMPLOYER'">Apply Now</button>
                  <button *ngIf="company.directly_applied" type="submit" class="btn btn-primary apply-to-company-btn" disabled>Applied</button>
                </div>
              </div>
  
            </div>
          </div>
        
          <!-- <div style="direction:ltr;margin: 0 -20px;" *ngIf="lazyloadItems[4]">
            <p-carousel
            [value]="silverCompanies" [numVisible]="6" [numScroll]="1"
            [responsiveOptions]="responsiveOptions"
            [autoplayInterval]="3000" [circular]="true">
            <ng-template let-company pTemplate="item">
              <div class="company-logo-div text-center" [routerLink]="['/i/c', company.user_name]">
                  <img *ngIf="company.company_logo" [src]="compLogoPath + company.company_logo">
                  <img *ngIf="!company.company_logo" src="./assets/images/Confidential-icon.png">
                  <div>{{company.company_name}}</div>
              </div>
            </ng-template>
          </p-carousel>
          </div> -->
        </div>

      </div>  <!-- End of cveek-partners-section -->

        <div class="section search-job text-center center-p" (deferLoad)="lazyloadItems[6]=true">
          <div class="text-content">
            <div *ngIf="pageInfo.direction === 'rtl'; else section7Part1_ltr">
                <h2>انطلق بالبحث عن فرصة أحلامك</h2>
                <p class="important-txt">هل تبحث عن عمل مميز براتب عالي ؟!</p>
                <p class="important-txt">أو عن عمل من المنزل | عمل عن بعد | عمل عبر الإنترنت | شغل أون لاين ؟</p>
            </div>
            <ng-template #section7Part1_ltr>
                <p><strong>Wondering where to start looking for a job?</strong></p>
                <p><strong>What are the best job-hunting sites out there?</strong></p>
            </ng-template>
          </div>
          <br><br>
          <div class="row">
            <div class="col-md-6"></div>
            <div class="col-md-6">
                <h2>START SEARCHING FOR YOUR DREAM OPPORTUNITY</h2>
            </div>
          </div>
          <div class="row equal">
            <div class="col-sm-6 col-xs-12 text-center">
              <img *ngIf="lazyloadItems[6]" class="img-responsive search-jobs-cveek"  src="./assets/images/home/<USER>" alt="search-jobs-cveek">
              <div class="text-content">
                  <div *ngIf="pageInfo.direction === 'rtl'; else section7Part2_ltr">
                      <p>
                          نسعى في موقع سيفيك لمساعدتك بإيجاد أفضل الوظائف الشاغرة وبرواتب عالية ، من خلال واجهة بحث عن عمل ، تحوي كافة الخيارات اللازمة لعملية البحث. 
                        </p> 
                  </div>
                  <ng-template #section7Part2_ltr>
                    <p>On CVeek <strong>job-search</strong> section you can <strong>find</strong> the <strong>job</strong> that suits you perfectly.</p>
                    <p>With the help of our simple interface and filters, you can find exactly what you're looking for, from specialty and <strong>experience</strong> to <strong>skills</strong> you have on your <strong>resume</strong>.</p>
                    <p>You can <strong>search for jobs near you</strong> or all over the world! </p>
                  </ng-template>
              </div>
            </div>
            <div class="col-sm-6 col-xs-12 text-center">
                <img *ngIf="lazyloadItems[6]" class="img-responsive customize-jobs"  src="./assets/images/home/<USER>" alt="customize-displaying-jobs-data-cveek">
                <div class="text-content" style="width:100%;">
                  <div *ngIf="pageInfo.direction === 'rtl'; else section6Part3_ltr">
                      <p>
                          كما يمكنك تخصيص طريقة عرض البيانات من خلال النقر على زر الاعدادات في أعلى الصفحة، كما في الشكل الموضح جانباً، لاظهار البيانات التي ترغب بعرضها وفق اهتماماتك.
                      </p>
                  </div>
                  <ng-template #section6Part3_ltr>
                    <p>And even <strong>customize</strong> the way <strong>job ads</strong> are displayed based on what your interests are!</p>
                    <p><strong>There are no limits for you, and it's all for free!</strong></p>
                  </ng-template>
                </div>
            </div>
          </div>
          <br><br>

            <div class="text-center job-search-link-sec">
                <div *ngIf="pageInfo.direction === 'rtl'; else section6Part4_ltr">
                    <a *ngIf="!pageInfo.country" routerLink="/search-job" class="important-link">انطلق إلى واجهة البحث عن عمل وتصفح إعلانات التوظيف الآن </a>
                    <a *ngIf="pageInfo.country" [routerLink]="['/search-job/p/-jobs-'+pageInfo.country]" class="important-link">انطلق إلى واجهة البحث عن عمل وتصفح إعلانات التوظيف الآن </a>
                </div>
                <ng-template #section6Part4_ltr>
                    <a *ngIf="!pageInfo.country" routerLink="/search-job" class="important-link">Go to the job search interface and browse job ads now</a>
                    <a *ngIf="pageInfo.country" [routerLink]="['/search-job/p/-jobs-'+pageInfo.country]" class="important-link">Go to the job search interface and browse job ads now</a>
                </ng-template>
            </div>
        </div>

         <!-- <div>
          <a class="expField-link" [routerLink]="['/search-job', 'f', 'artificial-intelligence---ai-jobs-syria']">Health jobs in Qatar</a>
        </div>  -->

        <!-- <div class="section video-container" (deferLoad)="lazyloadItems[4]=true">
          <div class="video-section" *ngIf="lazyloadItems[4]">
            <iframe loading="lazy" width="100%" height="100%" src="https://www.youtube.com/embed/hlkSJ-AEjGc" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
          </div>
        </div> -->

        <!-- old manually way -->
        <!-- <div class="section video-container" id="lazyload-resource">
          <div class="video-section" *ngIf="displayVideo">
            <iframe loading="lazy" width="100%" height="100%" src="https://www.youtube.com/embed/hlkSJ-AEjGc" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
          </div>
        </div> -->

      <!-- <div class="section text-center">
        <h2 class="section-title">Jobs</h2>
        <div class="container-fluid jobs-section text-left">
          <div class="row">
            <div class="col-sm-4">
                <h3>Jobs in Qatar</h3>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Health-jobs-jobs-Qatar']">Health jobs in Qatar</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_&_Network_Security-jobs-jobs-Qatar']">Computer & Network Security jobs in Qatar</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_Games-jobs-jobs-Qatar']">Computer Games jobs in Qatar</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_Hardware-jobs-jobs-Qatar']">Computer Hardware in Qatar</a>
            </div>
            <div class="col-sm-4">
                <h3>Jobs in Syria</h3>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Health-jobs-jobs-Syria']">Health jobs in Syria</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_&_Network_Security-jobs-jobs-Syria']">Computer & Network Security jobs in Syria</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_Games-jobs-jobs-Syria']">Computer Games jobs in Syriar</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_Hardware-jobs-jobs-Syria']">Computer Hardware in Syria</a>
            </div>
            <div class="col-sm-4">
                <h3>Jobs in Lebanon</h3>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Health-jobs-jobs-Lebanon']">Health jobs in Lebanon</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_&_Network_Security-jobs-jobs-Lebanon']">Computer & Network Security jobs in Lebanon</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_Games-jobs-jobs-Lebanon']">Computer Games jobs in Lebanon</a>
                <a class="expField-link" [routerLink]="['/search-job', 'f', 'Computer_Hardware-jobs-jobs-Lebanon']">Computer Hardware in Lebanon</a>
            </div>
          </div>
        </div>
      </div> -->
 </div> <!-- End Home Sections div -->
</div>

<div class="flex-space-fix"></div>
<app-footer></app-footer>


<div class="modal fade" id="authModal" tabindex="-1" role="dialog" aria-labelledby="authLabelModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="authLabelModal" translate>Job Seeker Sign In</h4>
            </div>
            <login [fromPage]="'home'"></login>
        </div>
    </div>
</div>