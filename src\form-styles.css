/* html, body {width: auto!important; overflow-x: hidden!important}  */
/* html {
  scroll-behavior: smooth;
}
body{
  position:relative;
} */

/* @font-face {
  font-family: 'Droid Arabic Kufi';
  src: url('assets/fonts/Droid-Arabic-Kufi.ttf') format('truetype');
  font-style: normal;
} */

body{
  color: #444;
}
/* width */
::-webkit-scrollbar {
  width: 7px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #3D7BCE;
}
.text-bold{
  font-weight: bold;
}
.page-navbar{
  top: 70px;
}
app-user-wrapper , app-company-wrapper , app-wrapper , app-membership-wrapper,
app-home , app-advrs-interface , app-single-post , page-not-found { 
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  /* justify-content: space-between; */

  position: relative;
}
.general-wrapper{
  margin-top:100px;
  /* margin-bottom: 200px; */
  /* min-height: 400px; */
}
.flex-space-fix{
  flex-grow:1;
}
.form-horizontal, .fixed-helper {
  font-family: 'Roboto', sans-serif;
}

.form-control:focus {
  box-shadow: none;
  border-bottom: 1px solid #ccc;
}

.form-control {
  box-shadow: none;
  border-radius: 0px;
  border: 0;
  border-bottom: 1px solid #ccc;
  transition: all .2s ease;
  padding: 0;
  /* padding-bottom: 0; */
  height: 32px;
}

.form-control[disabled] {
  background: #fff;
  border-bottom: 1px dashed #ccc;
}

/* I Deleted { :host >>> } when move it outside the component */
.has-error .form-control {
  box-shadow: none !important;
  border-color: #db1a1a;
  /* border-color: #a94442; */
}

label {
  font-weight: normal;
}

.has-error .control-label {
  color: #db1a1a !important;
  /* color: #a94442 !important; */
}

.has-error .error-message {
  color:#db1a1a !important;
  /* color: #a94442; */
  position: absolute;
  left: 100%;
  top: 10px;
  width: 60%;
  z-index: 2;
}
.error-message {
  color:#db1a1a !important;
  /* color: #a94442; */
}
.has-error .normal-error-message{
  color:#db1a1a;
  /* color: #a94442; */
}
.form-control-feedback {
  right: 25px;
  z-index: 0;
}

.alert-danger {
  color: red;
  background-color: #f7e0e0;
}
.form-control-feedback.glyphicon-ok {
  color: #35b558;
}

.form-horizontal .has-feedback .form-control-feedback {
  right: 25px;
}
.has-feedback .form-control {
  padding-right: 0;
}
.alignment-right {
  text-align: right;
}
.alignment-right label{
  color:#4f94df;
}

.radio-choose.container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  margin-top: 7px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.radio-choose.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.radio-choose .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  border: 2px solid #aaa;
  background-color: #fff;
  border-radius: 50%;
  transition: all .15s ease;
}

.radio-choose.container input:checked ~ .checkmark {
  background-color: #fff;
  border: 2px solid #3d7bce;
}

.radio-choose .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.radio-choose.container input:checked ~ .checkmark:after {
  display: block;
  animation: zoom-it-out .2s ease;
}

.radio-choose.container .checkmark:after {
  left: 2px;
  top: 2px;
  width: 12px;
  height: 12px;
  background: #3d7bce;
  border-radius: 50%;
}

.radio-choose .checkmark:before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.radio-choose.container input:checked ~ .checkmark:before {
  animation: change-color .5s ease;
}

@keyframes zoom-it-out {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes change-color {
  0% {
    background: #03253f44;
    transform: scale(1);
  }
  60% {
    background: #03253f22;
    transform: scale(2.5);
  }
  100% {
    background: #03253f00;
    transform: scale(2.5);
  }
}

.focus-no-padding {
  padding-left: 0 !important;
  background: #fff;
  min-height: 32px;
}
/* .focus-no-padding:has(> p-dropdown){
  max-height: 33px !important;
} */

.focus-no-padding .custom-underline {
  position: absolute;
  width: calc(100% - 15px);
  height: 2px;
  left: 0;
  bottom: 0;
  background-color: #4f94df;
  transform: scale(0);
  transform-origin: left;
  transition: all .3s ease;
}
p-dropdown + .custom-underline {
  bottom:11px !important;
}
.focus-no-padding .form-control:focus ~ .custom-underline {
  transform: scale(1);
}
.focus-no-padding .ng-select-focused ~ .custom-underline {
  transform: scale(1);
}

.focus-no-padding:after {
  transform-origin: left;
}

/* .ui-autocomplete .ui-inputtext{
  transform: scale(0);
  transform-origin: left;
  transition: all .3s ease;
	border-bottom: 1px solid #ccc;
}
.ui-inputwrapper-focus .ui-inputtext{
  border-bottom: 1px solid #4f94df;
  transform: scale(1);
} */

/* customize syncfusion multiselect focus color */
.e-multiselect.e-input-group.e-control-wrapper.e-input-focus::before, .e-multiselect.e-input-group.e-control-wrapper.e-input-focus::after {
  background: #4f94df;
}
.btn-primary {
  background-color: #276ea4;
}

.btn {
  border-radius: 5px;
}

.btn-block {
  width: auto;
  margin: auto;
}
.btn .fa{
  margin-right:3px;
}
.btn .fa-info , .btn .fa-plus , .btn .fa-trush{
  margin-right:0px;
}
.small-btn{
  width:100px;
}
.cust-cancel-btn{
  background: #888;
  color: #fff;
}
.cust-cancel-btn:hover{
  background: #777;
  color: #fff;
}

/*.btn a:hover{*/
  /*text-decoration: none;*/
  /*color: white;*/
/*}*/

/*.form-group, .custom-row {*/
  /*margin-bottom: 25px;*/
/*}*/

textarea {
  resize: vertical;
}

.text-area-letters {
  position: absolute;
  right: 15px;
  width: calc(100% - 15px);
  text-align: right;
  padding: 3px 5px;
}

.div-margin-top-40 {
  margin-top: 40px;
}
.div-margin-top-30 {
  margin-top: 30px;
}
.div-margin-top-20 {
  margin-top: 20px;
}
.div-margin-top-15 {
  margin-top: 15px;
}
.div-margin-bo-60 {
  margin-bottom: 60px;
}
.div-margin-bo-35 {
  margin-bottom: 35px;
}
.div-margin-bo-30 {
  margin-bottom: 30px;
}
.div-margin-bo-25 {
  margin-bottom: 25px;
}
.div-margin-bo-20 {
  margin-bottom: 20px;
}
.div-margin-bo-15 {
  margin-bottom: 15px;
}
.div-margin-bo-10 {
  margin-bottom: 10px;
}

.flex-container{
  display: flex;
  width: 100%;
}
.equal-height-cols{
  flex: 1; 
}
.sticky-confirm-toast .ui-toast-top-right {
  top: 48px !important;
  right: 10px !important;
}
.btn-delete {
  background: transparent;
  color: #aaa;
  border: 0;
  outline: 0;
  font-size: 18px;
  padding: 0;
  margin: 0 6px;
}

.btn-delete .fa-trash {
  width: 25px;
  height: 25px;
  line-height: 25px;
}

.btn-delete:hover {
  background: #ffd46a;
  color: #777;
  border: 0;
  outline: 0;
}

.btn-delete-big {
  width: 37px;
  height: 34px;
  margin: 0;
}
.label-color{
  color:#4f94df;
}
/* show password eye button */
.password-eye-icon {
  float: right;
  margin-right: 4px;
  margin-top: 8px;
  position: relative;
  z-index: 2;
  font-size: 1.1rem;
}


/* center modal vertically */
.modal {
  text-align: center;
  padding: 0!important;
}
  
.modal:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
}
  
.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}
  
@media screen and (max-width: 767px) {
  .modal-dialog{
    width:95%;
  }

  /* show password eye button */
  .password-eye-icon {
    margin-top: 4px;
    font-size: 1rem;
  }
  .alignment-right {
    text-align: left;
  }

  .fixed-helper {
    display: none;
  }

  .has-error .error-message {
    top: 105%;
    left: 0;
    width: calc(100% - 15px);
    text-align: left !important;
    z-index: 2;
  }
  .error-message{
    text-align: left !important;
  }

  .has-error textarea ~ .error-message {
    top: calc(100% + 26px);
  }
}

.control-label {
  font-weight: normal;
  font-size: 16px;
  color: #03253f;
}

.form-control {
  font-size: 16px;
}

.flex_row {
  display: flex;
  align-items: center;
}

.prev-border {
  border: 2px solid #ddd;
  padding-top: 30px;
  border-radius: 10px;
}

.prev-border p.add-certification-p {
  text-align: center;
  background: white;
  color: #999;
  position: absolute;
  padding: 0 10px;
  top: -20px;
  height: 40px;
  line-height: 40px;
  left: 5%;
  font-size: 16px;
}

.minamize-certification {
  position: absolute;
  right: 5%;
  width: 40px;
  height: 22px;
  text-align: center;
  line-height: 22px;
  font-size: 1.6rem;
  background: #eee;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.minamize-certification .fa {
  transform: rotateZ(0deg);
  transition: all .5s ease;
}

.minamize-certification .fa.rotate {
  transform: rotateZ(180deg);
}

/*.form-horizontal .form-group {*/
  /*margin-left: 0;*/
  /*margin-right: 0;*/
/*}*/

.table-preview {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

.table-preview td, th {
  border: 0;
  text-align: center;
  padding: 5px 8px;
}

.table-preview td:not(:first-of-type) {
  border-left: 1px solid #ddd;
}

.table-preview th {
  font-size: 16px;
  padding: 10px 0;
}

.table-preview th span {
  width: 100%;
  display: block;
  padding: 0 10px;
}

.table-preview th:not(:first-of-type) span {
  border-left: 1px solid #03253f;
}

.table-preview tbody tr {
  border-bottom: 1px solid #ddd;
  cursor: -webkit-grab;
  cursor: grab;
}

.table-preview tbody tr.ui-sortable-helper {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.table-preview thead tr:first-of-type {
  border-bottom: 2px solid #c8d4dd;
  background: #c8d4dd;
  color: #03253f;
}

.table-preview tr:nth-of-type(even) {
  background: #f4f4f4;
}
.table-preview tr:nth-of-type(odd) {
  background: #fff;
}

.table-preview tr:last-of-type {
  border-bottom: 0;
}

.table-preview-container {
  border: 2px solid #c8d4dd;
  border-radius: 10px;
  margin-top: 5px;
  overflow: auto;
  position:relative;
}
.table-preview-container table{
  min-height:120px;
}
.table-preview-container table .table-no-data{
  font-size: 16px;
}
.preview-no-data{
  margin-top:20px;
  margin-bottom: 35px;
  text-align: center;
}
/* start styles for table custom loader */
.loading-custom-table{
  overflow:visible;
}
.loading-custom-table .loader-container{
  position: absolute;
  top:60px;
  left:50%;
  transform: translateX(-50%);
  z-index:1000;
}
.loaded-custom-table .loader-container{
  display:none;
}
@keyframes  ui-progress-spinner-color {
  100%,
  0% {
      stroke: #4876BA;
  }
  40% {
      stroke: #E3B442;
  }
  66% {
      stroke: #4876BA;
  }
  80%,
  90% {
      stroke: #E3B442;
  }

  /* 100%,
  0% {
      stroke: #186ba0;
  } */

}
/* end styles for table custom loader */
.modal {
  z-index: 10010;
}

.strike {
  display: block;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
}

.strike label {
  margin-bottom: 0;
}

.strike > span {
  position: relative;
  display: inline-block;
}

.strike > span:before, .strike > span:after {
  content: "";
  position: absolute;
  top: 50%;
  width: 9999px;
  height: 1px;
  background: #ddd;
}

.strike > span:before {
  right: 100%;
  margin-right: 15px;
}

.strike > span:after {
  left: 100%;
  margin-left: 15px;
}

.text-area-p {
  position: absolute;
  right: 15px;
  top: 100%;
  text-align: right;
}

.extraInformation {
  display: none;
}

h3.sub-title {
  margin: 20px;
  color: #03253f;
}

.m-b-25 {
  margin-bottom: 25px;
}

.btn-fa-info {
  padding:0;
  margin: 0 6px;
  border: 0;
}
.btn-fa-info img{
  width:30px;
}
.btn-fa-info .fa-info {
  width: 25px;
  height: 25px;
  border-radius: 4px;
  line-height: 25px;
}

.btn-fa-help {
  padding: 0;
  margin: 0 6px;
  border: 0;
  position: absolute;
  left: -34px;
  top: 8px;
  background-color: #aaa
}

.btn-fa-help .fa-info {
  width: 25px;
  height: 25px;
  border-radius: 4px;
  line-height: 25px;
  color: #fff;
}
.btn-gray{
  width: 30px;
  height: 30px;
  padding: 6px;
  font-size: 12px;
  background:#909cad;
  color:#fff;
}
.btn-gray:hover,.btn-gray:focus{
  color:#fff;
}
.label-bot-bit {
  color: #4f94df;
}

.input-group .custom-underline {
  width: calc(100% - 33px);
}

.focus-no-padding-textarea textarea.form-control {
  border: 1px solid #ccc;
  position: relative;
  width: 100%;
}

.focus-no-padding-textarea textarea.form-control:focus {
  border: 1px solid #ccc;
}

.focus-no-padding-textarea textarea.form-control:focus {
  border: 1px solid #4f94df;
}

.custom-full-line {
  display: none;
}

.focus-container {
  position: relative;
}

.add-project {
  position: absolute;
  top: 0;
  left: 85%;
  height: 100%;
  border-left: 1px solid #ccc;
  display: flex;
  align-items: center;
  padding: 0 5px;
}
.modal-body-container{
  position: relative;
  overflow-x: hidden;
}
.nav-header .logo-img img , .nav-header .site-title{
  cursor: pointer;
}
:focus {
  outline: none !important;
}
#page-content-wrapper .page-title{
  color:#3D7BCE !important;
}

/* start membership styles */
app-membership-wrapper h1{
  font-size:30px;
  color:#4876BA;
}
.help-link:hover{
  text-decoration:none;
  font-weight: bold;
}
.social-login-btns{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap:wrap;
}
#googleLoginButtonDiv{
  display:inline-block;
}
.social-login-btns .facebook-btn{
  margin-right:9px;
  padding: 5px 10px;
}

.or-container{
  position: relative;
  margin: 40px auto;
  width: 70%;
  text-align: center;
}
.or-container hr{
  border-color:#bbb;
}
.or-text{
  position: absolute;
  width: 100%;
  text-align: center;
  top: -8px;
  left: 0;
  line-height: 1;
  font-size: 16px;
}
.or-text span{
  display: inline-block;
  background: #fff;
  width: 45px;
  color: #4876BA;
  font-weight: bold;
}
/* end membership styles */

.cust-p-multiselect .ui-multiselect-close{
  display:none;
} 

/* Start customize toast messages styles */
body .ui-toast .ui-toast-message{
  border-radius: 3px;
}
body .ui-toast .ui-toast-message.ui-toast-message-success {
  /* background-color: #10996D; */
  /* background-color: #0EB37D; */
  background-color:#0fb680;
  color: #fff;
}
body .ui-toast .ui-toast-message.ui-toast-message-warn {
  background-color: #fdbc22;
  color: #fff;
}
body .ui-toast .ui-toast-message.ui-toast-message-info {
  background-color: #3d80f2;
  color: #fff;
}
body .ui-toast .ui-toast-message.ui-toast-message-error{
  background: #f1434b;
  color:#fff;
}
.ui-toast .ui-toast-icon{
  left: 0.6em;
  top: 0.7em;
  font-size: 1.5em;
  border-radius: 50%;
  background: #fff;
}
body .ui-toast .ui-toast-message.ui-toast-message-success .ui-toast-icon{
  color: #0fb680;
}
body .ui-toast .ui-toast-message.ui-toast-message-warn .ui-toast-icon{
  color:#fdbc22;
}
body .ui-toast .ui-toast-message.ui-toast-message-info .ui-toast-icon{
  color: #3d80f2;
}
body .ui-toast .ui-toast-message.ui-toast-message-error .ui-toast-icon{
  color:#f1434b;
}
body .ui-toast .ui-toast-message .ui-toast-close-icon,body .ui-toast .ui-toast-message.ui-toast-message-error .ui-toast-close-icon {
  color: #fff !important;
}

/* body .ui-toast .ui-toast-message.ui-toast-message-success .ui-toast-close-icon {
  color: #fff;
} */

/* End customize toast messages styles */

@media all and (max-width: 992px) and (min-width: 768px) {
  .add-project {
    left: 75%;
  }
}
@media all and (max-width: 992px){
  .sticky-confirm-toast .ui-toast-top-right {
    font-size:12px;
  }
}
@media screen and (max-width:905px){
  .page-navbar{
    top:61px;
  }
  #page-content-wrapper .page-content{
    padding-top: 32px !important;
  }
}
@media all and (max-width: 768px) {
  .add-project {
    width: 60px;
  }
}
@media screen  and (max-width:767px){
  .page-navbar{
    top:58px;
    /* top:72px; */
  }

  app-membership-wrapper h1{
    font-size:25px;
  }
  .or-container{
    width:100%;
  }

  /* smaller size for resume sections page title */
  app-user-wrapper #page-content-wrapper .page-content {
    padding:30px !important;
  }
  app-user-wrapper #page-content-wrapper .page-title {
    font-size: 1.1rem !important;
    margin-left: -13px;
  }
  app-user-wrapper #page-content-wrapper .page-title img{
      width: 23px !important;
      margin-right: 8px !important;
  }

  /* #page-content-wrapper{
    margin-left: 70px !important;
  }     */
}
@media all and (min-width: 768px) {
  .flex_row {
    margin-left: 0;
    margin-right: 0;
  }

  .modal-dialog {
    width: 90%;
  }
  .image-editor-modal .modal-dialog , .med-modal .modal-dialog{
    width: 750px;
  }
  

}

.table-preview .th-mobile{
  display:none;
}
@media only screen and (max-width: 767px) {
  /* responsive custom table styles */
  .table-preview-container {
    margin: 0 -15px;
  }

  .table-preview td{
    text-align: left;
    display: block;
    border: 0!important;
    width: 100%!important;
    float: left;
    clear: left;
    border: 0;
  }

  .table-preview .th-mobile{
    padding: 0.4em;
    min-width: 48%;
    display: inline-block;
    margin: -0.4em 1em -0.4em -0.4em;
    font-weight: 700;
    text-align: right;
  }
  .table-preview .th-mobile + p{
    display: inline-block;
    margin-bottom: 0;
  }
  .table-preview .th-mobile + p p{
    margin-bottom: 0;
  }
  .table-preview th{
    display:none;
  }

  /* table, thead, tbody, th, td, tr {
    display: block;
  }

  thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  tr {
    margin: 0;
  }

  thead tr:nth-of-type(1) {
    display: none;
  }

  .table-preview tbody tr {
    cursor: default;
  }

  .table-preview td {
    border: none !important;
    position: relative;
    padding-left: 50%;
    text-align: left;
  }

  td:before {
    position: absolute;
    top: 0;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    padding-top: 5px;
    white-space: nowrap;
    text-align: right;
  }

  th:last-of-type {
    display: none;
  } */
  .loading-custom-table .loader-container{
    top: 16px;
  }
  /* I Deleted { :host >>> } when move it outside the component */
  .ui-dialog.ui-widget .ui-dialog-content {
    padding-right: 0px;
    padding-left: 0px;
  }

   /* write cv table responsive  */
   .table-preview-container table{
    min-height:60px;
  }
  td.table-no-data:before {
    content: "" !important;
  }
  td.table-no-data{
    padding-left: 0 ;
    text-align: center;
    margin-top: 11px;
  }
}

.focus-no-padding.has-val {
  background: #fff;
}

.form-control {
  background: transparent;
  /* z-index: 2; */
  /* position: absolute; */
  /* width: calc(100% - 15px); */
  width:100%;
}
input.form-control{
  position: absolute;
  z-index: 2;
  cursor: pointer;
}
.form-control[disabled] {
  background: transparent;
}
.ui-autocomplete{
  position: unset;
}
.custom-control-label {
  position: absolute;
  top: 0;
  left: 0;
  margin-left: 15px;
  font-size: 16px;
  color: #999;
  transition: all .3s ease;
  cursor: pointer;
}

.form-control:focus ~ .custom-control-label, .has-val .form-control ~ .custom-control-label {
  transform: translateX(-100%);
  margin-left: -15px;
  font-size: 16px;
  color: #4f94df;
}

.form-group, .custom-row {
  margin-bottom: 35px;
}

/*@media screen and (max-width: 768px) {*/
  /*.form-control:focus ~ .custom-control-label, .has-val .form-control ~ .custom-control-label {*/
    /*transform: translate(0, -75%);*/
    /*margin-left: 0px;*/
    /*font-size: 14px;*/
  /*}*/

  /*.focus-no-padding {*/
    /*min-height: 26px;*/
  /*}*/

  /*.form-control {*/
    /*padding-top: 0;*/
    /*height: 26px;*/
  /*}*/
/*}*/

.ui-sortable-helper {
  display: table;
}


/****** START I Deleted { :host >>> } when move it outside the component ********/

.ui-autocomplete .ui-inputtext {
  height: 30px;
  margin-top: 2px;
  width: 100% !important;
  border: 0;
  border-bottom: 1px solid #ccc;
  border-radius: 0;
  box-shadow: none !important;
  background: transparent !important;
}

.form-control.ui-autocomplete.ui-widget {
  border: 0;
  padding: 0 15px 0 0 ;
  display: inherit;
  width: 100% !important;
}
.focus-no-padding .ui-inputwrapper-focus ~ .custom-underline {
  transform: scale(1);
}

.focus-no-padding .ui-inputwrapper-focus ~ .custom-control-label,
.has-val ~ .custom-control-label {
  transform: translateX(-100%);
  margin-left: -15px;
  font-size: 16px;
  color: #4f94df;
}

/* Dropdown styles*/

.ui-dropdown .ui-dropdown-trigger {
  border: 0 !important;
}

.ui-dropdown label.ui-dropdown-label:hover {
  border: 0;
}

.ui-dropdown {
  height: 30px;
  margin-top: 3px;
  width: 100% !important;
  border: 0 !important;
  border-bottom: 1px solid #ccc !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
}

.has-error .ui-dropdown {
  border-bottom: 1px solid #db1a1a !important;
  /* border-bottom: 1px solid #a94442 !important; */
}

.has-error .ui-dropdown label.ui-dropdown-label {
  color:#db1a1a;
  /* color: #a94442; */
}

.ui-dropdown:not(.ui-state-disabled):hover {
  background-color: #fff;
}

/* .ui-dropdown:focus {
  background-color: red;
} */

p-dropdown.ng-dirty.ng-invalid > .ui-dropdown {
  border-bottom-color: #db1a1a !important;
  /* border-bottom-color: #a94442 !important; */
}

/* fix dropdown items big width */
/* .ui-dropdown-items-wrapper, .ui-dropdown-panel .ui-dropdown-filter-container{
  width: min-content;
} */

p-autocomplete.ng-dirty.ng-invalid > .ui-autocomplete > .ui-inputtext {
  border-bottom-color: #db1a1a;
  /* border-bottom-color: #a94442; */
}

p-autocomplete.ng-dirty.ng-invalid ~ .custom-control-label {
  color: #db1a1a;
  /* color: #a94442; */
}

.has-val p-autocomplete.ng-dirty.ng-invalid ~ .custom-control-label, p-autocomplete.ng-dirty.ng-invalid.ui-inputwrapper-focus ~ .custom-control-label {
  color: #4f94df;
}

.has-error p-autocomplete .ui-autocomplete .ui-inputtext {
  border-bottom-color: #db1a1a;
  /* border-bottom-color: #a94442; */
}

.ui-dropdown .ui-dropdown-trigger {
  height: 96%;
  background: #fff;
}

.form-control.ng-invalid:focus {
  border-bottom-color: grey !important;
}

.ui-inputwrapper-focus ~ .custom-control-label, .has-val ~ .custom-control-label {
  transform: translateX(-100%);
  margin-left: -15px;
  color: #4f94df;
}

.ui-dropdown .ui-dropdown-trigger .ui-dropdown-trigger-icon:before {
  content: "\f0d7";
  font-family: fontAwesome;
}

.ui-dropdown-items-wrapper, .ui-dropdown-panel .ui-dropdown-filter-container {
  min-width: 100%;
}

.ui-dropdown-filter.ui-inputtext.ui-widget.ui-state-default.ui-corner-all {
  z-index: 0;
}


/* .custom-fileupload{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.custom-fileupload .ui-fileupload-choose input[type=file]{
  position: relative !important;
  left: 0;
  right: auto !important;
  width:100%;
}
.custom-fileupload .ui-button , .custom-fileupload .ui-fileupload-choose:not(.ui-state-disabled):hover , .custom-fileupload .ui-fileupload-choose:not(.ui-state-disabled):active{
  background: transparent;
  border: 0;
  width: 100%;
  outline: transparent;
}
.custom-fileupload .ui-button-text-icon-left .ui-button-icon-left{
  display: none;
} */


/*.ui-dropdown-panel.ui-widget-content.ui-corner-all {*/
  /*width: 110px;*/
/*}*/

p-dropdown.ng-dirty.ng-invalid > .ui-dropdown {
  border-bottom-color: #ddd !important;
}

 p-autocomplete.ng-dirty.ng-invalid > .ui-autocomplete > .ui-inputtext {
  border-bottom-color: #ccc;
}

p-autocomplete.ng-dirty.ng-invalid ~ .custom-control-label {
  color: #999;
}

.has-val p-autocomplete.ng-dirty.ng-invalid ~ .custom-control-label, p-autocomplete.ng-dirty.ng-invalid.ui-inputwrapper-focus ~ .custom-control-label {
  color: #4f94df;
}

.has-error p-autocomplete .ui-autocomplete .ui-inputtext {
  border-bottom-color: #db1a1a !important;
  /* border-bottom-color: #a94442 !important; */
}


/* primng multiselect custom styles */
.custom-p-multiselect{
    border: 0 !important;
    border-bottom: 1px solid #ccc !important;
    border-radius: 0 !important;
    width: 100%;
    background: transparent !important;
}
.p-multiselect-container .custom-control-label{
  z-index:-1;
}
.custom-p-multiselect .ui-corner-right{
  border:0;
}
.custom-p-multiselect .ui-multiselect-label{
  /* color: #808080 !important; */
  color:#999 !important;
  padding-left: 15px !important;
  font-family: 'Roboto', sans-serif !important;
  font-size: 16px;
}
.custom-p-multiselect:not(.ui-state-disabled):hover {
    border-color: #ccc;
}
.custom-p-multiselect:focus {
    border-bottom:1px solid #4f94df !important;
}
.custom-p-multiselect .pi{
  font-size: 0.9em;
}
.has-error .custom-p-multiselect{
  border-color: #db1a1a !important;
}
.has-error .ui-multiselect-label{
  color:#db1a1a !important;
}
.p-multiselect-container .has-val .ui-multiselect-label{
  padding-left: 8px !important;
  color:#555 !important;
}


/* fix primeng multiselect option item big width when using property appendTo="body" and text option is long  */
.ui-multiselect-panel .ui-multiselect-item{
  white-space: normal;
}
.ui-multiselect-panel .ui-multiselect-item .ui-chkbox ,  .ui-multiselect-header .ui-chkbox{
  display:none !important;
}
/* when clicking on multiselect, ui-multiselect-panel appears at the end of the body element (outside any 
component so we can't put any specific styles for .ui-multiselect-panel class inside components) 
currently affected interface is search-job */
.ui-multiselect-panel{
  width:350px;
}
@media screen and (max-width:430px){
  .ui-multiselect-panel{
    width:255px;
  }
}

/* start editor styles - use editor-description class in the page you want to display the text entered 
  using p-editor - like help and advr preview pages  */
/* for p-editor align right class */
.ql-align-right{
  direction:rtl;
}
.ql-align-left{
  text-align: left;
}
.ql-align-center{
  text-align: center;
}
.ql-align-justify{
  text-align: justify;
}
.ql-editor .ql-align-right{
  padding-right: 1.5em;
  padding-left:0;
}
.ql-editor .ql-align-right::before {
  /* margin-right: -1.5em !important; */
  margin-left: 0.3em !important;
  text-align: left !important;
}
/* to fix ul padding if direction rtl */
.desc-justify ul , .editor-description ul{
  padding-right:20px;
}
.editor-description h1{
  font-size:23px;
}
.editor-description h2{
  font-size:20px;
}
.editor-description h3{
  font-size:18px;
}
.editor-description h4{
  font-size:16px;
}
.editor-description p{
  font-size:16px;
}
.editor-description img{
  max-width: 100%;
}
/* fix editor copied text style */
pre{
  background: transparent;
  border: none;
  color: inherit;
  display: inherit;
  font-size: inherit;
  padding: inherit;
  margin: inherit;
  line-height: inherit;
}

/* end editor styles */

.flex-row-all{
  display: flex;
  flex-wrap: wrap;
}

.warning-msg-link, .warning-msg-link:hover{
  color: #666;
  text-decoration: underline;
}


/* .arabic-direction-font h1 , .arabic-direction-font h2 , .arabic-direction-font h3 ,.arabic-direction-font h4 , .arabic-direction-font span , .arabic-direction-font a{
  direction:rtl;
  text-align: right;
  font-family:'Droid Arabic Kufi', serif;
}
.arabic-direction-font p, .arabic-direction-font ul {
  direction:rtl;
  font-family:'Droid Arabic Kufi', serif;
  line-height: 1.6;
} */

/* Dialog Styles */
/*.ui-dialog.ui-corner-all {*/
  /*width: 85%;*/
  /*z-index: 100000 !important;*/
/*}*/

/*.ui-dialog.ui-shadow {*/
  /*box-shadow: 0 0 0 1000px #000a;*/
/*}*/

/*.ui-dialog .ui-dialog-titlebar-icon {*/

  /*width: 30px !important;*/
  /*height: 30px !important;*/
  /*text-align: center;*/
  /*border: 0 !important;*/
  /*padding: 0 !important;*/
  /*top: 22px;*/
  /*right: 12px;*/
  /*position: absolute;*/
/*}*/

/*.ui-dialog.ui-widget .ui-dialog-titlebar {*/
  /*background: #fff;*/
  /*border-bottom: 1px solid #ddd;*/
/*}*/

/*.ui-dialog .ui-dialog-titlebar-icon:hover {*/
  /*background: transparent;*/
  /*border-color: transparent;*/
/*}*/

/*.ui-dialog .ui-dialog-titlebar-icon span {*/
  /*width: 30px !important;*/
  /*height: 30px !important;*/
  /*line-height: 30px;*/
  /*font-size: 18px;*/
  /*display: inline-block;*/
  /*content: "\00d7";*/
  /*font-family: FontAwesome;*/
  /*background: transparent !important;*/
/*}*/

/*.ui-dialog .ui-dialog-titlebar-icon span:before {*/
  /*content: "\f00d";*/
/*}*/

@media screen and (max-width: 768px) {
  .form-control:focus ~ .custom-control-label, .has-val .form-control ~ .custom-control-label {
    transform: translate(0, -75%);
    margin-left: 0px;
    font-size: 14px;
  }

  .focus-no-padding .ui-inputwrapper-focus ~ .custom-control-label, .has-val ~ .custom-control-label {
    transform: translate(0, -95%);
    /* transform: translate(0, -75%); */
    margin-left: 0px;
    font-size: 14px;
    color: #4f94df;
  }

  .focus-no-padding {
    min-height: 26px !important;
  }

  .form-control {
    padding-top: 0;
    height: 26px;
  }

  .margin-top-7 {
    margin-top: 7px;
  }

  .ui-autocomplete .ui-inputtext {
    height: 26px;
    margin-top: 0px;
  }
}

.editStyle {
  text-decoration: none !important;
  color: #fff !important;
  width: fit-content;
}
.ui-dropdown{
  display: inline-block;
  min-width: 50px;
}
.ui-dropdown-label-container{
  width:100%;
}
.ui-dropdown label.ui-dropdown-label{
  padding-left: 12px;
  padding-right: 30px;
  font-size: 16px;
  background: transparent;
  color: #555;
}
.ui-autocomplete .ui-inputtext{
  padding-left: 12px;
  padding-right: 30px;
  font-size: 16px;
  color: #555;
}

.ui-dropdown{
  background: transparent !important;
  z-index:2;
  -webkit-transition: z-index 0s ease 1s;
  -moz-transition: z-index 0s ease 1s;
  -ms-transition: z-index 0s ease 1s;
  -o-transition: z-index 0s ease 1s;
  transition: z-index 0s ease 1s;

}
/* .ui-dropdown .ui-dropdown-label{
  z-index:2;
} */
.ui-dropdown.ui-dropdown-open{
  z-index:3;
  -webkit-transition: z-index 0s ease 0s;
  -moz-transition: z-index 0s ease 0s;
  -ms-transition: z-index 0s ease 0s;
  -o-transition: z-index 0s ease 0s;
  transition: z-index 0s ease 0s;
}
.has-error  p-dropdown.ng-invalid > .ui-dropdown {
  border-bottom-color: #db1a1a !important;
  /* border-bottom-color: #a94442 !important; */
}

.ui-dialog.ui-widget .ui-dialog-content{
  padding: 0px;
}


.custom-checkbox{
  display:flex;
  align-items: center;
}
.custom-checkbox input{
  width:12%;
  height: 18px;
  margin:0;
}

.ui-dropdown .ui-dropdown-label{
    /* border-bottom: 1px solid #ccc !important;
    border-radius: 0; */
    background: transparent !important;
}
body .ui-inputtext{
  font-size: 16px;
  color: #555;
}

 /* start modify ng-select lazyload styles */
.ng-select{
  width:100%;
  height: auto;
}
.form-control .ng-select-container{
  z-index:2;
}
.ng-select .ng-select-container , .ng-select .ng-select-container:focus{
  border:none;
  border-radius:0;
  box-shadow:none !important;
  outline:none;
  background: transparent;
}
 .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{
  background-color: #eee;
  padding: 3px;
  border-radius: 25px;
}
.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right{
  border-radius: 50%;
  background: #6D6D6D;
  color:#eee;
  border:none;
  font-size: 11px;
  padding: 0px 4px 2px 4px;
}
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{
  font-size:14px;
}
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked{
  background-color: #eee !important;
}
/* to fix Selected options goes blank in mobile */
.ng-select.ng-select.ng-select-single.ng-select-filtered:not(.ng-select-opened) .ng-select-container.ng-has-value .ng-value-container .ng-value{
  visibility: visible;
}

/* .ng-select-autocomplete .ng-clear-wrapper {
  display: none;
} */

/* .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value:hover .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right fa-times{
  color:red;
} */
 /* end modify ng-select lazyload styles */
.modal-body{
  max-height: 500px;
  overflow: auto;
}
/****** END I Deleted { :host >>> } when move it outside the component ********/
.btn-no-corner{
  border-radius: 0;
  min-width:92px;
}
.golden-star{
  color:gold;
}
.gray-star{
  color:gray;
}
.description-cell-align{
  text-align: justify !important;
}
.description-cell-align ol , .description-cell-align ul{
  padding-left:16px !important;
}

/************* PRELOADER *************/

.preloader{
  min-height: 100vh;
  /* display: flex;
  justify-content: center;
  align-items: center; */
  position: absolute;
  top:50%;
  left:50%;
  /* transform: translate(-50%,0); */
  transform: translate(-50%,-110px);
  z-index: 1000000;
  /* margin-top:-100px; */
}
.infinity {
  width: 120px;
  height: 60px;
  position: relative;
}
.infinity div, .infinity span {
  position: absolute;
}
.infinity div {
  top: 0;
  left: 50%;
  width: 60px;
  height: 60px;
  animation: rotate 6.9s linear infinite;
}
.infinity div  span {
  left: -8px;
  top: 50%;
  margin: -8px 0 0 0;
  width: 16px;
  height: 16px;
  display: block;
  background: #8c6ff0;
  box-shadow: 2px 2px 8px rgba(140 , 111, 240, .09);
  border-radius: 50%;
  transform: rotate(90deg);
  animation: move 6.9s linear infinite;
}
.infinity div  span:before,
.infinity div  span:after {
  content: '';
  position: absolute;
  display: block;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  background: inherit;
  top: 50%;
  left: 50%;
  margin: -7px 0 0 -7px;
  box-shadow: inherit;
}
.infinity div  span:before {
  animation: drop1 .8s linear infinite;
}
.infinity div  span:after{
  animation: drop2 .8s linear infinite .4s;
}
.infinity div:nth-child(2){
  animation-delay: -2.3s;
}
.infinity div:nth-child(2) span {
  animation-delay: -2.3s;
}
.infinity div:nth-child(3) {
  animation-delay: -4.6s;
}
.infinity div:nth-child(3) span {
  animation-delay: -4.6s;
}

/* app-user-wrapper .preloader{
  margin-top:-180px;
} */
/* @media screen and (max-width :767px){
  app-education .preloader{
    transform: translate(-50%,-300px);
  }
} */
@keyframes rotate {
  50% {
    transform: rotate(360deg);
    margin-left: 0;
  }
  50.0001%,
  100% {
    margin-left: -60px;
  }
}

@keyframes move {
  0%,50% {
    left: -8px;
  }
  25% {
    background: #5628EE;
  }
  75% {
    background: #23C4F8;;
  }
  50.0001%,
  100% {
    left: auto;
    right: -8px;
  }
}

@keyframes drop1 {
  100% {
    transform: translate(32px, 8px) scale(0);
  }
}

@keyframes drop2 {
  0% {
    transform: translate(0, 0) scale(.9);
  }
  100% {
    transform: translate(32px, -8px) scale(0);
  }
}

body .ui-inputtext:enabled:hover:not(.ui-state-error) {
  border-color: #ccc;
}
body .ui-inputtext {
  font-family: "Exo2-Regular", sans-serif;
}
.e-multi-select-wrapper .e-chips>.e-chipcontent{
  font-family: "Exo2-Regular", sans-serif;
}
/* in user-topbar */
.country-dropdown .ui-dropdown .ui-dropdown-panel {
  min-width: 200px !important;
}
.country-dropdown .ui-dropdown{
  border-bottom: 0 !important;
}
.navbar-search-job-form .country-dropdown .ui-dropdown .ui-dropdown-label{
  padding:0 !important;
}
.search-div .form-control.ui-autocomplete.ui-widget{
  width: calc(100% - 110px) !important;
}
/* .search-div .focus-no-padding .custom-underline{
  width: calc(100% - 125px) !important;
  bottom:14px !important; 
} */


.country-dropdown .ui-dropdown {
  position: relative;
  min-width: 16px !important;
}
.country-dropdown .ui-dropdown label.ui-dropdown-label {
    padding: 0 !important;
    /* width:16px !important; */
}
.navbar-search-job-form .country-dropdown .ui-dropdown .ui-dropdown-trigger{
    display: none !important;
}

.ui-carousel-dots-container{
  display: none;
}

input[type=checkbox], input[type=radio] {
  margin: 4px 4px 0 4px;
  line-height: normal;
}
.dis-flex{
  display: flex;
}
.desc-justify{
  text-align: justify;
}
.desc-tabel-cell{
  text-align: left !important;
}
/*
  Bootstrap Carousel Fade Transition (for Bootstrap 3.3.x)
  CSS from:       http://codepen.io/transportedman/pen/NPWRGq
  and:            http://stackoverflow.com/questions/18548731/bootstrap-3-carousel-fading-to-new-slide-instead-of-sliding-to-new-slide
  Inspired from:  http://codepen.io/Rowno/pen/Afykb 
*/
.carousel-fade .carousel-inner .item {
  opacity: 0;
  transition-property: opacity;
}

.carousel-fade .carousel-inner .active {
  opacity: 1;
}

.carousel-fade .carousel-inner .active.left,
.carousel-fade .carousel-inner .active.right {
  left: 0;
  opacity: 0;
  z-index: 1;
}

.carousel-fade .carousel-inner .next.left,
.carousel-fade .carousel-inner .prev.right {
  opacity: 1;
}

.carousel-fade .carousel-control {
  z-index: 2;
}

/*
  WHAT IS NEW IN 3.3: "Added transforms to improve carousel performance in modern browsers."
  Need to override the 3.3 new styles for modern browsers & apply opacity
*/
@media all and (transform-3d), (-webkit-transform-3d) {
    .carousel-fade .carousel-inner > .item.next,
    .carousel-fade .carousel-inner > .item.active.right {
      opacity: 0;
      -webkit-transform: translate3d(0, 0, 0);
              transform: translate3d(0, 0, 0);
    }
    .carousel-fade .carousel-inner > .item.prev,
    .carousel-fade .carousel-inner > .item.active.left {
      opacity: 0;
      -webkit-transform: translate3d(0, 0, 0);
              transform: translate3d(0, 0, 0);
    }
    .carousel-fade .carousel-inner > .item.next.left,
    .carousel-fade .carousel-inner > .item.prev.right,
    .carousel-fade .carousel-inner > .item.active {
      opacity: 1;
      -webkit-transform: translate3d(0, 0, 0);
              transform: translate3d(0, 0, 0);
    }
}

/* end Bootstrap Carousel Fade Transition (for Bootstrap 3.3.x) */


/* should clean all css code in cv components with #page-content-wrapper .page-content , also in media query */
#page-content-wrapper .page-content{
  margin-top:0 !important;
}

/* .btn:active , .btn:focus{
  border-color:transparent !important;
} */

.noParagraphMarginB p{
  margin-bottom: 0 !important;
}
.circle-img{
  border-radius: 50%;
}


/* start recieve cvs styles */
.expand-folders .folder-title{
  display:inline !important;
}
.expand-folders .folder-icon{
  font-size: 22px;
}

.collapse-folders .folder-title{
  display:none;
}
.collapse-folders .folder-icon{
  font-size: 25px;
}

.collapse-folders .advs-title-folder .folder-title , .collapse-folders .advs-title-folder .cvs-count{
  display:none !important;
}

legend{
	border:none;
}
/* end recieve cvs styles */
/* .override-form-control-zindex .form-control{
  z-index: 0 !important;
} */

/* start side options bar */
.preview-all-wrapper{
  position:relative;
}
.side-options-bar{
  min-width:30px;
  position: sticky;
  left: 22px;
  top:30%;
  float: left;
}

.side-options-bar button{
  display:block;
  margin: auto auto 20px auto;
  padding:7px;
  cursor: pointer;
  color:#fff;
  text-align: center;
  border-radius: 3px;
}
.side-options-bar button:nth-child(1) {
  background: #10996D;
}
.side-options-bar button:nth-child(2) {
  background: #3D7BCE;
}
.side-options-bar button:nth-child(3) {
  background: #db1a1a;
  /* background: #a94442; */
}
.side-options-bar button:nth-child(4) {
  background: #FFE399;
}
.side-options-bar button img{
  width:23px;
}
.alert-warning{
  background:#ffe399;
}
.alert-warning a{
  display: inline-block;
  margin-top: 10px;
  font-weight: bold;
  font-size: 16px;
  text-decoration: none;
}

/* end side options bar */

/* start pdf styles */
.pdfViewer .page {
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 20%), 0 1px 1px 0 rgb(0 0 0 / 14%), 0 2px 1px -1px rgb(0 0 0 / 12%) !important;
}
.pdf-container{
  margin-top: 30px;
  margin-left: 135px;
  margin-right: 90px;
}
.pdf-options-bar{
  left:50px;
}

/* end pdf styles */

@media screen and (max-width :1130px){
  .pdf-options-bar{
    left:30px;
  }
  .pdf-container{
    margin-left: 135px;
    margin-right: 40px;
  }
  
}

@media screen and (max-width :900px){
  #page-content-wrapper .page-title{
    padding: 0 0 25px;
    display: block;
    position: static;
  }
}

@media screen and (max-width :768px){
  .pdf-container{
    margin-left: 37px;
    margin-right: 20px;
    padding:0 12px;
  }
  .side-options-bar{
    left: 5px;
  }
  .pdf-options-bar{
    left:8px;
  }
  .side-options-bar button{
    padding:3px; 
  }
  .side-options-bar button img{
    width:18px;
  }
  
  /* editor styles */
  .editor-description h1{
    font-size:20px;
  }
  .editor-description h2{
    font-size:18px;
  }
  .editor-description h3{
    font-size:16px;
  }
  .editor-description h4{
    font-size:14px;
  }
  .editor-description p{
    font-size:14px;
  }
}
@media screen and (orientation:landscape) and (max-width:767px) {
  .side-options-bar{
    top:45%;
      /* top: 59%; */
  }
  .side-options-bar button{
      margin:auto auto 8px;
  }
}
@media screen and (max-width :500px){
  .pdf-container{
    margin-right: 5px;
  }
}

/* Start navbar home on scroll  */
.home-page-content .nav-menu1 ul{
  display:none;
}

.home-page-content .cus-navbar .cveek-logo-white{
  display:block !important;
}
.home-page-content .cus-navbar .cveek-logo-original{
  display:none !important;
}
.home-page-content .cus-navbar{
  background: transparent !important;
}
.home-page-content nav .scrolled {
  background-color: #fff !important;
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}
.home-page-content nav .scrolled .cveek-logo-white{
  display:none !important;
}
.home-page-content nav .scrolled .cveek-logo-original{
  display:block !important;
}
.home-page-content .join-btn{
  background:#fff  !important;
  color: #e3b442  !important;
  font-weight: bold;
  border-radius: 4px  !important;
}
.home-page-content nav .scrolled .join-btn{
  background:#e3b442  !important;
  color: #fff  !important;
  font-weight: bold;
  border-radius: 4px  !important;
}
.home-page-content .cus-toggle-btn svg{
  color: #fff !important;
  fill: #fff !important;
}
.home-page-content nav .scrolled .cus-toggle-btn svg{
  color: #999 !important;
  fill: #999 !important;
}
.cus-navbar .search-job-form{
  flex-wrap:nowrap !important;
}
/* End navbar home on scroll  */

/* fix footer isuue with short content */
/* @media screen and (min-width: 1400px){
  .general-wrapper{
    min-height:500px;
   }
}
@media screen and (min-width: 1650px){
  .general-wrapper{
    min-height:600px;
   }
}
@media screen and (min-width: 1800px){
  .general-wrapper{
    min-height:700px;
   }
}

@media screen and (orientation:landscape) and (max-width:767px) {
  .general-wrapper{
    min-height: 200px;
  }
} */

/* Start article pages styles */


/* Start responsive styles */
@media (min-width: 1200px){
	.article-container{
		width:1100px  !important;
	}
}
@media (min-width: 1400px){
	.article-container{
		width:1250px  !important;
	}
}
@media (min-width: 1650px){
	.article-container{
		width:1500px  !important;
	}
}
@media (min-width: 1800px){
	.article-container{
		width:1700px !important;
	}
}

.article-container {
  padding: 35px 50px;
  margin:auto;
}
.article-container .article-img {
  margin-bottom:35px;
}
.article-container .article-img img{
  border-radius: 20px;
  display:block;
  margin:0 auto;
}
.article-container h1{
  color: #4876ba;
  margin-top:0;
  margin-bottom: 25px;
  font-size: 30px;
  text-align: center;
}
.article-container h2{
  font-size: 24px;
  /* margin: 0 0 10px 0; */
  color:#E2B233;
}
.article-container h3{
  font-size: 21px;
  color:#7b7b7b;
}
.article-container .section{
  margin:27px 0;
}
.article-container .last-section{
  margin-bottom:0;
}
.article-container p{
  text-align: justify;
  font-size:16px;
}
.article-container li{
  font-size:16px;
  text-align: justify;
  margin-bottom: 5px;
}

.article-container a {
  text-decoration:none;
  color:#4876ba;
  font-weight: bold;
}
.article-container a:hover {
  color:#E2B233;
}
.article-container .page-link a{
  font-size:22px;
}
@media screen and (max-width:991px){
  .article-container {
      padding: 35px 30px;
  }
  .article-container h1{
      margin-bottom: 23px;
      font-size: 26px;
  }
  .article-container h2{
      font-size: 22px;
  }
}

@media screen and (max-width:767px){
  .article-container {
      padding: 35px 15px;
  }
  .article-container h1{
      margin-bottom: 20px;
      font-size: 24px;
  }
  .article-container h2{
      font-size: 20px;
  }
  .article-container ul{
      padding-left: 23px;
  }
  .article-container .page-link a{
    font-size:18px;
  }
}

/* End article pages styles */