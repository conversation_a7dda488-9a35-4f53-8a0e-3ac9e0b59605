<div class="container">
    <div class="row">
        <div class="col-md-3 col-sm-3 col-xs-12 col-xxs-12">

            <app-sidebar (toggleSidebarClicked)="toggleTable()" (createFaqClicked)="displayCreateModal()" (createHelpTopicClicked)="displayCreateTopicModal()" (createHelpTipClicked)="displayCreateTipModal()"></app-sidebar>

        </div>
        <div class="col-md-9 col-sm-9 col-xs-12 col-xxs-12 collapsed">
            <div  *ngIf="name">
                <div class="dropdown" >
                  <button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <!-- <i class="fa fa-lg fa-user "></i> -->
                    <div class="username">{{ name }}</div>
                  </button>
                  <div class="dropdown-menu">
                      <ul style="list-style:none">
                          <li class="dropdown-item">
                            <!-- <img [src]="img" style="border-radius:50px; width:50px; height:50px;border: 1px solid darkblue;margin-right: 15px;" /> -->
                            <button class="btn btn-light"  style="border-radius: 100%;margin-right: 15px;box-shadow: none;"><i class="fa fa-user" style="font-size: 18px;"></i></button>
                            <a  > {{ name }}
                           <!-- <br> <small style="color: #a9a6a6;">See Your Profile</small> -->
                           </a>
                          </li>
                          <li class="dropdown-item"><button class="btn btn-light"  style="border-radius: 100%;margin-right: 15px;box-shadow: none;"><i class="fa fa-home" style="font-size: 18px;"></i></button><a [routerLink]="['/']" > Home Page </a></li>
                          <li class="dropdown-item" ><button class="btn btn-light" style="border-radius: 100%;margin-right: 15px;;box-shadow: none;"><i class="fa fa-cogs" style="font-size: 18px;"></i></button><a >Settings</a></li>
                          <div class="dropdown-divider"><hr></div>
                          <li><button type="button" class="btn btn-light" (click)="logout()" style="margin-left: 15px;"><i class="fa fa-door-open"></i>Logout</button></li>
                      </ul>
                  </div>
                </div>

                <!-- <app-notification></app-notification> -->
            </div>

            <router-outlet></router-outlet>



        </div>
    </div>
</div>



<!--Start of modal-->
<div class="modal fade" *ngIf="displayModal" id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h3 class="modal-title" *ngIf="displayCreatePopup" id="modalLabel" translate>faqs.labels.AddNewFAQ</h3>
                <h3 class="modal-title" *ngIf="displayCreateTopic" id="modalLabel" translate>help.labels.AddNewHelpTopic</h3>
                <h3 class="modal-title" *ngIf="displayCreateTip" id="modalLabel" translate>help.labels.AddNewHelpTip</h3>

            </div>

            <app-edit-modal *ngIf="displayCreatePopup" [mode]="mode" (closeFormModal)="closeModal()" [openedFromSidebar]="true"></app-edit-modal>
            <app-edit-topic-modal *ngIf="displayCreateTopic" [mode]="mode" (closeCreateModal)="closeModal()" [openedFromSidebar]="true"></app-edit-topic-modal>
            <app-edit-tip-modal *ngIf="displayCreateTip" [mode]="mode" (closeCreateModal)="closeModal()" [openedFromSidebar]="true"></app-edit-tip-modal>

            <div class="modal-footer">
                <!-- <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="closeCreateFormModal()"  translate>faqs.labels.Close</button> -->
            </div>
        </div>
    </div>
</div>
<!-- end of  modal-->
