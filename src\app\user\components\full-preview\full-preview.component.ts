import {Component, OnDestroy, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {Subject} from 'rxjs/Subject';
import { ResumeService } from '../../cv-services/resume.service';
import { TemplatesService } from '../../cv-services/templates.service';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { GeneralService } from '../../../general/services/general.service';

@Component({
  selector: 'app-full-preview',
  templateUrl: './full-preview.component.html',
  styleUrls: ['./full-preview.component.css']
})
export class FullPreviewComponent implements OnInit , OnDestroy {

  resumeId : number = null;
  userResumeId='';
  username='';
   private ngUnsubscribe: Subject<any> = new Subject();
  templateId: number ;
  templatesUrl = '';
  pdfSrc = '';
  displaySideOptionsBar = false;
  resumeHaveNoData = false;
  errorOjb:any;
  constructor(private route: ActivatedRoute,
              private resumeService: ResumeService,
              private templatesService: TemplatesService,
              private privateSharedURL: ExportUrlService,
              private generalService:GeneralService) {

    this.setRoutingParams();
  }

  ngOnInit() {
    this.setResumeId();
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.templatesUrl = data + 'storage/cvs/pdf/';
    });

    this.generalService.internalMessage.subscribe((data) => {
      // when data ready in child component , display options bar
      if (data['src'] === 'resume-preview') {
        if(data['message'] === 'displaySideOptionsBar'){
          this.displaySideOptionsBar = true;
        }
        if(data['message'] === 'resumeHaveNoData'){
          this.resumeHaveNoData = true;
          this.errorOjb = data['mData'].errorDetails;
        }
      }
    });

  }

  setRoutingParams(){
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
    this.route.params.subscribe(res => {
      this.userResumeId = res['resumeId'];
    });
  }

  setResumeId(){
    this.resumeService.getGlobalResumeId(this.userResumeId).switchMap(res => {
      this.resumeId = res['data'].id;
      return this.templatesService.getAppliedTemplate(this.resumeId)
    }).takeUntil(this.ngUnsubscribe)
    .subscribe(res => {
      this.templateId = res['cv_template_id'];
    });
  }

  // notifyDownloadPdf($event){
  //   let action = $event.msg;
  //   if(action === 'download'){
  //     this.downloadPdf();
  //   }
  // }

  downloadPdf(){
    this.templatesService.getPdfUrl(this.resumeId,0).subscribe(url => {
      this.pdfSrc = this.templatesUrl + url["file"] + '?v='+Math.floor(Math.random() * 10000);
      if(this.pdfSrc !== ''){
          window.open(this.pdfSrc, "_blank");
      }
    });
  }

  downloadAtsPdfFile(){
    this.templatesService.getPdfUrl(this.resumeId,-1).subscribe(url => {
      this.pdfSrc = this.templatesUrl + url["file"] + '?v='+Math.floor(Math.random() * 10000);
      if(this.pdfSrc !== ''){
          window.open(this.pdfSrc, "_blank");
      }
    });
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
