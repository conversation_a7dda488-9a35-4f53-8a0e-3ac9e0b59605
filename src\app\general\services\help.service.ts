import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable()
export class HelpService {

  url = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data ;
      this.url     = data + 'admin';
    });

  }

  // getLanguages() {
  //   let headers = new HttpHeaders().set('Content-Type', 'application/json');
  //   return this.http.get(this.baseUrl + '/translated_languages', { headers });

  // }


  getData(langId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/helpCenter/users/' + langId, { headers });
  }

  getHelpTopic(id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/helpCenter/user_show/' + id, { headers });
  }

  getMainCat(id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/get_categories_data_by_category_id/' + id, { headers });
  }

  getSubCat(id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/get_help_centers_with_sub_cats/' + id, { headers });
  }
}
