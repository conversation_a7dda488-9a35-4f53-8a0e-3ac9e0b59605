import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, HostListener, ChangeDetectorRef } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validator, Validators, NgForm } from '@angular/forms';
import { CompanyFormService } from '../../services/company-form.service';
import {LangChangeEvent, TranslateService} from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import 'rxjs/add/operator/takeUntil';

import { CompanyWrapperService } from '../../services/company-wrapper.service';
import { MessageService, ConfirmationService, Message } from "primeng/api";
import { DataMap } from "shared/Models/data_map";
import { ImageProcessingService } from "shared/shared-services/image-processing.service";
import { CompanyInfoValidators } from "app/company/components/company-form/company_info.validators";
import { GeneralService } from '../../../general/services/general.service';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { UrlValidator } from 'shared/validators/url.validators';


declare var $: any;

@Component({
  selector: 'company-form',
  templateUrl: './company-form.component.html',
  styleUrls: ['./company-form.component.scss'],
  providers: [MessageService]
})
export class CompanyFormComponent implements OnInit, OnDestroy {

  tab_exist: boolean;
  new_language_profile;
  new_language;
  current_language = Number (localStorage.getItem('current_company_language'));
  companyForm;
  username='';
  selectedIndus: string[] = [];
  //fix company_id
  companyId = Number (localStorage.getItem('company_id'));
  public value: any = [];
  public value2: any = [];
  companyProfileId: number = null;
  private ngUnsubscribe: Subject<any> = new Subject();
  companyIndustriesSpecialtiesOpt = [];
  // companyIndustryOpt: { [key: string]: Object }[] = [];
  // public remoteFields: Object = { text: 'name', value: 'company_industry_id' };
  public remoteFields: Object = { text: 'name', value: 'id' };
  socialMediaOpt: SocialMediaInterface[] = [];
  // companyspecialtiesOpt = [];
  companyspecialtiesOptSelected = [];
  // public remoteFields2: Object = { text: 'specialty_translation_name', value: 'specialty_id' };
  companyTypeOpt = [];
  companySizeOpt = [];
  extraChecked = false;
  externalInfoVisible = false;
  currentYear = (new Date()).getFullYear();
  yearOpts = [];
  status: boolean;
  profile: boolean;
  edit_status = false;
  display_save_msg = false;
  display_Profile_Languages = false;
  sendData2 ;
  languageForm =[];
  data_map = new DataMap();
  get_Data = [];
  company_profiles_languages = [];
  company_profiles_translated = [];
  subscription: Subscription;
  image_uploaded_url = './assets/images/Capture.PNG';
  image_code_to_send:{file:string, file_type:string, is_deleted:boolean};
  label_option = "Upload Photo";
  // monthOpts = this.data_map.monthOpts;
  monthOpts = [
    {'value': '1', 'label': 'January' , 'code':'1' },
    {'value': '2', 'label': 'February', 'code':'2'},
    {'value': '3', 'label': 'March', 'code':'3'},
    {'value': '4', 'label': 'April', 'code':'4'},
    {'value': '5', 'label': 'May', 'code':'5'},
    {'value': '6', 'label': 'June', 'code':'6'},
    {'value': '7', 'label': 'July', 'code':'7'},
    {'value': '8', 'label': 'August', 'code':'8'},
    {'value': '9', 'label': 'September', 'code':'9'},
    {'value': '10', 'label': 'October', 'code':'10'},
    {'value': '11', 'label': 'November', 'code':'11'},
    {'value': '12', 'label': 'December', 'code':'12'}
  ];
  
  @HostListener('window:beforeunload',['$event'])
  showMessage($event) {
     $event.returnValue='Your data will be lost!';
  }
  uploadedFiles : any [] = [];

  msgs: Message[] = [];
  imgError:string = null;
  uploadLabelDisplay = true;

  perPhotoSrc: string="./assets/images/Capture.PNG";
  noProfilePicSet : string="./assets/images/Capture.PNG";
  // fileUrl: any = '';
  position: string;
  displayApplyInfoDialog: boolean = false;

  //UrlReg = '(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?';

  constructor(
    private fb: FormBuilder,
    private companyFormService: CompanyFormService,
    private route: ActivatedRoute,
    private router: Router,
    private translate: TranslateService,
  //  private upload_img : ImageProcessingService,
    private imageProcessingService : ImageProcessingService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private cdRef:ChangeDetectorRef,
    private generalService: GeneralService,
    private privateSharedURL: ExportUrlService,
  ) {

    
    
    this.companyForm = this.fb.group({
      logo:[{}],
      company_id: [''],
      translated_languages_id:[this.current_language],
      name: ['', [Validators.required,
        // Validators.minLength(2),
        // Validators.maxLength(40)
      ]
      ],
        industries: ['', Validators.required],
        specialties: [''],
      // 'company_industries': this.fb.array([]),
      // 'company_industries_temp': ['', Validators.required],

      // 'company_specialties': this.fb.array ([]),
      // 'company_specialities_temp': ['', {disabled:true}],
      'company_type_id_temp': [''],
      'company_type_id': [''],
      'company_website': ['', UrlValidator.isValidUrlFormat],
    //  'company_website': ['',Validators.pattern(this.UrlReg)],
      description: ['',Validators.maxLength(4000)],
      'show_apply_button':[1, Validators.required],
      'company_size_id_temp': [''],
      'company_size_id': [''],
      'path_company_imagelogo':[''],
      founded_date: this.fb.group({
        year: [''],
        month: ['']
      }),
      is_month:[''],
      'company_social_links': this.fb.array([]),
     'company_social_medias' :this.fb.array ([]),
     
    });

    for (let year = this.currentYear; year >= 1918; year--) {
      this.yearOpts.push({'value': year.toString(), 'label': year});
    }
    this.yearOpts.unshift({'value': '', 'label': ''});
    this.route.parent.parent.params.subscribe(res => {
      this.username = res['username'];
    });
   }


private createSLinks() {
  return new FormGroup({
    'type': new FormControl(''),
    'social_media_id': new FormControl(''),
    'url': new FormControl('', UrlValidator.isValidUrlFormat)
  },CompanyInfoValidators.companyLinkValidator);
}

selectSocialMediaChange(socialMedia, i) {
  this.company_social_links.at(i).get('social_media_id').setValue(socialMedia.value.id);
}

get company_social_links() {
  return this.companyForm.get('company_social_links') as FormArray;
}

addSLinks() {
  this.company_social_links.insert(0, this.createSLinks());
}

removeSLinks(i) {
  this.company_social_links.removeAt(i);
}

setcompanyLanguage(langaugeId) {
  if (langaugeId === 1) {
    this.translate.setDefaultLang('en');

  } else if (langaugeId === 2) {
    this.translate.setDefaultLang('ar');

  } else {
    this.translate.setDefaultLang('en');
  }
}

minimize() {
  if ( !this.externalInfoVisible ) {
    this.externalInfoVisible = true;
    $('.extraInformation').slideDown('slow');
  } else {
    this.externalInfoVisible = false;
    $('.extraInformation').slideUp('slow');
  }
}

/*--- get The form's elements data such as dropdowns options multiselect options ... etc */

NewFormData(langaugeId,data?) {
    this.languageForm = [];
     this.languageForm.push({
       'language_id':langaugeId
     });
     this.current_language = langaugeId;
     this.companyFormService.getCompanyFormDataNew(this.languageForm[0].language_id, this.companyId).subscribe(
       (res) => {
        //  this.companyIndustryOpt = [];
        //  this.companyspecialtiesOpt = [];
         this.companyIndustriesSpecialtiesOpt = [];
         this.companyTypeOpt = [];
         this.companySizeOpt = [];
         this.socialMediaOpt = [];

         this.companyIndustriesSpecialtiesOpt = res['company_industries_specialties'];

        //  for (let i = 0; i < res['company_industries'].length ; i++) {
        //    if( res['company_industries'][i].company_industry_translation[0]
        //      !== undefined) {
        //       this.companyIndustryOpt[i] = {'company_industry_id': res['company_industries'][i].company_industry_translation[0]
        //       .company_industry_id,
        //        'name': res['company_industries'][i].company_industry_translation[0].name} ;
        //     }
        //  }
    
        //  for (let i = 0; i < res['specialties'].length; i++) {
        //    if( res['specialties'][i].specialties_translation[0]
        //     !== undefined) {
        //       this.companyspecialtiesOpt[i] = {'company_industry_id': res['specialties'][i].company_industry_id,
        //       'specialty_translation_name': res['specialties'][i].specialties_translation[0].specialty_translation_name,
        //       'specialty_id': res['specialties'][i].specialties_translation[0].specialty_id} ;
        //     }
        //  }
        
 
         for (let i = 0; i < res['company_type'].length; i++) {
           this.companyTypeOpt[i] = res['company_type'][i].company_type_translations[0] ;
         }
 
         this.companyTypeOpt.unshift({'company_type_id': '', 'company_type_name': ''});
 
 
         for (let i = 0; i < res['company_size'].length; i++) {
           this.companySizeOpt[i] = res['company_size'][i].company_size_translation[0] ;
         }
 
         this.companySizeOpt.unshift({'company_size_id': '', 'name': ''});
 
 
         for (let i = 0; i < res['socialMedia'].length; i++) {
           this.socialMediaOpt[i] = res['socialMedia'][i] ;
         }
         this.socialMediaOpt.unshift({'id': '', 'name': ''});

         if(data !== undefined) {
          this.BasicForm(data);
         } else {
           this.company_social_links.push (this.createSLinks());
         }
         
       });
       
}


/*--- fill the form with company profile data ---*/

BasicForm(data_get) {
  let company_type_id;
  let data = data_get;
  this.value = [];
  this.value2 = [];
  let social_media_selected = [];
  let company_name;
  let company_description;
  let company_website;
  let founded_date;
  let company_size_id;
  let media = [];
  let company_social_media_info = [];
  let media_id = [];
  
  if(data_get['translated_profile_data'] !== undefined && this.new_language !== null) {
    this.companyForm.controls['industries'].setValue(data['translated_profile_data'].industries);
    this.companyForm.controls['specialties'].setValue(data['translated_profile_data'].specialties);
    // for (let i = 0 ; i < data['translated_profile_data'].industries.length ; i++) {
    //   this.value[i] = data['translated_profile_data'].industries[i].id;
    // }
    // for (let i = 0 ; i < data['translated_profile_data'].specialties.length ; i++) {
    //   this.value2[i] = data['translated_profile_data'].specialties[i].id;
    // }
    // let y = [];
    // let x = data['translated_profile_data'].company_industries_for_companies;
    // for (let i = 0 ; i < data['translated_profile_data'].company_industries_for_companies.length ; i++) {
    //   this.value[i] = data['translated_profile_data'].company_industries_for_companies[i].company_industry_id;
    // }

    // let z = [];
    // let m = data['translated_profile_data'].company_specialties_for_companies;
    // for (let i = 0 ; i < m.length ; i++) {
    //   this.value2[i] = m[i].specialty.specialties_translation[0].specialty_id;
    // }

      social_media_selected = data['translated_profile_data'].company_social_medias;

    if ( data['translated_profile_data'].company_type.company_type_translations[0].company_type_id !== null ) {

          company_type_id = data['translated_profile_data'].company_type.company_type_translations[0].company_type_id;
    }

    if ( data['translated_profile_data'].company_size.company_size_translation[0].company_size_id !== null ) {
      company_size_id = data['translated_profile_data'].company_size.company_size_translation[0].company_size_id;
  }
  } else if(data_get['existing_profile_data'] !== undefined) {
    this.companyForm.controls['industries'].setValue(data['existing_profile_data'].industries);
    this.companyForm.controls['specialties'].setValue(data['existing_profile_data'].specialties);
    // for (let i = 0 ; i < data['existing_profile_data'].industries.length ; i++) {
    //   this.value[i] = data['existing_profile_data'].industries[i].id;
    // }
    // for (let i = 0 ; i < data['existing_profile_data'].specialties.length ; i++) {
    //   this.value2[i] = data['existing_profile_data'].specialties[i].id;
    // }
    // let y = [];
    // let x = data['existing_profile_data'].company_industries_for_companies;
    // for (let i = 0 ; i < data['existing_profile_data'].company_industries_for_companies.length ; i++) {
    //   this.value[i] = data['existing_profile_data'].company_industries_for_companies[i].company_industry_id;
    // }
    
    // let z = [];
    // let m = data['existing_profile_data'].company_specialties_for_companies;
    // for (let i = 0 ; i < m.length ; i++) {
    //   this.value2[i] = m[i].specialty.specialties_translation[0].specialty_id;
    // }
    
    this.companyProfileId = data['existing_profile_data']['company_profile_translations'][0].company_profile_id;
    company_name = data['existing_profile_data'].company_profile_translations[0].name;
    company_description = data['existing_profile_data'].company_profile_translations[0].company_description;
    company_website = data['company_website'];
    social_media_selected = data['existing_profile_data'].company_social_medias;

    if ( data['existing_profile_data'].company_type !== null ) {

          company_type_id = data['existing_profile_data'].company_type.company_type_translations[0].company_type_id;
    }

    if(data['existing_profile_data'].company_size !== null){
      
          company_size_id = data['existing_profile_data'].company_size.company_size_translation[0].company_size_id;
      }

    this.companyForm.controls['name'].setValue(company_name);
    this.companyForm.controls['description'].setValue(company_description);
    this.companyForm.controls['company_website'].setValue(company_website);
    this.companyForm.controls['show_apply_button'].setValue(data['show_apply_button']);

    if(data['founded'] !== null  && data['founded'] !== undefined){
      founded_date  = new Date(data['founded']);
      this.founded_dateControl.controls['year'].setValue(founded_date.getFullYear().toString());
    //  this.founded_dateControl.controls['month'].setValue((founded_date.getMonth() + 1).toString());
      if(data['is_month']===1)
        this.founded_dateControl.controls['month'].setValue((founded_date.getMonth() + 1).toString());
      else   this.founded_dateControl.controls['month'].setValue('');
      
    }
    else{
      this.founded_dateControl.controls['year'].setValue('');
      this.founded_dateControl.controls['month'].setValue('');
    }

    if(data_get['existing_profile_data'].path_company_imagelogo) {
      this.perPhotoSrc = this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail',data_get['existing_profile_data'].path_company_imagelogo);
    //  this.perPhotoSrc = this.fileUrl + 'storage/company/logo/'+ data_get['existing_profile_data'].path_company_imagelogo;
      this.uploadLabelDisplay = false;
    }
    
  } else {
    this.value = [];
    this.companyForm.controls['company_type_id_temp'].setValue('');
    this.companyForm.controls['company_size_id_temp'].setValue('');
    company_size_id = [];
    this.value2 = [];
    social_media_selected = [];
    this.companyForm.controls['name'].setValue('');
    this.companyForm.controls['description'].setValue('');
    this.companyForm.controls['company_website'].setValue('');
    this.companyForm.controls['show_apply_button'].setValue(1);
    this.founded_dateControl.controls['year'].setValue('');
    this.founded_dateControl.controls['month'].setValue('');
  }

      this.company_social_links.controls = [];
      for (let i = 0 ; i < social_media_selected.length ; i++) {
        company_social_media_info.push(social_media_selected[i].company_social_media_info);
        media.push(social_media_selected[i].social_media.name);
        media_id.push(social_media_selected[i].social_media.id);
      }

      if (social_media_selected.length == 0) {

        this.company_social_links.push(this.createSLinks());
      } else {

        for (let i = 0 ; i < media_id.length ; i++) {
          this.company_social_links.push(
            this.fb.group(
              {
                'type': new FormControl({'id': media_id[i], 'name': media[i]}),
                'social_media_id': new FormControl(media_id[i]),
                'url': new FormControl( company_social_media_info[i], UrlValidator.isValidUrlFormat)
              }, {validator : CompanyInfoValidators.companyLinkValidator})
          );
        }
      }
      this.companyForm.controls['company_id'].setValue(this.companyId);
      for (let i = 0 ; i < this.companyTypeOpt.length ; i++) {
        if (this.companyTypeOpt[i].company_type_id === company_type_id) {
          this.companyForm.controls['company_type_id_temp'].setValue(this.companyTypeOpt[i]);
        }
      }
      for (let i = 0 ; i < this.companySizeOpt.length ; i++) {
        if (this.companySizeOpt[i].company_size_id === company_size_id) {
          this.companyForm.controls['company_size_id_temp'].setValue(this.companySizeOpt[i]);
            }
          }

        
}

 
/*---- here there is a state process about what to do
       if there is company profile data the first condition is ON and we fill up the form
       with this data..... if there is no data we jist initilize the form ----*/
/*---- if there is data then we may have one profile and the translated one for it which means
       that the backend will send the same profile but in another language
       and we will use that when the company choose another profile language ----*/

buildFilledForm() {
    this.company_profiles_languages = [];
    this.company_profiles_translated = [];
    
    if(this.get_Data.length > 0) {
      
      if( this.get_Data[0].existing_profiles.length > 1) {
        for ( let i = 0 ; i < this.get_Data[0].existing_profiles.length; i++ ) {

            this.company_profiles_languages.push({
              'profile_language_id':this.get_Data[0].existing_profiles[i].translated_languages_id,
              'language': this.get_Data[0].existing_profiles[i].existing_profile_data.company_profile_translations[0].
              translated_languages.name,
              'data': this.get_Data[0].existing_profiles[i]
            });

            if(this.get_Data['language_passed'] !== undefined) {
              if(this.get_Data['language_passed'] === Number(this.get_Data[0].existing_profiles[i].translated_languages_id)) {
                
                this.NewFormData(Number(this.get_Data[0].existing_profiles[i].translated_languages_id),this.get_Data[0].existing_profiles[i]);
                this.setcompanyLanguage(Number(this.get_Data[0].existing_profiles[i].translated_languages_id));
              }
            } else {

              if(this.current_language === Number(this.get_Data[0].existing_profiles[i].translated_languages_id)) {
                
                this.NewFormData(Number(this.get_Data[0].existing_profiles[i].translated_languages_id),this.get_Data[0].existing_profiles[i]);
                this.setcompanyLanguage(Number(this.get_Data[0].existing_profiles[i].translated_languages_id));
              }
            }
            
        }

      } else {
        this.setcompanyLanguage(Number(this.get_Data[0].existing_profiles[0].translated_languages_id));
        this.company_profiles_languages.push({
          'profile_language_id':this.get_Data[0].existing_profiles[0].translated_languages_id,
          'language': this.get_Data[0].existing_profiles[0].existing_profile_data.language_name,
          'data': this.get_Data[0].existing_profiles[0]
        });

        this.NewFormData(Number(this.get_Data[0].existing_profiles[0].translated_languages_id),
        this.get_Data[0].existing_profiles[0]);
      }

      if(this.get_Data[0].translated_profiles.length) {
        for ( let i = 0 ; i < this.get_Data[0].translated_profiles.length; i++ ) {

          this.company_profiles_translated.push({
            'profile_language_id':this.get_Data[0].translated_profiles[i].translated_languages_id,
            'language': this.get_Data[0].translated_profiles[i].translated_profile_data.language_name,
            'data': this.get_Data[0].translated_profiles[i]
          });
   
          }
        // for ( let i = 0 ; i < this.get_Data[0].translated_profiles.length; i++ ) {

        // this.company_profiles_translated.push({
        //   'profile_language_id':this.get_Data[0].translated_profiles[i].translated_languages_id,
        //   'language': this.get_Data[0].translated_profiles[i].translated_profile_data.company_industries_for_companies[0].
        //   company_industry.company_industry_translation[0].translated_languages.name,
        //   'data': this.get_Data[0].translated_profiles[i]
        // });
 
        // }

       
      }

      


    } else if(this.get_Data.length === 0) {
      let lang;
        if(this.current_language === 1) {
          lang = 'English'
        } else {
          lang = 'Arabic'
        }
      this.company_profiles_languages.push({
        'profile_language_id':this.current_language,
        'language': lang,
        'data': null
      });

      this.setcompanyLanguage(this.current_language);
      this.NewFormData(this.current_language,[]);
    }



}

delete_profile(langId){
  if(confirm("Are you sure you want to delete this profile?..All data will be removed!")) {
    this.companyFormService.deleteCompanyForm(this.companyId,langId).toPromise().then(
    (res) => {
      this.companyFormService.getProfiles(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {          
          if(res['translation'] !== undefined) {

            let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
            this.get_Data = send_Data_to_preview;

          } else if(res['data'].length === 0) {
            this.get_Data = [];
          }     
          this.buildFilledForm();         
        });
    })
  }
  
}

/*---- when the company choose another profile language so
       we must show a new tab with the language' name and
       we must take the translated data we got it on buildfield form
       to initilize the form again with the translated new data to make
       a new profile with a new language */

handlePopup($event){
  this.new_language_profile = [];
  let new_lang_id = $event['new'];
  this.tab_exist = true;
  for(let i = 0 ; i < this.company_profiles_translated.length; i++){
    if(this.company_profiles_translated[i].profile_language_id === new_lang_id) {

      this.new_language_profile.push({
        'profile_language_id': this.company_profiles_translated[i].profile_language_id,
        'language': this.company_profiles_translated[i].language,
        'data' :  this.company_profiles_translated[i].data
      });

      this.new_language = this.company_profiles_translated[i].profile_language_id;
      this.setcompanyLanguage(Number(this.company_profiles_translated[i].profile_language_id));
      this.NewFormData(Number(this.company_profiles_translated[i].profile_language_id), this.company_profiles_translated[i].data);
      
    }
  }

}

handleImageEditorPopup($event){
  if($event['editedImage'].file !== ''){
    this.image_code_to_send = $event['editedImage'];
    this.perPhotoSrc = this.image_code_to_send.file;
    this.uploadLabelDisplay = false;
  }
}

/* if the company move between tabs then we must consider that the company may have opened a new
   tab but did'nt save it so we must handle this */

changeProfile(data,langId) {

  if(this.new_language_profile) {
      if(langId === this.new_language_profile.profile_language_id) {  
          this.setcompanyLanguage(Number(this.new_language_profile.profile_language_id));
          this.NewFormData(Number(this.new_language_profile.profile_language_id),this.new_language_profile.data);
        
      } else {

        this.setcompanyLanguage(Number(langId));
        this.current_language = langId
        if(data !== null) {
          this.NewFormData(Number(langId),data);
        }

      }
  } else {
    this.setcompanyLanguage(Number(langId));
    this.current_language = langId
    if(data !== null) {
      this.NewFormData(langId,data);
    }
  }
    
  
}

/*---this function determine if the company was previously in the company-information-preview---*/

change() {
    this.companyFormService.currentedit.subscribe(edit => this.status = edit);
    this.edit_status = this.status;
    if (this.status) {
      this.companyFormService.changeStatus(!this.edit_status);
    }
}

/*--- delete tab when click the X button near the tab ---*/

delete_tab() {
  this.new_language_profile = [];
  this.tab_exist = false;
  this.buildFilledForm()
}


send_Data(sendD) {
  this.companyFormService.send_Data(sendD);
}

Display_save() {
  $('#companyedit').modal('toggle');
  // this.display_save_msg = true;
}

closeModal_save() {
  $('#companyedit').modal('hide');
  //this.display_save_msg = false;
  this.sendData2 = [];
}


Display_Profile_Languages() {
  this.display_Profile_Languages = true;
}

closeModal_Profile_Languages() {
  this.display_Profile_Languages = false;
}


ngOnInit() {
    /* console.log('form data1',this.get_Data)
    if(this.get_Data.length === 0) {
      this.subscription=this.companyFormService.Data.takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          console.log('form data res',res)
          this.get_Data = res;
        }
      )
      this.subscription.unsubscribe();
      console.log('form data2',this.get_Data)
    } */
    let haveProfile = localStorage.getItem('have_profile');
    
    if(haveProfile === '1'){
      this.companyFormService.Data.takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.get_Data = res;
          // in case user refreshed the page we should get profile data from backend
          if(this.get_Data.length === 0){
            this.getProfiles_route();
          }
        }
      );
    }

    // this.privateSharedURL.publicUrl.take(1).subscribe(data => {
    //   this.fileUrl = data;
    // });
    this.change();
    this.buildFilledForm();
}

getProfiles_route() {
  this.companyFormService.getProfiles(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
  (res) => {
    if(res['translation'] !== undefined) {
      this.get_Data = this.data_map.optimiziaion_Data(res);
      this.change();
      this.buildFilledForm();
    }
  });
}

get founded_dateControl() {
  return (this.companyForm.controls['founded_date'] as FormGroup);
}

get company_industries() {
  return this.companyForm.get('company_industries') as FormArray;
}

get company_specialities() {
  return this.companyForm.get('company_industries') as FormArray;
}

get company_social_medias() {
  return this.companyForm.get('company_social_medias') as FormArray;
}

get founded_Date() {
  return this.companyForm.get('founded_date') as FormGroup;
}



is_month() {
  let dateData = this.companyForm.value;
  if (dateData.founded_date.year && !dateData.founded_date.month) {
    this.companyForm.value.is_month = '' ;

  } else {
    if (dateData.founded_date.year && dateData.founded_date.month) {
      this.companyForm.value.is_month = '1' ;

    }
  }
}

convertToIdsArr(arrayOfObj) {
  let idsArray = [];
  for (let obj of arrayOfObj) {
    idsArray.push(obj.id);
  }
  return idsArray;
}

/*--- when we click save so we must consider if this is an Add a New Profile or Edit an Old one
      and this depending on the edit_status which detemine if firstly we were in
      the company-information-preview or company-form */
submit(form) {
   // form.submitted = false;
  /* let test = btoa(this.companyForm.value.logo) */
    if (this.companyForm.valid) {
      let sendData = this.companyForm.value;
  
      if(this.image_code_to_send) {
        sendData.logo = this.image_code_to_send
      } else {
        
        sendData.logo.file = ""
        sendData.logo.file_type = ""
        sendData.logo.is_deleted = false
      }
    
      this.is_month();
        sendData.company_id = this.companyId;
          // if (sendData.company_industries_temp.length === 0 || sendData.company_industries_temp === undefined) {
          //   this.companyForm.controls['company_industries_temp'].setValue("");
          // }
          // if (sendData.company_industries_temp.length) {
          //   sendData.company_industries = [];
          //   sendData.company_specialties = [];
          // for ( let i = 0 ; i < sendData.company_industries_temp.length; i++ ) {
          //   sendData.company_industries.push({
          //     'company_industry_id': sendData.company_industries_temp[i]
          //   }) ;
          // }

          // for ( let i = 0 ; i < sendData.company_specialities_temp.length; i++ ) {
          //   sendData.company_specialties.push({
          //     'specialty_id': sendData.company_specialities_temp[i]
          //   }) ;
          //   }
          // }

          sendData.industries = this.convertToIdsArr(sendData.industries);
          sendData.specialties = this.convertToIdsArr(sendData.specialties);

          if (sendData.company_social_links.length) {
            sendData.company_social_medias = [];
            for ( let i = 0 ; i < sendData.company_social_links.length; i++ ) {
              if (sendData.company_social_links[i].social_media_id !== "") {
              sendData.company_social_medias.push({
                'social_media_id': sendData.company_social_links[i].social_media_id,
                'company_social_media_info':sendData.company_social_links[i].url
              }) ;
                }
              }
          }

          if(sendData.company_size_id_temp) {
            sendData.company_size_id = sendData.company_size_id_temp.company_size_id;
          }

          if(sendData.company_type_id_temp) {
            sendData.company_type_id = sendData.company_type_id_temp.company_type_id;
          }

          if (this.new_language && this.new_language === this.current_language) {
            sendData.translated_languages_id = this.new_language;
          } else {
            sendData.translated_languages_id = this.current_language;
          }

         /*  localStorage.setItem('current_company_language',sendData.translated_languages_id); */
          this.sendData2 = sendData;

          if (this.companyProfileId !== null && sendData.translated_languages_id !== Number(this.new_language)) {
            
            if (this.edit_status) {
                this.sendData2['language_passed'] = Number(this.sendData2.translated_languages_id);
                this.Display_save();
                this.send_Data(this.sendData2);     
              } 
              else {
               
                this.companyFormService.editCompanyForm(sendData, this.companyProfileId).toPromise().then(
                  (res) =>  {
                    //company logo changed , so pass new company logo to navigation bar
                    if(sendData.logo.file !== "" && sendData.logo.is_deleted === false){
                      this.generalService.notify('profilePictureChanged' , 'companyProfile','navbar' , {'profile_picture':res['translation'][1].path_company_imagelogo});
                      localStorage.setItem("pic",res['translation'][1].path_company_imagelogo);
                    }
                    //company logo deleted , so remove company logo in navigation bar
                    else if(sendData.logo.file === "" && sendData.logo.is_deleted === true){
                      this.generalService.notify('profilePictureRemoved' , 'companyProfile','navbar' , {'profile_picture':"none"});
                      localStorage.setItem("pic","none");
                    }
                    localStorage.setItem("have_profile","1");
                    let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
                    this.companyFormService.send_Data(send_Data_to_preview);
                    this.router.navigate(['/c',this.username,'profile','preview'])
                  }
                );
              }
            
          } else if(sendData.translated_languages_id === Number(this.new_language) || this.companyProfileId === null) {
              
            this.companyFormService.addCompanyForm(sendData).toPromise().then(
              (res) =>  {
                //company added a logo , so pass company logo to navigation bar
                if(sendData.logo.file !== "" && sendData.logo.is_deleted === false){
                  this.generalService.notify('profilePictureChanged' , 'companyProfile','navbar' , {'profile_picture':res['translation'][1].path_company_imagelogo});
                  localStorage.setItem("pic",res['translation'][1].path_company_imagelogo);
                }
                localStorage.setItem("have_profile","1");
                let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
                send_Data_to_preview['language_passed'] = Number(this.sendData2.translated_languages_id);
                this.companyFormService.send_Data(send_Data_to_preview);
                this.router.navigate(['/c',this.username,'profile','preview']);
              }
      );
    }
    }
}

// onFileChanged(event: any) {
//   let files = event.target.files;
//   let result;

//   if(files[0].size > 1048576 || (files[0].type !== 'image/jpeg' && files[0].type !== 'image/png') ){
//     if(files[0].size > 1048576){
//       this.imgError = "validationMessages.imageSizeBig";
//     }
//     else if(files[0].type !== 'image/jpeg' && files[0].type !== 'image/png'){
//       this.imgError = "validationMessages.invalidImageType";
//     }
//   }

//  else{
//   this.imgError = null;   
//   const reader = new FileReader()
//   for(let file of files) {
//     this.uploadedFiles.push(file) ;
//   }
//   let file = this.uploadedFiles[0];
//   this.data_map.upload_file(file).then(
//     (res)=>{
//       result = res;
//       this.image_code_to_send = result;
//       this.uploadLabelDisplay = false;
//       this.uploadedFiles = [];      
//       this.cdRef.detectChanges();
//     });
  
//   reader.onload = () => {
//     this.perPhotoSrc = reader.result as string;      
//   }
//   reader.readAsDataURL(file)

//  }   
// }
deleteProfilePicture(){
  if(confirm(this.translate.instant("confirm.deleteLogo"))){     
    this.uploadLabelDisplay = true;
    this.perPhotoSrc = this.noProfilePicSet;
    this.image_code_to_send = {file:'',file_type:'',is_deleted:true};
    this.generalService.notify('image-deleted' , 'companyProfile','image-editor' , {});
  //  this.cdRef.detectChanges();

    // this.upload_img.delete_image_profile(this.personalInformationId).subscribe(res => {
    //   this.uploadLabelDisplay = true;
    //   this.perPhotoSrc = this.noProfilePicSet;
    // });
  }
 
  
}

// onUpload(event,fileUpload) {
//   this.uploadedFiles = []
//   let result;
//   if(event.files[0].size > 1000000) {

//     this.messageService.add({severity:'error', summary: 'Error Message', detail:'Image size bigger than 1.0 MB!'});
//     fileUpload.clear();

//   } else {
//     const reader = new FileReader()
//     for(let file of event.files) {
//       this.uploadedFiles.push(file) ;
//     }

//     let file = this.uploadedFiles[0];
//     this.data_map.upload_file(file).then(
//       (res)=>{
//         result = res;
//         this.image_code_to_send = result
     
//       })
    
//     reader.onload = () => {
//       this.image_uploaded_url = reader.result as string;
//     }
//     reader.readAsDataURL(file)
//     this.label_option = "Change Photo"
//     this.messageService.add({severity: 'info', summary: 'Image Uploaded Successfully!', detail: ''});
    
//     fileUpload.clear();
//   } 
   
// }

confirmPosition(position: string) {
  this.position = position;
  this.confirmationService.confirm({
      message: 'Do you want to delete this image?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      accept: () => {
        this.uploadedFiles = []
        this.image_uploaded_url = './assets/images/Capture.PNG'
        this.label_option = "Upload Photo"
        this.image_code_to_send =  { file: "", file_type: "", is_deleted:true}
          this.messageService.add({severity: 'info', summary: 'Confirmed', detail:'Image deleted'});
      },
      /* reject: () => {
          this.msgs = [{severity:'info', summary:'Rejected', detail:'You have rejected'}];
      }, */
      key: "positionDialog"
  });
}

remove_img() {
  this.messageService.add({severity: 'info', summary: 'Image Uploaded Successfully!', detail: ''});
  /* this.upload_img.upload_image_company(this.uploadedFiles,this.companyId).subscribe(
    (res) => {
      this.image_uploaded_url = './assets/images/Capture.PNG'
      this.label_option = "Upload Photo"
    }
  ) */
}

showApplyInfoDialog(){
  this.displayApplyInfoDialog = true;
}

ngOnDestroy() {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();

}
}

interface SocialMediaInterface {
  id: string;
  name: string;
}