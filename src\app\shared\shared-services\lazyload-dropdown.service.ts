import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class LazyloadDropdownService {

  url = '';
  constructor(private http:HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data=>{
      this.url = data ;
    });
  }

  getData(type,name,pgsize, pgnum,lang_id,additional_param?) {
    let params = new HttpParams();
    params = params.append('type', type);
    params = params.append('name', name);
    params = params.append('pgsize', pgsize);
    params = params.append('pgnum', pgnum);
    params = params.append('lang_id', lang_id);
    if(additional_param){
      params = params.append(additional_param.paramName, additional_param.paramValue);
    }

    return this.http.get(this.url +'data_list',{ params: params});
  }

  getNextPageData(next_page_url){
    return this.http.get(next_page_url);
  }


}
