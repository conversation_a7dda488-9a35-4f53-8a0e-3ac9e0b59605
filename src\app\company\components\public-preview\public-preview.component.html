<company-preview [companyId]="companyId" [sourceInterface]="'company-account-public-preview'">
    <ng-container class="profile-edit">
        <div class="edit-link">
            <!-- <a (click)="goToProfile()">
                <i class="fa fa-edit"></i>
            </a> -->
            <a (click)="edit_profile()">
                <i class="fa fa-edit"></i>
            </a>
        </div>
    </ng-container>
    <ng-container class="social-media-edit">
        <div class="edit-link">
            <!-- <a (click)="goToProfile()">
                <i class="fa fa-edit"></i>
            </a> -->
            <a (click)="edit_profile()">
                <i class="fa fa-edit"></i>
            </a>
        </div>
    </ng-container>
    <ng-container class="branches-edit">
        <div class="edit-link">
            <a [routerLink]="['/c',username,'profile','location']">
                <i class="fa fa-edit"></i>
            </a>
        </div>
    </ng-container>
</company-preview>

