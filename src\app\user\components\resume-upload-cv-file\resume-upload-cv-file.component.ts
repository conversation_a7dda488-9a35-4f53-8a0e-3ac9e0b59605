import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {  FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { UploadCvPdfService } from 'app/user/cv-services/upload-cv-pdf.service';
import { DataMap } from 'shared/Models/data_map';
declare var $: any;
@Component({
  selector: 'resume-upload-cv-file',
  templateUrl: './resume-upload-cv-file.component.html',
  styleUrls: ['./resume-upload-cv-file.component.css']
})
export class ResumeUploadCvFileComponent implements OnInit {

  @Input('resumeId') resumeId: any;
  @Input() languageId: number | null = 1;
  @Input('uploadedFile') uploadedFile: any;
  @Input('fromSteps') fromSteps: false;
  @Output() closeUploadCV = new EventEmitter();
  fileForm: FormGroup;
  username = '';

  //////File /////
  fileSrc: string = "./assets/images/Capture.PNG";
  // noProfilePicSet: string = "./assets/images/Capture.PNG";
  uploadedFiles: any[] = [];
  fileUploadError: string = null;
  fileName: string = 'myCv.pdf';
  file_code_to_send: { file: string, file_type: string, is_deleted: boolean, name: string } = { file: '', file_type: '', is_deleted: false, name: '' }
  uploadLabelDisplay = true;

  showLoader = false;
  ///// File //////
  data_map = new DataMap();
  analyzeByAI = false;
  constructor(private fb: FormBuilder,
    private translate: TranslateService,
    private uploadCvPdfService: UploadCvPdfService,
    private cdRef: ChangeDetectorRef,
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.translate.setDefaultLang('en');
    this.translate.use("en");
    this.initializeForms();
    this.setRoutingParams();
  }
  setRoutingParams() {
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
  }

  initializeForms() {
    this.fileForm = this.fb.group({
      resume_id: [this.resumeId ? this.resumeId : '', Validators.required],
      file: [null],
      analyzeByAI: [false]


    }, { validator: [] });
    ////  show fileName By fileName Uploaded Pdf File from Backend
    if (this.uploadedFile != null) {
      this.uploadLabelDisplay = false;
      this.fileName = this.uploadedFile;
    }
  }
  // Manual file validation logic
  validateFileField(file: any): string | null {
    if (!file) {
      return "Please upload a PDF file.";
    }
    if (file instanceof File) {
      if (file.size > 1048576) {
        return "File size must be less than 1MB.";
      }
      if (file.type !== 'application/pdf') {
        return "Only PDF files are allowed.";
      }
    }
    return null;
  }

  submitStepUploadCv() {
    console.log('submitStepUploadCv', this.fileForm);
    if (this.fileForm.valid) {
      let sendData = this.fileForm.value;
      sendData.file = this.file_code_to_send;
      // Include analyzeByAI in the data sent to the service
      sendData.analyzeByAI = this.analyzeByAI;

      this.showLoader = true;
      this.uploadCvPdfService.addStepData('upload_cv', sendData).subscribe(
        (res: any) => {
          this.showLoader = false;
          if (this.fromSteps) {
            this.router.navigate(['u/', this.username, 'resumes']);
          }
          else {
            this.closeUploadCV.emit({ 'new': res, 'old': res });
            $('#ResumeUploadCvFileModal').modal('hide');
          }
        },
        (error) => {
          this.showLoader = false;
          console.error('Error uploading CV:', error);
        }
      );
    }
  }

  nextPage() {
    if (this.fromSteps) {
      const file = this.fileForm.get('file').value;
      const errorMsg = this.validateFileField(file);
      if (errorMsg) {
        this.fileUploadError = errorMsg;
        this.fileForm.get('file').markAsTouched();
        return;
      } else {
        this.fileUploadError = null;
      }
      if (this.fileForm.valid) {
        let sendData = this.fileForm.value;
        sendData.file = this.file_code_to_send;
        // Include analyzeByAI in the data sent to the service
        sendData.analyze_by_ai = this.analyzeByAI;

        this.showLoader = true;
        this.uploadCvPdfService.addStepData('upload_cv', sendData).subscribe(
          (res: any) => {
            this.showLoader = false;
            // The response will include AI extraction data if analyzeByAI was true
            this.closeUploadCV.emit({
              'nextPage': true,
              'data': res,
              'ai': res['ai_data'] || null
            });
          },
          (error) => {
            this.showLoader = false;
            console.error('Error uploading CV:', error);
          }
        );
      }
    }
  }
  onFileChanged(event: any) {
    let files = event.target.files;
    if (!files || files.length === 0) {
      this.fileForm.get('file').setValue(null);
      this.fileUploadError = null;
      return;
    }
    const file = files[0];

    // Set file in form control, but do not validate here
    this.fileForm.get('file').setValue(file);
    this.fileForm.get('file').markAsTouched();
    this.fileForm.get('file').markAsDirty();
    this.fileUploadError = null;

    // If valid, proceed with upload logic
    const reader = new FileReader();
    this.uploadedFiles = [file];
    this.data_map.upload_file(file).then(
      (res: { file: string, file_type: string, is_deleted: boolean, name: string }) => {
        this.file_code_to_send = res;
        this.uploadLabelDisplay = false;
        this.uploadedFiles = [];
        this.cdRef.detectChanges();
      });

    reader.onload = () => {
      this.fileSrc = reader.result as string;
    }
    reader.readAsDataURL(file);
    this.fileName = file.name;
  }
  deleteUploadedFile() {
    if (confirm('Are you sure to delete The Uploaded File ?')) {
      this.uploadLabelDisplay = true;
      // this.fileSrc = this.noProfilePicSet;
      this.file_code_to_send = { file: '', file_type: '', is_deleted: true, name: '' }
      // Reset the file form control and clear validation error
      this.fileForm.get('file').setValue(null);
      this.fileForm.get('file').markAsPristine();
      this.fileForm.get('file').markAsUntouched();
      this.fileForm.get('file').updateValueAndValidity();
      this.fileUploadError = null;

      this.cdRef.detectChanges();
    }
  }
}
