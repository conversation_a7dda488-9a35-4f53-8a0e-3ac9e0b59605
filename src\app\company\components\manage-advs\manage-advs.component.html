<!-- Old manage advs component - currently not used -->

<!-- id="page-content-wrapper" -->
<div>
    <page-navbar [navType]="'manageAdvs'" (outData)="changeOption($event)"></page-navbar>
    <div class="page-content table-page-content">
        <div class="row">
            <div style=" position: absolute;left: 44.9%;
            top: 35%;
             min-height: 50vh; text-align: center;" class="preloader" *ngIf="no_adver">
                <div style=" position: relative;right: 25%;top: -40%;color: #186ba0;font-weight: bold;">
                    <span>You don't have any advertisement in this section!</span>
                </div>
            </div>
        </div>
        <div class="row">
            <div>
                <div style="margin:5%; margin-top: 20px;" *ngIf="status.value === 'publish'">

                    <p-table [loading]="showLoader" #dt [value]="data_Source" styleClass="ui-table-customers" [reorderableColumns]="true"
                        [rows]="4" [showCurrentPageReport]="true" [loading]="loading" [responsive]="true"
                        [filterDelay]="0" (click)="dragTable()"
                        [globalFilterFields]="['adv_id_by_company','job_advertisement_translation.0.job_adv_title','days','active_status', 'cv_read_count', 'cv_received_count', 'cv_rejected_count']"
                        [rowHover]="true">
                        <ng-template pTemplate="caption">
                            Total : {{totalRecords}}
                            <div class="ui-table-globalfilter-container">
                                <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Page Search" />
                            </div>
                        </ng-template>
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 3%;"></th>
                                <th style="width:3%;" pReorderableColumn><span translate></span></th>
                                <th style="width:5%;" pReorderableColumn><span translate></span></th>
                                <th style="width:6%;" pReorderableColumn pSortableColumn="adv_id_by_company" translate>
                                    manage_advs.id
                                    <p-sortIcon field="adv_id_by_company"></p-sortIcon>
                                </th>
                                <th style="width:25%;" pReorderableColumn
                                    pSortableColumn='job_advertisement_translation.0.job_adv_title' translate>
                                    manage_advs.title
                                    <p-sortIcon field='job_advertisement_translation.0.job_adv_title'></p-sortIcon>
                                </th>
                                <th style="width:11%;" pReorderableColumn pSortableColumn="days" translate>
                                    manage_advs.days
                                    <p-sortIcon field="days"></p-sortIcon>
                                </th>
                                <th style="width:12%;" pReorderableColumn pSortableColumn="status" translate>
                                    manage_advs.status
                                    <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th style="width:15%;" pReorderableColumn pSortableColumn="manage_advs.statistics"
                                    translate>manage_advs.statistics
                                    <p-sortIcon field="manage_advs.statistics"></p-sortIcon>


                                </th>
                                <th style="width:9%;" pReorderableColumn><span translate>manage_advs.actions</span>
                                </th>
                            </tr>
                            <tr>
                                <th></th>

                                <th></th>
                                <th>
                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                                </th>
                                <th>
                                    <!--<input pInputText type="text"
                                        (input)="dt.filter($event.target.value, 'adv_id_by_company', 'startsWith')"
                                        placeholder="Search" class="ui-column-filter"
                                        style="border: 1px solid #c5c5c5; border-radius: 2px;">-->
                                </th>
                                <th>
                                    <input pInputText type="text"
                                        (input)="dt.filter($event.target.value, 'job_advertisement_translation.0.job_adv_title', 'contains')"
                                        placeholder="Search" class="ui-column-filter"
                                        style="border: 1px solid #c5c5c5; border-radius: 2px; width:100% !important">
                                </th>

                                <!-- <th>
                                <p-multiSelect [options]="representatives" placeholder="All" (onChange)="onRepresentativeChange($event)" styleClass="ui-column-filter" optionLabel="name">
                                    <ng-template let-option pTemplate="item">
                                        <div class="ui-multiselect-representative-option">
                                            <img [alt]="option.label" src="assets/showcase/images/demo/avatar/{{option.value.image}}" width="32" />
                                            <span>{{option.label}}</span>
                                        </div>
                                    </ng-template>
                                </p-multiSelect>
                            </th>-->
                                <th>
                                    <input pInputText type="text"
                                        (input)="dt.filter($event.target.value, 'days', 'contains')"
                                        class="ui-column-filter" style="border: 1px solid #c5c5c5; border-radius: 2px;"
                                        placeholder="Search">
                                </th>
                                <th>

                                </th>
                                <th>
                                    <div>
                                        <span class="pi pi-eye" pTooltip="Seen"></span>
                                        <span style="padding-left:11px" class="pi pi-ban"
                                            pTooltip="Auto rejected"></span>
                                        <span style="padding-left:11px" class="pi pi-envelope"
                                            pTooltip="delivered"></span>
                                    </div>
                                </th>
                                <th></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns"
                            let-index="rowIndex">
                            <tr class="ui-selectable-row" id="{{ data.order }}" [pReorderableRow]="index">
                                <td>
                                    <i class="fa fa-arrows" aria-hidden="true"
                                        style="display: flex;justify-content: center;" pReorderableRowHandle></i>

                                </td>

                                <td>
                                    <i class="pi pi-info-circle" (click)="displayAdvrLogModal(data.id)" pTooltip="Log"
                                        style="font-size: 24px; color:#30457c;"></i>
                                </td>
                                <td>
                                    <p-tableCheckbox [value]="data"></p-tableCheckbox>
                                </td>
                                <td>
                                    <span class="ui-column-title">ID</span>

                                    <span class="fa fa-address-card card" pTooltip="Browse CVs"
                                        (click)="move_to_received_cvs(data.folder_id)" style="font-size:18px;"></span>
                                    <span> {{ data.adv_id_by_company }}</span>

                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Title </span>
                                    <span style="font-size:1.1em; font-weight: bold; color:#30457c"
                                        *ngIf="data.job_advertisement_translation.length">
                                        {{ data.job_advertisement_translation[0].job_adv_title}}

                                    </span>
                                    <br>
                                    <span style="color:#999" pTooltip="published {{published_dates[index].date}}">
                                        {{published_dates[index].date}}
                                    </span>
                                    <span *ngIf="!data.job_title_synonyms">
                                        -
                                    </span>

                                </td>
                                <td (click)="displayAdvrModal(data.id)">

                                    <span class="ui-column-title">Days</span>

                                    <div class="tooltip">
                                        <span class="pi pi-refresh"
                                            style="cursor: pointer; font-size:24px; color: gold; font-weight: bold;"
                                            *ngIf="data.days <= 5 && data.renew_count < 3 && data.active_status"
                                            (click)="renew(data.id)">
                                        </span>
                                        <span class="tooltiptext">Renew</span>
                                    </div>

                                    <span style="font-weight: bold; color: black;"
                                        *ngIf="data.active_status else notActive">
                                        {{ data.days }}
                                    </span>
                                    <ng-template #notActive>
                                        <span>
                                            -
                                        </span>
                                    </ng-template>

                                </td>
                                <td>
                                    <span class="ui-column-title">Status</span>

                                    <span class="customer-badge status-unqualified" style="cursor: pointer;"
                                        *ngIf="!data.active_status"
                                        (click)="activate(data.id,data.active_status,data.adv_id_by_company)">
                                        <span translate>
                                            manage_advs.deactive
                                        </span>
                                    </span>
                                    <span class="customer-badge status-qualified" style="cursor: pointer;"
                                        *ngIf="data.active_status"
                                        (click)="activate(data.id,data.active_status,data.adv_id_by_company)">
                                        <span translate>
                                            manage_advs.active

                                        </span>
                                    </span>
                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Statistics</span>

                                    <span style="padding-left:6px; color:#757373">{{ data.cv_read_count }}</span>

                                    <span style="padding-left:22px; color:darkgrey">{{ data.cv_rejected_count }}</span>

                                    <span style="padding-left:22px; color:#4CAF50">{{ data.cv_received_count }}</span>
                                </td>
                                <td class="last">
                                    <span class="ui-column-title">Actions </span>

                                    <div class="tooltip">
                                        <i style="font-size: 20px; background-color: teal; color: white; border-radius: 50%;"
                                            class="pi pi-undo" aria-hidden="true" (click)="refreshListing(data.id)"></i>
                                        <span style="width:90px" class="tooltiptext">Refresh Listing</span>
                                    </div>
                                    &nbsp;
                                    <div class="dropdown">
                                        <i style="font-size: 20px;" class="pi pi-list" aria-hidden="true"></i>
                                        <div class="dropdown-content">
                                            <a (click)="activate(data.id,data.active_status)" style="cursor:pointer"><i
                                                    class="fa fa-power-off" style="font-size: 20px; color: #4CAF50;"
                                                    aria-hidden="true"></i><span translate>
                                                    manage_advs.activate_deactivate</span></a>
                                            <a (click)="edit_advr(data.id,data.job_advertisement_translation[0].translated_languages_id)"
                                                style="cursor:pointer"><i class="fa fa-edit"
                                                    style="font-size: 20px; color: #30457c;"
                                                    aria-hidden="true"></i><span translate> manage_advs.edit</span></a>
                                            <a (click)="endWorkFlow(data.id,data.adv_id_by_company)"
                                                style="cursor:pointer"><i class="fa fa-times-circle"
                                                    style="font-size: 20px; color: #C63720" aria-hidden="true"></i><span
                                                    translate> manage_advs.end_work_flow</span> </a>
                                            <a (click)="saveAsTemplate(data.id,data.has_template)"
                                                style="cursor:pointer"><i class="fa fa-save"
                                                    style="font-size: 20px; color: black" aria-hidden="true"> </i><span
                                                    translate> advr_preview.save_as_template</span></a>
                                        </div>
                                    </div>
                                </td>

                            </tr>
                        </ng-template>

                        <!--  <div class="col-sm-12 col-xs-5 col-xs-offset-5" style="padding: 5px;">
                            <pagination-template #p="paginationApi" [id]="config.id" (pageChange)="pageChange($event)">
                                <div class="ui-paginator">
                                    <div class="pagination-previous" [class.disabled]="p.isFirstPage()">
                                        <span style="cursor: pointer" *ngIf="!p.isFirstPage()" (click)="p.previous()">

                                    </span>
                                    </div>
                                    <div class="page-number" *ngFor="let page of p.pages" [class.current]="p.getCurrent() === page.value">
                                        <span (click)="p.setCurrent(page.value)" *ngIf="p.getCurrent() !== page.value">{{ page.label }}</span>
                                        <div *ngIf="p.getCurrent() === page.value">
                                            <span>{{ page.label }}</span>
                                        </div>
                                    </div>
                                    <div class="pagination-next" [class.disabled]="p.isLastPage()">
                                        <span style="cursor: pointer" *ngIf="!p.isLastPage()" (click)="p.next()"> </span>
                                    </div>
                                </div>

                            </pagination-template>
                        </div> -->

                    </p-table>
                    <p-paginator [rows]="start" showCurrentPageReport="true" [totalRecords]="totalRecords"
                        (onPageChange)="paginate($event)">
                    </p-paginator>
                </div>
                <div style="margin:5%; margin-top: 20px;" *ngIf="status.value === 'expired'">
                    <p-table [loading]="showLoader" #dt [value]="data_Source" styleClass="ui-table-customers" [reorderableColumns]="true"
                        [rows]="4" [showCurrentPageReport]="true" [loading]="loading" [responsive]="true"
                        [filterDelay]="0"
                        [globalFilterFields]="['adv_id_by_company','job_advertisement_translation.0.job_adv_title','days','active_status', 'cv_read_count', 'cv_received_count', 'cv_rejected_count']"
                        [rowHover]="true">
                        <ng-template pTemplate="caption">

                            Total : {{totalRecords}}
                            <div class="ui-table-globalfilter-container">

                                <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Page Search" />
                            </div>
                        </ng-template>
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width:8%;"><span translate></span></th>
                                <th style="width:7%;"><span translate></span></th>
                                <th style="width:6%;" pSortableColumn="adv_id_by_company" translate> manage_advs.id
                                    <!--<p-sortIcon field="adv_id_by_company"></p-sortIcon>-->
                                </th>
                                <th style="width:25%;" pSortableColumn='job_advertisement_translation.0.job_adv_title'
                                    translate> manage_advs.title
                                    <p-sortIcon field='job_advertisement_translation.0.job_adv_title'></p-sortIcon>
                                </th>
                                <th style="width:13%;" pSortableColumn="expires_at" translate>
                                    manage_advs.expiration_date
                                    <p-sortIcon field="expires_at"></p-sortIcon>
                                </th>
                                <th style="width:15%;" pSortableColumn="manage_advs.statistics" translate>
                                    manage_advs.statistics
                                    <p-sortIcon field="manage_advs.statistics"></p-sortIcon>
                                </th>
                                <th style="width:11%;"><span translate>manage_advs.actions</span></th>
                            </tr>
                            <tr>
                                <th>
                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                                </th>
                                <th></th>
                                <th>
                                    <!--<input pInputText type="text" style="border: 1px solid #c5c5c5; border: raduis 2px;"
                                        (input)="dt.filter($event.target.value, 'adv_id_by_company', 'startsWith')"
                                        placeholder="Search" class="ui-column-filter">-->
                                </th>
                                <th>
                                    <input pInputText type="text"
                                        style="width:90%;border: 1px solid #c5c5c5; border: raduis 2px;"
                                        (input)="dt.filter($event.target.value, 'job_advertisement_translation.0.job_adv_title', 'contains')"
                                        placeholder="Search" class="ui-column-filter">
                                </th>

                                <!-- <th>
                            <p-multiSelect [options]="representatives" placeholder="All" (onChange)="onRepresentativeChange($event)" styleClass="ui-column-filter" optionLabel="name">
                                <ng-template let-option pTemplate="item">
                                    <div class="ui-multiselect-representative-option">
                                        <img [alt]="option.label" src="assets/showcase/images/demo/avatar/{{option.value.image}}" width="32" />
                                        <span>{{option.label}}</span>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                        </th>-->
                                <th>
                                    <input pInputText type="text"
                                        style="width:90%;border: 1px solid #c5c5c5; border: raduis 2px;"
                                        (input)="dt.filter($event.target.value, 'expires_at', 'contains')"
                                        class="ui-column-filter" placeholder="Search">
                                </th>
                                <th>
                                    <div>
                                        <span class="pi pi-eye" pTooltip="Log"></span>
                                        <span style="padding-left:11px" class="pi pi-ban" pTooltip="Log"></span>
                                        <span style="padding-left:11px" class="pi pi-envelope" pTooltip="Log"></span>
                                    </div>
                                </th>
                                <th>

                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns"
                            let-index="rowIndex">
                            <tr class="ui-selectable-row" id=" {{ data.order }}" [pReorderableRow]="index">
                                <td>
                                    <p-tableCheckbox [value]="data"></p-tableCheckbox>
                                </td>
                                <td>
                                    <i class="pi pi-info-circle" (click)="displayAdvrLogModal(data.id)"
                                        style="font-size: 24px; color:#30457c;"></i>
                                </td>
                                <td>
                                    <span class="ui-column-title">ID</span>

                                    <span class="fa fa-address-card card" style="font-size:18px;"></span>
                                    <span> {{ data.adv_id_by_company }}</span>

                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Title </span>
                                    <span style="font-size:1.1em; font-weight: bold; color:#30457c"
                                        *ngIf="data.job_advertisement_translation.length">
                                        {{ data.job_advertisement_translation[0].job_adv_title }}
                                    </span>

                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title"> Expiration Date
                                    </span>
                                    <span style="font-weight: bold; color: black;">
                                        {{data.expiration_date}}
                                    </span>
                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Statistics</span>

                                    <span style="padding-left:6px; color:#757373">{{ data.cv_read_count }}</span>

                                    <span style="padding-left:22px; color:darkgrey">{{ data.cv_rejected_count }}</span>

                                    <span style="padding-left:22px; color:#4CAF50">{{ data.cv_received_count }}</span>
                                </td>
                                <td class="last">
                                    <span class="ui-column-title">Actions</span>

                                    <div class="tooltip">

                                        <span class="fa fa-refresh"
                                            style="cursor: pointer; font-size:18px; color: gold; font-weight: bold;"
                                            *ngIf="data.renew_count < 3" (click)="renew(data.id)">
                                        </span>
                                        <span class="tooltiptext">Renew</span>
                                    </div>
                                    &nbsp;

                                    <div class="dropdown">
                                        <i style="font-size: 20px;" class="pi pi-list" aria-hidden="true"></i>
                                        <div class="dropdown-content">
                                            <!-- <a (click)="deleteAdvr(data.id)" style="cursor:pointer"><i class="fa fa-trash" aria-hidden="true" style="font-size: 20px; color: red;" ></i><span  translate> manage_advs.delete</span></a> -->
                                            <a (click)="saveAsTemplate(data.id,data.has_template)"
                                                style="cursor:pointer; "><i class="fa fa-save" aria-hidden="true"
                                                    style="font-size: 20px; color: black"></i><span translate>
                                                    advr_preview.save_as_template</span></a>
                                        </div>
                                    </div>
                                    &nbsp;
                                    <!-- <svg width="20px" fill="gray" viewBox="0 0 24 24" style="cursor: grap;">
                                    <path d="M10 9h4V6h3l-5-5-5 5h3v3zm-1 1H6V7l-5 5 5 5v-3h3v-4zm14 2l-5-5v3h-3v4h3v3l5-5zm-9 3h-4v3H7l5 5 5-5h-3v-3z"></path>
                                    <path d="M0 0h24v24H0z" fill="none"></path>
                                  </svg> -->
                                </td>

                            </tr>
                        </ng-template>
                    </p-table>
                    <!-- <pagination-controls (pageChange)="pageChanged($event)"></pagination-controls> -->
                    <p-paginator [rows]="start" showCurrentPageReport="true" [totalRecords]="totalRecords"
                        (onPageChange)="paginate($event)">
                    </p-paginator>
                </div>
                <div style="margin:5%; margin-top: 20px;" *ngIf="status.value === 'draft'">
                    <p-table [loading]="showLoader" #dt [value]="data_Source" dataKey="id" styleClass="ui-table-customers"
                        [reorderableColumns]="true" [rows]="4" [showCurrentPageReport]="true" [loading]="loading"
                        [responsive]="true" [filterDelay]="0"
                        [globalFilterFields]="['adv_id_by_company','job_advertisement_translation.0.job_adv_title']"
                        [rowHover]="true">
                        <ng-template pTemplate="caption">
                            Total : {{totalRecords}}
                            <div class="ui-table-globalfilter-container">
                                <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Page Search" />
                            </div>
                        </ng-template>
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width:8%;"><span translate></span></th>
                                <th style="width:7%;"><span translate></span></th>
                                <th style="width:6%;" pSortableColumn="adv_id_by_company" translate> manage_advs.id
                                    <p-sortIcon field="adv_id_by_company"></p-sortIcon>
                                </th>
                                <th style="width:25%;" pSortableColumn="job_advertisement_translation.0.job_adv_title"
                                    translate> manage_advs.title
                                    <p-sortIcon field="job_advertisement_translation.0.job_adv_titlee"></p-sortIcon>
                                </th>
                                <th style="width:13%;" pSortableColumn="created_at" translate>manage_advs.create_date
                                    <p-sortIcon field="created_at"></p-sortIcon>
                                </th>
                                <th style="width:15%;" pSortableColumn="updated_at" translate>manage_advs.last_update
                                    <p-sortIcon field="updated_at"></p-sortIcon>
                                </th>
                                <th style="width:11%;"><span translate>manage_advs.actions</span></th>
                            </tr>
                            <tr>
                                <th>
                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                                </th>
                                <th></th>
                                <th>
                                    <!--<input pInputText type="text"
                                        style="width:90%;border: 1px solid #c5c5c5; border: raduis 2px;"
                                        (input)="dt.filter($event.target.value, 'adv_id_by_company', 'startsWith')"
                                        placeholder="Search" class="ui-column-filter">-->
                                </th>
                                <th>
                                    <input pInputText type="text"
                                        style="width:90%; border: 1px solid #c5c5c5; border: raduis 2px;"
                                        (input)="dt.filter($event.target.value, 'job_advertisement_translation.0.job_adv_title', 'contains')"
                                        placeholder="Search" class="ui-column-filter">
                                </th>

                                <!-- <th>
                                    <p-multiSelect [options]="representatives" placeholder="All" (onChange)="onRepresentativeChange($event)" styleClass="ui-column-filter" optionLabel="name">
                                        <ng-template let-option pTemplate="item">
                                            <div class="ui-multiselect-representative-option">
                                                <img [alt]="option.label" src="assets/showcase/images/demo/avatar/{{option.value.image}}" width="32" />
                                                <span>{{option.label}}</span>
                                            </div>
                                        </ng-template>
                                    </p-multiSelect>
                                </th> (onSelect)="dt.filter($event.target.value.slice(0,10), 'created_at', 'contains')"-->
                                <th>
                                    <p-calendar styleClass="calendar" [(ngModel)]="createdDates" selectionMode="range"
                                        dateFormat="yy-mm-dd"
                                        (onSelect)="dt.filter($event, 'created_at', 'betweenCreatedDates')"
                                        placeholder="Search" showButtonBar="true" (onClearClick)="Clear($event)"
                                        [readonlyInput]="true">
                                    </p-calendar>
                                    <!--   id = "createdDates"
                                    -->
                                </th>
                                <th>
                                    <p-calendar styleClass="calendar" [(ngModel)]="updatedDates" selectionMode="range"
                                        dateFormat="yy-mm-dd" showButtonBar="true"
                                        (onSelect)="dt.filter($event, 'updated_at', 'betweenUpdatedDates')"
                                        placeholder="Search" (onClearClick)="Clear($event)" id="calendarUpdatedDates"
                                        [readonlyInput]="true">

                                        <!--   <p-button label="clear Dates" (click) = "Clear($event)"></p-button> -->
                                    </p-calendar>

                                </th>
                                <th>

                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns"
                            let-index="rowIndex">
                            <tr class="ui-selectable-row" id=" {{ data.order }}" [pReorderableRow]="index">
                                <td>
                                    <p-tableCheckbox [value]="data"></p-tableCheckbox>
                                </td>
                                <td>
                                    <i class="pi pi-info-circle" (click)="displayAdvrLogModal(data.id)"
                                        style="font-size: 24px; color:#30457c;"></i>
                                </td>
                                <td>
                                    <span class="ui-column-title">Id </span>
                                    <span class="fa fa-address-card card" style="font-size:18px;"></span>

                                    <span>&nbsp;{{ data.adv_id_by_company }}</span>

                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Title </span>
                                    <span style="font-size:1.1em; font-weight: bold; color:#30457c"
                                        *ngIf="data.job_advertisement_translation.length">
                                        {{ data.job_advertisement_translation[0].job_adv_title }}
                                    </span>
                                    <span *ngIf="!data.job_title_synonyms">
                                        -
                                    </span>
                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Create Date</span>
                                    <span style="font-weight: bold; color: black;">
                                        {{ data.created_at }}
                                    </span>
                                    <!-- <span *ngIf="!data.expires_at">
                                        -
                                    </span> -->
                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Update Date </span>
                                    <span style="font-weight: bold; color: black;">
                                        {{ data.updated_at }}
                                    </span>
                                </td>
                                <td class="last">
                                    <span class="ui-column-title">Actions </span>
                                    <div class="tooltip">
                                        <span
                                            (click)="edit_advr(data.id,data.job_advertisement_translation[0].translated_languages_id)"
                                            class="fa fa-edit" style="cursor:pointer; font-size: 20px; color:#30457c;"
                                            aria-hidden="true">
                                        </span>
                                        <span class="tooltiptext">Edit</span>
                                    </div>
                                    &nbsp;

                                    <div class="dropdown">
                                        <i style="font-size: 20px;" class="pi pi-list" aria-hidden="true"></i>
                                        <div class="dropdown-content">

                                            <a (click)="saveAsPublish(data.id,data.adv_id_by_company)"
                                                style="cursor:pointer;"><i class="fa fa-external-link"
                                                    aria-hidden="true" style="font-size: 20px; color: #4CAF50"></i><span
                                                    translate> advr_preview.publish</span></a>
                                            <a (click)="deleteAdvr(data.id,data.adv_id_by_company)"
                                                style="cursor:pointer"><i class="fa fa-trash"
                                                    style="font-size:18px; color:red; font-weight: bold;"
                                                    aria-hidden="true"></i><span translate>
                                                    manage_advs.delete</span></a>
                                            <a (click)="saveAsTemplate(data.id)" style="cursor:pointer;"><i
                                                    class="fa fa-save" aria-hidden="true"
                                                    style="font-size: 20px; color: black"></i><span translate>
                                                    advr_preview.save_as_template</span></a>
                                        </div>
                                    </div>
                                    &nbsp;
                                    <!--  <svg width="20px" fill="gray" viewBox="0 0 24 24" style="cursor: grap;">
                                        <path d="M10 9h4V6h3l-5-5-5 5h3v3zm-1 1H6V7l-5 5 5 5v-3h3v-4zm14 2l-5-5v3h-3v4h3v3l5-5zm-9 3h-4v3H7l5 5 5-5h-3v-3z"></path>
                                        <path d="M0 0h24v24H0z" fill="none"></path>
                                      </svg> -->
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <!-- <pagination-controls (pageChange)="pageChanged($event)"></pagination-controls> -->
                    <p-paginator [rows]="start" showCurrentPageReport="true" [totalRecords]="totalRecords"
                        (onPageChange)="paginate($event)">
                    </p-paginator>
                </div>
                <div style="margin:5%; margin-top: 20px;" *ngIf="status.value === 'template'">
                    <p-table [loading]="showLoader" #dt [value]="data_Source" dataKey="id" styleClass="ui-table-customers"
                        [reorderableColumns]="true" [rows]="50" [showCurrentPageReport]="true" [loading]="loading"
                        [responsive]="true" [filterDelay]="0"
                        [globalFilterFields]="['adv_template_id_by_company','job_advertisement_template_trans.0.job_adv_title','days','active_status', 'manage_advs.statistics']"
                        [rowHover]="true">
                        <ng-template pTemplate="caption">
                            Total : {{totalRecords}}
                            <div class="ui-table-globalfilter-container">
                                <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Page Search" />
                            </div>
                        </ng-template>
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width:8%;"><span translate></span></th>
                                <th style="width:7%;"><span translate></span></th>
                                <th style="width:6%;" pSortableColumn="adv_template_id_by_company" translate>
                                    manage_advs.id
                                    <p-sortIcon field="adv_template_id_by_company"></p-sortIcon>
                                </th>
                                <th style="width:25%;"
                                    pSortableColumn="job_advertisement_template_trans.0.job_adv_title" translate>
                                    manage_advs.title
                                    <p-sortIcon field="job_advertisement_template_trans.0.job_adv_title"></p-sortIcon>
                                </th>
                                <th style="width:18%;" pSortableColumn="manage_advs.created_at" translate>
                                    manage_advs.last_update
                                    <p-sortIcon field="manage_advs.created_at"></p-sortIcon>
                                </th>
                                <th style="width:11%;"><span translate>manage_advs.actions</span></th>
                            </tr>
                            <tr>
                                <th>
                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                                </th>
                                <th></th>
                                <th>
                                    <!--<input pInputText type="text"
                                        style="border: 1px solid #c5c5c5; border: raduis 2px; width: 100% !important;"
                                        (input)="dt.filter($event.target.value, 'adv_template_id_by_company', 'startsWith')"
                                        placeholder="Search" class="ui-column-filter">-->
                                </th>
                                <th>
                                    <input pInputText type="text"
                                        style="width:90%; border: 1px solid #c5c5c5; border: raduis 2px;" (input)="dt.filter($event.target.value,
                                'job_advertisement_template_trans.0.job_adv_title', 'contains')" placeholder="Search"
                                        class="ui-column-filter">
                                </th>

                                <!-- <th>
                                <p-multiSelect [options]="representatives" placeholder="All" (onChange)="onRepresentativeChange($event)" styleClass="ui-column-filter" optionLabel="name">
                                    <ng-template let-option pTemplate="item">
                                        <div class="ui-multiselect-representative-option">
                                            <img [alt]="option.label" src="assets/showcase/images/demo/avatar/{{option.value.image}}" width="32" />
                                            <span>{{option.label}}</span>
                                        </div>
                                    </ng-template>
                                </p-multiSelect>
                            </th>-->
                                <th>
                                    <!--  <input pInputText type="date" (input)="dt.filter($event.target.value, 'created_at', 'contains')" class="ui-column-filter" placeholder="Search"> -->
                                    <p-calendar styleClass="calendar" [(ngModel)]="templatesDates" selectionMode="range"
                                        dateFormat="yy-mm-dd"
                                        (onSelect)="dt.filter($event, 'created_at', 'betweenTemplateDates')"
                                        placeholder="Search" showButtonBar="true" (onClearClick)="Clear($event)"
                                        [readonlyInput]="true">
                                    </p-calendar>
                                </th>
                                <th>

                                </th>

                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns"
                            let-index="rowIndex">
                            <tr class="ui-selectable-row" id=" {{ data.order }}" [pReorderableRow]="index">
                                <td>
                                    <p-tableCheckbox [value]="data"></p-tableCheckbox>
                                </td>
                                <td>
                                    <i class="pi pi-info-circle" (click)="displayAdvrLogModal(data.id)"
                                        style="font-size: 24px; color:#30457c;"></i>
                                </td>
                                <td>
                                    <span class="ui-column-title">Id </span>
                                    <!-- <span class=" pi pi-id-card card" style="font-size:24px;"></span> -->
                                    <span class="fa fa-address-card card" style="font-size:18px;"></span>
                                    <span>&nbsp;{{ data.adv_template_id_by_company }}</span>

                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Title </span>
                                    <span style="font-size:1.1em; font-weight: bold; color:#30457c"
                                        *ngIf="data.job_advertisement_template_trans">
                                        {{ data.job_advertisement_template_trans[0].job_adv_title }}

                                    </span>
                                    <span *ngIf="!data.job_title_synonyms">
                                        -
                                    </span>
                                </td>
                                <td (click)="displayAdvrModal(data.id)">
                                    <span class="ui-column-title">Last Update </span>
                                    <span style="font-weight: bold; color: black;">
                                        {{ data.updated_at }}
                                    </span>
                                </td>
                                <td class="last">
                                    <span class="ui-column-title">Actions </span>
                                    <div class="tooltip">

                                        <span class="fa fa-external-link"
                                            style="cursor: pointer; font-size:18px; color: #4CAF50; font-weight: bold;"
                                            (click)="saveTemplateAsPublish(data.id,data.adv_template_id_by_company)">
                                        </span>
                                        <span class="tooltiptext">Publish</span>
                                    </div>
                                    &nbsp;
                                    <div class="dropdown">
                                        <i style="font-size: 20px;" class="pi pi-list" aria-hidden="true"></i>
                                        <div class="dropdown-content">
                                            <a (click)="edit_advr_temp(data.id,data.job_advertisement_template_trans[0].translated_languages_id)"
                                                style="cursor:pointer"><i class="fa fa-edit"
                                                    style="font-size: 20px; color:#30457c;" aria-hidden="true"></i><span
                                                    translate>manage_advs.edit</span></a>
                                            <a (click)="delete_temp(data.id,data.adv_template_id_by_company)"
                                                style="cursor:pointer"><i class="fa fa-trash" aria-hidden="true"
                                                    style="font-size: 20px; color: red;"></i><span translate>
                                                    manage_advs.delete</span></a>

                                        </div>
                                    </div>
                                    &nbsp;
                                    <!-- <svg width="20px" fill="gray" viewBox="0 0 24 24" style="cursor: grap;">
                                    <path d="M10 9h4V6h3l-5-5-5 5h3v3zm-1 1H6V7l-5 5 5 5v-3h3v-4zm14 2l-5-5v3h-3v4h3v3l5-5zm-9 3h-4v3H7l5 5 5-5h-3v-3z"></path>
                                    <path d="M0 0h24v24H0z" fill="none"></path>
                                  </svg> -->
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <p-paginator [rows]="start" showCurrentPageReport="true" [totalRecords]="totalRecords"
                        (onPageChange)="paginate($event)">
                    </p-paginator>
                </div>
            </div>
        </div>
    </div>
    <!--<div class="row" [formGroup]="available_langs" *ngIf="data_Source.length != 0 && available_languages_temp.length !=0">
        <div class="col-sm-5 col-sm-offset-3 focus-no-padding validate-input" [ngClass]="{'has-val':available_langs.controls['languages'].value.id}">
            <p-dropdown [options]="available_languages_temp" optionLabel="name" formControlName="languages" (onChange)="onChange($event)" [filter]="true" [ngClass]="{'has-val':available_langs.controls['languages'].value.id}">
            </p-dropdown>
            <span class="custom-underline"></span>
            <label *ngIf="status.value === 'publish' " class="control-label custom-control-label " translate>manage_advs.choose_language</label>
            <label *ngIf="status.value !== 'publish' " class="control-label custom-control-label " translate>manage_advs.choose_language_others<span> {{ choice.label }} </span></label>
        </div>
    </div>-->

</div>

<div class="modal fade" id="AdvrsModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal3Label" translate>Advertisement Preview</h4>
            </div>
            <app-advr-preview (closeModalPopup)="handlePopup($event)">

            </app-advr-preview>
        </div>
    </div>
</div>

<div class="modal fade" id="AdvrsLogModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal3Label" translate>Advertisement Log</h4>
            </div>
            <app-advr-log (closeModalPopup)="handlePopup($event)">

            </app-advr-log>
        </div>
    </div>
</div>
