import { NewValueService } from './../../../services/new-value.service';
import { LanguageService } from './../../../services/language.service';
import { FormBuilder, FormControl, FormGroup, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit, Input, ViewChild, ElementRef, AfterViewInit, OnDestroy, Output, EventEmitter, HostListener, OnChanges } from '@angular/core';
import { Validators } from '@angular/forms';
import { Language } from 'app/admin/models/language';
import { Subject } from 'rxjs/Subject';
import { Place } from 'shared/Models/place';
import { DataMap } from 'shared/Models/data_map';
import { MapToFormService } from 'app/user/cv-services/map-to-form.service';
import { BehaviorSubject, pipe, Subscription } from 'rxjs';
import { MessageService, ConfirmationService } from 'primeng/api';
import * as XLSX from 'xlsx';
import { UrlValidator } from 'shared/validators/url.validators';
@Component({
  selector: 'app-new-value-modal',
  templateUrl: './new-value-modal.component.html',
  styleUrls: ['./new-value-modal.component.css']
})
export class NewValueModalComponent implements OnInit, OnDestroy, AfterViewInit{
@Input()languagesArray: Language[] = [];
@Input()type: string;
@Input()openedInAModal = false;
@Input()item: any;
@Input()locationsI: { 'country': string, 'city': string, 'country_code': string,
                      'street_address': string, 'postal_code': string, 'latitude': number, 'longitude': number}[] = [];
@Output()closeModal = new EventEmitter();
@Output()parentAdded = new EventEmitter();
@Output()closeLocationModal = new EventEmitter();
// @Output()closeEditModal = new EventEmitter();
itemForm : any;
@Input()mode = 'create';
@Input()experienceFields = [];
@Input()majors = [];
@Input()compInParents = [];
@Input()skillCats = [];
@Input()jobTitles = [];
@Input()compIndustries = [];
@Input() majorEducationField = [];
selectedFields = [];
notification;
viewNotific = false;
showLocationInput = false;
lat: number = 0;
lng: number = 0;
city: string;
country: string;
countryCode: string;
streetAddress: string;
postalCode: string;
currentLocation: Place = new Place('', '', '', '', '', '', null, null, '');
currentSub: BehaviorSubject<any> = new BehaviorSubject('');
currentType: string = 'currentLocation';
// @ViewChild('locationn') public locationnRef: ElementRef<any>  = new ElementRef(null);

@ViewChild('googleLocation') googleLocation: any;
@ViewChild('googleLocation') public googleLocationRef: ElementRef;

locationService = new DataMap();
private ngUnsubscribe: Subject<any> = new Subject();
// exfields: any = [];
data_map = new DataMap();
file_uploaded_url = '';
file_code_to_send: { 'file': string, 'file_type': string, 'is_deleted': boolean } = { file: '', file_type: '', is_deleted: false};
label_option = 'Upload Excel File';
uploadedFiles = [];
uploadedFiles2 = [];
position: string;
displayConfirm = false;
exceltoJson = {};
addOneItem = true;
multiItemsForm;
destroy$ = new Subscription();
  loading = false;
  @HostListener('unloaded')unloaded;
  formBuilt = false;
  constructor(private route: ActivatedRoute,
              private router: Router,
              private fb: FormBuilder,
              private languageService: LanguageService,
              private valueService: NewValueService,
              private messageService: MessageService,
              private confirmationService: ConfirmationService,
              private elementRef: ElementRef,
              private mapToFormService: MapToFormService) {
                console.log('constructor');
               }

  ngOnInit(): void {
    console.log('ng on init');

    if (!this.openedInAModal) {
      this.getType();
      this.getLanguages();
      // this.getDDlData();

    } else {
      if (this.mode === 'create') {
         this.buildEmptyForm();
        //   this.destroy$ = this.itemForm.valueChanges
        //  .subscribe(res => {
        //    console.log(res);
        //            });
      } else if (this.mode === 'edit') {
        console.log("edit mode test===========");
         this.buildFilledForm();
         this.destroy$ =  this.itemForm.valueChanges.subscribe(res => { console.log(res)});

      } else if (this.mode === 'location') {
         this.buildLocationForm();
      }

    }
  }


  getType() {
    this.route.paramMap.subscribe(params => {
      this.type = '';
      this.type = params.get('type');
      this.formBuilt = false;
      // this.clearData();
      console.log('type', this.type);
      if (this.type === 'Institution') {
        // this.initializePlaceData();
              // this.currentLocationControl.controls['location_temp'].valueChanges.takeUntil(this.ngUnsubscribe).subscribe(
        //  () => {
        //     if (this.currentLocation.latitude === null || this.currentLocation.longitude === null) {
        //       this.currentLocationControl.controls['fullLocation'].setErrors({'incorrect': true});
        //     } else {
        //       this.currentLocationControl.controls['fullLocation'].setErrors(null);
        //       // this.getPlaceAutocomplete('current');

        //     }
        //   });
      }
      this.getLanguages();
    });
  }


  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log(res);
      this.languagesArray = [];
      let temp = res['data'];
      for ( let lang of temp) {
        this.languagesArray.push({
          'id'  : lang.id,
          'name': lang.name
        });
      
      }
     
      this.getDDlData();
      console.log('languages array', this.languagesArray);
  });
}

getDDlData() {
  console.log("in get DDL data");
  // this.type === 'Major' || this.type === 'Minor' || this.type === 'CompanyIndustry' || this.type === 'CompanySpecialty'
  if (this.type === 'JobTitle' ||  this.type === 'EducationField' ||
      this.type === 'Skill' || this.type === 'ExperienceField' ) {
    this.valueService.getDDlData(this.type).takeUntil(this.ngUnsubscribe).subscribe(res => {
    
      if (this.type === 'JobTitle') {
        this.experienceFields = [];
        for (let cat of res['experience_fields']) {
          if (cat.experience_field_translation.length) {
            this.experienceFields.push({
              'value': cat.id,
              'label': cat.experience_field_translation[0].name
            });
          }

        }
         this.experienceFields.unshift({ 'value': null, 'label': ''});
        for (let cat of res['major_job_title']) {
          if (cat.job_title_translation.length) {
            this.majors.push({
              'value': cat.id,
              'label': cat.job_title_translation[0].name
            });
          }
        }
        this.majors.unshift({ 'value': null, 'label': ''});

      }  

      else if (this.type === 'EducationField') {
        for (let cat of res as Array<any>) {
          this.majorEducationField.push({
            'value': cat.id,
            'label': cat.name
          });
        }
        this.majorEducationField.unshift({ 'value': '', 'label': '' });
      } 

      else if (this.type === 'Skill') {
        this.skillCats = [];
        for (let cat of res['skill_category'] as Array<any>) {

            this.skillCats.push({
              'value': cat.skill_category_id,
              'label': cat.name
            });

        }
        this.skillCats.unshift({ 'value': null, 'label': ''});
        for (let cat of res['job_titles'] as Array<any>) {

            this.jobTitles.push({
              'value': cat.id,
              'label': cat.name
            });

        }
        this.jobTitles.unshift({ 'value': null, 'label': ''});

      } else if (this.type === 'ExperienceField') {
        for (let cat of res as Array<any>) {
          if (cat.experience_field_translation.length) {
            this.experienceFields.push({
              'value': cat.id,
              'label': cat.experience_field_translation[0].name
            });
          }

        }
        this.experienceFields.unshift({ 'value': null, 'label': ''});
        console.log('exp', this.experienceFields);
      }  

    });

  }

  this.buildEmptyForm();

}


  private fillTrans(langId, value = '') {
    switch (this.type) {
      case 'Institution':
        this.institution_trans.insert(this.institution_trans.length, this.createTransControls(langId, value));
      break;
      case 'JobTitle':
          this.job_title_trans.insert(this.job_title_trans.length, this.createTransControls(langId, value));
      break;
      case 'EducationField':
     //   this.maj_education_field_trans.insert(this.maj_education_field_trans.length, this.createTransControls(langId, value));
        this.education_field_trans.insert(this.education_field_trans.length, this.createTransControls(langId, value));
      break;
      
      case 'Skill':
        this.skill_trans.insert(this.skill_trans.length, this.createTransControls(langId, value));
      break;
      case 'ExperienceField':
        this.experience_field_trans.insert(this.experience_field_trans.length, this.createTransControls(langId, value));
      break;
    }
  }


  private createTransControls(langId: number, value = '') {
      return new FormGroup({
        'translated_language_id': new FormControl(langId),
        'name'               : new FormControl(value, Validators.required),
      });
  }



  buildEmptyForm() {
    console.log('type', this.type);
    switch (this.type) {
      case 'Institution':
        this.itemForm = this.fb.group({
          'location_temp': [''],
          'url'          : [ '', [Validators.required, UrlValidator.isValidUrlFormat]],
          'location'    : this.fb.group({
              'country'      : [ '', Validators.required],
              'country_code' : [ '',  Validators.required],
              'city'         : [''],
              // 'city'         : ['', Validators.required],
              'latitude'     : [ ''],
              'longitude'    : [ ''],
              'postal_code'  : [ ''],
              'street_address': [ '']
          }),
          'institution_trans' : this.fb.array([]),
        });
      break;
      case 'JobTitle':
          this.itemForm = this.fb.group({
            'major_job_title': [''],
            // 'job_title_synonyms': [this.file_code_to_send ],
            'experience_fields' : [''],
            'job_title_trans' : this.fb.array([]),
            'job_title_synonyms_trans' : this.fb.array([]),
          });

          let index = this.job_title_synonyms_trans.insert(this.job_title_synonyms_trans.length,
              new FormArray([]));
          console.log( this.job_title_synonyms_trans, index);
          for (let j = 0; j < this.languagesArray.length; j++) {
            this.job_title_synonyms_trans.controls[this.job_title_synonyms_trans.length - 1].insert(j,
              new FormGroup({
                'translated_language_id': new FormControl(this.languagesArray[j].id),
                'name': new FormControl('')
              }));
          }
      if (this.mode === 'create') {
        this.multiItemsForm = this.fb.group({
          'major_job_title': [''],
          'experience_fields' : [''],
          'job_titles_trans' : this.fb.array([]),
        });

        this.job_titles_trans.insert(this.job_titles_trans.length,
            new FormArray([]));
        console.log( this.job_titles_trans);
        for (let j = 0; j < this.languagesArray.length; j++) {
          this.job_titles_trans.controls[this.job_titles_trans.length - 1].insert(j,
            new FormGroup({
              'translated_language_id': new FormControl(this.languagesArray[j].id),
              'name': new FormControl('')
            }));
        }

      }
      break;

      case 'EducationField':
        this.itemForm = this.fb.group({
          'majors_education_field' : [''],
          'education_field_trans' : this.fb.array([]),
        });
       
        // this.multiItemsForm = this.fb.group({
        //   'maj_education_field_trans' : this.fb.array([]),
        // });
      break;
      
      case 'Skill':
          this.itemForm = this.fb.group({
            'multi': 0,
            'skill_category_id': [''],
            'job_titles' : [''],
            'skill_trans' : this.fb.array([]),
          });

      if (this.mode === 'create') {
        this.multiItemsForm = this.fb.group({
          'multi': 1,
          'skill_category_id': [''],
          'skill_trans' : this.fb.array([]),
        });

        this.skill_transs.insert(this.skill_transs.length,
            new FormArray([]));
        console.log( this.skill_transs);
        for (let j = 0; j < this.languagesArray.length; j++) {
          this.skill_transs.controls[this.skill_transs.length - 1].insert(j,
            new FormGroup({
              'translated_language_id': new FormControl(this.languagesArray[j].id),
              'name': new FormControl('')
            }));
        }

      }

      break;
      case 'ExperienceField':
        this.itemForm = this.fb.group({
          'major_experience_field_id' : [ null, Validators.required],
          'experience_field_translation' : this.fb.array([]),
        });

        if (this.mode === 'create') {
          this.multiItemsForm = this.fb.group({
            'major_experience_field_id' : [ null],
            'experience_field_translation' : this.fb.array([]),
          });

          for (let j = 0; j < this.languagesArray.length; j++) {
            this.experience_field_transs.insert(j,
              new FormGroup({
                'translated_language_id': new FormControl(this.languagesArray[j].id),
                'name': new FormControl('', Validators.required)
              }));
          }
          console.log( this.experience_field_transs);

         }
      break;

    }

    // if (this.openedInAModal) {
      for (let lang of this.languagesArray) {
        this.fillTrans(lang.id);
      }
  // }
    this.formBuilt = true;
    console.log(this.type + ' form', this.itemForm);
  }

  buildFilledForm() {
    console.log('type', this.type, this.mode);
    console.log('passed item', this.item);
    switch (this.type) {
      case 'Institution':
        this.itemForm = this.fb.group({
          'location_temp': [(this.item.location === null)? '' : this.item.location.country + ', ' + this.item.location.city+', '+this.item.location.street_address],
          'url'          : [ this.item.url, [Validators.required, UrlValidator.isValidUrlFormat]],
          'location'     :  this.fb.group({
            'country'      : [(this.item.location === null) ? '' : this.item.location.country, Validators.required ],
            'country_code' : [(this.item.location === null) ? '' : this.item.location.country_code, Validators.required ],
            'city'         : [(this.item.location === null) ? '' : this.item.location.city],
            // 'city'         : [(this.item.location === null) ? '' : this.item.location.city, Validators.required],
            'latitude'     : [(this.item.location === null) ? '' : this.item.location.latitude],
            'longitude'    : [(this.item.location === null) ? '' : this.item.location.longitude],
            'postal_code'  : [(this.item.location === null) ? '' : this.item.location.postal_code],
            'street_address': [(this.item.location === null) ? '' : this.item.location.street_address]
          }),
          'institution_trans' : this.fb.array([]),
        });



          for (let i = 0; i <  this.languagesArray.length; i++) {
            this.fillTrans(this.languagesArray[i].id, this.item.translations[i].name);
          }

        if (this.item.location !== null) {
          if (this.item.location.latitude && this.item.location.longitude) {
            let currentCoords = {
              lat:  +this.item.location.latitude,
              lng:  +this.item.location.longitude,
            };
            // this.notifyCurrentMap(currentCoords);
          }
         }
    break;
      case 'JobTitle':
        this.itemForm = this.fb.group({
          'major_job_title': [this.item.major_job_title],
          'job_title_synonyms': [this.file_code_to_send ],
          'experience_fields' : [''],
          'job_title_trans' : this.fb.array([]),
          'job_title_synonyms_trans' : this.fb.array([]),
        });
        this.selectedFields = this.item.experience_fields;
        console.log("this.item.experience_fields",this.item.experience_fields);
        for (let i = 0; i < this.languagesArray.length; i++) {
          this.fillTrans(this.languagesArray[i].id, this.item.job_title_translations[i].name);
        }

        if (this.item.synonyms.length) {
          for (let i = 0; i < this.item.synonyms.length; i++) {
            this.job_title_synonyms_trans.insert(this.job_title_synonyms_trans.length,
                new FormArray([]));
            console.log( this.job_title_synonyms_trans);
            for (let j = 0; j < this.languagesArray.length; j++) {
              this.job_title_synonyms_trans.controls[this.job_title_synonyms_trans.length - 1]
              .insert(j,
                new FormGroup({
                  'translated_language_id': new FormControl(this.languagesArray[j].id),
                  'name': new FormControl((this.item.synonyms[i].translations[j].name|| ''))
                }));
            }
          }
        }
       console.log( this.job_title_synonyms_trans);

      break;

      case 'EducationField':
        this.itemForm = this.fb.group({
          'majors_education_field' : [this.item.majors_education_field],
          'education_field_trans' : this.fb.array([]),
        });
        console.log(this.itemForm.value);
      //  this.majors_education_field.patchValue(this.item.majors_education_field);
        for (let i = 0; i < this.languagesArray.length; i++) {
          if (this.item.translations[i] !== undefined) {
            this.fillTrans(this.languagesArray[i].id, this.item.translations[i].name);
          } else {
            this.fillTrans(this.languagesArray[i].id, '');
          }
        }
      break;

      case 'Skill':
        this.itemForm = this.fb.group({
          'skill_category_id': [this.item.skill_category_id],
          'job_titles' : [''],
          'skill_trans' : this.fb.array([]),
        });

        for (let i = 0; i < this.languagesArray.length; i++) {
          this.fillTrans(this.languagesArray[i].id, ( (this.item.skill_trans[i] === undefined) ? '' : this.item.skill_trans[i].name));
        }
        this.selectedFields = this.item.job_titles;
      break;

      case 'ExperienceField':
        this.itemForm = this.fb.group({
          'major_experience_field_id' : [ this.item.major_experience_field_id, Validators.required],
          'experience_field_translation' : this.fb.array([]),
        });

        for (let i = 0; i < this.languagesArray.length; i++) {
          if (this.item.experience_field_translation[i] !== undefined) {
            this.fillTrans(this.languagesArray[i].id, this.item.experience_field_translation[i].name);
          } else {
            this.fillTrans(this.languagesArray[i].id, '');
          }
        }
      break;

    }
    this.formBuilt = true;
    console.log( this.type, 'form', this.itemForm);

  }


 buildLocationForm() {
  this.itemForm = this.fb.group({
    // 'location_temp': [''],
    'location_temp': [(this.item.location === null)? '' : this.item.location.country + ', ' + this.item.location.city+', '+this.item.location.street_address],
    'location'    :  this.fb.group({
      'country'      : [(this.item.location === null) ? '' : this.item.location.country, Validators.required ],
      'country_code' : [(this.item.location === null) ? '' : this.item.location.country_code, Validators.required ],
      'city'         : [(this.item.location === null) ? '' : this.item.location.city],
      // 'city'         : [(this.item.location === null) ? '' : this.item.location.city, Validators.required],
      'latitude'     : [(this.item.location === null) ? '' : this.item.location.latitude],
      'longitude'    : [(this.item.location === null) ? '' : this.item.location.longitude],
      'postal_code'  : [(this.item.location === null) ? '' : this.item.location.postal_code],
      'street_address': [(this.item.location === null) ? '' : this.item.location.street_address]
    }),

  });


  this.formBuilt = true;
  let currentCoords = {
    lat: this.item.location.latitude,
    lng: this.item.location.longitude,
  };
  // this.notifyCurrentMap(currentCoords);
  // console.log('location',this.item.location);

  console.log('institution location form', this.itemForm);

  }


  AddNewItem(values = { 'English': '', 'Arabic': ''} ) {
    if (this.type === 'JobTitle') {
      if (this.addOneItem) {
        this.job_title_synonyms_trans.insert(this.job_title_synonyms_trans.length,
          new FormArray([]));
      console.log(values, this.job_title_synonyms_trans);
      for (let j = 0; j < this.languagesArray.length; j++) {
        this.job_title_synonyms_trans.controls[this.job_title_synonyms_trans.length - 1]
          .insert(j,
          new FormGroup({
            'translated_language_id': new FormControl(this.languagesArray[j].id),
            'name': new FormControl((values[this.languagesArray[j].name] || ''))
          }));
      }
      } else {
        this.job_titles_trans.insert(this.job_titles_trans.length,
          new FormArray([]));
      console.log(values, this.job_titles_trans);
      for (let j = 0; j < this.languagesArray.length; j++) {
        this.job_titles_trans.controls[this.job_titles_trans.length - 1]
          .insert(j,
          new FormGroup({
            'translated_language_id': new FormControl(this.languagesArray[j].id),
            'name': new FormControl((values[this.languagesArray[j].name] || ''))
          }));
      }
      }

    } else if (this.type === 'Skill') {
      this.skill_transs.insert(this.skill_transs.length,
        new FormArray([]));
      console.log(values, this.skill_transs);
      for (let j = 0; j < this.languagesArray.length; j++) {
        this.skill_transs.controls[this.skill_transs.length - 1]
          .insert(j,
          new FormGroup({
            'translated_language_id': new FormControl(this.languagesArray[j].id),
            'name': new FormControl((values[this.languagesArray[j].name] || ''))
          }));
      }
    }

 }


 removeSynonym(i) {
  this.job_title_synonyms_Array.removeAt(i);
 }

 removeItem(i) {
   if (this.type === 'JobTitle') {
      this.job_titles_array.removeAt(i);
   } else if (this.type === 'Skill') {
     this.skill_transs.removeAt(i);
   }
 }

 displayExFForm() {

 }

 addNewExperienceField() {

 }

 private createLocationsControls() {
  return new FormGroup({
    'country'      : new FormControl( '', Validators.required),
    'country_code' : new FormControl( '',  Validators.required),
    'city'         : new FormControl( ''),
    // 'city'         : new FormControl( '', Validators.required),
    'latitude'     : new FormControl( ''),
    'longitude'    : new FormControl( ''),
    'postal_code'  : new FormControl( ''),
    'street_address': new FormControl( '')
    });

 }




onFileChange(event: any, fileUpload) {
  console.log('event', event);
  if (event.files[0].size > 1000000) {

    this.messageService.add({severity: 'error', summary: 'Error Message', detail: 'File size bigger than 1.0 MB!'});
    // fileUpload.clear();

  } else {
    for (let file of event.files) {
      if (this.addOneItem) {
        this.uploadedFiles.push(file) ;
      } else {
        this.uploadedFiles2.push(file) ;
      }
     }
    this.exceltoJson = {};
    let headerJson = {};
    /* wire up file reader */
  // const target: DataTransfer = <DataTransfer>(event.target);
    // if (target.files.length !== 1) {
    //   throw new Error('Cannot use multiple files');
    // }
    const reader: FileReader = new FileReader();
    reader.readAsBinaryString(event.files[0]);
    console.log('filename', event.files[0].name);
    this.exceltoJson['filename'] = event.files[0].name;
    reader.onload = (e: any) => {
    /* create workbook */
    const binarystr: string = e.target.result;
    const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });
    for (let i = 0; i < wb.SheetNames.length; i++) {
      const wsname: string = wb.SheetNames[i];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];
      const data = XLSX.utils.sheet_to_json(ws); // to get 2d array pass 2nd parameter as object {header: 1}
      this.exceltoJson[`sheet${i + 1}`] = data;
      for (let item of this.exceltoJson[`sheet${i + 1}`]) {
        this.AddNewItem(item);
      }
      const headers = this.get_header_row(ws);
      headerJson[`header${i + 1}`] = headers;
      //  console.log("json",headers)
    }
    this.exceltoJson['headers'] = headerJson;
    console.log(this.exceltoJson);
    // this.label_option = 'Change File';
    this.messageService.add({severity: 'info', summary: 'File Uploaded Successfully!', detail: ''});
    return this.exceltoJson;
  };
  }
}

get_header_row(sheet) {
  let headers = [];
  let range = XLSX.utils.decode_range(sheet['!ref']);
  let C, R = range.s.r; /* start in the first row */
  /* walk every column in the range */
  for (C = range.s.c; C <= range.e.c; ++C) {
    let cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]; /* find the cell in the first row */
    console.log('cell', cell);
    let hdr = 'UNKNOWN'  + C; // <-- replace with your desired default
    if (cell && cell.t) {
      hdr = XLSX.utils.format_cell(cell);
      headers.push(hdr);
    }
  }
  console.log('headers', headers);
  return headers;
}

  save( i: any) {
    // for unknown reason, majors_education_field geting validation some where in the code, i didn't find where!!
    // but this field shouldn't have validation, so i'm clearing validation here so it won't stop submitting the form
    if(this.type === 'EducationField') {
      this.majors_education_field.clearValidators();
      this.majors_education_field.updateValueAndValidity();
    }

      console.log('is form valid?', ((this.addOneItem) ? this.itemForm.valid : this.multiItemsForm.valid));
      console.log((this.addOneItem) ? this.itemForm.value : this.multiItemsForm.value);
      if ((this.itemForm && this.itemForm.valid && this.addOneItem) || (this.multiItemsForm && this.multiItemsForm.valid && !this.addOneItem)) {
          let dataToSend = (this.addOneItem) ? this.itemForm.value : this.multiItemsForm.value;
          console.log('data to send', dataToSend);
          if (this.mode === 'create') {
              this.valueService.createItem(this.type, dataToSend).takeUntil(this.ngUnsubscribe).subscribe(res => {
                console.log('add item res', res);
                // send reply to table and update status
                this.notification = 'new item was added successfully!';
                this.viewNotific = true;
                setTimeout(() => {
                   this.viewNotific = false;
                   if (this.openedInAModal) {
                    this.closeModal.emit({ 'id': null , 'data': res , 'add_one': this.addOneItem });
                   }
                   else {
                    this.router.navigate(['/manage/dashboard/manage-values/' + this.type]);
                   }
                  }, 2000);
              }, error => {
                console.log(error);
                alert('error while saving item');
              });
             // }
            this.itemForm.reset();

          } else if (this.mode === 'edit') {
            this.valueService.updateItem(this.type, dataToSend, this.item.id).takeUntil(this.ngUnsubscribe).subscribe(res => {
                console.log('update item res', res);
                this.notification = 'item was updated successfully!';
                this.viewNotific = true;
                setTimeout(() => {
                  this.viewNotific = false;
                  // send reply to table and update status
                  this.closeModal.emit({ 'id': this.item.id, 'data': res, 'add_one': this.addOneItem });
                }, 2000);
            }, error => {
              console.log(error);
              alert('error while saving item');
            });
          }
      }

  }

  saveLocation(form) {
    console.log('is form valid?', this.itemForm.valid);
    console.log(this.itemForm.value);
    if (this.itemForm.valid) {
    let dataToSend = this.itemForm.value;
    this.valueService.updateField(this.item.id, this.type, dataToSend ).takeUntil(this.ngUnsubscribe).subscribe((res) => {
      console.log('res', res);
      this.notification = 'location was updated successfully!';
      this.viewNotific = true;
      setTimeout(() => {
         this.viewNotific = false;
        //  this.closeLocationModal.emit({ 'id': this.item.id , 'data': res });
         this.closeModal.emit({ 'id': this.item.id, 'data': res, 'add_one': this.addOneItem });
       }, 3000);
    },
    (error: Error) => {
      console.log('error', error);
      this.notification = 'failed to update location!!';
      this.viewNotific = true;
      setTimeout(() => { this.viewNotific = false; }, 5000);
    });
    console.log('items', this.item);
    }
  }


  end() {

  }

  search() {

  }




  ngAfterViewInit() {
    if (this.type === 'Institution') {
      setTimeout(() => {
        this.getPlaceAutocomplete();
      }, 1500);
    }
   }


private getPlaceAutocomplete() {
  //to stop bot traffic to google maps
  if(navigator.userAgent.match(/Googlebot/i)){
    return;
  }
  //const autocomplete = new google.maps.places.Autocomplete(this.locationnRef.nativeElement,
    const autocomplete = new google.maps.places.Autocomplete(this.googleLocation.nativeElement,
      {
        types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']
      });

  autocomplete.addListener('place_changed', () => {
      console.log('current place changed');
      let place = autocomplete.getPlace();
      console.log('place', place);
      this.getAddress(place);
    });

}


getAddress(place: object) {
  let newPlace = this.locationService.getLocationDataFromGoogleMap(place);
  console.log('inside getAddress method', 'place', place);
  console.log('new place',  newPlace);
    this.city = newPlace.city;
    this.country = newPlace.country;
    this.countryCode = newPlace.country_code;
    this.streetAddress = newPlace.street_address;
    this.postalCode = newPlace.postal_code;
    this.lat = newPlace.latitude;
    this.lng = newPlace.longitude;
    console.log('current',  this.lng, this.lat);
    if (this.lat && this.lng) {
      let currentCoords = {
        lat: +this.lat,
        lng: +this.lng
      };
      // this.notifyCurrentMap(currentCoords);
    }
    this.locationGroup.controls['country'].setValue(this.country);
    this.locationGroup.controls['country_code'].setValue(this.countryCode);
    this.locationGroup.controls['city'].setValue(this.city);
    this.locationGroup.controls['latitude'].setValue(this.lat);
    this.locationGroup.controls['longitude'].setValue(this.lng);
    this.locationGroup.controls['street_address'].setValue(this.streetAddress);
    this.locationGroup.controls['postal_code'].setValue(this.postalCode);
    console.log('current',  this.lng, this.lat);

  //  console.log( 'current', this.location );
  // $('#location').focus();
}


currentLocationKeyUp($event) {
  console.log('currentLocationKeyUp');
  // old index:  [this.locations.length - 1]
  this.locationGroup.controls['country'].setValue('');
  this.locationGroup.controls['country_code'].setValue('');
  this.locationGroup.controls['city'].setValue('');
  this.locationGroup.controls['postal_code'].setValue('');
  this.locationGroup.controls['street_address'].setValue('');

  // this.notifyCurrentMap({lat: this.locationGroup.controls['latitude'].value,
  // lng: this.locationGroup.controls['longitude'].value});
 
  this.loading = true;
}

// notifyCurrentMap(val) {
//   console.log('notify current map', val);
//   this.currentSub.next(val);
// }


// setCurrentLocationControls(place: Place) {
//   console.log('place', place);
//   console.log('location', this.locationGroup);
 
//     this.location_temp.setValue(place.country + ', ' + place.city + ', ' + place.streetAddress + ', ' + place.countryCode);
//     this.locationGroup.controls['country'].setValue(place.country);
//     this.locationGroup.controls['country_code'].setValue(place.countryCode);
//     this.locationGroup.controls['city'].setValue(place.city);
//     this.locationGroup.controls['postal_code'].setValue(place.postalCode);
//     this.locationGroup.controls['street_address'].setValue(place.streetAddress);
//     this.locationGroup.controls['latitude'].setValue(place.latitude);
//     this.locationGroup.controls['longitude'].setValue(place.longitude);
 
//   console.log(this.locationGroup);
//   this.loading = false;
//   if (place.latitude === null && place.longitude === null) {
//    this.location_temp.setErrors({'incorrect': true});
//   } else {
//    this.location_temp.setErrors(null);
//   }
// }

// initializePlaceData() {
//   this.mapToFormService.personalMapData.skip(1).takeUntil(this.ngUnsubscribe).subscribe(
//     (place: Place) => {
//         this.loading = true;
//         console.log('new place from map', place);
//         this.currentLocation = place;
//         this.setCurrentLocationControls(this.currentLocation);
//     });
// }

// institution
get institution_trans() {
  return this.itemForm.get('institution_trans');
}

get id() {
  return this.item.get('id');
}

get location_temp() {
  return this.itemForm.get('location_temp');
}

get locationGroup() {
  return this.itemForm.get('location');
}

// get locationsArray() {
//   return this.itemForm.get('locations') as FormArray;
// }

get url() {
  return this.itemForm.get('url');
}


// job title
get job_title_trans() {
  return this.itemForm.get('job_title_trans');
}

get job_titles_trans() {
  return this.multiItemsForm.get('job_titles_trans');
}

get job_titles_array() {
  return this.multiItemsForm.get('job_titles_trans') as FormArray;
}



get job_title_synonyms_trans() {
  return this.itemForm.get('job_title_synonyms_trans');
}

get job_title_synonyms_Array() {
  return this.itemForm.get('job_title_synonyms_trans') as FormArray;
}

get experience_fields() {
  return this.itemForm.get('experience_fields');
}

get major_job_title() {
  return this.itemForm.get('major_job_title');
}



get education_field_trans() {
  return this.itemForm.get('education_field_trans');
}

// for multiselect field in education_field interface
get majors_education_field() {
  return this.itemForm.get('majors_education_field');
}
// get maj_education_field_trans() {
//   return this.multiItemsForm.get('maj_education_field_trans');
// }

// major
// get major_trans() {
//   return this.itemForm.get('major_trans');
// }

// get major_parent_id() {
//   return this.itemForm.get('major_parent_id');
// }

// get major_parent_trans() {
//   return this.itemForm.get('major_parent_trans');
// }


// get major_parent_transs() {
//   return this.multiItemsForm.get('major_trans');
// }


// minor
// get minor_trans() {
//   return this.itemForm.get('minor_trans');
// }

// get major_id() {
//   return this.itemForm.get('major_id');
// }
// comp

get company_industry_parent_id() {
  return this.itemForm.get('company_industry_parent_id');
}

get company_industry_trans() {
  return this.itemForm.get('company_industry_trans');
}

get company_industry_transs() {
  return this.multiItemsForm.get('company_industry_trans');
}

// skill
get skill_trans() {
  return this.itemForm.get('skill_trans');
}

get skill_transs() {
  return this.multiItemsForm.get('skill_trans');
}


get skill_category_id() {
  return this.itemForm.get('skill_category_id');
}

get job_titles() {
  return this.itemForm.get('job_titles');
}
// exp field

get experience_field_trans() {
  return this.itemForm.get('experience_field_translation');
}

get experience_field_transs() {
  return this.multiItemsForm.get('experience_field_translation');
}


get major_experience_field_id() {
  return this.itemForm.get('major_experience_field_id');
}

// comp specialty

get company_specialty_trans() {
  return this.itemForm.get('company_specialty_trans');
}

get company_industry_id() {
  return this.itemForm.get('company_industry_id');
}


  clearData() {
    this.item = null;
    // this.destroy$.unsubscribe();
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    console.log('destructor');
    // this.elementRef.nativeElement.remove();

    switch (this.type) {
      case 'Institution':
        this.itemForm.removeControl('url');
        this.itemForm.removeControl('location');
        this.itemForm.removeControl('location_temp');
        this.itemForm.removeControl('institution_trans');
      break;
      case 'JobTitle':
        this.itemForm.removeControl('major_job_title');
        this.itemForm.removeControl('experience_fields');
        this.itemForm.removeControl('job_title_trans');
        this.itemForm.removeControl('job_title_synonyms_trans');

        this.multiItemsForm.removeControl('major_job_title');
        this.multiItemsForm.removeControl('job_titles_trans');

      break;
      
      case 'Skill':
        this.itemForm.removeControl('multi');
        this.itemForm.removeControl('skill_category_id');
        this.itemForm.removeControl('job_titles');
        this.itemForm.removeControl('skill_trans');


        this.multiItemsForm.removeControl('multi');
        this.multiItemsForm.removeControl('skill_category_id');
        this.multiItemsForm.removeControl('skill_trans');

      break;
      case 'ExperienceField':

        this.itemForm.removeControl('major_experience_field_id');
        this.itemForm.removeControl('experience_field_translation');

      break;

      default:
      break;
    }
  }

  ngOnDestroy() {
  //  this.clearData();
  }

}

