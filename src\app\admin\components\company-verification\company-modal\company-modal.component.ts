import { Component, OnInit, EventEmitter, Input, Output, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { FormBuilder, Validators } from '@angular/forms';
import { CompanyService } from 'app/admin/services/company.service';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
import { CompanyModalValidators } from './company-modal.validators';

@Component({
  selector: 'app-company-modal',
  templateUrl: './company-modal.component.html',
  styleUrls: ['./company-modal.component.css']
})
export class CompanyModalComponent implements OnInit, OnDestroy {
  @Input('company')company = {'id': null, 'name': '','username':'', 'description': '', 'website': '','logo': '',
  'status': '', 'email': '', 'country': '', 'city': '', 'industry': '', 'profile': '', 'handled_by': '', 'mobile_number': ''
,'message':'','company_method_verification_id':'', 'admin_description':'', 'company_status_id':'','permissions':'','company_domain_url':'' ,'installed_plugin':0
};
  @Input('companyLog')companyLog = {'id': null, 'name': '', log: []};
  @Input('mode')mode;
  @Input('statuses')statuses;
  @Input('methods')methods;
  @Input('permissions') permissions;
  @Output('updateCompanyStatusClicked')updateCompanyStatusClicked = new EventEmitter();
  @Output('closeModalClicked')closeModalClicked = new EventEmitter();
  @Output('sendCompanyStatus')sendCompanyStatus = new EventEmitter();
  @Output('openCoProfileClicked')openCoProfileClicked = new EventEmitter();

  private ngUnsubscribe: Subject<any> = new Subject();
  companyForm;
  companyInstallPluginForm;
  displayMore = false;


  constructor(private fb: FormBuilder,
              private imageProcessingService: ImageProcessingService,
              private contact: CompanyService) { }

  ngOnInit() {
    this.buildEmptyForm();
    // console.log("company fff",this.company,this.mode);
  }

  getImageLogo(logo) {
    return (logo) ? this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail',logo) : './assets/images/no-image.png' ;
  }
  getFlagImage(code) {
      return './assets/images/CountryCode/' + code + '.gif' ;
  }


  buildEmptyForm() {
    if (this.mode === 'preview_mode') {
        this.companyForm = this.fb.group({
          'company_id'                    : [ this.company.id, Validators.required],
          'admin_description'             : [ '', Validators.required],
          'company_method_verification_id': [ null, Validators.required],
          'company_status_id'             : [ null, Validators.required],
          'permissions': ['']
         // 'show_in_home_page'                  : [0]
        }
        , {validator : CompanyModalValidators.verifiedPermissionsValidator }
        );
        
        this.buildFilledForm();
    }
    else if(this.mode=='install_plugin_mode'){
        this.companyInstallPluginForm = this.fb.group({
          'company_id'                    : [ this.company.id, Validators.required],
          'company_domain_url'             : [ '',[ Validators.required,  Validators.pattern('https?://.+')]],
          'plugin_name'             : [ '', Validators.required],
        }
        );
        this.companyInstallPluginForm.controls['company_id'].setValue(this.company.id);
        this.companyInstallPluginForm.controls['plugin_name'].setValue('cveek_plugin');
    }


  }

  buildFilledForm(){
    this.companyForm.controls['permissions'].setValue(this.company.permissions);
    this.company_method_verification_id.setValue(this.company.company_method_verification_id);
    this.admin_description.setValue(this.company.admin_description);
    this.company_status_id.setValue(this.company.company_status_id);
  }



  confirm(replyForm) {
    if (this.companyForm.valid) {
        let dataToSend = this.companyForm.value;

        let permissionsIds = [];
        for (let obj of this.companyForm.value.permissions) {
          permissionsIds.push(obj.id);
        }
        dataToSend.permissions= permissionsIds;

        this.contact.updateCompanyStatus(dataToSend).subscribe(res => {
          if(res['admin_log']){
            // send reply to table and update status
            let temp = res['admin_log'];
            this.sendCompanyStatus.emit({ 'status_id': temp.company_status_id,
              'company_id': temp.company_id, 'admin_description': temp.admin_description,
              'admin_name': temp.admin_name, 'permissions':temp.company_permissions,
              'company_domain_url':this.company.company_domain_url,'installed_plugin':this.company.installed_plugin   });

          }
          else if(res['error']){
            console.log(res['error']);
          }

        });
    } else {
          alert('invalid entries');
    }

  }

  cancel() {
    this.companyForm.reset();
  }

  setComId() {
    this.company_id.setValue(this.company.id);
  }


  get company_id() {
    return this.companyForm.get('company_id');
  }


  get company_method_verification_id() {
      return this.companyForm.get('company_method_verification_id');
  }


  get company_status_id() {
      return this.companyForm.get('company_status_id');
  }


  get admin_description() {
      return this.companyForm.get('admin_description');
  }


  openCompanyProfile() {
    this.openCoProfileClicked.emit({ 'id': this.company.id });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
  confirmInstallPlugin(replyForm) {
    if (this.companyInstallPluginForm.valid) {
        let dataToSend = this.companyInstallPluginForm.value;
        this.contact.setPluginUrl(dataToSend).subscribe(res => {
          if(res['message']){
            this.sendCompanyStatus.emit({ 'status_id': this.company.company_status_id,
              'company_id': this.company.id, 'admin_description': this.company.admin_description,
              'admin_name': this.company.handled_by, 'permissions':this.company.permissions ,
              'company_domain_url':res['plugin']['company_domain_url'],'installed_plugin':res['plugin']['installed']? 'Installed' : null });
          }
          else if(res['error']){
            console.log(res['error']);
          }
          
        });
    } else {
          alert('invalid entries');
    }

  }
  cancelInstallPlugin() {
    this.companyInstallPluginForm.reset();
  }

}
