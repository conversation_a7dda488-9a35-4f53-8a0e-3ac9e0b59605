.page-navbar {
    font-size: 16px;
    width: 100%;
    list-style: none;
    z-index: 9999;
    text-align: center;
  }
  .page-navbar2{
    font-size: 18px;
    width: 100%;
    list-style: none;
    z-index: 9999;
    text-align: left;
    border-bottom: none;
border-radius: 10px; 

 }
 .page-navbar2 p{
   font-size: 16px;
 }
  
  
  .page-navbar ul {
    margin-bottom: 0;
    padding: 0px 0px 10px 0px;
  }
  
  .page-navbar ul li {
    display: inline-block;
    padding: 5px 0px 10px;
    margin-right: 20px;
    margin-bottom: 30px;
    
  }
  .page-navbar2 ul {
    margin-bottom: 0;
    width: 100%;
    height: 100%;
  }
  
  .page-navbar2 ul li {
    display: inline-block;
    margin-right: 20px;
    width: 100%;
  
  }
  
  
  .page-navbar ul li::after {
    background-color: white;
  }
  
  .page-navbar ul li a {
    text-decoration: none;
    transition: all .4s ease;
    color: #276ea4 !important;
  
  }
  .container-fluid{
    padding: 20px;
    background-color: #eee;
    background-attachment: fixed;
    background-size: 100% 100%;
    height: 100vh;
    overflow-y: scroll;
    
    }  
    .details{
        background-color: white;
        border-radius: 10px;
        font-family: 'Exo2-Regular', sans-serif !important;
        padding:30px;
        margin-left: -50px;

      
      }
     
      
      .has-error .error-message {
        color: #a94442;
        position: absolute;
        left: 70%;
        top: 10px;
        width: 60%;
      }
      
      
      
    
     
     
    
     
       
       
      
      
      
      /* @media only screen and (max-width: 760px){
        th:last-of-type {
        display: none;
        }
      } */
      :host ::ng-deep aw-wizard {
        display: flex !important;
        justify-content: flex-start !important; }
      
      :host ::ng-deep aw-wizard .wizard-steps {
          top: 0 !important;
          display: flex !important; }
      
      :host ::ng-deep aw-wizard.vertical {
        flex-direction: row !important; }
      
      :host ::ng-deep aw-wizard.vertical .wizard-steps {
          min-width: calc(100% - 280px) !important;
          width: 50% !important;
          flex-direction: column !important; }
      
          :host ::ng-deep aw-wizard-step,
          :host ::ng-deep aw-wizard-completion-step {
        height: auto !important;
        width: 100% !important; }
      
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator * {
        -webkit-box-sizing: border-box !important;
        -moz-box-sizing: border-box !important;
        box-sizing: border-box !important; 
      background-color: white;}
        :host ::ng-deep aw-wizard-navigation-bar ul{
          background-color: white;
          width: 350px;
          border-radius: 10px;
          height: 78%;
        }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator{
      
        }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li {
        position: relative !important;
        pointer-events: none !important; }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li:hover ul.steps-indicator{
          background-color:#30457c ; }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li a .label {
          color: black !important;
          line-height: 20px !important;
          font-size: 16px !important;
          text-transform: none;
          margin-bottom:30px;
          text-decoration: none !important;
        font-family: 'Exo2-Regular', sans-serif !important; 
      text-align: center;}
      
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable {
        pointer-events: auto !important; }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable a:hover {
          cursor: pointer !important;
        text-decoration: none; }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable a:hover .label {
          color: #30457c !important; }
      
        :host ::ng-deep aw-wizard-navigation-bar.vertical {
        max-width: 280px !important;
        width: 50% !important;
        position: sticky !important;
        top: 0 !important; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical:hover {
          background-color: #eee !important;
        }
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator {
          display: flex !important;
          flex-direction: column !important;
          justify-content: center !important;
          list-style: none !important;
          margin: auto !important;
        z-index: 101 !important;
        padding-top:60px;
        text-align: center !important;
       
      }
        
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .step-indicator {
          background-color: #30457c !important;
      
        }
      
      
        
        
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .label {
          background-color: #eee !important;
          
      
        }
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .label p {
          background-color: #eee !important;
      
        }
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.current  a .label{
          background-color: #eee;
        }
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.current  a .label p{
          background-color: #eee;
        }
       
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li:not(:last-child) {
            margin-bottom: 0 !important;
            padding-bottom: 20px !important; }
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a {
            display: flex !important;
            flex-direction: row !important;
            align-items: center !important; }
            aw-wizard-navigation-bar.vertical ul.steps-indicator li a .label {
              text-align: center !important; }
              :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical ul.steps-indicator li a .label {
                margin-left: 0 !important;
                margin-right: 15px !important;
                text-align: right !important; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator {
      }
        :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator {
          padding: 5px 5px 5px 5px; 
        }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li:not(:last-child):after {
          background-color: #E6E6E6;
          content: '';
          position: absolute;
          left: -25px;
          top: 0px;
          height: calc(100% - 50px);
          width: 0px; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li:not(:last-child):after {
            left: auto;
            right: -25px; }
            :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li a {
          min-height: 50px; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li .step-indicator {
          top: 0;
          left: -10px;
          position: absolute;
          width: 25px;
          height: 25px;
          text-align: center !important;
          vertical-align: middle;
          line-height: 46px;
          border-radius: 50%;
          border: 1px solid #30457c; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li .step-indicator {
            left: auto;
            right: -50px;}
            :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.optional .step-indicator {
          border: 2px solid #30A03E; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.done .step-indicator {
          border: 2px solid #30A03E;}
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.current .step-indicator {
          border: 2px solid #808080; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.editing .step-indicator {
          border: 2px solid #FF0000; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.completed .step-indicator {
          border: 2px solid #339933; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable a:hover .step-indicator {
          position: absolute;
          width: 50px;
          height: 50px;
          text-align: center;
          vertical-align: middle;
          line-height: 46px;
          border-radius: 100%;
          border: 2px solid #30457c; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.optional a:hover .step-indicator {
          border: 2px solid #30457c; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.done a:hover .step-indicator {
          border: 2px solid #30A03E !important; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.current a:hover .step-indicator {
          border: 2px solid #676767; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.editing a:hover .step-indicator {
          border: 2px solid #cc0000; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.completed a:hover .step-indicator {
          border: 2px solid #267326; }
      
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator {
        padding: 5px 5px 5px 50px; }
        :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator {
          padding: 5px 55px 5px 5px; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li:not(:last-child):after {
          background-color: #E6E6E6;
          content: '';
          position: absolute;
          left: -25px;
          top: 50px;
          height: calc(100% - 50px);
          width: 0px; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li:not(:last-child):after {
            left: auto;
            right: -25px; }
            :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li a {
          min-height: 50px; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li .step-indicator {
          top: 0;
          left: -50px;
          position: absolute;
          width: 50px;
          height: 50px;
          text-align: center;
          vertical-align: middle;
          line-height: 50px;
          border-radius: 100%;
          background-color: #30457c;
          color: black; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li .step-indicator {
            left: auto;
            right: -50px; }
            :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.optional .step-indicator {
          background-color: #38ef38;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.done .step-indicator {
          background-color: #339933;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.current .step-indicator {
          background-color: #808080;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.editing .step-indicator {
          background-color: #FF0000;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.completed .step-indicator {
          background-color: #339933;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable a:hover .step-indicator {
          position: absolute;
          width: 50px;
          height: 50px;
          text-align: center;
          vertical-align: middle;
          line-height: 50px;
          border-radius: 100%;
          background-color: #30457c;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.optional a:hover .step-indicator {
          background-color: #20ed20;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.done a:hover .step-indicator {
          background-color: #2d862d;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.current a:hover .step-indicator {
          background-color: #737373;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.editing a:hover .step-indicator {
          background-color: #e60000;
          color: black; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li.navigable.completed a:hover .step-indicator {
          background-color: #2d862d;
          color: black; }
      
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator {
        padding: 0px 0px 0px 50px; }
        :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator {
          padding: 5px 5px 5px 5px; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li:not(:last-child):after {
          background-color: #E6E6E6;
          content: '';
          position: absolute;
          left: -25px;
          top: 50px;
          height: calc(100% - 50px);
          width: 0px; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li:not(:last-child):after {
            left: auto;
            right: -25px; }
            :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li a {
          min-height: 50px; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
          top: 0;
          left: -50px;
          position: absolute;
          width: 50px;
          height: 50px;
          text-align: center;
          vertical-align: middle;
          line-height: 46px;
          border-radius: 100%;
          border: 2px solid #E6E6E6;
          color: #E6E6E6; }
          :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
            left: auto;
            right: -50px; }
            :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.optional .step-indicator {
          border: 2px solid #30457c;
          color: #30457c; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.done .step-indicator {
          border: 2px solid #30A03E;
          color:#30A03E; }
          :host ::ng-deep .red aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
            border: 2px solid red;
            color:red; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.current .step-indicator {
          border: 2px solid white;
          color: white;
          background-color:#30457c ;
            }
          
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.completed .step-indicator {
          border: 2px solid red ;
          color: red; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable a:hover .step-indicator {
          position: absolute;
          width: 50px;
          height: 50px;
          text-align: center;
          vertical-align: middle;
          line-height: 46px;
          border-radius: 100%;
          border: 2px solid #eee ;
          color: #eee; 
        background-color: #30457c;}
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.optional a:hover .step-indicator {
          border: 2px solid #eee;
          color: #eee; 
        background-color: #30457c;}
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.done a:hover .step-indicator {
          border: 2px solid #eee;
          color: #eee;
        background-color: #30457c; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.current a:hover .step-indicator {
          border: 2px solid #eee;
          color: #eee; 
        background-color: #30457c;}
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.editing a:hover .step-indicator {
          border: 2px solid #cc0000;
          color: #cc0000; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.red a:hover .step-indicator {
            border: 2px solid #cc0000;
            color: #cc0000; }
          :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.completed a:hover .step-indicator {
          border: 2px solid #267326;
          color: #267326; }
      @media only screen and (max-width: 767px) {
        :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator {
          
        padding:10px;
        padding-top:40px;
        width: 100%;
       
      }
        :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li a .label {
          display:inline-block !important;
        }
        :host ::ng-deep aw-wizard-navigation-bar.vertical {
          width:80% !important;
        }
        :host ::ng-deep aw-wizard.vertical .wizard-steps {
          width: 100% !important;
          max-width: 280px !important;
        }
        :host ::ng-deep aw-wizard-navigation-bar ul{
          background-color: white;
          border-radius: 10px;
        }
        .details{
          margin-left: 20px;
        }
      }
      
      
     
      .red{
        color:red !important;
      }