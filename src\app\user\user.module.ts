import { JobReferenceComponent } from './components/job-reference/job-reference.component';
import { NgModule } from '@angular/core';
import { SharedModule } from 'shared/shared.module';

import {
  AchievementsModalPreviewComponent,
} from './components/achievements-modal-preview/achievements-modal-preview.component';
import { AchievementsModalComponent } from './components/achievements-modal/achievements-modal.component';
import { AchievementsComponent } from './components/achievements/achievements.component';
import {
  CertificationsModalPreviewComponent,
} from './components/certifications-modal-preview/certifications-modal-preview.component';
import { CertificationsModalComponent } from './components/certifications-modal/certifications-modal.component';
import { CertificationsComponent } from './components/certifications/certifications.component';
import {
  ConferencesModalPreviewComponent,
} from './components/conferences-modal-preview/conferences-modal-preview.component';
import { ConferencesModalComponent } from './components/conferences-modal/conferences-modal.component';
import { ConferencesComponent } from './components/conferences/conferences.component';
import { ContactInfoPreviewComponent } from './components/contact-info-preview/contact-info-preview.component';
import { ContactInformationComponent } from './components/contact-information/contact-information.component';
import { CvWrapperComponent } from './components/cv-wrapper/cv-wrapper.component';
import { DrivingLicenseComponent } from './components/driving-license/driving-license.component';
import { DrivingPreviewComponent } from './components/driving-preview/driving-preview.component';
import { EducationModalPreviewComponent } from './components/education-modal-preview/education-modal-preview.component';
import { EducationModalComponent } from './components/education-modal/education-modal.component';
import { EducationComponent } from './components/education/education.component';
import { FullPreviewComponent } from './components/full-preview/full-preview.component';
import { HelperComponent } from './components/helper/helper.component';
import {
  HobbiesInterestsPreviewComponent,
} from './components/hobbies-interests-preview/hobbies-interests-preview.component';
import { HobbiesInterestsComponent } from './components/hobbies-interests/hobbies-interests.component';
import { LanguagesModalPreviewComponent } from './components/languages-modal-preview/languages-modal-preview.component';
import { LanguagesModalComponent } from './components/languages-modal/languages-modal.component';
import { LanguagesComponent } from './components/languages/languages.component';
import {
  MembershipsModalPreviewComponent,
} from './components/memberships-modal-preview/memberships-modal-preview.component';
import { MembershipsModalComponent } from './components/memberships-modal/memberships-modal.component';
import { MembershipsComponent } from './components/memberships/memberships.component';
import { ObjectivePreviewComponent } from './components/objective-preview/objective-preview.component';
import { ObjectiveComponent } from './components/objective/objective.component';
import {
  PersonalInformationPreviewComponent,
} from './components/personal-information-preview/personal-information-preview.component';
import { PersonalInformationComponent } from './components/personal-information/personal-information.component';
import { PortfoliosModalPreviewComponent } from './components/portfolios-modal-preview/portfolios-modal-preview.component';
import { PortfoliosModalComponent } from './components/portfolios-modal/portfolios-modal.component';
import { PortfoliosComponent } from './components/portfolios/portfolios.component';
import { ProjectsModalPreviewComponent } from './components/projects-modal-preview/projects-modal-preview.component';
import { ProjectsModalComponent } from './components/projects-modal/projects-modal.component';
import { ProjectsComponent } from './components/projects/projects.component';
import {
  PublicationsModalPreviewComponent,
} from './components/publications-modal-preview/publications-modal-preview.component';
import { PublicationsModalComponent } from './components/publications-modal/publications-modal.component';
import { PublicationsComponent } from './components/publications/publications.component';
import { ReferencesModalPreviewComponent } from './components/references-modal-preview/references-modal-preview.component';
import { ReferencesModalComponent } from './components/references-modal/references-modal.component';
import { ReferencesComponent } from './components/references/references.component';
import { ReumeComponent } from './components/reume/reume.component';
import { ResumeModalComponent } from './components/resume-modal/resume-modal.component';
import { SectionsManagementComponent } from './components/sections-management/sections-management.component';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { SkillsModalPreviewComponent } from './components/skills-modal-preview/skills-modal-preview.component';
import { SkillsModalComponent } from './components/skills-modal/skills-modal.component';
import { SkillsComponent } from './components/skills/skills.component';
import { SummaryPreviewComponent } from './components/summary-preview/summary-preview.component';
import { SummaryComponent } from './components/summary/summary.component';
import { TrainingModalPreviewComponent } from './components/training-modal-preview/training-modal-preview.component';
import { TrainingModalComponent } from './components/training-modal/training-modal.component';
import { TrainingComponent } from './components/training/training.component';
import { VolunteersModalPreviewComponent } from './components/volunteers-modal-preview/volunteers-modal-preview.component';
import { VolunteersModalComponent } from './components/volunteers-modal/volunteers-modal.component';
import { VolunteersComponent } from './components/volunteers/volunteers.component';
import {
  WorkExperienceModalPreviewComponent,
} from './components/work-experience-modal-preview/work-experience-modal-preview.component';
import { WorkExperienceModalComponent } from './components/work-experience-modal/work-experience-modal.component';
import { WorkExperienceComponent } from './components/work-experience/work-experience.component';
import { AchievementsService } from './cv-services/achievements.service';
import { CertificationsService } from './cv-services/certifications.service';
import { ConferencesService } from './cv-services/conferences.service';
import { ContactInfoService } from './cv-services/contact-info.service';
import { DrivingService } from './cv-services/driving.service';
import { EducationService } from './cv-services/education.service';
import { FullPreviewService } from './cv-services/full-preview.service';
import { HobbiesInterestsService } from './cv-services/hobbies-interests.service';
import { LanguageService } from './cv-services/language.service';
import { MembershipService } from './cv-services/membership.service';
import { ObjectiveService } from './cv-services/objective.service';
import { PersonalInformationService } from './cv-services/personal-information.service';
import { PortfoliosService } from './cv-services/portfolios.service';
import { ProjectsService } from './cv-services/projects.service';
import { PublicationsService } from './cv-services/publications.service';
import { ReferencesService } from './cv-services/references.service';
import { ResumeService } from './cv-services/resume.service';
import { SectionsManagementService } from './cv-services/sections-management.service';
import { SkillsService } from './cv-services/skills.service';
import { SummaryService } from './cv-services/summary.service';
import { TrainingsService } from './cv-services/trainings.service';
import { VolunteersService } from './cv-services/volunteers.service';
import { WorkExperiencesService } from './cv-services/work-experiences.service';
import { UserRoutingModule } from './user-routing.module';
import { CvTemplateModalComponent } from './components/cv-template-modal/cv-template-modal.component';

import { TemplatePreviewModalComponent } from './components/template-preview-modal/template-preview-modal.component';
import { TemplatesService } from './cv-services/templates.service';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { UserWrapperComponent } from './components/user-wrapper/user-wrapper.component';
import { CommonModule } from '@angular/common';
import { TooltipModule } from 'primeng/tooltip';
import { CheckboxModule, FileUploadModule, MessageService } from 'primeng';
import { AccountSettingsComponent } from './components/account-settings/account-settings.component';
import { UserAccountComponent } from './components/user-account/user-account.component';
import { InplaceModule } from 'primeng/inplace';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordStrengthMeterModule } from 'angular-password-strength-meter';
import { PrivacyComponent } from './components/privacy/privacy.component';
import { UploadCvWizardComponent } from './components/upload-cv-wizard/upload-cv-wizard.component';
import { ResumeUploadCvFileComponent } from './components/resume-upload-cv-file/resume-upload-cv-file.component';
import { PreviewUploadedCvComponent } from './components/preview-uploaded-cv/preview-uploaded-cv.component';
import { StepsModule } from 'primeng/steps';
// import { DragulaModule } from 'ng2-dragula';
import { SortablejsModule } from 'ngx-sortablejs';
import { EducationsFormComponent } from './components/upload-cv-wizard/educations/educations-form/educations-form.component';
import { WorkExpsFormComponent } from './components/upload-cv-wizard/work-exps-form/work-exps-form.component';

@NgModule({
  imports: [
    SharedModule,
    UserRoutingModule,
    PdfViewerModule,
    CommonModule,
    TooltipModule,
    InplaceModule,
    InputTextModule,
    //  FileUploadModule
    PasswordStrengthMeterModule,
    StepsModule,
    //  DragulaModule,
    SortablejsModule,
    CheckboxModule
  ],
  declarations: [
    SidebarComponent,
    HelperComponent,
    CvWrapperComponent,
    SummaryComponent,
    ObjectiveComponent,
    ReumeComponent,
    ResumeModalComponent,
    PersonalInformationComponent,
    ContactInformationComponent,
    ContactInfoPreviewComponent,
    SummaryPreviewComponent,
    ObjectivePreviewComponent,
    EducationComponent,
    PersonalInformationPreviewComponent,
    EducationModalComponent,
    EducationModalPreviewComponent,
    LanguagesComponent,
    LanguagesModalComponent,
    LanguagesModalPreviewComponent,
    DrivingLicenseComponent,
    DrivingPreviewComponent,
    AchievementsComponent,
    AchievementsModalComponent,
    AchievementsModalPreviewComponent,
    PublicationsComponent,
    PublicationsModalComponent,
    PublicationsModalPreviewComponent,
    ProjectsComponent,
    ProjectsModalComponent,
    ProjectsModalPreviewComponent,
    MembershipsComponent,
    MembershipsModalComponent,
    MembershipsModalPreviewComponent,
    VolunteersComponent,
    VolunteersModalComponent,
    VolunteersModalPreviewComponent,
    HobbiesInterestsComponent,
    HobbiesInterestsPreviewComponent,
    PortfoliosComponent,
    PortfoliosModalComponent,
    PortfoliosModalPreviewComponent,
    ConferencesComponent,
    ConferencesModalComponent,
    ConferencesModalPreviewComponent,
    CertificationsComponent,
    CertificationsModalComponent,
    CertificationsModalPreviewComponent,
    TrainingComponent,
    TrainingModalComponent,
    TrainingModalPreviewComponent,
    ReferencesComponent,
    ReferencesModalComponent,
    WorkExperienceComponent,
    WorkExperienceModalComponent,
    WorkExperienceModalPreviewComponent,
    ReferencesModalPreviewComponent,
    SkillsComponent,
    SkillsModalComponent,
    SkillsModalPreviewComponent,
    FullPreviewComponent,
    SectionsManagementComponent,
    CvTemplateModalComponent,
    TemplatePreviewModalComponent,
    UserWrapperComponent,
    AccountSettingsComponent,
    UserAccountComponent,
    PrivacyComponent,
    JobReferenceComponent,
    UploadCvWizardComponent,
    ResumeUploadCvFileComponent,
    EducationsFormComponent,
    PreviewUploadedCvComponent,
    WorkExpsFormComponent,
  ],
  providers: [
    PersonalInformationService,
    SummaryService,
    ObjectiveService,
    ContactInfoService,
    ResumeService,
    EducationService,
    LanguageService,
    DrivingService,
    AchievementsService,
    PublicationsService,
    ProjectsService,
    MembershipService,
    VolunteersService,
    HobbiesInterestsService,
    PortfoliosService,
    ConferencesService,
    CertificationsService,
    TrainingsService,
    ReferencesService,
    WorkExperiencesService,
    SkillsService,
    FullPreviewService,
    SectionsManagementService,
    TemplatesService,
    MessageService,
  ],
  exports: [
    ReumeComponent,
  ]
})
export class UserModule { }
