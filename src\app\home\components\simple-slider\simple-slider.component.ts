import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';

@Component({
  selector: 'simple-slider',
  templateUrl: './simple-slider.component.html',
  styleUrls: ['./simple-slider.component.css']
})
export class SimpleSliderComponent implements OnInit, OnDestroy {

  @Input() images: string[] = [];
  @Input() direction: 'left' | 'right' | 'up' | 'down' = 'left';
  @Input() intervals: number[] = [3000];

  currentIndex = 0;
  nextIndex = 1;
  intervalIndex = 0;
  animating = false;

  private timerId: any;

  ngOnInit() {
    if (this.images.length < 2) {
      console.warn('SimpleSliderComponent needs at least 2 images.');
      return;
    }

    this.scheduleNext();
  }

  ngOnDestroy() {
    if (this.timerId) {
      clearTimeout(this.timerId);
    }
  }

  private scheduleNext() {
    const delay = this.intervals[this.intervalIndex] || this.intervals[this.intervals.length - 1];
    this.timerId = setTimeout(() => this.startAnimation(), delay);
  }

  private startAnimation() {
    this.animating = true;

    setTimeout(() => {
      this.animating = false;
      this.currentIndex = this.nextIndex;
      this.nextIndex = (this.nextIndex + 1) % this.images.length;
      this.intervalIndex = (this.intervalIndex + 1) % this.intervals.length;
      this.scheduleNext();
    }, 1000);  // must match CSS transition time
  }

  getOutClass(): string {
    return `slide-${this.direction}-out`;
  }

  getInClass(): string {
    return `slide-${this.direction}-in`;
  }
}
