import { Component, OnInit } from '@angular/core';
import { MessageService } from 'primeng';
import { ActivatedRoute, Router } from '@angular/router';
import { UserAccountService } from '../../../user/cv-services/user-account.service';
import { FormB<PERSON>er, Validators, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { AuthService } from 'shared/shared-services/auth-service';
import { UserAccountValidators } from '../../../user/components/user-account/user-account.validators';
import { Subject } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { EmailValidator } from 'shared/validators/email.validators';
declare var $: any;

@Component({
  selector: 'app-company-account-settings',
  templateUrl: './company-account-settings.component.html',
  styleUrls: ['./company-account-settings.component.scss'],
  providers: [MessageService]
})
export class CompanyAccountSettingsComponent implements OnInit {
  companyId;
  username = '';
  accountData;
  companyAccountForm;
  
  countryCodeOptions;
  reasonsDeleteAccount;
  private ngUnsubscribe: Subject<any> = new Subject();
  
  email = '';
  errorEmailVerify = '';
  newEmail;
  passwordChangeMesg = "Change";
  mobile = {"country_code":"","mobile_number":""};
  newMobile = {"country_code":"","mobile_number":""};
  profileUrl = '';

  emailErrorMessage;
  emailConfirmErrorMessage;
  passwordErrorMessage;
  MobileErrorMessage;
  MobileConfirmErrorMessage;
  profileUrlErrorMessage;
  deleteAccountErrorMessage;

  emailState;
  mobileState;

  typeOldPassword= 'password';
  showOldPassword = false;

  typeNewPassword= 'password';
  showNewPassword = false;

  constructor(private route: ActivatedRoute,
    private userAccountService:UserAccountService,
    private fb:FormBuilder,
    private messageService: MessageService,
    private authService: AuthService,
    private router: Router,
    private title: Title,
    private translate: TranslateService) {
      if (localStorage.getItem('defaultLang')) {
        if(localStorage.getItem('defaultLang') === "1"){
          translate.setDefaultLang('en');
        }
        else if(localStorage.getItem('defaultLang') === "2"){
          translate.setDefaultLang('ar');
        }                 
      } 

      this.buildEmptyForm();
     }

  ngOnInit(): void {
    this.companyId = JSON.parse(localStorage.getItem("company_id"));
    this.title.setTitle('Employer Account Settings | CVeek');
    this.getUserAccountData();
    this.getInterfaceDDLData();
    this.setRoutingParams();
  }

  setRoutingParams(){
    this.route.parent.params.subscribe(res => {
    this.username = res['username'];
    });
  }

  buildEmptyForm(){
    this.companyAccountForm = this.fb.group({             
      email_group:this.fb.group({
        email : ['', [Validators.required , EmailValidator.isValidEmailFormat],
                UserAccountValidators.emailTaken(this.userAccountService)],
        // email : ['', [Validators.required , Validators.email],
        //         UserAccountValidators.emailTaken(this.userAccountService)],
        code : ['', Validators.required],
      }),
      password:this.fb.group({
        old_password : ['', [Validators.required , Validators.minLength(6), Validators.maxLength(20)]],
        new_password : ['', [Validators.required , Validators.minLength(6), Validators.maxLength(20)]],
      }),                       
      contact_number : this.fb.group({
        country_code : ['',Validators.required],
        mobile_number : ['',[Validators.required,UserAccountValidators.contactNumberValidator]],
        code:['',Validators.required],
        country_code_options:[''],
      } , { validator : UserAccountValidators.fullNumberValidator }),
      user_name:['',[Validators.required,Validators.pattern('^[0-9a-zA-Z-]+$')]],
      delete_account:this.fb.group({
        reason_delete_id:['',Validators.required],
        comment:['']
      }),
      delete_account_options:['']
      });
  }

  getUserAccountData(){
    this.userAccountService.getAccount(this.companyId).subscribe(account => {
      this.accountData = account['data'];
      this.email = this.accountData.email;
      
      if(this.accountData.new_mail){
        this.newEmail = this.accountData.new_mail;
        this.emailGroup['controls'].email.setValue(this.accountData.new_mail);
      }
      if(this.accountData.mobile_number){
        this.mobile.mobile_number = this.accountData.mobile_number;
        this.mobile.country_code = this.accountData.country_code;

         // added code , for confirm mobile
         this.contactNumber['controls'].mobile_number.setValue(this.accountData.mobile_number);
         this.contactNumber['controls'].country_code.setValue(this.accountData.country_code);
         this.contactNumber['controls'].country_code_options.setValue({"name":this.accountData.country.name,"id":this.accountData.country.id,"country_code":this.accountData.country.country_code});
      } 
      // commented code , for confirm mobile
      // if(this.accountData.new_mobile_number){
      //   this.newMobile.mobile_number = this.accountData.new_mobile_number;
      //   this.contactNumber['controls'].mobile_number.setValue(this.accountData.new_mobile_number);
      //   this.contactNumber['controls'].country_code.setValue(this.accountData.new_country_code);
      //   this.newMobile.country_code = this.accountData.new_country_code;
      //   this.contactNumber['controls'].country_code_options.setValue({"name":this.accountData.new_country.name,"id":this.accountData.new_country.id,"country_code":this.accountData.new_country.country_code});
      // }
      this.profileUrl = "cveek.com/i/c/" +  this.accountData.user_name;
    });
  }

  getInterfaceDDLData(){
    this.userAccountService.getDDLData().subscribe(res => {
      this.countryCodeOptions = res['country_code'];
      this.countryCodeOptions.unshift({'name': '' , 'id': '', 'country_code': ''});
      this.reasonsDeleteAccount = res['reasons_delete_account'];
    //  this.reasonsDeleteAccount.unshift({'id': '' , 'translated_languages_id': '', 'name': ''});
    });
  }

  verifyEmail(){
    if(this.emailGroup.controls['email'].valid){
      this.userAccountService.updateAccount({"email":this.emailGroup.controls['email'].value},this.companyId).subscribe(res => {      
        if(res['data']){
          this.newEmail = res['data'].new_mail;    
          this.messageService.add({ severity:'success', detail:'Verification code has been sent to your email,please check your email, this code will expire after five minutes',life:7000});
          this.emailState = "notConfirmed";
        //  this.userAccountService.changeEmailConfirmState(this.emailState);
        }
        else if(res['error']){
          this.emailErrorMessage = res['error'];
        }        
       } 
      );
    } 
    else if(this.emailGroup.controls['email'].errors) {
      if(this.emailGroup.controls['email'].errors.required){
        this.emailErrorMessage = "Required";
      }
    } 
  }

  confirmEmail(){
    if(this.emailGroup.controls['email'].errors || this.emailGroup.controls['code'].errors){
      if(this.emailGroup.controls['email'].errors && this.emailGroup.controls['code'].errors){
        if(this.emailGroup.controls['email'].errors.required && this.emailGroup.controls['code'].errors.required){
          this.emailConfirmErrorMessage = "Please insert your new email then insert the code";
        }
      }
      else {
        if(this.emailGroup.controls['email'].errors){
          if(this.emailGroup.controls['email'].errors.required){
            this.emailErrorMessage = "Please insert your new email";
          }
        }
        if(this.emailGroup.controls['code'].errors){
          if(this.emailGroup.controls['code'].errors.required){
            this.emailConfirmErrorMessage = "Please insert the code";
          }
        }
      }     
    }
    else{
      this.userAccountService.confirmEmail(this.emailGroup.value).subscribe(res => {
        if(res['data']){
          this.messageService.add({ severity:'success', detail:res['data'],life:7000});
          this.email = this.newEmail;         
          this.emailGroup.reset();     
          this.emailGroup['controls'].email.setValue('');
          this.emailGroup['controls'].code.setValue('');
          this.emailState = "confirmed";
          this.userAccountService.changeEmailConfirmState(this.emailState);
          this.closeNav();
          setTimeout(() => {
            this.authService.logout();
          }, 3000);
        }
        else if(res['error']){
          this.emailConfirmErrorMessage = res['error'];
        }
      }
      );
    }  
  }

  resendEmailCode(){
    if(this.emailGroup.controls['email'].errors){
      if(this.emailGroup.controls['email'].errors.required){
        this.emailErrorMessage = "Please insert your new email";
      }
      if(this.emailGroup.controls['email'].errors.email){
        this.emailErrorMessage = "";
      }
    }
    else{
      this.userAccountService.resendEmailCode({"email":this.emailGroup.controls['email'].value}).subscribe( res => {
        if(res['data']){
          this.messageService.add({ severity:'success', detail:'Verification code has been resent to you email,please check your email, this code will expire after five minutes',life:7000});
        }
        else if(res['error']){
          this.emailConfirmErrorMessage = res['error'];
        }      
      });
    }   
  }

  savePassword(){
    if(this.password.controls['old_password'].errors || this.password.controls['new_password'].errors){
      if(this.password.controls['old_password'].errors && this.password.controls['new_password'].errors){
        if(this.password.controls['old_password'].errors.required && this.password.controls['new_password'].errors.required){
          this.passwordErrorMessage = "Please insert your old and new password";
        }
      }
      else {
        if(this.password.controls['old_password'].errors){
          if(this.password.controls['old_password'].errors.required){
            this.passwordErrorMessage = "Please insert your old password";
          }
        }
        if(this.password.controls['new_password'].errors){
          if(this.password.controls['new_password'].errors.required){
            this.passwordErrorMessage = "Please insert your new password";
          }
        }
      }     
    }
    else{
      this.userAccountService.updateAccount(this.password.value,this.companyId).subscribe(res => {   
        if(res['data']){
          this.passwordChangeMesg = "Changed";
          this.password.reset();
          this.password['controls'].old_password.setValue('');
          this.password['controls'].new_password.setValue('');
          this.messageService.add({ severity:'success', detail:res['data'],life:7000}); 
          this.closeNav1();
          setTimeout(() => {
            this.authService.logout();
          }, 3000);
        }      
        else if(res['error']){
          this.passwordErrorMessage = res['error'];
        }
      });
    }
  }

  verifyMobileNumber(){
    if(this.contactNumber['controls'].country_code.value === '' || this.contactNumber['controls'].mobile_number.value === ''){
      this.MobileErrorMessage = "You should provide mobile number";
    }
    else if(!this.contactNumber['controls'].country_code.errors && !this.contactNumber['controls'].mobile_number.errors){
      this.userAccountService.updateAccount(this.contactNumber.value,this.companyId).subscribe(res => {
        if(res['data']){
          this.mobile.mobile_number = this.contactNumber.get('mobile_number').value;      
          this.mobile.country_code = this.contactNumber.get('country_code').value;   
          this.messageService.add({ severity:'success', detail:res['data'],life:7000}); 

           // commented code - for mobile confirmation
          // this.newMobile.mobile_number = res['data'].new_mobile_number;      
          // this.newMobile.country_code = res['data'].new_country_code;    
        //  this.messageService.add({ severity:'success', detail:'Verification code has been sent to your whatsapp number,this code will expire after five minutes',life:7000}); 
          this.mobileState = "notConfirmed";
        }
        else if(res['error']){
          this.MobileErrorMessage = res['error'];
        }
      });
    }  
  }

  confirmMobileNumber(){
    if(this.contactNumber['controls'].country_code.value === '' && this.contactNumber['controls'].mobile_number.value === ''
        && this.contactNumber['controls'].code.value === ''){
      this.MobileConfirmErrorMessage = "Please insert your new mobile number then insert the code";
    }
    else if(this.contactNumber['controls'].country_code.value === '' || this.contactNumber['controls'].mobile_number.value === ''){
      this.MobileConfirmErrorMessage = "Please insert your new mobile number";
    }
    else if(this.contactNumber['controls'].code.value === ''){
      this.MobileConfirmErrorMessage = "Please insert the code";
    }
    else {
      this.userAccountService.confirmMobileNumber(this.contactNumber.value).subscribe(res => {
        if(res['data']){       
          this.mobile.mobile_number = this.newMobile.mobile_number;
          this.mobile.country_code = this.newMobile.country_code;
          this.contactNumber.reset();
          this.contactNumber['controls'].country_code.setValue('');
          this.contactNumber['controls'].mobile_number.setValue('');
          this.contactNumber['controls'].code.setValue('');
          this.contactNumber['controls'].country_code_options.setValue('');
          this.messageService.add({ severity:'success', detail:res['data'],life:7000}); 
          this.mobileState = "confirmed";
          this.userAccountService.changeMobileConfirmState(this.mobileState);
          this.closeNav2();
        }
        else if(res['error']){
          this.MobileConfirmErrorMessage = res['error'];
        }
      });
    }   
  }

  resendMobileCode(){
    if(this.contactNumber['controls'].country_code.value === '' || this.contactNumber['controls'].mobile_number.value === ''){
      this.MobileConfirmErrorMessage = "Please insert your new mobile number";
    }
    else{
      this.userAccountService.resendMobileCode(this.contactNumber.value).subscribe( res => {
        if(res['data']){
          this.messageService.add({ severity:'success', detail:'Verification code has been sent to your whatsapp number,this code will expire after five minutes',life:7000});
        }
        else if(res['error']){
          this.MobileConfirmErrorMessage = res['error'];
        }      
      });
    }   
  }

  openProfileUrlModal(){
    if(this.companyAccountForm['controls'].user_name.errors){
      if(this.companyAccountForm['controls'].user_name.errors.required){
        this.profileUrlErrorMessage = "required";
      }    
    }
    else if(this.companyAccountForm['controls'].user_name.valid){
      $('#updateProfileUrlModal').modal('toggle');
    }
  }

  updateProfileUrl(){
    if(this.companyAccountForm['controls'].user_name.valid){
      this.userAccountService.updateAccount({"user_name":this.companyAccountForm.controls['user_name'].value},this.companyId).subscribe(res => {
        if(res['data']){
          this.profileUrl = "cveek.com/c/" +  res['data'].user_name;
          localStorage.setItem("username",res['data'].user_name);
          this.userAccountService.changeCompanyUsername(res['data'].user_name);
          this.messageService.add({ severity:'success', detail: 'Your profile url changed successfully' ,life:7000});
          this.closeNav3();
        //  this.location.replaceState('u/' + res['data'].user_name + '/account-settings');
        //  this.location.go('u/' + res['data'].user_name + '/account-settings');
          this.router.navigate(['c/',res['data'].user_name, 'account-settings']);
        }
        else if(res['error']){
          this.profileUrlErrorMessage = res['error'];
        }   
      });
    }   
  }


  deleteUserAccount(){
    if(this.deleteAccount['controls'].reason_delete_id.value !== ""){
      this.userAccountService.deleteAccount(this.companyId,this.deleteAccount.value).subscribe(res => {
        
        if(res['error']!=null){
          $('#deleteAccountModal').modal('hide');
          this.messageService.add({
            key:"company_installed_plugin",
            severity: "error",
            detail: res['error'],
            data:res['help'],
            life:7000
          });
        }

        if(res === "delete account"){
          $('#deleteAccountModal').modal('hide');
          this.messageService.add({ severity:'success', detail:'Your account deleted successfully',life:3000}); 
          setTimeout(() => {
            this.authService.clearAfterLogout();
            this.router.navigate(['']);
          }, 3500);       
        }   
      });
    }
    else {
      this.deleteAccountErrorMessage = "Required";
    }
    
  }

  get emailGroup() {
    return (this.companyAccountForm.controls['email_group'] as FormGroup);
  }
  get password() {
    return (this.companyAccountForm.controls['password'] as FormGroup);
  }
  get contactNumber() {
    return (this.companyAccountForm.controls['contact_number'] as FormGroup);
  }
  get deleteAccount() {
    return (this.companyAccountForm.controls['delete_account'] as FormGroup);
  }

  toggleShowPassword(field) {    
    if(field === 'old'){
      this.showOldPassword = !this.showOldPassword;
      if (this.showOldPassword) {
          this.typeOldPassword = 'text';
      } else {
          this.typeOldPassword = 'password';
      }
    }
    else{
      this.showNewPassword = !this.showNewPassword;
      if (this.showNewPassword) {
          this.typeNewPassword = 'text';
      } else {
          this.typeNewPassword = 'password';
      }
    }
    
  }

  CountryCodeChange(countryCode){
    this.contactNumber.controls['country_code'].setValue(countryCode.value.country_code);
    this.MobileErrorMessage = null;
  }
  deleteReasonChange(reason){
    this.deleteAccount.controls['reason_delete_id'].setValue(reason.value.id);
    this.deleteAccountErrorMessage = null;
  }

  openNav() {
    document.getElementById("mySidenav").style.width = "100%";
  }

  closeNav() {
    document.getElementById("mySidenav").style.width = "0";
  }
  openNav1() {
    document.getElementById("mySidenav1").style.width = "100%";
  }

  closeNav1() {
    document.getElementById("mySidenav1").style.width = "0";
  }
  openNav2() {
    document.getElementById("mySidenav2").style.width = "100%";
  }

  closeNav2() {
    document.getElementById("mySidenav2").style.width = "0";
  }
  openNav3() {
    document.getElementById("mySidenav3").style.width = "100%";
  }

  closeNav3() {
    document.getElementById("mySidenav3").style.width = "0";
  }
 
  ngOnDestroy(): void {
    if(this.emailState === "notConfirmed"){
      this.userAccountService.changeEmailConfirmState(this.emailState);
    }
    if(this.mobileState === "notConfirmed"){
      this.userAccountService.changeMobileConfirmState(this.mobileState);
    }
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
