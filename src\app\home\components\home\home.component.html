<confirm-message *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_EMPLOYER'"></confirm-message>

<app-pre-loader [show]="showLoader"></app-pre-loader>

<!-- add it to following div to lazyload youtube video
appResourcesLazyload -->

<div class="home-page-content">
    <un-auth-top-navbar *ngIf="role === 'unauth'" [inHomePage]="inHomePage"></un-auth-top-navbar>
    <user-topbar *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_ADMIN'|| role === 'ROLE_CONTACT_ADMIN'" [inHomePage]="inHomePage"></user-topbar>
    <company-topbar *ngIf="role === 'ROLE_EMPLOYER'" [inHomePage]="inHomePage"></company-topbar>

    <p-toast position="bottom-right"></p-toast>

    <div class="hero-section">
      <div class="inner-hero-section">
        <div class="row flex-row-all section-width">
          <div class="col-sm-5">
            <img 
            src="./assets/images/home/<USER>"
            fetchpriority="high"
            decoding="async"
            loading="eager"
            class="img-responsive"
            alt="welcome-to-cveek"
            (load)="showLoader = false;">
            <!-- <img  src="./assets/images/home/<USER>" class="img-responsive" alt="welcome-to-cveek" (load)="showLoader = false;"> -->
          </div>
          <div class="col-sm-7 hero-text-and-search">
            <div class="hero-text">
                <p>Where <span class="text-blue text-bold">Artificial Intelligence</span> Meets Human Ambition</p>
                <h1>Create Your CV<br> Follow Your <span class="smaller-blue-box">Passion</span><br> Get Hired</h1>
                <p><span class="text-bold">CVeek</span> empowers job seekers and employers with smart tools to build, hire, and grow globally, efficiently, and intelligently.</p>
            </div>
            <div class="job-search search-container">
              <job-search-form-top-bar [inAdvrsInterface]="false" [inHomePage]="inHomePage"></job-search-form-top-bar>
            </div>
          </div>
        </div>
      </div> 
    </div>

  
      <!-- End of Hero Section -->

    <div class="home-sections" *ngIf="!showLoader">

      <div class="jobs-by-location">  
        <div class="section section-width">
          <h2 class="text-gray">Jobs by location</h2>
          <h3 class="text-blue">Find you favorite job in different countries</h3>
          <br>
          <div class="container-fluid">
            <div class="row">
              <div class="col-md-3 col-sm-6 col-xs-12" *ngFor="let location of mainCountriesJobs">
                  <a [routerLink]="location.jobLink" class="image-grid text-center">
                    <img [src]="'./assets/images/home/'+location.imgName" class="img-responsive" [alt]="location.imgAlt">
                    <h4 class="text-gray">Job Opportunities in <span class="text-blue">{{location.countryName}}</span></h4>
                  </a>
              </div>
            </div>
          </div>
          <!-- <div *ngIf="isMobile; else desktopCarousel">
            <div class="image-grid">
              <ng-container *ngFor="let location of mobileMainCountriesJobs">
                <a [routerLink]="location.jobLink" class="text-center">
                  <img [src]="'./assets/images/home/'+location.imgName" class="img-responsive" [alt]="location.imgAlt">
                  <h4 class="text-gray">Job Opportunities in <span class="text-blue">{{location.countryName}}</span></h4>
                </a> 
              </ng-container>
            </div>
          </div>
          <ng-template #desktopCarousel>
            <p-carousel
              [value]="mainCountriesJobs" [numVisible]="4" [numScroll]="1"
              [responsiveOptions]="responsiveOptions" [autoplayInterval]="5000" [circular]="true"
              orientation="horizontal">
              <ng-template let-location pTemplate="item">
                <a [routerLink]="location.jobLink" class="text-center">
                  <img [src]="'./assets/images/home/'+location.imgName" class="img-responsive" [alt]="location.imgAlt">
                  <h4 class="text-gray">Job Opportunities in <span class="text-blue">{{location.countryName}}</span></h4>
                </a> 
              </ng-template>
            </p-carousel>
          </ng-template> -->

          <br><br>
          <div class="text-center">
              <button class="btn btn-orange" routerLink="/search-job">View all Locations</button>
          </div>
        </div>
      </div>

      <div class="build-cv-steps">  
        <div class="section section-width">
          <h2 class="text-center text-blue">Build a Professional CV in Minutes</h2>
          <br>
          <div class="row">
            <div class="col-md-4 col-sm-6 col-xs-12 text-center bottom-spacing-mob">
              <div class="step step1">
                <div class="row flex-row-all">
                  <div class="col-sm-12 col-xs-2">
                    <p class="number">01</p>
                  </div>
                  <div class="col-sm-12 col-xs-10 step-name-div">
                    <h3>Register Your Account</h3>
                  </div>
                </div>
                <p>Create your free CVeek account in seconds using just your email, then verify it, no complicated forms or subscriptions required. Instantly unlock access to our smart CV builder and global job search.</p>
              </div>
            </div>
            <div class="col-md-4 col-sm-6 col-xs-12 text-center bottom-spacing-med bottom-spacing-mob">
              <div class="step step2">
                <div class="row flex-row-all">
                  <div class="col-sm-12 col-xs-2">
                    <p class="number">02</p>
                  </div>
                  <div class="col-sm-12 col-xs-10 step-name-div">
                    <h3>Create or upload CV</h3>
                  </div>
                </div>
                <p>Use our intuitive builder to create your CV from scratch and choose from a variety of modern, professional templates, or simply upload your existing PDF. It's fast,flexible, and completely free.</p>
              </div>
            </div>
            <div class="col-md-4 col-sm-6 col-xs-12 text-center">
              <div class="step step1">
                <div class="row flex-row-all">
                  <div class="col-sm-12 col-xs-2">
                    <p class="number">03</p>
                  </div>
                  <div class="col-sm-12 col-xs-10 step-name-div">
                    <h3>Apply For Dream Jobs</h3>
                  </div>
                </div>
                <p>Once your CV is ready, explore thousands of job listings across multiple countries. Use our smart filters to find the perfect match and apply instantly, all from one seamless platform.</p>
              </div>
            </div>
          </div>
          
        </div>
      </div>

      <div class="">  
        <div class="section section-width">
          <div class="row">
            <div class="col-sm-6 col-xs-12 text-center">
              <img src="./assets/images/home/<USER>" class="img-responsive" alt="Trusted-by 40 countries"> 
            </div>
            <div class="col-sm-6 col-xs-12">
              <h2 class="text-blue">Trusted by People</h2>
              <h3 class="number">+40 Countries</h3>
              <br>
              <p>Join a thriving global community of professionals who trust CVeek to power their careers and hiring needs. From students to CEOs, from the Middle East to Europe and beyond - our platform is built for every culture. every profession, and every ambition. Whether you're searching for your dream job or building your dream team, you're not alone.</p>
              <br>
              <h3 class="text-orange">Be part of the future work</h3> 
              <p>Join thousands of users across the world who trust CVeek every day.</p> 
            </div>
          </div>
          <br><br>
          <div class="text-center">
              <button class="btn btn-blue" routerLink="/m/company/sign-up">Sign up now</button>
          </div>
        </div>
      </div>

      <div class="section section-width">
        <div class="masonry-section">
          <div class="masonry-col bottom-spacing-mob flex-row-all">
            <div style="width:100%;" class="order-mob-2 bottom-spacing">
              <simple-slider [images]="imagesSet1" [intervals]="[5000, 4000, 9000]" direction="left"></simple-slider>
            </div>    
            <div style="width:100%;" class="order-mob-1">
              <div class="bottom-spacing" style="padding:15px;border-radius: 20px;">
                <div class="branding">
                  <h2 class="headline">
                    <span class="global">Global</span>
                    <span class="job-line">
                      <span class="job">Job</span>
                      <span class="subheadline">Opportunities in Every Industry</span>
                    </span>
                  </h2>
                </div>
              </div>
              <div class="blue-box">
                <h3 style="margin:0;">Expore job listings tailored to your field - locally or globally</h3>
              </div>
            </div>
          </div>

          <div class="masonry-col bottom-spacing-mob">
            <div class="bottom-spacing">
              <simple-slider [images]="imagesSet2" [intervals]="[7000, 10000, 7000]" direction="right"></simple-slider>
            </div>
            <simple-slider [images]="imagesSet3" [intervals]="[3000, 9000, 5000]" direction="up"></simple-slider>
          </div>

          <div class="masonry-col">
            <div class="bottom-spacing">
              <simple-slider [images]="imagesSet4" [intervals]="[9000, 6000, 5000]" direction="down"></simple-slider>
            </div>
            <div class="blue-box">
              <ul>
                <li><a [routerLink]="['/search-job', 'f', 'IT-jobs-']">Information Technology</a></li>
                <li><a [routerLink]="['/search-job', 'f', 'Marketing-and-Sales-jobs-']">Marketing & Sales</a></li>
                <li><a [routerLink]="['/search-job', 'f', 'Administrative-Services-jobs-']">Administrative Services</a></li>
                <li><a [routerLink]="['/search-job', 'f', 'Management-Consultancy-jobs-']">Management Consultancy</a></li>
                <li><a [routerLink]="['/search-job', 'f', 'Finance-Banking-Insurance-jobs-']">Finance - Banking - Insurance</a></li>
                <li><a [routerLink]="['/search-job', 'f', 'Hospitality-Hotels-Food-Services-jobs-']">Hospitality - Hotels - Food Services</a></li>
                <li><a [routerLink]="['/search-job', 'f', 'Oil-Gas-Energy-jobs-']">Oil, Gas & Energy</a></li>
              </ul>
            </div>
          </div>
        </div>
            

      </div>


 </div> <!-- End Home Sections div -->
</div>

<div class="flex-space-fix"></div>
<app-footer [inHomePage]="true"></app-footer>


<div class="modal fade" id="authModal" tabindex="-1" role="dialog" aria-labelledby="authLabelModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="authLabelModal" translate>Job Seeker Sign In</h4>
            </div>
            <login [fromPage]="'home'"></login>
        </div>
    </div>
</div>