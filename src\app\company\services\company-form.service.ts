import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from 'rxjs';
import { Observable } from 'rxjs/Observable';


@Injectable()
export class CompanyFormService {
  private edit = new BehaviorSubject(false);
  currentedit = this.edit.asObservable();
  private ok = new BehaviorSubject([]);
  Data = this.ok.asObservable();
  private New = new BehaviorSubject(false);
  newPro = this.New.asObservable();
  url = '';
  dataUrl = '';
  newUrl = '';
  deleUrl = '';
  chPro = '';
  allData = '';
  language;
  
  constructor(private http: HttpClient , private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url = data + 'company/profile';
      this.dataUrl = data + 'company/company_data';
      this.newUrl = data + 'company/company_profile_by_language_id';
      this.deleUrl = data + 'company/profile';
      this.chPro = data + 'company/show_company_languages';
      this.allData = data + 'company/profile/all_languages'
    });

  }

  changeStatus(edit_status: boolean) {
    this.edit.next(edit_status);
  }

  send_Data(value) {

    this.ok.next(value);
  }

  NProfile(new_pro: boolean) {
    this.New.next(new_pro);
  }

 //dropdown data
  getCompanyFormData(companyId) {
    console.log('url', this.dataUrl + '/' + companyId) ;
    return this.http.get(this.dataUrl + '/' + companyId);

  }

  getCompanyFormDataNew(current_language, companyId) {
    const params = new  HttpParams().set('language_id', current_language);

    let lang;
    lang = JSON.stringify(current_language);
    return this.http.get(this.dataUrl + '/' + companyId, { params });
  }

  //upload and get data
  addCompanyForm(CompanyInformation) {
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url, JSON.stringify(CompanyInformation), {headers});
  }

  getCompanyFormNew(langId, company_id) {
    console.log('langID', langId);
    return this.http.get(this.newUrl + '/' + company_id + '/' + langId);
  }
  
  getCompanyFormMain(company_id) {
    console.log('url', this.url + '/' + company_id);
    return this.http.get(this.url + '/' + company_id);
  }

  getCompanyForm(company_id, current_langauge) {

    const params = new  HttpParams().set('translated_languages_id', current_langauge);
    console.log('url', this.url + '/' + company_id , { params });
    return this.http.get(this.url + '/' + company_id, { params });
  }

  editCompanyForm(companyInformation, companyProfileId) {
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + '/' + companyProfileId, JSON.stringify(companyInformation), {headers});
  }

  deleteCompanyForm(company_id,langId) {
    const params = new  HttpParams().set('translated_languages_id', langId);
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.delete(this.deleUrl + '/' + company_id,{ params });
  }

  //Check Profiles
  checkProfiles(companyId) {
    return this.http.get(this.chPro + '/' + companyId);
  }

  //get all profiles

  getProfiles(companyId) {
    return this.http.get(this.allData + '/' + companyId);
  }


}


