<app-pre-loader [show]="loader"></app-pre-loader>
<div class="home-page-content">
  <div class="container">
    <div>
      <form
        #form="ngForm"
        [formGroup]="loginForm"
        class="form-horizontal validate-form"
        (ngSubmit)="login()">
        <br>
        <h1>Employer Sign in</h1>
        <br>
        <div class="row" *ngIf="resetMessage">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            <div class="alert alert-danger alert-dismissible" role="alert" *ngIf="resetMessage">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              {{resetMessage}}
              <a class="help-link" *ngIf="helpLink" routerLink={{helpLink}}>Sign in</a>
            </div>
          </div>
        </div>
        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && isInvalid('email')) || (form.submitted && isInvalidSyn('email') && loginForm.controls['email'].value) || (clickForgetPassword && isInvalidSyn('email') && loginForm.controls['email'].value) || (clickForgetPassword && isInvalid('email'))}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':loginForm.controls['email'].value}">
            <input  type="email" formControlName="email" class="form-control" id="exampleInputEmail2" >
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Email address</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="(form.submitted && isInvalid('email')) || (clickForgetPassword && isInvalid('email'))">Required</span>
            <span class="error-message " *ngIf="(form.submitted && isInvalidSyn('email') && loginForm.controls['email'].value) || (clickForgetPassword && isInvalidSyn('email') && loginForm.controls['email'].value)">Invalid Email Syntax</span>
          </div>
        </div>
        <div class="form-group focus-container has-feedback" [ngClass]="{'has-error': form.submitted && (isInvalid('password') || isInvalidMin('password') || isInvalidMax('password'))}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':loginForm.controls['password'].value}">
            <input  type="{{ type }}" formControlName="password" class="form-control" id="exampleInputPassword2" >
            <span type="button" 
              (click)="toggleShowPassword()"
              class="fa fa-fw password-eye-icon"  
              [ngClass]="{
                'fa-eye' : show,
                'fa-eye-slash' : !show
              }">
            </span>
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Password</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('password')">Required</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidMin('password')">Too Short Password</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidMax('password')">Too Long Password</span>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            <a routerLink="/m/company/forget-password">Forget Password?</a>
            <!-- <a (click)="forgetPassword()" style="cursor:pointer">Forget Password?</a> -->
          </div>
        </div>
        <div class="text-center form-group">
          <button type="submit"  class="btn btn-success">Sign in</button>

          <div class="or-container">
            <hr>
            <div class="or-text">
              <span>OR</span>
            </div>
          </div>
          
          <div class="social-login-btns">
            <button class="btn btn-default facebook-btn" (click)="$event.preventDefault();facebookLogin()"><i class="fa fa-facebook"></i> Sign in with Facebook</button> &nbsp;
            <!-- <button class="btn btn-default google-btn" (click)="$event.preventDefault();signinWithGoogle()">Sign in with Google <i class="fa fa-google"></i></button> -->
            <div id="googleLoginButtonDiv"></div> 
          </div>
        </div>
        <div class="row">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            New to CVeek?<a  routerLink="/m/company/sign-up" routerLinkActive="active"> Join Now</a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
