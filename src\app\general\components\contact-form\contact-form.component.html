<div *ngIf="mainCat.length === 0 && subCat.length === 0" class="row">
  <div  class="col-md-offset-5 col-md-2">
    <p-progressSpinner  fill="transparent"   animationDuration="1s"></p-progressSpinner>
  </div>
</div>



  <div *ngIf="mainCat.length !== 0 && subCat.length !== 0" class="container-contact100">
		<div class="wrap-contact100">
			<form [formGroup]="contactForm"  class="contact100-form validate-form">
        <i class="fa fa-phone-alt"></i>
        <span class="contact100-form-title" translate>
					 contactUs.ContactUs
				</span>


				<div class="wrap-input100 validate-input" >
					<input class="input100" formControlName="email" type="text" name="email" placeholder="E-mail">

          <div *ngIf="email.touched && email.invalid" >
            <div *ngIf="email.errors.required" class="alert alert-danger wrap-input100 validate-input alert-validate" translate>contactUs.errorMessages.Required</div>
            <div *ngIf="email.errors.email" class="alert alert-danger wrap-input100 validate-input alert-validate" translate>contactUs.errorMessages.InvalidEmail</div>
          </div>
        </div>


        <div *ngIf="visitor" class="form-group type-form-group">
          <div class="row ">
            <!-- <div class=" col-sm-4 col-xs-4 ">
              <label for="type" class="control-label" translate>contactUs.Type<span *ngIf="type.touched && type.invalid" class="required">**</span></label>
            </div> -->
            <div class="col-sm-6 col-xs-6">
              <label class="container radio-choose" for="job-seeker" translate>contactUs.JobSeeker
                <input type="radio" formControlName="email_role"  id="job-seeker" value="ROLE_JOB_SEEKER">
                <span class="checkmark"></span>
              </label>
            </div>
            <div class="col-sm-6 col-xs-6">
              <label class="container radio-choose" for="employer" translate>contactUs.Employer
                <input type="radio" formControlName="email_role"  id="employer" value="ROLE_EMPLOYER">
                <span class="checkmark"></span>
              </label>
            </div>
          </div>
          <div *ngIf="email_role.touched && email_role.invalid" >
            <div *ngIf="email_role.errors.required" class="alert alert-danger wrap-input100 validate-input alert-validate"  translate>contactUs.errorMessages.Required</div>
         </div>
        </div>

        <!--start of main cat -->
        <div class="dropdown-input wrap-input100 validate-input">
          <div class="form-group cat-form-group" title="" >
            <!-- <label for="main-category" translate>contactUs.MainCategory<span *ngIf="contact_main_cat_id.touched && contact_main_cat_id.invalid" class="required">**</span></label> -->
            <p-dropdown [options]="mainCat[currentLangId - 1]" formControlName="contact_main_cat_id" [required]="true"  id="main_cat" name="main_cat" placeholder="{{ 'contactUs.MainCategory' | translate}}"  (onChange)="filter()">
              <ng-template let-category pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;color:#8f8fa1;font-family: 'Exo2-Regular', sans-serif;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
          </div>

        </div>
        <div *ngIf="contact_main_cat_id.touched && contact_main_cat_id.invalid" >
          <div *ngIf="contact_main_cat_id.errors.required" class="alert alert-danger wrap-input100 validate-input alert-validate" style="margin-top:-30px;"  translate>contactUs.errorMessages.Required</div>
        </div>
        <!--end of main cat -->


       <!--start of sub cat -->
       <div  *ngIf="contact_main_cat_id.value !== null && filteredSubCats[currentLangId - 1].length > 1">
				<div class="dropdown-input wrap-input100 validate-input">
					<div  class="form-group cat-form-group" title="" >
            <!-- <label for="sub-category" translate>contactUs.SubCategory<span *ngIf="contact_sub_cat_id.touched && contact_sub_cat_id.invalid" class="required">**</span></label> -->
            <p-dropdown [options]="filteredSubCats[currentLangId - 1]" formControlName="contact_sub_cat_id"  [required]="filteredSubCats[currentLangId - 1].length > 1" placeholder="{{ 'contactUs.SubCategory' | translate }}" id="sub_cat" name="sub_cat" >
              <ng-template let-category pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
          </div>
        </div>
        <div *ngIf="contact_sub_cat_id.touched && contact_sub_cat_id.invalid" >
          <div *ngIf="contact_sub_cat_id.errors.required" class="alert alert-danger wrap-input100 validate-input alert-validate" style="margin-top:-30px;"  translate>contactUs.errorMessages.Required</div>
        </div>
        </div>
        <!--end of sub cat -->

        <!-- start of msg -->
				<div class="wrap-input100 validate-input" >
					<textarea class="input100"  formControlName="message"  name="message" placeholder="Your Message"></textarea>

          <div *ngIf="message.touched && message.invalid" >
            <div *ngIf="message.errors.required" class="alert alert-danger wrap-input100 validate-input alert-validate" translate>contactUs.errorMessages.Required</div>
          </div>
				</div>
        <!-- end of msg -->



        <p-messages [severity]="severity" *ngIf="displayAlert" (click)="hideAlert()" showTransitionOptions="0.5s ease-out" hideTransitionOptions="0.5s ease-in" closable="true">
          <ng-template pTemplate>
              <span class="custom-message" translate>{{ "contactUs." +  msg }}</span>
          </ng-template>
        </p-messages>


				<div class="container-contact100-form-btn">
					<button type="button" pButton   class="ui-button-success ui-button-danger"  (click)="send(contactForm.value)"  class="contact100-form-btn" [disabled]="contactForm.invalid">
						<span style="padding-left: 22px;" translate>
							<i class="fa fa-paper-plane-o m-r-6" aria-hidden="true"></i>
							contactUs.Send
						</span>
					</button>
        </div>



        <!-- <p> {{ contactForm.value | json }} </p> -->

        <!-- <span *ngFor="let lang of languagesArray"  class="languages">
          <button class="btn"  translate [class.btn-primary]="lang.id === currentLangId" (click)="changeLang(lang.id)" translate>{{ "contactUs.languages."+ lang.name }}</button>
        </span> -->

      </form>

		</div>
	</div>





