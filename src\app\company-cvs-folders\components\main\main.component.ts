import { Component, OnInit } from '@angular/core';
import { GeneralService } from '../../../general/services/general.service';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.css']
})
export class MainComponent implements OnInit {
  firstLoad:boolean = true;
  foldersCollapsed = window.innerWidth <= 1000;

  constructor(private generalService:GeneralService) { }

  ngOnInit(): void {
    if(window.innerWidth > 1180){
      this.foldersCollapsed = false;
    }
    else{
      this.foldersCollapsed = true;
    }

    this.generalService.internalMessage.subscribe( (data) => {
      //when cvs table data ready, stop page loader
      if (data['message'] === 'stop-loader' && data['src'] === 'cvs-table') {
        this.firstLoad = data['mData'].firstLoad;
      }

      // to check if mobile and folders not collapsed state , then collapse it after click
      // if (data['message'] === 'collapseFolders' && data['src'] === 'cvs-folders') {
      //   this.foldersCollapsed = true;
      //   this.generalService.notify('expandedChanged' , 'main' , 'cvs-folders' , {'expanded' : false}) ;
      // }

      //in mobile we get toggle event from page-navbar
      if (data['message'] === 'toggleFolders' && data['src'] === 'filters-wrapper') {
        this.toggleFolders();
      }
    });
  }

  toggleFolders() {
    this.foldersCollapsed = !this.foldersCollapsed;
    if(this.foldersCollapsed === true){
      this.generalService.notify('expandedChanged' , 'main' , 'folder-list' , {'expanded' : false}) ;
    }
  }

}
