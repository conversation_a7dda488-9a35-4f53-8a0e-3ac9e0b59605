<!-- <company-topbar></company-topbar> -->
<page-navbar [username]="username" [navType]="'companyProfile'" (outData)="getProfiles_route()"></page-navbar>
<!-- <div class="page-navbar">
    <ul id="page-navbar-list">
        <li style="cursor: pointer;">
            <a (click)="getProfiles_route()" ><i style="font-size: 19px;cursor: pointer;margin-right:6px" class="fa fa-info"></i> <span>Company Profile Info</span></a>
        </li>
        <li>  
            <a routerLink='location'><i style="margin-right:6px" class="fa fa-building"></i> <span>Location</span></a>
        </li>
        <li>
            <a routerLink='#'><i style="margin-right:6px" class="fa fa-group"></i> <span>Public Preview</span></a>
        </li>
    </ul>
</div> -->

<router-outlet></router-outlet>


