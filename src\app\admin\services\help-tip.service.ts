import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { Observable } from 'rxjs/observable';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { HelpTip } from '../models/help-tip';

@Injectable()
export class HelpTipService {

  url = '';
  baseUrl = '';
  public faqsCount = null;
  private hTipSource = new BehaviorSubject<HelpTip>({  id: null, section_id: null, section: '', field: '',
   field_id: null, active: false, langId: null, description: '' });
  newHelpTip: Observable<HelpTip> = this.hTipSource.asObservable();

  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.url     = data + 'admin/helpField';
          this.baseUrl = data;
    });
  }


  getSectionsFields() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + 'admin/get_all_sections_fields', { headers });
  }

  refreshHTValue(ht) {
    this.hTipSource.next(ht);
  }


  activateHtip(body, id: number) {
    console.log('body, id', JSON.stringify(body), id );
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/active/' + id, JSON.stringify(body), { headers });
  }

  // getHTCategories(langId: number) {
  //   let headers = new HttpHeaders().set('Content-Type', 'application/json');
  //   return this.http.get(this.url + '/categories/' + langId, { headers });
  // }

  gethelpTip( htId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + htId + '/get' , { headers });
  }

  gethtInSpecificLang( htId: number, langId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + htId + '/' + langId, { headers });
  }

  getHelpTips() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url, { headers });
  }

  createHTip( ht ) {
    // console.log('after json.stringify', JSON.stringify(ht));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url , JSON.stringify(ht), { headers } );

  }

  // addHTTranslation(ht) {
  //   console.log('after json.stringify', JSON.stringify(ht));
  //   let headers = new HttpHeaders().set('Content-Type', 'application/json');
  //   return this.http.post(this.url , JSON.stringify(ht), { headers } );
  // }

  updateHTip( ht , id) {
    console.log('after json.stringify', JSON.stringify(ht));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + '/' + id , JSON.stringify(ht), { headers });
  }

  deleteHTip(id) {
    console.log('id', id);
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.delete(this.url + '/' + id, { headers } );
  }


}
