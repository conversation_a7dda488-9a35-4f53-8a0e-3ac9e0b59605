<div *ngIf="!loading && formBuilt">
<div *ngIf="languagesArray.length !== 0">
<!-- start of university form -->
<div *ngIf="(mode === 'create' || mode === 'edit') && type === 'Institution' && languagesArray.length !== 0 ">
  <form [formGroup]="itemForm" >

    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New {{ type }}</caption>
      <!-- <caption  *ngIf="templateMode === 'update'"  class="text-center table-caption">Edit Template</caption> -->
      <tbody>
        <tr>
          <th>URL</th>
          <td colspan="8" class="border-right">
            <input type="text" name="url" formControlName="url" />
            <div *ngIf="url.errors !== null">
              <span class="alert alert-danger inline-alert" *ngIf="url.touched&& url.errors.required">required</span>
              <span class="alert alert-danger inline-alert" *ngIf="url.value && url.errors?.invalidUrlError" translate>{{url.errors?.invalidUrlError}}</span>
            </div>
          </td>
        </tr>
        <tr>
          <th> <div *ngIf="mode === 'create'">Add</div><div *ngIf="mode === 'edit'">Change</div> Location
            <!-- <button type="button" class="btn btn-primary"> -->
              <!-- <i class="fa fa-plus" title="add new location" (click)="AddLocation()"></i> -->
            <!-- </button> -->
          </th>
          <td colspan="8" class="border-right" >
              <input type="text" name="location" formControlName="location_temp" #googleLocation
              (keyup.enter)="currentLocationKeyUp($event);loading = true" />
             <!-- <input type="text" name="location" formControlName="location_temp" #locationn
               (keyup.enter)="currentLocationKeyUp($event);loading = true" /> -->
          </td>
        </tr>
        <ng-container  formGroupName="location">
          <tr hidden>
            <th>Country</th>
            <td>
              <input type="text"   name="country" formControlName="country"  />
              <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.country.touched && locationGroup.controls.country.invalid">required</span>
            </td>
          </tr>
          <tr hidden >
            <th>City</th>
            <td>
              <input type="text"   name="city" formControlName="city" />
              <!-- <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.city.touched && locationGroup.controls.city.invalid">required</span> -->
            </td>
          </tr>
          <tr hidden>
            <th>Country Code</th>
            <td>
              <input type="text"   name="country_code" formControlName="country_code" />
              <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.country_code.touched && locationGroup.controls.country_code.invalid">required</span>
            </td>
          </tr>
          <tr hidden>
            <th>Street Address</th>
            <td>
              <input type="text"   name="street_address" formControlName="street_address" />
            </td>
          </tr>
          <tr hidden>
            <th>Postal Code</th>
            <td>
              <input type="text" hidden name="postal_code" formControlName="postal_code" />
            </td>
          </tr>
          <tr hidden>
            <th>Longitude</th>
            <td>
              <input type="text"  hidden name="longitude" formControlName="longitude" />
            </td>
          </tr>
          <tr hidden>
            <th>Latitude</th>
            <td>
              <input type="text"  hidden name="latitude" formControlName="latitude" />
            </td>
          </tr>
        </ng-container>

        <tr>
          <th></th>
          <td colspan="8">
            <!-- <div class="custom-row clearfix" >
              <div class="col-sm-8  focus-no-padding">
                <app-map [sub]="currentSub" birthOrCurrent="current" [type]="currentType" [search]="locationnRef"></app-map>
              </div>
            </div> -->
          </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="institution_trans" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="institution_trans.controls[i].controls.name.touched && institution_trans.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit" *ngIf="mode === 'create'"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)"><i class="fa fa-plus" style="color:white"></i>Add </button>
        <button type="submit" *ngIf="mode === 'edit'"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)"><i class="fa fa-save" style="font-size: 1em;"></i>Save</button>
      </div>
    </div>
  </form>
</div>
<!-- end of university form -->


<!-- start of location form -->
<div *ngIf="(mode === 'location'  && type === 'Institution' ) ">
  <form [formGroup]="itemForm" >

    <table  class="table">
      <tbody>
       <tr>
          <th> Change Location
            <!-- <button type="button" class="btn btn-primary"> -->
              <!-- <i class="fa fa-plus" title="add new location" (click)="AddLocation()"></i> -->
            <!-- </button> -->
          </th>
          <td colspan="8" class="border-right" >
            <div> <input type="text" name="locationn" formControlName="location_temp" #locationn (keyup.enter)="currentLocationKeyUp($event); loading = true;" /></div>
          </td>
        </tr>
         <ng-container formGroupName="location">
              <tr hidden>
                <th>Country</th>
                <td>
                  <input disabled="true" type="text" name="country" formControlName="country"  />
                  <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.country.touched && locationGroup.controls.country.invalid">required</span>
                </td>
              </tr>
              <tr hidden>
                <th>City</th>
                <td>
                  <input disabled="true" type="text" name="city" formControlName="city" />
                  <!-- <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.city.touched && locationGroup.controls.city.invalid">required</span> -->
                </td>
              </tr>
              <tr hidden>
                <th>Country Code</th>
                <td>
                  <input disabled="true" type="text" name="country_code" formControlName="country_code" />
                  <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.country_code.touched && locationGroup.controls.country_code.invalid">required</span>
                </td>
              </tr>
              <tr hidden>
                <th>Street Address</th>
                <td>
                  <input disabled="true" type="text"  name="street_address" formControlName="street_address" />
                  <!-- <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.street_address.touched && locationGroup.controls.street_address.invalid">required</span> -->
                </td>
              </tr>
              <tr hidden>
                <th>Postal Code</th>
                <td>
                  <input type="text" disabled="true" name="postal_code" formControlName="postal_code" />
                  <!-- <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.postal_code.touched && locationGroup.controls.postal_code.invalid">required</span> -->
                </td>
              </tr>
              <tr hidden>
                <th>Longitude</th>
                <td>
                  <input type="text" disabled="true" name="longitude" formControlName="longitude" />
                  <!-- <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.longitude.touched && locationGroup.controls.longitude.invalid">required</span> -->
                </td>
              </tr>
              <tr hidden>
                <th>Latitude</th>
                <td>
                  <input type="text" disabled="true" name="latitude" formControlName="latitude" />
                  <!-- <span class="alert alert-danger inline-alert" *ngIf="locationGroup.controls.latitude.touched && locationGroup.controls.latitude.invalid">required</span> -->
                </td>
              </tr>
         </ng-container>


          <tr>
            <th></th>
            <td colspan="8">
              <!-- <div class="custom-row clearfix" >
                <div class="col-sm-8  focus-no-padding">
                  <app-map [sub]="currentSub" birthOrCurrent="current" [type]="currentType" [search]="locationn"></app-map>
                </div>
              </div> -->
            </td>
          </tr>

     </tbody>
    </table>

    <div class="col-md-12">
      <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
    </div>
    <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
      <button type="submit"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="saveLocation(itemForm.value)"><i class="fa fa-save" style="font-size: 1em;"></i>Save</button>
    </div>
  </form>
</div>
<!-- end of location form -->


<!-- start of job title form -->
<div *ngIf="(mode === 'create' || mode === 'edit') && type === 'JobTitle' && languagesArray.length !== 0 ">
  <ul *ngIf="mode === 'create'"  class="nav nav-tabs">
    <li  role="presentation" class="btn" [class.active]="addOneItem"
        (click)="addOneItem = true" translate>Add New Job Title</li>
    <li  role="presentation" class="btn" [class.active]="!addOneItem"
    (click)="addOneItem = false" translate>Add Multiple  job Titles</li>
  </ul>
  <form *ngIf="addOneItem" [formGroup]="itemForm" >
    <table  class="table">
      <tbody>
        <tr>
          <th>Job Title Major</th>
          <td colspan="8">
            <p-dropdown [options]="majors"  formControlName="major_job_title"  [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <!-- <span class="alert alert-danger inline-alert" *ngIf="major_job_title.touched && major_job_title.invalid">required</span> -->
          </td>
        </tr>
        <tr >
          <th> Experience Fields
            <!-- <button type="button" class="btn btn-primary">
              <i class="fa fa-plus" title="add new experience field" ></i>
            </button> -->
          </th>
          <td colspan="8"  >
             <p-multiSelect [options]="experienceFields" [(ngModel)]="selectedFields"  formControlName="experience_fields"  [panelStyle]="{minWidth:'13em'}" [required]="true">
              <ng-template let-field pTemplate="item">
                  <div style="font-size:14px;float:right;margin-top:4px">{{field.label}}</div>
              </ng-template>
            </p-multiSelect>
             <span class="alert alert-danger inline-alert" *ngIf="experience_fields.touched && experience_fields.invalid">required</span>
           </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr >
          <th>Name</th>
          <ng-container formArrayName="job_title_trans" *ngFor="let lang of languagesArray; let i = index;">
            <td colspan="4" >
              <ng-container [formGroupName]="i">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="job_title_trans.controls[i].controls.name.touched && job_title_trans.controls[i].controls.name.invalid">required</span>
              </ng-container>
            </td>
          </ng-container>
        </tr>

        <!-- *ngIf="job_title_synonyms_trans.length !== 0" -->
        <tr>
          <th style="padding-top: 15px;">Job Title Synonyms <i class="fa fa-plus" title="add new synonym" (click)="AddNewItem()" ></i></th>
          <td></td>
        </tr>
          <ng-container *ngIf="job_title_synonyms_trans.length !== 0" formArrayName="job_title_synonyms_trans" >
            <tr *ngFor="let synonym of job_title_synonyms_trans.controls; let j = index;">
              <th></th>
              <ng-container  [formArrayName]="j" *ngFor="let lang of languagesArray; let i = index;"    style="padding-top: 15px;">
                  <td colspan="4">
                    <ng-container [formGroupName]="i" >
                      <input type="text"  formControlName="name"    name="synonym"  />
                    </ng-container>
                  </td>
                  <!-- <span class="alert alert-danger inline-alert" *ngIf="job_title_synonyms_trans.controls[i].controls.name.touched && job_title_synonyms_trans.controls[i].controls.name.invalid">required</span> -->
               </ng-container>
               <button *ngIf="j !== 0" class="btn btn-danger"  title="remove this synonym" (click)="removeSynonym(j)"><i class="fa fa-trash"></i></button>
            </tr>
          </ng-container>
        <tr >
          <th> Upload Job Title Synonyms</th>
          <td colspan="8"  style="padding-top: 40px;">

           <p-fileUpload  #fileUpload name="uploadedFiles" customUpload="true"
           (uploadHandler)="onFileChange($event, fileUpload)"
           accept=".xlsx" maxFileSize="2000000" auto="true"  [chooseLabel]="label_option">
            <!-- <ng-template pTemplate="content">
              <ul *ngIf="uploadedFiles.length"  style="float: right;margin-top: -60px;font-weight: 600;font-style: italic;">
                  <li *ngFor="let file of uploadedFiles">{{file.name}} - {{file.size}} bytes
                  <button class="btn btn-danger" style="padding: 5px 12px;margin-top: 6px; margin-left: 10px;" *ngIf="label_option === 'Change File'" type="button"
                    title="Delete File" (click)="confirmDelete()"><i class="fa fa-trash"></i></button>
                </li>
              </ul>
            </ng-template> -->
          </p-fileUpload>

           <!-- <p-confirmDialog *ngIf="displayConfirm" [style]="{width: '100%'}" key="positionDialog" [position]="position" ></p-confirmDialog> -->
           <p-messages [style] = "{width: '100%'}" ></p-messages>
          </td>
        </tr>
        <tr>
          <th></th>
          <td colspan="8" ><p class="note">
            <img src="assets/images/xl.png" style="height: 200px; width:auto;"  alt="excel file example" /> <br>
            <i class="fa fa-info-circle"></i>: Expected file type: <b>Excel(.xlsx)</b>, required columns: <span *ngFor="let lang of languagesArray"> <span>column {{lang.id }} for {{ lang.name }} translation, </span></span></p></td>
        </tr>
        <!-- <tr>
          <th></th>
          <td colspan="8" style="padding-bottom:20px"><img src="" /></td>
        </tr> -->
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>

  <form *ngIf="!addOneItem" [formGroup]="multiItemsForm" >
    <table  class="table">
      <tbody>
        <tr>
          <th>Job Title Major</th>
          <td colspan="8">
            <p-dropdown [options]="majors"  formControlName="major_job_title"  [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <!-- <span class="alert alert-danger inline-alert" *ngIf="major_job_title.touched && major_job_title.invalid">required</span> -->
          </td>
        </tr>
        <tr >
          <th> Experience Fields
            <!-- <button type="button" class="btn btn-primary">
              <i class="fa fa-plus" title="add new experience field" ></i>
            </button> -->
          </th>
          <td colspan="8"  >
             <p-multiSelect [options]="experienceFields" [(ngModel)]="selectedFields"  formControlName="experience_fields"  [panelStyle]="{minWidth:'13em'}" [required]="true">
              <ng-template let-field pTemplate="item">
                  <div style="font-size:14px;float:right;margin-top:4px">{{field.label}}</div>
              </ng-template>
            </p-multiSelect>
             <span class="alert alert-danger inline-alert" *ngIf="experience_fields.touched && experience_fields.invalid">required</span>
           </td>
        </tr>
         <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>

        <!-- *ngIf="job_titles_trans.length !== 0" -->
          <tr>
            <th style="padding-top: 15px;">Job Titles <i class="fa fa-plus"  title="add new job title" (click)="AddNewItem()"></i></th>
            <td></td>
          </tr>
            <ng-container *ngIf="job_titles_trans.length !== 0" formArrayName="job_titles_trans" >
              <tr *ngFor="let synonym of job_titles_trans.controls; let j = index;">
                <th></th>
                <ng-container  [formArrayName]="j" *ngFor="let lang of languagesArray; let i = index;"    style="padding-top: 15px;">
                    <td colspan="4">
                      <ng-container [formGroupName]="i" >
                        <input type="text"  formControlName="name"    name="job_title"  />
                      </ng-container>
                    </td>
                    <!-- <span class="alert alert-danger inline-alert" *ngIf="job_titles_trans.controls[i].controls.name.touched && job_titles_trans.controls[i].controls.name.invalid">required</span> -->
                 </ng-container>
                <button *ngIf="j !== 0" class="btn btn-danger"  title="remove this synonym" (click)="removeItem(j)" ><i class="fa fa-trash"></i></button>
              </tr>
            </ng-container>
        <tr >
          <th> Upload Job Titles</th>
          <td colspan="8"  style="padding-top: 40px;">
           <p-fileUpload  #fileUpload name="uploadedFiles" customUpload="true"
             (uploadHandler)="onFileChange($event, fileUpload)"
           accept=".xlsx" maxFileSize="2000000" auto="true"  [chooseLabel]="label_option">
              <ng-template pTemplate="content">
                <ul *ngIf="uploadedFiles2.length"  style="float: right;margin-top: -60px;font-weight: 600;font-style: italic;">
                    <li *ngFor="let file of uploadedFiles2">{{file.name}} - {{file.size}} bytes
                    <!-- <button class="btn btn-danger" style="padding: 5px 12px;margin-top: 6px; margin-left: 10px;" *ngIf="label_option === 'Change File'" type="button"
                      title="Delete File" (click)="confirmDelete()"><i class="fa fa-trash"></i></button> -->
                  </li>
                </ul>
              </ng-template>
            </p-fileUpload>
           <p-messages [style] = "{width: '100%'}" ></p-messages>
          </td>
        </tr>

        <tr>
          <th></th>
          <td colspan="8" ><p class="note">
            <img src="assets/images/xl.png" style="height: 200px; width:auto;"  alt="excel file example" /> <br>
            <i class="fa fa-info-circle"></i>: Expected file type: <b>Excel(.xlsx)</b>, required columns: <span *ngFor="let lang of languagesArray"> <span>column {{lang.id }} for {{ lang.name }} translation, </span></span></p></td>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="multiItemsForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>

</div>
<!-- end of job title form -->

<!-- start of Education Field form -->
<div *ngIf="(mode === 'create' || mode === 'edit') && type === 'EducationField' && languagesArray.length !== 0 ">
  <!-- <ul *ngIf="mode === 'create'"  class="nav nav-tabs">
    <li  role="presentation" class="btn" [class.active]="!addOneItem"
      (click)="addOneItem = false" translate>Add New Major Education Field</li>
    <li  role="presentation" class="btn" [class.active]="addOneItem"
      (click)="addOneItem = true" translate>Add New Education Field</li>
  </ul> -->

  <!-- <form   *ngIf="!addOneItem" [formGroup]="multiItemsForm" >
      <table  class="table">
        <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New  Major Education Field </caption>
        <tbody>
          <tr>
            <th></th>
            <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
          </tr>
          <tr>
            <th>Name</th>
            <ng-container formArrayName="maj_education_field_trans" *ngFor="let lang of languagesArray; let i = index;">
              <ng-container [formGroupName]="i">
                <td colspan="4" class="border-right">
                  <input type="text" formControlName="name"  name="name" />
                  <span class="alert alert-danger inline-alert" *ngIf="major_parent_transs.controls[i].controls.name.touched && major_parent_transs.controls[i].controls.name.invalid">required</span>
                </td>
              </ng-container>
            </ng-container>
          </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
        </div>
        <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
          <button type="submit"   class="btn btn-done submit-button" [disabled]="multiItemsForm.invalid" (click)="addMajorParent(multiItemsForm.value)">
            <i class="fa fa-plus" style="color:white"></i> Add
          </button>
        </div>
      </div>
    </form> -->

  <form  *ngIf="addOneItem" [formGroup]="itemForm" >

    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New {{ type }}</caption>
     <!-- <caption  *ngIf="templateMode === 'update'"  class="text-center table-caption">Edit Template</caption> -->
      <tbody>
        <tr>
          <th> Education Field</th>
          <td colspan="8" class="border-right">
              <!-- [(ngModel)]="selectedFields"   -->
              <p-multiSelect 
                  [options]="majorEducationField"  
                  formControlName="majors_education_field"  
                  [panelStyle]="{minWidth:'13em'}" 
                  [required]="true">
                  <ng-template let-field pTemplate="item">
                      <div style="font-size:14px;float:right;margin-top:4px">{{field.label}}</div>
                  </ng-template>
                </p-multiSelect>
                 <!-- <span class="alert alert-danger inline-alert" *ngIf="majors_education_field.touched && majors_education_field.invalid">required</span> -->
          </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="education_field_trans" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="education_field_trans.controls[i].controls.name.touched && education_field_trans.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"   class="btn btn-done submit-button" [disabled]="education_field_trans.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
        <!-- <button type="submit"   class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)">
            <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
            <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
          </button> -->
      </div>
    </div>
  </form>

</div>
<!-- end of Education Field form -->


<!-- start of comp form -->
<!-- <div *ngIf="(mode === 'create' || mode === 'edit') && type === 'CompanyIndustry' && languagesArray.length !== 0 ">
  <ul *ngIf="mode === 'create'"  class="nav nav-tabs">
    <li  role="presentation" class="btn" [class.active]="addOneItem"
        (click)="addOneItem = true" translate>Add New Company Industry</li>
    <li  role="presentation" class="btn" [class.active]="!addOneItem"
    (click)="addOneItem = false" translate>Add New Company Industry Parent</li>
  </ul>
  <form *ngIf="addOneItem" [formGroup]="itemForm" >

    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New Company Industry </caption>
      <tbody>
        <tr>
          <th>Company Industry Parent</th>
          <td colspan="8" class="border-right">
            <p-dropdown [options]="compInParents"  formControlName="company_industry_parent_id" [required]="true" [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <span class="alert alert-danger inline-alert" *ngIf="company_industry_parent_id.touched && company_industry_parent_id.invalid">required</span>
          </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="company_industry_trans" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="company_industry_trans.controls[i].controls.name.touched && company_industry_trans.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>
  <form   *ngIf="!addOneItem" [formGroup]="multiItemsForm" >
    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New  Company Industry Parent </caption>
      <tbody>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="company_industry_trans" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="company_industry_transs.controls[i].controls.name.touched && company_industry_transs.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"   class="btn btn-done submit-button" [disabled]="multiItemsForm.invalid" (click)="addCompanyIndustryParent(multiItemsForm.value)">
          <i class="fa fa-plus" style="color:white"></i> Add
        </button>
      </div>
    </div>
  </form>
</div> -->
<!-- end of comp form -->



<!-- start of Skill form -->
<div *ngIf="(mode === 'create' || mode === 'edit') && type === 'Skill' && languagesArray.length !== 0 ">
  <ul *ngIf="mode === 'create'"  class="nav nav-tabs">
    <li  role="presentation" class="btn" [class.active]="addOneItem"
        (click)="addOneItem = true" translate>Add New Skill</li>
    <li  role="presentation" class="btn" [class.active]="!addOneItem"
    (click)="addOneItem = false" translate>Add Multiple Skills</li>
  </ul>
  <form *ngIf="addOneItem" [formGroup]="itemForm" >
    <table  class="table">
      <tbody>
        <tr>
          <th>Skill Category</th>
          <td colspan="8">
            <p-dropdown [options]="skillCats"  formControlName="skill_category_id" [required]="true" [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <span class="alert alert-danger inline-alert" *ngIf="skill_category_id.touched && skill_category_id.invalid">required</span>
          </td>
        </tr>
        <tr >
          <th>Job Titles
            <!-- <button type="button" class="btn btn-primary">
              <i class="fa fa-plus" title="add new experience field" ></i>
            </button> -->
          </th>
          <td colspan="8"  >
             <p-multiSelect [options]="jobTitles" [(ngModel)]="selectedFields"  formControlName="job_titles"  [panelStyle]="{minWidth:'13em'}" [required]="true">
              <ng-template let-field pTemplate="item">
                  <div style="font-size:14px;float:right;margin-top:4px">{{field.label}}</div>
              </ng-template>
            </p-multiSelect>
             <span class="alert alert-danger inline-alert" *ngIf="job_titles.touched && job_titles.invalid">required</span>
           </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr >
          <th>Name</th>
          <ng-container formArrayName="skill_trans" *ngFor="let lang of languagesArray; let i = index;">
            <td colspan="4" >
              <ng-container [formGroupName]="i">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="skill_trans.controls[i].controls.name.touched && skill_trans.controls[i].controls.name.invalid">required</span>
              </ng-container>
            </td>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>

  <form *ngIf="!addOneItem" [formGroup]="multiItemsForm" >
    <table  class="table">
      <tbody>
        <tr>
          <th>Skill Category</th>
          <td colspan="8">
            <p-dropdown [options]="skillCats"  formControlName="skill_category_id"  [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <!-- <span class="alert alert-danger inline-alert" *ngIf="skill_category_id.touched && skill_category_id.invalid">required</span> -->
          </td>
        </tr>
         <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
          <tr>
            <th style="padding-top: 15px;">Skills <i class="fa fa-plus"  title="add new Skill" (click)="AddNewItem()"></i></th>
            <td></td>
          </tr>
            <ng-container *ngIf="skill_transs.length !== 0" formArrayName="skill_trans" >
              <tr *ngFor="let s of skill_transs.controls; let j = index;">
                <th></th>
                <ng-container  [formArrayName]="j" *ngFor="let lang of languagesArray; let i = index;"    style="padding-top: 15px;">
                    <td colspan="4">
                      <ng-container [formGroupName]="i" >
                        <input type="text"  formControlName="name"    name="skill"  />
                      </ng-container>
                    </td>
                    <!-- <span class="alert alert-danger inline-alert" *ngIf="job_titles_trans.controls[i].controls.name.touched && job_titles_trans.controls[i].controls.name.invalid">required</span> -->
                 </ng-container>
                <button *ngIf="j !== 0" class="btn btn-danger"  title="remove this line" (click)="removeItem(j)" ><i class="fa fa-trash"></i></button>
              </tr>
            </ng-container>
        <tr >
          <th> Upload Skills</th>
          <td colspan="8"  style="padding-top: 40px;">
           <p-fileUpload  #fileUpload name="uploadedFiles" customUpload="true"
             (uploadHandler)="onFileChange($event, fileUpload)"
           accept=".xlsx" maxFileSize="2000000" auto="true"  [chooseLabel]="label_option">
              <ng-template pTemplate="content">
                <ul *ngIf="uploadedFiles2.length"  style="float: right;margin-top: -60px;font-weight: 600;font-style: italic;">
                    <li *ngFor="let file of uploadedFiles2">{{file.name}} - {{file.size}} bytes
                    <!-- <button class="btn btn-danger" style="padding: 5px 12px;margin-top: 6px; margin-left: 10px;" *ngIf="label_option === 'Change File'" type="button"
                      title="Delete File" (click)="confirmDelete()"><i class="fa fa-trash"></i></button> -->
                  </li>
                </ul>
              </ng-template>
            </p-fileUpload>
           <p-messages [style] = "{width: '100%'}" ></p-messages>
          </td>
        </tr>

        <tr>
          <th></th>
          <td colspan="8" ><p class="note">
            <img src="assets/images/xl.png" style="height: 200px; width:auto;"  alt="excel file example" /> <br>
            <i class="fa fa-info-circle"></i>: Expected file type: <b>Excel(.xlsx)</b>, required columns: <span *ngFor="let lang of languagesArray"> <span>column {{lang.id }} for {{ lang.name }} translation, </span></span></p></td>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="multiItemsForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>

</div>
<!-- end of Skill form -->



<!-- start of experiene field form -->
<div *ngIf="(mode === 'create' || mode === 'edit') && type === 'ExperienceField' && languagesArray.length !== 0 ">
  <ul *ngIf="mode === 'create'"  class="nav nav-tabs">
    <li  role="presentation" class="btn" [class.active]="addOneItem"
        (click)="addOneItem = true" translate>Add New Experience Field</li>
    <li  role="presentation" class="btn" [class.active]="!addOneItem"
    (click)="addOneItem = false" translate>Add Experience Field Parent</li>
  </ul>
  <form *ngIf="addOneItem" [formGroup]="itemForm" >

    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New Experience Field </caption>
      <tbody>
        <tr>
          <th>Experience Field Parent</th>
          <td colspan="8" class="border-right">
            <p-dropdown [options]="experienceFields"  formControlName="major_experience_field_id" [required]="true" [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <span class="alert alert-danger inline-alert" *ngIf="major_experience_field_id.touched && major_experience_field_id.invalid">required</span>
          </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="experience_field_translation" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="experience_field_trans.controls[i].controls.name.touched && experience_field_trans.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>

  <form *ngIf="!addOneItem" [formGroup]="multiItemsForm" >

    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New Experience Field Major </caption>
      <tbody>
        <!-- <tr>
          <th>Experience Field Parent</th>
          <td colspan="8" class="border-right">
            <p-dropdown [options]="experienceFields"  formControlName="major_experience_field_id" [required]="true" [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <span class="alert alert-danger inline-alert" *ngIf="major_experience_field_id.touched && major_experience_field_id.invalid">required</span>
          </td>
        </tr> -->
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="experience_field_translation" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="experience_field_transs.controls[i].controls.name.touched && experience_field_transs.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="multiItemsForm.invalid" (click)="save(multiItemsForm.value)">
          <span  ><i class="fa fa-plus" style="color:white"></i>Add </span>
        </button>
      </div>
    </div>
  </form>
</div>
<!-- end of experience field form -->


<!-- start of CompanySpecialty form -->
<!-- <div *ngIf="(mode === 'create' || mode === 'edit') && type === 'CompanySpecialty' && languagesArray.length !== 0 ">
  <form [formGroup]="itemForm" >

    <table  class="table">
      <caption *ngIf="!openedInAModal" class="text-center table-caption">Add New Company Specialty</caption>
      <tbody>
        <tr>
          <th>Company Industry</th>
          <td colspan="8" class="border-right">
            <p-dropdown [options]="compIndustries"  formControlName="company_industry_id" [required]="true" [filter]="true">
              <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <span class="alert alert-danger inline-alert" *ngIf="company_industry_id.touched && company_industry_id.invalid">required</span>
          </td>
        </tr>
        <tr>
          <th></th>
          <ng-container *ngFor="let lang of languagesArray"><th colspan="4" style="text-align:center"><span class="badge badge-primary">{{ lang.name }}</span></th></ng-container>
        </tr>
        <tr>
          <th>Name</th>
          <ng-container formArrayName="company_specialty_trans" *ngFor="let lang of languagesArray; let i = index;">
            <ng-container [formGroupName]="i">
              <td colspan="4" class="border-right">
                <input type="text" formControlName="name"  name="name" />
                <span class="alert alert-danger inline-alert" *ngIf="company_specialty_trans.controls[i].controls.name.touched && company_specialty_trans.controls[i].controls.name.invalid">required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <div *ngIf="viewNotific"  style="color:green; font-size:18px; margin-left:200px">{{ notification }}</div>
      </div>
      <div class="col-md-4 col-md-offset-4 col-sm-4 col-md-offset-4 ">
        <button type="submit"  class="btn btn-done submit-button" [disabled]="itemForm.invalid" (click)="save(itemForm.value)">
          <span  *ngIf="mode === 'create'"><i class="fa fa-plus" style="color:white"></i>Add </span>
          <span *ngIf="mode === 'edit'" ><i class="fa fa-save" style="font-size: 1em;"></i>Save</span>
        </button>
      </div>
    </div>
  </form>
</div> -->
<!-- end of CompanySpecialty form -->


<div *ngIf="languagesArray.length">
    <!-- <p >{{ multiItemsForm.value | json }}</p> -->
  <p *ngIf="addOneItem">{{ itemForm.value | json }}</p>
  <p *ngIf="!addOneItem">{{ multiItemsForm.value | json }}</p>
</div>
</div>

</div>
<p-progressSpinner *ngIf="loading === true"></p-progressSpinner>
