import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class PublicPreviewService {
  baseUrl = '';
  url = '';

  constructor(private  http: HttpClient , private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data;
      this.url = data + 'company/public_preview';
    });
  }

  getPreviewData(company_id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + company_id, {headers});
  }

  getPreviewDataByUsername(username: string) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    let params = new HttpParams();
    params = params.append('by_username', "1");
    return this.http.get(this.url + '/' + username, {headers,params: params});
  }

  applyToCompany(company_id: number){
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.baseUrl + 'emp_application?company_id=' +company_id, {headers});
  }

}
