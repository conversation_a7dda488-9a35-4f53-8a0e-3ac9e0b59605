import {Component, OnInit, OnDestroy, Input, Output, EventEmitter} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Subject} from 'rxjs/Subject';
import 'rxjs/add/operator/takeUntil';
import {SkillsService} from '../../cv-services/skills.service';
import { HelpTipsService } from '../../cv-services/help-tips.service';

import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
declare var $: any;

@Component({
  selector: 'app-skills-modal',
  templateUrl: './skills-modal.component.html',
  styleUrls: ['./skills-modal.component.css']
})
export class SkillsModalComponent implements OnInit {

  skillsDD:any;
  skillForm ;
  skills = [];
  skillTypes = [];
  skillLevels = [];
  //filteredSkillTypes = [];

  skillLevelValues = [
    {'value': '', 'label': ''},
    {'value': 'Beginner', 'label': 'Beginner'},
    {'value': 'Elementary', 'label': 'Elementary'},
    {'value': 'Intermediate', 'label': 'Intermediate'},
    {'value': 'Upper Intermediate', 'label': 'Upper Intermediate'},
    {'value': 'Advanced', 'label': 'Advanced'},
    {'value': 'Proficiency', 'label': 'Proficiency'},
  ];
  
  private ngUnsubscribe: Subject<any> = new Subject();
  resumeIdField: number;
  skillFormData;
  @Input('skill_item') skill_item: any;
 // @Input('skill_Types') skill_Types: any;
  @Input('skill_Levels') skill_Levels: any;
  @Input('resumeLang') resumeLang: number;
  @Output() closeModalPopup = new EventEmitter();

  constructor(private  fb: FormBuilder,
              private skillService: SkillsService,
              private helpTipService:HelpTipsService,
              private lazyloadDropdownService:LazyloadDropdownService) {
    this.skillForm = this.fb.group({
      resume_id : [this.resumeIdField ? this.resumeIdField : '', Validators.required],
      'skill_types' : ['' , Validators.required],
      // 'skill_type': this.fb.group({
      //   'name': ['']
      // }),
      'skill_level' : [''],
      'skill_level_id' : ['',]
    });
  }

  ngOnInit() {
    this.helpTipService.nextHelpTip("");
    this.skillFormData = this.skill_item;
    this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,this.resumeLang);
   // this.skillTypes = this.skill_Types;
    this.skillLevels = this.skill_Levels;
    this.buildFilledForm();
  }

  buildFilledForm() {
    this.resumeIdField = this.skillFormData.resume_id;
    this.skillForm = this.fb.group({
      resume_id : [this.resumeIdField ? this.resumeIdField : '', Validators.required],
      'skill_types': [this.skillFormData.skill_types,Validators.required],
      // 'skill_types_id' : [{'skill_type_id': this.skillFormData.skill_types_id,
      //   'name': this.skillFormData.skill_type.skill_type_trans[0].name} , Validators.required],
      // 'skill_type': this.fb.group({
      //   'name': ['']
      // }),
      'skill_level' : [this.skillFormData.skill_level_id ? {'skill_level_id': this.skillFormData.skill_level_id,
        'name': this.skillFormData.skill_level.skill_level_translation[0].name} : ''],
      'skill_level_id' : [this.skillFormData.skill_level_id,]
    });
  }

  selectSkillLevelChange(skillLevel) {
    this.skillForm.controls['skill_level_id'].setValue(skillLevel.value.skill_level_id);
  }

  // get skillTypeControl() {
  //   return this.skillForm.controls['skill_type'] as FormGroup;
  // }

  // selectSkillType() {
  //   this.skillForm.controls['skill_types_id'].setValue(this.skillForm.controls['skill_types_id'].value.skill_type_id);
  // }

  // keyUpSkillType() {
  //   if (this.skillForm.controls['skill_types_id'].value.name) {
  //     this.skillTypeControl.controls['name'].setValue(this.skillForm.controls['skill_types_id'].value.name);
  //   } else {
  //     this.skillTypeControl.controls['name'].setValue(this.skillForm.controls['skill_types_id'].value);
  //   }
  //   this.skillForm.controls['skill_types_id'].setValue(-1);
  // }

  submit(form) {
    if (this.skillForm.valid) {
      // if (this.skillForm.controls['skill_types_id'].value.skill_type_id) {
      //   this.selectSkillType();
      // } else {
      //   this.keyUpSkillType();
      // }
      const sendData = this.skillForm.value;
      this.skillService.updateSkill(sendData , this.skill_item.id).subscribe(res => {
        this.closeModalPopup.emit({'new': res['data'] , 'old': this.skill_item});
      });
      $('#myModal').modal('hide');

    }
  }

  // skillId(skillId) {
  //   let skill_id = this.skillTypes.filter( type =>
  //     type.id === skillId);
  //   skill_id = skill_id[0].id;
  //   return skill_id;
  // }

  // skillName(skillId) {
  //   let skill_name = this.skillTypes.filter( type =>
  //     type.id === skillId);
  //   skill_name = skill_name[0].name;
  //   return skill_name;
  // }
  // skillObject(skillId){
  //   let skill_object = this.skillTypes.filter( type =>
  //     type.id === skillId);
  //   skill_object = skill_object[0];
  //   return skill_object;
  // }

  // filterSkills(event) {
  //   this.filteredSkillTypes = [];
  //   for (let i = 0 ; i < this.skillTypes.length; i++) {
  //     let skillType = this.skillTypes[i].skill_type_trans[0];
  //     if (skillType['name'].toLowerCase().indexOf(event.query.toLowerCase()) == 0) {
  //       this.filteredSkillTypes.push(skillType);
  //     }
  //   }
  // }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
