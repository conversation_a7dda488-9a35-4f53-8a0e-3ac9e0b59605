
import { <PERSON>mponent, OnInit, ViewChild, ElementRef, ChangeDetectorRef, ViewChildren, QueryList } from '@angular/core';
import { FormGroup, Validators, FormBuilder, AbstractControl, FormArray, FormControl, ValidationErrors } from '@angular/forms';
import { PersonalInformationValidators } from '../personal-information/personal-information.validators';
import { TranslateService } from '@ngx-translate/core';
import { ContactInfoValidators } from '../contact-information/contact-info.validators';
import { UploadCvPdfService } from '../../cv-services/upload-cv-pdf.service';
import { Place } from 'shared/Models/place';
import { DataMap } from 'shared/Models/data_map';
import { GeneralService } from '../../../general/services/general.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ResumeService } from '../../cv-services/resume.service';
import { forkJoin, Subject } from 'rxjs';
import { MenuItem } from 'primeng/api';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
import { EmailValidator } from 'shared/validators/email.validators';
import { MessageService } from 'primeng';
import { EducationsFormComponent } from './educations/educations-form/educations-form.component';

declare var $: any;
@Component({
  selector: 'app-upload-cv-wizard',
  templateUrl: './upload-cv-wizard.component.html',
  styleUrls: ['./upload-cv-wizard.component.css'],
  providers: [MessageService]
})
export class UploadCvWizardComponent implements OnInit {
  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private uploadCvPdfService: UploadCvPdfService,
    private cdRef: ChangeDetectorRef,
    private route: ActivatedRoute,
    private resumeService: ResumeService,
    private generalService: GeneralService,
    private lazyloadDropdownService: LazyloadDropdownService,
    private messageService: MessageService,
    private router: Router
  ) {
    this.setRoutingParams();
  }
  userResumeId: number;
  resumeId: number;
  uploadedFileName: string;
  username: string;
  languageId: number;

  showLoader: boolean = true;
  steps: MenuItem[];
  currentStep: number = 0;

  personalForm: FormGroup;
  skillsLangsForm: FormGroup;
  uploadPdfForm: FormGroup;

  yearOpts = [];
  monthOpts = [
    { 'value': '1', 'label': 'January', 'code': '1' },
    { 'value': '2', 'label': 'February', 'code': '2' },
    { 'value': '3', 'label': 'March', 'code': '3' },
    { 'value': '4', 'label': 'April', 'code': '4' },
    { 'value': '5', 'label': 'May', 'code': '5' },
    { 'value': '6', 'label': 'June', 'code': '6' },
    { 'value': '7', 'label': 'July', 'code': '7' },
    { 'value': '8', 'label': 'August', 'code': '8' },
    { 'value': '9', 'label': 'September', 'code': '9' },
    { 'value': '10', 'label': 'October', 'code': '10' },
    { 'value': '11', 'label': 'November', 'code': '11' },
    { 'value': '12', 'label': 'December', 'code': '12' }
  ];
  dayOpts = [];
  of30DaysMonths = ['4', '6', '9', '11'];

  // dropdown options arrays
  nationalityOpts = [];
  // phoneTypeOpts = [];
  countryCodeOpts = [];
  languagesOpts = [];
  languagesLevelsOpts = [];
  skillsLevelsOpts = [];
  skillsDD: any;
  resumeLang: number;
  CvAiExtract: any;
  @ViewChild('currentSearch') currentSearch: ElementRef;
  @ViewChild('currentSearch') public currentSearchRef: ElementRef;
  @ViewChildren('currentSearch') currentSearchInputs: QueryList<ElementRef>;
  @ViewChild(EducationsFormComponent) educationsFormComponent: EducationsFormComponent;

  educations: any[] = [];
  school_education_fields: any[] = [];
  university_education_fields: any[] = [];
  degreeLevelValues: any[] = [];

  data_map = new DataMap();
  private ngUnsubscribe: Subject<any> = new Subject();

  //profile pic variables
  perPhotoSrc: string = "./assets/images/Capture.PNG";
  noProfilePicSet: string = "./assets/images/Capture.PNG";
  uploadedFiles: any[] = [];
  imgError: string = null;
  image_code_to_send: { file: string, file_type: string, is_deleted: boolean } = { file: '', file_type: '', is_deleted: false }
  uploadLabelDisplay = true;
  ptoastDisplayLink = '';

  workExps: any[] = [];
  exp_fields: any[] = [];
  employment_types: any[] = [];



  ngOnInit(): void {
    this.initializeForms();
    this.setResumeIdAndGetData();

    this.steps = [
      { label: 'Upload Your CV', command: () => this.currentStep = 0 },
      { label: 'Personal Information', command: () => this.currentStep = 1 },
      { label: 'Skills & Languages', command: () => this.currentStep = 2 },
      { label: 'Educations', command: () => this.currentStep = 3 },
      { label: 'Work Experience', command: () => this.currentStep = 4 },
    ];
    // this.uploadCvPdfService.getDropdownData(this.resumeId).subscribe(res => {
    //   console.log(res);
    //   this.nationalityOpts = res['nationalities'];
    //   this.phoneTypeOpts = res['phone_types'];
    //   this.countryCodeOpts = res['countries'];
    //   this.languagesOpts = res['languages'];
    //   this.languagesLevelsOpts = res['languages_level'];
    //   this.skillsLevelsOpts = res['skills_level'];
    // });

    for (let day = 1; day <= 31; day++) {
      this.dayOpts.push({ 'value': day.toString(), 'label': day.toString() });
    }
    let currentYear = (new Date()).getFullYear();
    for (let year = currentYear; year >= 1918; year--) {
      this.yearOpts.push({ 'value': year.toString(), 'label': year });
    }

    // init dropdown first time till i get resume language
    // because if it didn't initialized here it will throw error in console
    this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService, 'skills', 10, this.resumeLang);


    // important note: messageService don't work with generalService(the service we use to pass messages between components)
    // so we used localStorage to pass the message in this case
    if (localStorage.getItem("activeCVMsg")) {
      let activeCVMsg = JSON.parse(localStorage.getItem("activeCVMsg"));
      this.ptoastDisplayLink = activeCVMsg.link;
      setTimeout(() => {
        this.messageService.add({ severity: 'warn', detail: activeCVMsg.msg, life: 15000 });
      }, 3000);
      localStorage.removeItem('activeCVMsg');
      setTimeout(() => {
        // if (this.currentSearch && this.currentSearch.nativeElement) {
        this.getPlaceAutocomplete('current');
        // }
      }, 5000);
    }

  }
  ngAfterViewInit() {
    this.initGooglePlaces();
    if (this.currentSearchInputs) {
      this.currentSearchInputs.changes.subscribe(() => {
        this.initGooglePlaces();
      });
    }
  }

  setRoutingParams() {
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
    this.route.params.subscribe(res => {
      this.userResumeId = res['resumeId'];
    });
  }

  setResumeIdAndGetData() {
    this.resumeService.getGlobalResumeId(this.userResumeId).switchMap(res => {
      this.resumeId = res['data'].id;
      this.uploadedFileName = res['data'].uploaded_file;
      this.personalForm.controls['resume_id'].setValue(this.resumeId);
      this.skillsLangsForm.controls['resume_id'].setValue(this.resumeId);
      this.resumeLang = res['data'].translated_languages_id;
      this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService, 'skills', 10, this.resumeLang);
      return forkJoin(
        this.resumeService.getResumeLanguage(this.resumeId),
        this.uploadCvPdfService.getDropdownData(this.resumeId));
    }).takeUntil(this.ngUnsubscribe)
      .subscribe(res => {
        this.languageId = res[0]['translated_languages_id'];
        if (res[0]['translated_languages_id'] == 1) {
          this.translate.setDefaultLang('en');
          this.translate.use("en");
        } else if (res[0]['translated_languages_id'] == 2) {
          this.translate.setDefaultLang('ar');
          this.translate.use("ar");
        }
        this.nationalityOpts = res[1]['nationalities'];
        this.nationalityOpts.unshift({ 'id': '', 'name': '' });
        // this.phoneTypeOpts = res[1]['phone_types'];
        // this.phoneTypeOpts.unshift({ 'id': '', 'name': '' });
        this.countryCodeOpts = res[1]['countries'];
        this.countryCodeOpts.unshift({ 'id': '', 'name': '' });
        this.languagesOpts = res[1]['languages'];
        this.languagesOpts.unshift({ 'id': '', 'name': '' });
        this.languagesLevelsOpts = res[1]['languages_level'];
        this.languagesLevelsOpts.unshift({ 'id': -1, 'name': 'languages.motherLanguage' });
        // if(res[0]['translated_languages_id'] == 1)
        //   this.languagesLevelsOpts.unshift({ 'id': -1, 'name': 'Mother' });
        // else if (res[0]['translated_languages_id'] == 2)
        //   this.languagesLevelsOpts.unshift({ 'id': -1, 'name': 'Mother' });
        this.languagesLevelsOpts.unshift({ 'id': '', 'name': '' });

        this.skillsLevelsOpts = res[1]['skills_level'];
        this.skillsLevelsOpts.unshift({ 'id': '', 'name': '' });

        // Set education dropdown data for child component
        this.school_education_fields = res[1]['school_education_fields'];
        this.university_education_fields = res[1]['university_education_fields'];
        this.degreeLevelValues = res[1]['degree_level_translations'];
        this.degreeLevelValues.unshift({ 'degree_level_id': '', 'name': '' });
        this.exp_fields = res[1]['exp_fields'];
        this.employment_types = res[1]['employment_types'];
        this.showLoader = false;
      });
  }

  initializeForms() {
    let email = '';
    if (localStorage.getItem("email"))
      email = localStorage.getItem("email");

    this.personalForm = this.fb.group({
      resume_id: ['', Validators.required],
      first_name: ['', [Validators.required,
      Validators.minLength(2),
      Validators.maxLength(40),
      Validators.pattern('[ ء-يa-zA-Z]+[a-zA-Z0-9ء-ي]*')]],
      //   middle_name: ['', Validators.pattern('[ ء-يa-zA-Z]+[a-zA-Z0-9ء-ي]*')],
      last_name: ['', [Validators.required,
      Validators.minLength(2),
      Validators.maxLength(40),
      Validators.pattern('[ ء-يa-zA-Z]+[a-zA-Z0-9ء-ي]*')]],
      gender: ['', Validators.required],
      nationalities: this.fb.array([
        ['', Validators.required],
      ]),
      date_of_birth: this.fb.group({
        year: ['', Validators.required],
        month: ['', Validators.required],
        day: ['', Validators.required]
      }),
      current_location: this.fb.group({
        fullLocation: ['', Validators.required],
        country: [''],
        country_code: ['', Validators.required],
        city: [''],
        postal_code: [''],
        street_address: [''],
        latitude: [''],
        longitude: ['']
      }),
      image: [''],
      email: [email, [Validators.required, EmailValidator.isValidEmailFormat]],
      //  email: ['', [Validators.required, Validators.email]],
      'contact_number': this.fb.group(
        {
          // 'phone_type': [''],
          // 'phone_type_id': [''],
          'country_code': ['', Validators.required],
          'country_id': ['', Validators.required],
          'phone_number': ['', [Validators.required, ContactInfoValidators.contactNumberLengthValidator]],
        }, { validator: ContactInfoValidators.fullNumberValidator }
      )
    }, { validator: [PersonalInformationValidators.validCurrentLocationValidator] });


    //init forms here
    // skillsLangsForm
    this.skillsLangsForm = this.fb.group({
      resume_id: ['', Validators.required],
      'languages': this.fb.array([this.createLangauges()]),
      'skills': this.fb.array([this.createSkills()]),
    }, {});

    // console.log(this.skillsLangsForm);
  }

  getValueToDD(item: AbstractControl) {
    let x = item.value;

    if (x === '00' || x === '') {
      return false;
    }
    return true;
  }

  isInvalid(controlName: string, index?: number) {
    if (controlName === 'nationalities') {
      return !this.nationalities.at(index).valid;
    }
    if (controlName === 'fullPlace') {
      return (!this.placeOfBirthControl.controls['country'].valid || !this.placeOfBirthControl.controls['country_code'].valid
        || !this.placeOfBirthControl.controls['city'].valid);
    }

    if (controlName === 'fullLocation') {
      return (!this.currentLocationControl.controls['country'].valid || !this.currentLocationControl.controls['country_code'].valid
        || !this.currentLocationControl.controls['city'].valid
        || !this.currentLocationControl.controls['postal_code'].valid || !this.currentLocationControl.controls['street_address'].valid);
    }

    if (['year', 'month', 'day'].includes(controlName)) {
      return !this.dateOfBirthControl.controls[controlName].valid;
    }

    return !this.personalForm.controls[controlName].valid;
  }
  formatDays(numOfDays: number) {
    for (let day = 1; day <= numOfDays; day++) {
      // this.days.push(day);
      this.dayOpts.push({ 'value': day.toString(), 'label': day.toString() });
    }
  }

  initializeDays(m: string, y: string) {
    if (this.of30DaysMonths.includes(m)) {
      // this.days = [];
      this.dayOpts = [];
      this.formatDays(30);
    } else if (m === '2') {
      if (y) {
        let year = Number(y);
        if (year % 4 === 0) {
          // this.days = [];
          this.dayOpts = [];
          this.formatDays(29);
        } else {
          // this.days = [];
          this.dayOpts = [];
          this.formatDays(28);
        }
      } else {
        // this.days = [];
        this.dayOpts = [];
        this.formatDays(31);
      }
    } else {
      // this.days = [];
      this.dayOpts = [];
      this.formatDays(31);
    }
  }
  onChangeMonth(selectedMonth, selectedYear, selectedDay) {
    //  this.dateOfBirthControl.controls['day'].reset();
    this.initializeDays(selectedMonth, selectedYear);
  }
  onChangeYear(selectedYear, selectedMonth, selectedDay) {
    //  this.dateOfBirthControl.controls['day'].reset();
    if (selectedMonth) {
      if (selectedMonth === '2') {
        let year = Number(selectedYear);
        if (year % 4 === 0) {
          // this.days = [];
          this.dayOpts = [];
          this.formatDays(29);
        } else {
          // this.days = [];
          this.dayOpts = [];
          this.formatDays(28);
        }
      }
    }
  }

  isDDValid(control: AbstractControl) {
    if (control.value !== '' && control.value !== null) {
      if (control.value.name !== '' && control.value.name !== null) {
        return true;
      }
    }
    return false;
  }
  // start nationality functions
  addNationality($event) {
    if ($event.detail == 0) {
      return false;
    }
    $event.preventDefault();

    this.nationalities.insert(0, this.fb.control('', Validators.required));
  }

  removeNationality($event, i) {
    $event.preventDefault();

    this.nationalities.removeAt(i);
    this.checkNationalities();
  }

  selectNationality(index, item: AbstractControl) {
    let x = item.value;
    //  console.log(x == 0);
    if (x === 0 || x === '') {
      //  console.log(x);
      return false;
    }
    this.nationalities.at(index).setValue(this.nationalities.at(index).value);
    //  this.nationalities.at(index).setValue(Number(this.nationalities.at(index).value));
    this.checkNationalities();
    return true;
  }

  checkNationalities() {
    let valid = [];
    let invalid = [];

    for (let index = 0; index < this.nationalities.length; index++) {
      for (let i = 0; i < this.nationalities.length; i++) {
        if (i == index) {
          //continue;
        } else {
          if (this.nationalities.at(i).value.id === this.nationalities.at(index).value.id) {
            if (!invalid.includes(i)) invalid.push(i);
            if (!invalid.includes(index)) invalid.push(index);
          }
        }
      }
    }
    for (let i = 0; i < invalid.length; i++) {
      this.nationalities.at(invalid[i]).setErrors({ 'incorrect': true });
    }

    for (let i = 0; i < this.nationalities.length; i++) {
      if (!invalid.includes(i)) {

        this.nationalities.at(i).setErrors(null);
      }
    }
  }
  isInvalidNationalities() {
    return this.nationalities.touched && !this.nationalities.valid;
  }
  // end nationality functions

  // Skills functions
  /*skillsValidator(control: AbstractControl): ValidationErrors | null {
    let skill = control.get('skill').value;
    let level = control.get('level').value;
    if ((skill === null || skill.id === undefined || skill.id === '') && (level.id === undefined || level.id === '')) {
      control.get('level').setErrors(null);
      control.get('skill').setErrors(null);
      // console.log('if1_level', level, 'if1_lang', skill,);
      return null;
    }
    if (level && (skill === null || skill.id === undefined || skill.id === '') && (level.id !== undefined || level.id !== '')) {
      // console.log('if2_level',level,'if2_skill',skill);
      control.get('skill').setErrors({ 'skillsError': 'validationMessages.skill' });
      control.get('level').setErrors(null);
    }
    if (skill && (skill.id !== undefined || skill.id !== '') && (level.id === undefined || level.id === '')) {
      // console.log('if3_level',level,'if3_skill',skill);
      control.get('skill').setErrors(null);
      control.get('level').setErrors({ 'skillsError': 'validationMessages.skillLevel' });
      // console.log('skillCont',control.get('skill'),'levelCont',control.get('level'));
    } 
    else {
      return null;
    }
  }*/
  skillsValidator(control: AbstractControl): ValidationErrors | null {
    const skill = control.get('skill').value;
    const level = control.get('level').value;

    // Check if skill is empty/invalid
    const isSkillEmpty = !skill || skill.id === undefined || skill.id === '' || skill.id === null;
    // Check if level is empty/invalid
    const isLevelEmpty = !level || level.id === undefined || level.id === '' || level.id === null;

    // If both skill and level are empty, it's valid (empty row)
    if (isSkillEmpty && isLevelEmpty) {
      control.get('skill').setErrors(null);
      control.get('level').setErrors(null);
      return null;
    }

    // If skill is empty but level is provided, skill is required
    if (isSkillEmpty && !isLevelEmpty) {
      control.get('skill').setErrors({ 'skillsError': 'validationMessages.skill' });
      control.get('level').setErrors(null);
      return { 'skillRequired': true };
    }

    // If skill is provided, it's valid regardless of level (level is optional)
    if (!isSkillEmpty) {
      control.get('skill').setErrors(null);
      control.get('level').setErrors(null);
      return null;
    }

    return null;
  }
  private createSkills() {
    return new FormGroup({
      'skill': new FormControl(''),
      'level': new FormControl(''),
    }, this.skillsValidator);
  }

  addSkill() {

    this.skills.insert(0, this.createSkills());
    // this.checkSkills();
  }
  removeISkill(i) {
    this.skills.removeAt(i);
    //  this.checkSkills();
  }

  selectSkill(skill, i) {
    let x = i.value;
    //  console.log(x == 0);
    if (x === 0 || x === '') {
      //  console.log(x);
      return false;
    }
    this.skills.at(i).get('skill').setValue(skill.value)
    //this.checkSkills();
    return true;
  }
  /// check if duplicate skill
  checkSkills() {
    let valid = [];
    let invalid = [];

    for (let index = 0; index < this.skills.length; index++) {
      for (let i = 0; i < this.skills.length; i++) {
        if (i == index) {
          //continue;
        } else {
          if (this.skills.at(i).value.id === this.skills.at(index).value.id) {
            if (!invalid.includes(i)) invalid.push(i);
            if (!invalid.includes(index)) invalid.push(index);
          }
        }
      }
    }
    for (let i = 0; i < invalid.length; i++) {
      this.skills.at(invalid[i]).setErrors({ 'incorrect': true });
    }

    for (let i = 0; i < this.skills.length; i++) {
      if (!invalid.includes(i)) {

        this.skills.at(i).setErrors(null);
      }
    }
  }

  //// skill level
  selectSkillLevel(level, i) {
    this.skills.at(i).get('level').setValue(level.value);
  }

  // Save and Exit button handler for Skills section
  saveAndExitSkills() {
    if (this.skillsLangsForm.valid) {
      this.submitSkillsStep();
      this.router.navigate(['u', this.username, 'resumes']);
    }else {
      this.skillsLangsForm.markAsTouched();
      console.log('Skills form is invalid');
    }
  }

  // end Skills funactions

  // Langauges funcations
  languagesValidator(control: AbstractControl): ValidationErrors | null {
    let langauge = control.get('language').value;
    let level = control.get('level').value;

    if ((langauge.id === undefined || langauge.id === '') && (level.id === undefined || level.id === '')) {
      // control.get('level').setErrors(null);
      // control.get('language').setErrors(null);
      control.get('language').setErrors({ 'languagesError': 'validationMessages.language' });
      control.get('level').setErrors({ 'languagesError': 'validationMessages.languageLevel' });
      // return null;
    }
    else if (level && (langauge.id === undefined || langauge.id === '') && (level.id !== undefined || level.id !== '')) {

      control.get('language').setErrors({ 'languagesError': 'validationMessages.language' });
    }
    else if (langauge && (langauge.id !== undefined || langauge.id !== '') && (level.id === undefined || level.id === '')) {

      control.get('level').setErrors({ 'languagesError': 'validationMessages.languageLevel' });
    } else {
      return null;
    }

  }
  private createLangauges() {
    return new FormGroup({
      'language': new FormControl(''),
      'level': new FormControl(''),
    }, [this.languagesValidator]);
  }

  addLanguage() {
    this.languages.insert(0, this.createLangauges());
    // this.checkLanguages();
  }
  removeILanguage(i) {
    this.languages.removeAt(i);
    //this.checkLanguages();
  }

  selectLanguage(language, i) {
    this.languages.at(i).get('language').setValue(language.value)
    // this.checkLanguages();
    return true;
  }
  /// check if duplicate languages
  checkLanguages() {
    let valid = [];
    let invalid = [];

    for (let index = 0; index < this.languages.length; index++) {
      for (let i = 0; i < this.languages.length; i++) {
        if (i == index) {
          //continue;
        } else {
          if (this.languages.at(i).value.language.id === this.languages.at(index).value.language.id) {
            if (!invalid.includes(i)) invalid.push(i);
            if (!invalid.includes(index)) invalid.push(index);
          }
        }
      }
    }
    for (let i = 0; i < invalid.length; i++) {
      this.languages.at(invalid[i]).setErrors({ 'incorrect': true });
    }

    for (let i = 0; i < this.languages.length; i++) {
      if (!invalid.includes(i)) {

        this.languages.at(i).setErrors(null);
      }
    }
  }

  //// Langauge level
  selectLanguageLevel(level, i) {
    this.languages.at(i).get('level').setValue(level.value)
  }


  // end languages funactions


  //start current location functions
  currentLocationChanged() {
    if (this.currentLocationControl.controls['fullLocation'].value === '') {
      this.currentLocationControl.controls['country'].setValue('');
      this.currentLocationControl.controls['country_code'].setValue('');
      this.currentLocationControl.controls['city'].setValue('');
      this.currentLocationControl.controls['postal_code'].setValue('');
      this.currentLocationControl.controls['street_address'].setValue('');
      this.currentLocationControl.controls['latitude'].setValue(null);
      this.currentLocationControl.controls['longitude'].setValue(null);
    }
  }

  currentLocationKeyUp($event) {
    this.currentLocationControl.controls['country'].setValue('');
    this.currentLocationControl.controls['country_code'].setValue('');
    this.currentLocationControl.controls['city'].setValue('');
    this.currentLocationControl.controls['postal_code'].setValue('');
    this.currentLocationControl.controls['street_address'].setValue('');
    if (this.currentLocationControl.controls['fullLocation'].value === '') {

      this.currentLocationControl.controls['latitude'].setValue(null);
      this.currentLocationControl.controls['longitude'].setValue(null);
    } else {
      // this.notifyCurrentMap({lat: this.currentLocationControl.controls['latitude'].value,
      // lng: this.currentLocationControl.controls['longitude'].value});
    }
  }

  setCurrentLocationControls(place: Place) {
    this.currentLocationControl.controls['country'].setValue(place.country);
    this.currentLocationControl.controls['country_code'].setValue(place.countryCode);
    this.currentLocationControl.controls['city'].setValue(place.city);
    this.currentLocationControl.controls['postal_code'].setValue(place.postalCode);
    this.currentLocationControl.controls['street_address'].setValue(place.streetAddress);
    this.currentLocationControl.controls['latitude'].setValue(place.latitude);
    this.currentLocationControl.controls['longitude'].setValue(place.longitude);
    this.currentLocationControl.controls['fullLocation'].setValue(place.country + ((place.city) ? ', ' : '') +
      place.city + ((place.streetAddress) ? ', ' : '') + place.streetAddress);
  }

  private getPlaceAutocomplete(type: string = 'current') {
    //to stop bot traffic to google maps
    if (navigator.userAgent.match(/Googlebot/i)) {
      return;
    }
    // Check if currentSearch is available
    if (!this.currentSearch || !this.currentSearch.nativeElement) {
      console.warn('currentSearch element is not available');
      return;
    }

    const autocomplete = new google.maps.places.Autocomplete(this.currentSearch.nativeElement,
      {
        types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components', 'geometry']  // 'name'
      });

    autocomplete.addListener('place_changed', () => {
      let place = autocomplete.getPlace();
      // Update all location fields from map selection
      this.getAddress(place, 'current');
    });
  }

  getAddress(place: object, type: string) {
    let newPlace = this.data_map.getLocationDataFromGoogleMap(place);
    this.currentLocationControl.controls['country'].setValue(newPlace.country);
    this.currentLocationControl.controls['country_code'].setValue(newPlace.country_code);
    this.currentLocationControl.controls['city'].setValue(newPlace.city);
    this.currentLocationControl.controls['street_address'].setValue(newPlace.street_address);
    this.currentLocationControl.controls['postal_code'].setValue(newPlace.postal_code);
    this.currentLocationControl.controls['latitude'].setValue(newPlace.latitude);
    this.currentLocationControl.controls['longitude'].setValue(newPlace.longitude);
    if (this.currentLocationControl.controls['street_address'].value !== "") {
      this.currentLocationControl.controls['fullLocation'].setValue(
        this.currentLocationControl.controls['country'].value + ', ' + this.currentLocationControl.controls['city'].value
        + ', ' + this.currentLocationControl.controls['street_address'].value);
    }
    else {
      this.currentLocationControl.controls['fullLocation'].setValue(
        this.currentLocationControl.controls['country'].value + ', ' + this.currentLocationControl.controls['city'].value);
    }

    // $('#location').focus();
  }

  // end current location functions

  //  start profile picture functions
  handleImageEditorPopup($event) {
    if ($event['editedImage'].file !== '') {
      this.image_code_to_send = $event['editedImage'];
      this.perPhotoSrc = this.image_code_to_send.file;
      this.uploadLabelDisplay = false;
    }
  }

  deleteProfilePicture() {
    if (confirm(this.translate.instant("confirm.deleteProfilePic"))) {
      this.uploadLabelDisplay = true;
      this.perPhotoSrc = this.noProfilePicSet;
      this.image_code_to_send = { file: '', file_type: '', is_deleted: true }
      this.generalService.notify('image-deleted', 'upload-cv-wizard', 'image-editor', {});
      this.cdRef.detectChanges();
    }
  }
  //  end profile picture functions
  get placeOfBirthControl() {
    return (this.personalForm.controls['place_of_birth'] as FormGroup);
  }
  get dateOfBirthControl() {
    return (this.personalForm.controls['date_of_birth'] as FormGroup);
  }
  get currentLocationControl() {
    return (this.personalForm.controls['current_location'] as FormGroup);
  }
  get contactNumberControl() {
    return (this.personalForm.controls['contact_number'] as FormGroup);
  }
  get nationalities() {
    return (this.personalForm.controls['nationalities'] as FormArray);
  }
  get languages() {
    return (this.skillsLangsForm.controls['languages'] as FormArray);
  }
  get skills() {
    return (this.skillsLangsForm.controls['skills'] as FormArray);
  }
  setContactId(type, obj) {
    this.contactNumberControl.get(type).setValue(obj.value.id);
  }

  initCurrentSearchAutocomplete(attempts = 0) {
    if (this.currentSearch && this.currentSearch.nativeElement) {
      this.getPlaceAutocomplete('current');
    } else if (attempts < 10) {
      setTimeout(() => this.initCurrentSearchAutocomplete(attempts + 1), 30000);
    } else {
      console.warn('currentSearch input not found after multiple attempts');
    }
  }

  initGooglePlaces(): void {
    // Handle the main current location input (currentSearch)
    if (this.currentSearch && this.currentSearch.nativeElement) {
      if (!this.currentSearch.nativeElement._autocompleteInitialized) {
        const autocomplete = new google.maps.places.Autocomplete(this.currentSearch.nativeElement, {
          types: ['geocode'],
          fields: ['address_components', 'geometry', 'formatted_address']
        });

        autocomplete.addListener('place_changed', () => {
          const selectedPlace = autocomplete.getPlace();
          this.getAddress(selectedPlace, 'current');
        });

        this.currentSearch.nativeElement._autocompleteInitialized = true;
      }
    }

    // Handle other location inputs if they exist
    if (this.currentSearchInputs) {
      setTimeout(() => {
        this.currentSearchInputs.forEach((input, index) => {
          if (!input.nativeElement._autocompleteInitialized) {
            const autocomplete = new google.maps.places.Autocomplete(input.nativeElement, {
              types: ['(cities)']
            });
            autocomplete.addListener('place_changed', () => {
              const selectedPlace = autocomplete.getPlace();
              this.onLocationSelect(selectedPlace, index);
            });
            input.nativeElement._autocompleteInitialized = true;
          }
        });
      }, 500);
    }
  }

  onLocationSelect(place: any, index?: number) {
    // Handle current location selection properly
    if (place && place.formatted_address) {
      // Use the existing getAddress method to properly set all location fields
      this.getAddress(place, 'current');
    }
  }

  set currentStepIndex(step: number) {
    this.currentStep = step;
    if (this.currentStep === 1) {
      this.initGooglePlaces();
    }
  }
  prevStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
      if (this.currentStep === 1) {
        this.initGooglePlaces();
      }
    }
  }
  nextPage() {
    if (this.currentStep < this.steps.length) {
      switch (this.currentStep) {
        case 1:
          if (this.personalForm.valid) {
            this.submitPersonalFormStep();
          } else {
            this.personalForm.markAsTouched();
            console.log('Personal form is invalid');
          }
          break;
        case 2:
          if (this.skillsLangsForm.valid) {
            this.submitSkillsStep();
          } else {
            this.skillsLangsForm.markAsTouched();
            console.log('Skills form is invalid');
          }
          break;
      }
      this.currentStep++;
      if (this.currentStep === 1) {
        this.initGooglePlaces();
      }
    }
  }

  submitPersonalFormStep() {
    if (this.personalForm.valid) {
      let sendData = this.personalForm.value;
      sendData.image = this.image_code_to_send;

      this.uploadCvPdfService.addStepData('personal_info', sendData).subscribe(res => {
        if (res['resumeActive']) {
          //profile picture of active cv changed , so get profile picture of active cv to change profile picture in navigation bar
          if (sendData.image.file !== '' && sendData.image.is_deleted === false) {
            this.generalService.notify('profilePictureChanged', 'personalInfo', 'navbar', { 'profile_picture': res['profile_picture'] });
            localStorage.setItem('pic', res['profile_picture']);
          }
        }
        this.currentStep++;
      });
    } else {
      console.log('Form is invalid:', this.personalForm.errors);
    }
  }

  submitSkillsStep() {
    if (this.skillsLangsForm.valid) {

      let sendData = this.skillsLangsForm.value;
      // Bug fix: 'res' was unused. Removed it from the callback.
      this.uploadCvPdfService.addStepData('skills_langs', sendData).subscribe(() => {
        this.currentStep++;
      });
    }
  }
  closeUploadCVStep(event: any) {
    if (event && event.ai) {
      // event.ai contains the AI extraction result
      this.CvAiExtract = event.ai;
      // Patch personalForm
      // Find country_code from countryCodeOpts by country_id
      let country_code = '';
      if (event.ai.contact_number?.country_id && this.countryCodeOpts && this.countryCodeOpts.length) {
        // countryCodeOpts is an array of {id, name, code}
        const found = this.countryCodeOpts.find(opt => opt.id == event.ai.contact_number.country_id);
        if (found) {
          country_code = found.code;
        }
      }
      this.personalForm.patchValue({
        first_name: event.ai.first_name || '',
        last_name: event.ai.last_name || '',
        gender: event.ai.gender || '',
        email: event.ai.email || '',
        date_of_birth: {
          year: event.ai.date_of_birth?.year || '',
          month: event.ai.date_of_birth?.month ? parseInt(event.ai.date_of_birth.month, 10).toString() : '',
          day: event.ai.date_of_birth?.day ? parseInt(event.ai.date_of_birth.day, 10).toString() : '01'
        },
        contact_number: {
          country_id: event.ai.contact_number?.country_id || '',
          country_code: country_code,
          phone_number: event.ai.contact_number?.phone_number || ''
        },
        current_location: {
          fullLocation: event.ai.current_location?.fullLocation || '',
          country: event.ai.current_location?.country || '',
          city: event.ai.current_location?.city || '',
          country_code: event.ai.current_location?.country_code || '',
        }
      });
      // Nationalities (FormArray)
      const nationalitiesArray = this.personalForm.get('nationalities') as FormArray;
      nationalitiesArray.clear();
      if (event.ai.nationalities && event.ai.nationalities.length) {
        event.ai.nationalities.forEach(nat => {
          // Find the nationality object by id
          const nationalityObj = this.nationalityOpts.find(opt => opt.id == nat.id);
          nationalitiesArray.push(new FormControl(nationalityObj || { id: '', name: '' }));
        });
      } else {
        nationalitiesArray.push(new FormControl({ id: '', name: '' }));
      }

      // Skills
      const skillsArray = this.skillsLangsForm.get('skills') as FormArray;
      skillsArray.clear();
      // Helper function to fetch skill by id if not found in skillsDD
      const fetchSkillById = async (skillId: string | number): Promise<any> => {
        const res = await this.skillsDD.getById(skillId).toPromise();
        return res && res['data'] && res['data'].length ? res['data'][0] : null;
      };
      // Helper function to fetch skill by name if not found in skillsDD
      const fetchSkillByName = async (skillName: string): Promise<any> => {
        // getOriginalValue returns an Observable
        const res = await this.skillsDD.getOriginalValue(skillName).toPromise();
        // Assume the backend returns an object with a 'data' array
        return res && res['data'] && res['data'].length ? res['data'][0] : { id: '', name: skillName };
      };
      if (event.ai.skills && event.ai.skills.length) {
        event.ai.skills.forEach(async (skillObj: any, idx: number) => {
          skillsArray.push(this.createSkills());

          let skillOption: any = null;

          // If skill ID is -1, use it directly as a custom skill
          if (skillObj.skill?.id === -1) {
            skillOption = { id: -1, name: skillObj.skill.name };
          } else {
            // Find the skill object by id in dropdown
            skillOption = this.skillsDD?.items?.find((opt: any) => opt.id == (skillObj.skill?.id || skillObj.skill));
            // If not found by id in local items, try to fetch by id from backend
            if (!skillOption && (skillObj.skill?.id || skillObj.skill)) {
              skillOption = await fetchSkillById(skillObj.skill?.id || skillObj.skill);
            }
            // If still not found by id, try to fetch by name
            if ((!skillOption || !skillOption?.id) && skillObj.skill && skillObj.skill.name) {
              skillOption = await fetchSkillByName(skillObj.skill.name);
            }
            // Ensure the fetched skill is in the dropdown options
            if (skillOption && skillOption.id && skillOption.id !== -1) {
              const exists = this.skillsDD.items.some((opt: any) => opt.id == skillOption.id);
              if (!exists) {
                this.skillsDD.items = [skillOption, ...this.skillsDD.items];
              }
            }
          }

          // Find the skill level object by id
          const skillLevelOption = this.skillsLevelsOpts?.find((opt: any) => opt.id == (skillObj.level?.id || skillObj.level));
          skillsArray.at(idx).patchValue({
            skill: skillOption || { id: '', name: '' },
            level: skillLevelOption || { id: '', name: '' }
          });
        });
      } else {
        skillsArray.push(this.createSkills());
      }
      // Languages
      const languagesArray = this.skillsLangsForm.get('languages') as FormArray;
      languagesArray.clear();
      if (event.ai.languages && event.ai.languages.length) {
        event.ai.languages.forEach(langObj => {
          languagesArray.push(this.createLangauges());
          const idx = languagesArray.length - 1;
          // Find the language object by id
          const languageOption = this.languagesOpts?.find((opt: any) => opt.id == (langObj.language?.id || langObj.language));

          // Find the language level object by id - handle custom levels with ID -1
          let languageLevelOption: any;
          if (langObj.level?.id === -1) {
            // If level ID is -1, use it directly as a custom level
            languageLevelOption = {
              id: -1,
              name: 'languages.motherLanguage'
            };
          } else {
            // Find the level in the dropdown options
            languageLevelOption = this.languagesLevelsOpts?.find((opt: any) => opt.id == (langObj.level?.id || langObj.level));
          }

          languagesArray.at(idx).patchValue({
            language: languageOption || { id: '', name: '' },
            level: languageLevelOption || { id: '', name: '' }
          });
        });
      } else {
        languagesArray.push(this.createLangauges());
      }

      // Educations
      if (event.ai.educations) {
        this.educations = event.ai.educations;
      }

      // Work Experiences
      if (event.ai.work_experiences) {
        this.workExps = event.ai.work_experiences;
      }
    }
    if (event['prevPage'] == true)
      this.prevStep();
    else if (event['nextPage'] == true)
      this.currentStep++;
  }
  onEducationsChange(educations: any[]): void {
    this.educations = educations;
    // Increment currentStep when educations form emits change (user clicked next)
    this.currentStep++;
  }
  ngAfterContentChecked() {
    this.cdRef.detectChanges();
  }
}
