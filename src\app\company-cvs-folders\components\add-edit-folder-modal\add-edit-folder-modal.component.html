<div class="modal-body">
    <app-pre-loader [show]="modalLoader" [size]="'small'"></app-pre-loader>
    <form #form="ngForm" [formGroup]="foldersForm" (ngSubmit)="addEditFolder()" class="form-horizontal">
        <div class="form-group has-feedback" [ngClass]="{'has-error': submitted && !foldersForm.controls['name'].valid}">
            <div class="col-sm-3 alignment-right">
            </div>
            <div class="col-sm-6 focus-no-padding" [ngClass]="{'has-val': foldersForm.controls['name'].value}">
                <input type="text" formControlName="name" class="form-control">
                <span class="custom-underline"></span>
                <label class="control-label custom-control-label">Folder Name</label>
            </div>
            <div class="col-sm-3">
                <span class="error-message" *ngIf="submitted && foldersForm.controls['name'].errors?.required" translate>validationMessages.required</span>
                <span class="error-message" *ngIf="nameBackendError !== null">{{nameBackendError}}</span>
            </div>
        </div>
        <div class="form-group has-feedback">
            <div class="col-sm-3 alignment-right">
            </div>
            <div class="col-sm-6 focus-no-padding">
                <p-dropdown 
                    [options]="foldersddData"
                    optionLabel="label"
                    formControlName="parent"  
                    [filter]="true"
                    [ngClass]="{'has-val':foldersForm.controls['parent'].value}">
                </p-dropdown>
                <span class="custom-underline"></span>
                <label class="control-label custom-control-label">Nest Folder Under</label>
            </div>
            <div class="col-sm-3">
                <span class="error-message" *ngIf="folderBackendError !== null">{{folderBackendError}}</span>
            </div>
        </div>
        <div class="text-center" style="margin-top: 73px;">
            <button class="btn btn-success small-btn" type="submit">Save</button>&nbsp;
            <button class="btn btn-default cust-cancel-btn small-btn" type="button" (click)="cancel()">Cancel</button>
        </div>
        <!-- <p>  {{ form.value | json }} </p> -->
    </form>
        
</div>


       