// Declare  CV's Data Table Component Inititial Data
export class DataModel {
    public folders = [] ;
    public advFolders = [];
    public firstFoldersSection = [];
    public secondFoldersSection = [];
    public expanded: boolean ;
///// basic  folders in  the list (Inbox , Fav , Interview , ..... etc)

public basicFolders = [
    {
        'icon': 'pi pi-envelope',
        'title': 'Inbox',
        'name': 'inbox',
        'type' : 'basic',
        'cssClass' : 'basic active',
        'data' : {},
        'selected' : true,
        'id': 1,
    },
    {
        'icon': 'pi pi-star',
        'title': 'Favourite',
        'name': 'favourite',
        'type' : 'basic',
        'data' : {},
        'cssClass' : 'basic',
        'selected' : false,
        'id': 6,
    },
    {
        'icon': 'pi pi-check',
        'title': 'Direct Apply',
        'name': 'direct-apply',
        'type' : 'basic',
        'data' : {},
        'cssClass' : 'basic',
        'selected' : false,
        'id': 3,
    },
    // {
    //     'icon': 'pi pi-clock',
    //     'title': 'Interview',
    //     'name': 'interview',
    //     'type' : 'basic',
    //     'data' : {},
    //     'cssClass' : 'basic',
    //     'selected' : false,
    //     'id': 2,
    // },
    {
        'icon': 'pi pi-times-circle',
        'title': 'Auto rejected',
        'name': 'auto_rejected',
        'type' : 'basic',
        'data' : {},
        'cssClass' : 'basic',
         'selected' : false,
        'id': 4,
    },
    {
        'icon': 'pi pi-trash',
        'title': 'Trash',
        'name': 'trash',
        'type' : 'basic',
        'data' : {},
        'cssClass' : 'basic',
        'selected' : false,
        'id': 5,
    },
    {
        'icon': 'pi pi-angle-right',
        'title': 'Advs Titles',
        'name': 'adv-title',
        'type' : 'title',
        'data' : {},
        'cssClass' : 'title',
        'selected' : false,
        'id': '',
    }
];
    constructor() {
        this.folders = this.basicFolders;
        this.expanded = false ;
        this.firstFoldersSection = this.folders.slice(0, 3);
        this.secondFoldersSection = this.folders.slice(3, 5);
        // this.firstFoldersSection = this.folders.slice(0, 3);
        // this.secondFoldersSection = this.folders.slice(3, 5);
    }

    //// Handle Advrs Folders - Which Are The Folders  Basid On Ads which published
    //// Here we push items to  folders  array

    handleAdvFolders(adFolders) {

        //// first init  advs folders
        this.advFolders =  [] ;

        ///// Third Add Advs folders to folders  array
         // tslint:disable-next-line:forin
         for (let el of adFolders) {
            let  f = {
                'icon': '',
                'title': el['folder'],
                'name': el['folder'],
                'type' : 'adv-folder',
                'cssClass' : 'adv-folder',
                'selected' : false,
                'data' : {'count' : el['count'] , 'adv-id' : el['adv_id']},
                'id': el['id'],
            };
            this.advFolders.push(f);
         }
        // Array.prototype.push.apply( this.folders, this.advFolders);
    }
}
