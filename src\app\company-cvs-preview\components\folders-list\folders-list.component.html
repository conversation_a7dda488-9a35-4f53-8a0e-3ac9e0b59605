<ul class="folders-ul">
    <li class="folder-li" (click)="selectFolder(item);fixCollapseIfMobile()" *ngFor="let item of dataModel.firstFoldersSection;">
        <a class="{{item.cssClass}}">
            <span *ngIf="item.type=='adv-folder'" class="adv-id">
                {{item.data['adv-id']}}
            </span>
            <i class="{{item.icon}} folder-icon"></i>
            <span class="folder-title">{{item.title}}</span>
        </a>
    </li>
</ul>


<ul *ngIf="dataModel.advFolders.length > 0" class="folders-ul advs-title-folder">
 <li class="folder-li" (click)="selectFolder(dataModel.folders[5])" >
        <a class="{{dataModel.folders[5].cssClass}}">
            <span  class="adv-id">
                {{dataModel.folders[5].data['adv-id']}}
            </span>
            <i class="{{dataModel.folders[5].icon}} folder-icon"></i>
            <span class="folder-title">{{dataModel.folders[5].title}}</span>
        </a>
    </li> 
    <li class="folder-li advs-title-li" (click)="selectFolder(item);fixCollapseIfMobile()" *ngFor="let item of dataModel.advFolders ; let i = index;">
        <a *ngIf="item.type == 'adv-folder' &&  dataModel.expanded" class="{{item.cssClass}} adv-folder">
            <span class="adv-id">
                {{item.data['adv-id']}}
            </span>
            <span class="folder-title" title="{{item.title}}">{{item.title | slice:0:22}}<span *ngIf="item.title?.length>22">..</span></span>
            <span class="cvs-count">
                {{item.data['count']}}
            </span>
        </a>
    </li>
</ul>


<ul class="folders-ul">
    <li class="folder-li" (click)="selectFolder(item);fixCollapseIfMobile()" *ngFor="let item of dataModel.secondFoldersSection;">
        <a class="{{item.cssClass}}">
            <span *ngIf="item.type=='adv-folder'" class="adv-id">
                {{item.data['adv-id']}}
            </span>
            <i class="{{item.icon}} folder-icon"></i>
            <span class="folder-title">{{item.title}}</span>
        </a>
    </li>
</ul>