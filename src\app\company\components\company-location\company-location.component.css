#page-content-wrapper {
    padding: 0;
    transition: margin-left .5s ease;
}

#page-content-wrapper .page-content {
    padding: 60px 34px 30px;
    padding-top: 60px;
}

.table-page-content {
    padding-top: 60px !important;
    position: relative;
}

.table-page-content .page-title {
    position: absolute;
    background: white;
    top: 60px;
    padding-right: 30px;
    z-index: 1;
}

.table-page-content .minamize-certification {
    bottom: 0;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

@media only screen and (max-width: 767px) {
    .table-page-content {
        padding-top: 40px !important;
    }
    /* td:nth-of-type(1):before {
        content: "Main Office";
    }
    td:nth-of-type(2):before {
        content: "Name";
    }
    td:nth-of-type(3):before {
        content: "Location";
    }
    td:nth-of-type(4):before {
        content: "Actions";
    } */
}

@media only screen and (max-width: 500px){
    .add-btn{
        margin-left: -7px;
        padding: 4px 9px;
    }
}
