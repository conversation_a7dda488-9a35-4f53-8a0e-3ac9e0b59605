import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
////////////////// Obada
import { ManagePostService } from 'app/company/services/manage-post.service';
import { SelectItem } from 'primeng/api';
import { FormBuilder, FormControl, Validator, Validators } from '@angular/forms';
import { Subject, Subscription } from 'rxjs';
import { PostJobService } from 'app/company/services/post-job.service';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { InputTextModule } from 'primeng/inputtext';
import { DataMap } from 'shared/Models/data_map';
import { FilterUtils } from 'primeng/utils';
import { DatePipe, getLocaleDateFormat } from '@angular/common';
import { Table } from 'primeng/table';
import { Title } from '@angular/platform-browser';
////////////////
declare var $: any;
@Component({
  selector: 'app-advs-manage',
  templateUrl: './advs-manage.component.html',
  styleUrls: ['./advs-manage.component.scss']
})
export class AdvsManageComponent implements OnInit {
  /////////////// Jaber
  sub: any;
  status = 'published';
  urlParams = {};
  companyId: any;
  data_Source: any;
  loaded: boolean;
  publishedDataSource: any;
  draftDataSource: any;
  templateDataSource: any;
  expiredDataSource: any;
  ////////// Obada //////////////////////////
  createdDates: any;
  updatedDates: any;
  templatesDates: any;
  expiredDates: any;
  publishedAdvsExpiredDates: any;
  AdvrId: any;
  published_dates: any[] = [];
  option_ar: SelectItem[];
  passed_Data_to_translated: any;
  current_language = Number(localStorage.getItem('current_company_language'));
  private ngUnsubscribe: Subject<any> = new Subject();
  options: SelectItem[];
  options_advrs: SelectItem[];
  option: SelectItem[];
  slcLang: any;
  choice;
  selectedlang: selectedLang;
  data_map = new DataMap();
  available_languages_temp: selectedLang[] = [];
  data_type: any[][] = [[]];
  total_advr;
  available_languages = [];
  companyAdversOrder = [];
  companyAdversAfterReorder: string[] = [];
  public start: number = 50;
  public page_number: number = 1;
  public current_page: number = 1;
  public autoHide: boolean = false;
  public responsive: boolean = true;
  available_langs;
  username;
  selected_lang: any;
  subscription: Subscription;
  status_passed;
  page_number_passed;
  no_adver = false;
  statuses: any[];
  totalRecords;
  showLoader: boolean;
  activeStatusOptions = [];
  @ViewChild('dt') private _table: Table;

  ///////////////////

  constructor(private route: ActivatedRoute,
    private privateSharedURL: ExportUrlService,
    private http: HttpClient,
    private managePost: ManagePostService,
    private postJobService: PostJobService,
    private fb: FormBuilder,
    private router: Router,
    private translate: TranslateService,
    private datepipe: DatePipe,
    private title: Title
  ) {
    this.urlParams = { 'pgnum': 1, 'pgsize': 500, 'language_id': 1 };
    this.companyId = localStorage.getItem('company_id');
    this.loaded = false;
    ///
    this.showLoader = false;
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });

    this.route.params.subscribe(res => {
      this.title.setTitle('CVeek');
      if (res['status']) {
        this.status =  res['status'];
      }
    });


    this.subscription = postJobService.Data.takeUntil(this.ngUnsubscribe).subscribe(val => {
      this.status_passed = val[0];
      this.page_number_passed = val[1];
    });
    this.option = [
      { label: 'Publish', value: 'publish' },
      { label: 'Expired', value: 'expired' },
      { label: 'Draft', value: 'draft' },
      { label: 'Template', value: 'template' }
    ];
    this.option_ar = [
      { label: 'منشورة', value: 'publish' },
      { label: 'منتهية الصلاحية', value: 'expired' },
      { label: 'مسودات', value: 'draft' },
      { label: 'قوالب', value: 'template' }
    ];
    this.available_langs = this.fb.group({
      'languages': ['', Validators.required],
    });
    this.activeStatusOptions = [
      { label: '', value: ' ' },
      { label: 'Active', value: '1' },
      { label: 'Inactive', value: '0' },
    ];
    //
  }

  ngOnInit(): void {
    this.translate.use("en");
    ///
    this.getData();
    ///////////////////////////////////// Obada
 //   this.title.setTitle('CVeek');
    //  this.BuildTable(this.page_number);
    this.choice = this.status;
    this.options_advrs = (this.current_language === 1) ? this.option : this.option_ar;
    let _self = this
    FilterUtils['betweenCreatedDates'] = (value, filter): boolean => {

      var s = _self.createdDates[0].getTime();
      var e;
      if (_self.createdDates[1]) {
        e = _self.createdDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    };

    FilterUtils['betweenUpdatedDates'] = (value, filter): boolean => {

      var s = _self.updatedDates[0].getTime();
      var e;
      if (_self.updatedDates[1]) {
        e = _self.updatedDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    };

    FilterUtils['betweenTemplateDates'] = (value, filter): boolean => {

      var s = _self.templatesDates[0].getTime();
      var e;
      if (_self.templatesDates[1]) {
        e = _self.templatesDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    };

    FilterUtils['betweenExpiredDates'] = (value, filter): boolean => {
      var s = _self.expiredDates[0].getTime();
      var e;
      if (_self.expiredDates[1]) {
        e = _self.expiredDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    };

    FilterUtils['betweenPublishedExpiredDates'] = (value, filter): boolean => {
      var s = _self.publishedAdvsExpiredDates[0].getTime();
      var e;
      if (_self.publishedAdvsExpiredDates[1]) {
        e = _self.publishedAdvsExpiredDates[1].getTime() + 86400000;
      } else {
        e = s + 86400000;
      }
      return new Date(value).getTime() >= s && new Date(value).getTime() <= e;
    };
    /////////////
  }

  getData() {
    this.showLoader = true ;
    // manage_advertisements/by_type/
    this.sendRequest('get', this.privateSharedURL.awsBasicUrl + 'manage_advs/by_type/' +
      this.companyId, this.urlParams).subscribe(res => {
        /////////////////////////
        this.data_Source = res['data'];
        this.loaded = true;
        this.showLoader = false ;
        ///// --------------
        this.publishedDataSource = [];
        this.draftDataSource = [];
        this.templateDataSource = [];
        this.expiredDataSource = [];
        this.publishedDataSource = this.filterPosts('published');
        this.draftDataSource = this.filterPosts('draft');
        this.templateDataSource = this.filterPosts('template');
        this.expiredDataSource = this.filterPosts('expired');
        /// ---------------
        ///
      });
  }
  filterPosts(status) {
    return this.data_Source.filter((el) => el['status'] === status);
  }

  changeOption(event) {
    this.status = event['type'];

  }


  ///////////////////////// One  Function For  Send Requests
  sendRequest(method, action, data) {
    const options = { body: data, params: data };
    return this.http.request(method, action, options);
  }
  //// ----------------------------


  /////////////////////////// Obada

  reOrderTable() {

    if (this.companyAdversAfterReorder.length !== 0) {
      for (let i = 0; i < this.companyAdversAfterReorder.length; i++) {
        let tirm = this.companyAdversAfterReorder[i].trim();

        this.companyAdversAfterReorder[i] = tirm;

      }

      for (let advr of this.companyAdversOrder) {

        let orderedItem = this.companyAdversAfterReorder.indexOf(advr.orderId.toString()) + 1;

        advr.orderId = orderedItem;
      }

    } else { console.log('empty'); }
  }

  getResumes(adv_id) {

    this.managePost.getResumes(adv_id).subscribe(
      (res) => {

      }
    )
  }

  reOrderDataWithServeInvokation() {
    this.reOrderTable();
    let orderedData = { 'orderAdv': this.companyAdversOrder };
    this.managePost.orderCompanyAdvertisement(this.companyId, orderedData).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {

      }
    );
  }

  renew(AdvId) {
    let holder = this.data_Source;
    this.managePost.renewCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
          ////////////////////// If your renew advrs more than 3 times
          if(res['error']) {
         alert(res['error']);
          }
            //// ---

        this.refresh_data(res['data']['renew_count'], 'renew_count', AdvId, holder);
      });
  }

  activate(AdvId, active, adv_id_by_company  = 0) {
    let active_status;
    let holder = this.data_Source;
    if (active) {
      if (confirm('Are you sure you want to deactivate Adverstisement ' + adv_id_by_company)) {
        active_status = { 'active': !active }
        this.managePost.activateCompanyAdvertisement(AdvId, active_status).takeUntil(this.ngUnsubscribe).subscribe(
          (res) => {

            this.refresh_data(res['data']['active_status'], 'active_status', AdvId, holder)
          });
      }
    } else {

      active_status = { 'active': !active }
      this.managePost.activateCompanyAdvertisement(AdvId, active_status).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          this.refresh_data(res['data']['active_status'], 'active_status', AdvId, holder)
        });
    }

  }

  refreshListing(AdvId) {
    let holder = this.data_Source;
    this.managePost.refreshListingCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        if (res['error']) {
          window.alert(res['error'])
        } else {
          this.refresh_data(res['data']['refresh_count'], 'refresh_count', AdvId, holder)
        }
      })


  }

  endWorkFlow(AdvId, adv_id_by_company) {
    if (confirm('Are you sure you want to make Advertisement ' + adv_id_by_company + ' as an Expired Advertisement?')) {
      let holder = this.data_Source;
      this.managePost.endWorkFlowCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          this.refresh_data(res['data']['expires_at'], 'expires_at', AdvId, holder)
        })
    }
  }

  refresh_data(newData = '', dataName = '', AdvId = '', holder = '') {
    this.getData();

  }

  saveAsTemplate(AdvId, has_template = 0, adv_id_by_company= null) {

    let holder = this.data_Source;
    
    if (has_template === null) {
      this.showLoader = true;
      this.managePost.saveAsTemplateCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.showLoader = false;
          if (res['data']) {
            window.alert('Advertisement has been saved as a Template Successfully!')
          }
              ////////////////////// If your adv has ended by system admin
          if(res['error']) {
                window.alert(res['error']);
          }
                   //// ---
          this.refresh_data(res['data']['has_template'], 'has_template', AdvId, holder)
        })
    } else {
      if (confirm('this Advertisement ' + adv_id_by_company + ' has a Template.. Are you sure you want to save another template for it?')) {
        this.showLoader = true;
        this.managePost.saveAsTemplateCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
          (res) => {
            this.showLoader = false;
            if (res['data']) {
              window.alert('Advertisement has been saved as a Template Successfully!')
            }
                  ////////////////////// If your adv has ended by system admin
          if(res['error']) {
            window.alert(res['error']);
      }
               //// ---
            this.refresh_data(res['data']['has_template'], 'has_template', AdvId, holder)
          })
      }
    }

  }

  deleteAdvr(AdvId, adv_id_by_company) {
    if (confirm('Are you sure you want to delete this Advertisement ' + adv_id_by_company + '?')) {
      let holder = this.data_Source;

      this.managePost.deleteCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {

          if (res['error']) {
            window.alert(res['error'])
          } else {

            this.refresh_data(res['data']['id'], 'deleted', AdvId, holder)
          }

        })
    }

  }

  delete_temp(AdvId, adv_template_id_by_company) {
    const confirmed = confirm('Are you sure you want to delete this Advertisement ' + 'template ' + adv_template_id_by_company + '?');
    const holder = this.data_Source;
    if (confirmed) {
      this.showLoader = true;
      this.managePost.deleteCompanyAdvertisementTemplate(AdvId).takeUntil(this.ngUnsubscribe).
        subscribe((res) => { 
          this.refresh_data(res['data']['id'], 'deleted', AdvId, holder); 
          this.showLoader = false;
        });
    }
  }


  saveAsPublish(AdvId, adv_id_by_company) {
    let holder = this.data_Source;
    if (confirm('this Advertisement ' + adv_id_by_company + ' will be published.. Are you sure you want to continue?')) {
      this.showLoader = true;
      this.managePost.saveAsPublishCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.showLoader = false  ;
       //   this.status  = 'published' ;
          window.alert('Draft advertisement has been published successfuly');
          this.refresh_data();

        });
    }

  }

  saveTemplateAsPublish(AdvId, adv_template_id_by_company) {
    let holder = this.data_Source;
    if (confirm('this Advertisement ' + adv_template_id_by_company + ' will be published.. Are you sure you want to continue?')) {
      this.showLoader = true;
      this.managePost.saveTemplateAsPublishCompanyAdvertisement(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.showLoader = false  ;
          this.refresh_data();

        });
    }

  }

  edit_advr(AdvId, langId) {
    this.showLoader = true  ;
    this.managePost.getCompanyAdvertisementData(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['advertisement_info']);
        this.passed_Data_to_translated.push({
          'translated_language_id': langId,
          'status': this.status,
          'page_number': this.page_number
        });
        this.showLoader = false  ;
        this.postJobService.send_Data(this.passed_Data_to_translated);
        this.router.navigate(['/c', this.username, 'post-job']);
      })
  }

  edit_advr_temp(AdvId, langId) {
    this.showLoader = true  ;
    this.managePost.getCompanyTemplateAdvertisementData(AdvId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        this.showLoader = false  ;
        this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['adv_temp_info']);
        this.passed_Data_to_translated.push({
          'translated_language_id': langId,
          'status': this.status,
          'page_number': this.page_number
        })
        this.postJobService.send_Data(this.passed_Data_to_translated);

        this.router.navigate(['/c', this.username, 'post-job']);
      })
  }


  setcompanyLanguage(languageId) {
    if (languageId && languageId === 1) {
      this.translate.setDefaultLang('en');

    } else if (languageId === 2) {
      this.translate.setDefaultLang('ar');

    } else {
      this.translate.setDefaultLang('en');
    }

  }

  calculateDiff(sentDate) {

    var Offset: any = new Date(sentDate).getTimezoneOffset();
    var backend_time: any = new Date(sentDate)
    var current_Date_ms = new Date().getTime()
    var current_Date: any = new Date(current_Date_ms + Offset * 60 * 1000);

    var diffDays: any = Math.floor((current_Date - backend_time) / (1000 * 60 * 60 * 24));
    var seconds = Math.floor(Math.abs(backend_time - (current_Date)) / 1000);
    var minutes = Math.floor(seconds / 60);
    var hours = Math.floor(minutes / 60);

    var backgroundColor = 'gray'
    if (diffDays === 0) {
      diffDays = hours + ' hours ago'
    } else {
      diffDays = diffDays + ' days ago'
    }
    return { diffDays, backgroundColor };
  }

  changeAdvId(Advid, AdvLang, source) {
    this.postJobService.changeAdvId_lang(Advid, AdvLang, source)
  }

  changeAdvIdLog(source_type_adv) {
    this.managePost.send_adv_id_log(source_type_adv);
  }

  displayAdvrModal(job_adv_id) {
    this.AdvrId = job_adv_id;
    let source_type_adv = ['manage_advs', this.status];
    this.changeAdvId(job_adv_id, 1, source_type_adv);
    $('#AdvrsModal').modal('toggle');
  }

  displayAdvrLogModal(job_adv_id) {
    let source_type_adv = [job_adv_id, this.status]
    this.changeAdvIdLog(source_type_adv)
    $('#AdvrsLogModal').modal('toggle');
  }

  handlePopup($event) {
    if ($event.AdvType === 'template' && $event.edit) {
      this.edit_advr_temp($event.AdvId, 1)
    } else if ($event.AdvType != 'template' && $event.edit) {
      this.edit_advr($event.AdvId, 1)
    }

    if (!$event.edit) {
     if($event.action === 'activate'){
      this[$event.action]($event.AdvId,$event.active,$event.companyAdvId);
     }
     else if($event.action === 'saveAsTemplate'){
      this[$event.action]($event.AdvId,0,$event.companyAdvId);
     }
     else if($event.action === 'endWorkFlow' || $event.action === 'deleteAdvr'  || $event.action === 'delete_temp' || $event.action === 'saveAsPublish'  || $event.action === 'saveTemplateAsPublish'){
      this[$event.action]($event.AdvId,$event.companyAdvId);
     }
     else{
      this[$event.action]($event.AdvId);
     }
    }
  }

  Clear($event) {

    this._table.reset()
  }

  move_to_received_cvs(folder_id) {

    this.managePost.send_folder_id(folder_id)
    this.router.navigate(['/c', this.username, 'cvs_preview'])
  }

  ngOnDestroy(): void {
    this.changeAdvIdLog([]);
  }

}


interface adrv_data {
  advr: any
}

interface selectedLang {
  id: any
  name: any
}
