<h3 class="verf-heading"> Manage Companies</h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ companies.length }}</span>
   Selected:<span class="badge badge-primary badge-pill">{{ filteredCompanies.length }}</span></p>




 <p-table #dt [value]="companies" [(selection)]="filteredCompanies" dataKey="id" styleClass="ui-table-companies" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="companies.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['id','name', 'country', 'city', 'date', 'status','handled_by']">
    <ng-template pTemplate="caption">
         <!-- Companies -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th style="width: 10px;"></th>
            <th pSortableColumn="id" style="width: 70px;">ID <p-sortIcon field="id"></p-sortIcon></th>
            <th pSortableColumn="logo">Logo <p-sortIcon field="logo"></p-sortIcon></th>
            <th pSortableColumn="name">Company Name <p-sortIcon field="name"></p-sortIcon></th>
            <th pSortableColumn="country">Country  <p-sortIcon field="country"></p-sortIcon></th>
            <th pSortableColumn="city">City <p-sortIcon field="city"></p-sortIcon></th>
            <th pSortableColumn="date">Registration Date/Time <p-sortIcon field="date"></p-sortIcon></th>
            <th pSortableColumn="status">Status <p-sortIcon field="status"></p-sortIcon></th>
            <th pSortableColumn="show_in_home_page">Show in home page <p-sortIcon field="show_in_home_page"></p-sortIcon></th>
            <th pSortableColumn="permission">Permissions <p-sortIcon field="permission"></p-sortIcon></th>
            <th pSortableColumn="handled_by">Handled By <p-sortIcon field="handled_by"></p-sortIcon></th>
            <th pSortableColumn="installed_plugin">Installed Plugin <p-sortIcon field="installed_plugin"></p-sortIcon></th>
            <th style="width: 70px;"></th>
        </tr>
        <tr>
            <th>
                <!-- <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i *ngIf="filteredCompanies.length" class="fa fa-trash" data-toggle="modal" data-target="#modal" title="delete selected companies" (click)="displayDeleteAlert(4)" style="margin-right:-20px;"></i> -->
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'id', 'startsWith')" placeholder="" class="ui-column-filter" style="width: 40px;">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'logo', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'name', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'country',  'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'city', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <p-calendar #cl [(ngModel)]="rangeDates" (onSelect)="dt.filter(rangeDates, 'date', 'isBetween')" (onClearClick)="dt.filter('', 'date', 'contains')" [showButtonBar]="true" yearRange="2020:2030" selectionMode="range" styleClass="ui-column-filter" placeholder="" [readonlyInput]="true" dateFormat="yy-mm-dd"></p-calendar>
            </th>
            <th>
              <!-- <select #status (change)="dt.filter(status.value, 'status', 'equals')">
                <option value=""></option>
                <option value="not-verified">not verified</option>
                <option value="verified">verified</option>
                <option value="seems-fake">seems fake</option>
                <option value="fake">fake</option>
                <option value="not-checked">not checked</option>
              </select> -->
              <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'status_id', 'equals')" styleClass="ui-column-filter" [(ngModel)]="status"  [showClear]="false">
                  <ng-template let-option pTemplate="item">
                      <span [class]="'company-badge status-' + option.value">{{option.label}}</span>
                  </ng-template>
              </p-dropdown>
            </th>
            <th>
                <input type="text" pInputText placeholder="" (input)="dt.filter($event.target.value, 'show_in_home_page','contains')" class="ui-column-filter">
                <!-- [(ngModel)]="status" -->
              <!-- <p-dropdown [options]="showLogoOps" (onChange)="dt.filter($event.target.value, 'show_in_home_page', 'equals')" styleClass="ui-column-filter"  [showClear]="false">
                <ng-template let-option pTemplate="item">
                  <span>{{option.label}}</span>
                </ng-template>
              </p-dropdown> -->
            </th>

            <th>
              <!-- <p-multiSelect  [options]="permissions" (onChange)="dt.filter($event.value, 'permissions' , 'in')" optionLabel="name"  defaultLabel="Filter">
                  <ng-template let-option pTemplate="item">
                      <span [class]="'company-badge status-' + option.value">{{option.label}}</span>
                  </ng-template>
              </p-multiSelect> -->

              <!-- <p-dropdown [options]="permissions" (onChange)="dt.filter($event.value, 'company_permission_ids', 'equals')" styleClass="ui-column-filter"  [showClear]="false" optionLabel="name">
                <ng-template let-option pTemplate="item">
                    <span [class]="'company-badge status-' + option.value">{{option.label}}</span>
                </ng-template>
              </p-dropdown> -->
            </th>

            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'handled_by', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input type="text" pInputText placeholder="" (input)="dt.filter($event.target.value, 'installed_plugin','contains')" class="ui-column-filter">
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-company>
        <tr class="ui-selectable-row" [class.new-msg]="company.status === 'new'" (mouseover)="company.display = true" (mouseleave)="company.display = false" >
            <td>
                <!-- <p-tableCheckbox [value]="company" (click)="addToSelected(company)"></p-tableCheckbox> -->
            </td>
            <td>
              {{company.id}}
              <i class="fa fa-info-circle" title="view company log"  data-toggle="modal" data-target="#companyModal"  (click)="displayCompanyLog(company)"></i>
            </td>
            <td>
                <img alt="Logo" [src]="getImageLogo(company.logo)" />
            </td>
            <td>
               {{ company.name | summary:15}}
            </td>
            <td>
               {{ company.country | summary:15  }}
            </td>
            <td>
              {{ company.city | summary:15 }}
            </td>
            <td>
              {{ company.date }}
            </td>
            <td>
              <span [class]="'company-badge status-' + company.status">{{company.status}}  </span>
           </td>
           <td>
              <span style="cursor:pointer;" class="company-badge" (click)="toggleShowInHome(company)"
                [ngClass]="{'status-shown': company.show_in_home_page === 'Shown' , 'status-hidden' : company.show_in_home_page === 'Hidden'}"
                >{{company.show_in_home_page}}
              </span>
           </td>

           <td>
             {{company.permissions}}
           </td>
           <td>
            {{ company.handled_by | summary:15}}
           </td>
            <td>
              <span style="cursor:pointer;" class="company-badge" [ngClass]="{'status-shown': company.installed_plugin === 'Installed'  }"
                >{{company.installed_plugin}}
              </span>
            </td>
            <td>
              <span *ngIf="company.display">
                <!-- <i class="fa fa-trash" data-toggle="modal" data-target="#modal" title="delete this company" (click)="displayDeleteAlert(3, company)"></i> -->
                <i class="fa fa-edit"  data-toggle="modal" data-target="#companyModal"  (click)="previewCompanyModal(company)"></i>
                <i *ngIf="company.installed_plugin == null" class="fa fa-wordpress"  data-toggle="modal" data-target="#companyModal"  (click)="previewCompanyModal(company,true)"></i>
              </span>
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptycompany">
        <tr>
            <td colspan="9"style="text-align:center;padding:15px;"> No companies found.</td>
        </tr>
    </ng-template>
</p-table>





<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayCompanyModal" id="companyModal"  tabindex="-1" role="dialog" aria-labelledby="msgModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
         <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
         <!-- <h3>View Company <span *ngIf="mode === 'log_mode'">Log</span></h3> -->
         <h3></h3>
      </div>
      <div class="modal-body">
         <app-company-modal *ngIf="(company.id !== null && (mode ==='preview_mode'|| mode ==='install_plugin_mode')) || (company.id === null && company.message !=='' && (mode ==='preview_mode'|| mode ==='install_plugin_mode')) || mode==='log_mode'"  [mode]="mode" [company]="company" [companyLog]="companyLog" [methods]="methods" [statuses]="statuses" [permissions]="permissions"
          (sendCompanyStatus)="updateCompanyStatus($event)"  (openCoProfileClicked)="openCoProfile($event)" ></app-company-modal>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<!-- end of  modal-->





<!-- alert modal-->
<div class="modal fade" *ngIf="displayModal" id="modal"  tabindex="-2" role="dialog" aria-labelledby="ModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeAddModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="opperationNum === 3 || opperationNum === 4">Delete </h3>
      </div>
      <div class="modal-body">
       <!-- delete alert -->
        <div *ngIf="opperationNum === 3">
           <p>Are you sure you want to delete this Company? </p>
           <button type="button" class="btn btn-light" (click)="deleteCompany()">Yes</button>
        </div>

        <div *ngIf="opperationNum === 4">
          <p>Are you sure you want to delete these {{ filteredCompanies.length }} Companies? </p>
          <button type="button" class="btn btn-light" (click)="deleteMultiCompanies()">Yes</button>
       </div>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->
