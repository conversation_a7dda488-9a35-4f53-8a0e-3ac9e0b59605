/// <reference types="@types/googlemaps" />
import { Component, OnInit, OnDestroy, ElementRef, ChangeDetectorRef } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
// import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import 'rxjs/add/operator/takeUntil';
import { MessageService, SelectItem } from 'primeng/api';
// import { interval } from 'rxjs/observable/interval';
// import { map } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { Options } from 'ng5-slider';
import { Place } from 'shared/Models/place';
import { PostJobService } from '../../services/post-job.service';
// import { CompanyFormService } from '../../services/company-form.service';
import { ManagePostService } from "app/company/services/manage-post.service";
// import {TableModule} from 'primeng/table';
import { ImageProcessingService } from "shared/shared-services/image-processing.service";
import { DataMap } from "shared/Models/data_map";
import { Title } from '@angular/platform-browser';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
import { GeneralService } from '../../../general/services/general.service';
import { UrlValidator } from 'shared/validators/url.validators';
import { CompanyPostJobValidators } from './company-post-job.validators';
// import { ConditionalExpr } from '@angular/compiler';
import { ViewChild } from '@angular/core';
import { AiJobDescriptionComponent } from '../ai-job-description/ai-job-description.component';

declare var $: any;

@Component({
  selector: 'app-company-post-job',
  templateUrl: './company-post-job.component.html',
  styleUrls: ['./company-post-job.component.scss'],
  providers: [MessageService]
})
export class CompanyPostJobComponent implements OnInit, OnDestroy {

  skillsDD: any;
  jobTitleDD: any;

  valid_advr: boolean = false;
  valid_company: boolean = false;
  valid_salary: boolean = false;
  currency: any = [];
  // country_code: any;
  advertisement_title: any;
  submitted: boolean = false;
  no_data_entered: boolean = true;
  canSee: boolean = false;
  translated_data: any = [];
  edit_status: string;
  advId_edit;
  company_name: any;
  company_industry: any;
  clickMessage = '';
  focusMessage = '';
  postForm;
  username = '';
  private ngUnsubscribe: Subject<any> = new Subject();
  externalInfoVisible = false;
  externalSalaryInfoVisible = false;
  extraChecked = false;
  extraCheckedSalary = false;
  current_language = Number(localStorage.getItem('current_company_language'));
  companyId = Number(localStorage.getItem('company_id'));
  minValue: number = 0;
  maxValue: number = 30;
  minValue2: number = 18;
  maxValue2: number = 80;
  show = true;
  hide = false;

  online = false;

  currentLocation: Place = new Place();
  employmentTypeOpt: { [key: string]: Object }[] = [];
  advHasNoAction: boolean = false;
  submitWaiting: boolean = false;

  public remoteFields: Object = { text: 'name', value: 'employment_type_id' };
  public remoteFields2: Object = { text: 'name', value: 'international_language_id' };
  public remoteFields3: Object = { text: 'name', value: 'skill_type_id' };
  public remoteFields4: Object = { text: 'name', value: 'nationality_id' };
  public remoteFields5: Object = { text: 'name', value: 'id' };
  public remoteFields6: Object = { text: 'name', value: 'experience_field_id' };
  nationalites = [];
  degreeLevelValues = [];
  YearExpValues = [];
  ExpField = [];
  ExpField2 = [];
  job_titles = [];
  educationFields = [];
  school_education_fields = [];
  university_education_fields = [];

  filteredJob = [];
  filteredSkills: any[] = [];
  languages: { [key: string]: Object }[] = [];
  skillsAf = [];
  public value_emp: any = [];
  public value_lang: any = [];
  public value_skill: any = [];
  public value_nation: any = [];
  driving_license;
  company_locations = [];
  locations = [];
  types1: SelectItem[];
  types1_1: SelectItem[];
  types2: SelectItem[];
  types2_2: SelectItem[];
  payment_Date: SelectItem[];
  gender: SelectItem[];
  selectedType_salary_type: string;
  selectedType_gender: string;
  selectedType_major: string;
  selectedType_major_holder: string
  selectedType_minor: string;
  salary_from_value: string;
  salary_to_value: string;
  filteredCurrency = [];
  noUploadedImage = './assets/images/Capture.PNG';
  image_uploaded_url = this.noUploadedImage;
  checklist = [];
  checkedlist = [];
  checked2list = [];

  page_name = '';
  checkedList: any;
  @ViewChild('googlelocationplace') public googlelocationplaceRef: ElementRef;
  @ViewChild('otherCompanyLocation') public otherCompanyLocationRef: ElementRef;
  @ViewChild('aiJobDescription') aiJobDescription: AiJobDescriptionComponent;

  options: Options = {
    floor: 0,
    ceil: 30,
    step: 1
  };
  subscription: Subscription;
  options2: Options = {
    floor: 18,
    ceil: 80,
    step: 1
  };
  data_map = new DataMap();
  selectedCurrency;
  ability_to_post: any;
  status: any = '';
  companyHaveProfile: boolean;
  formValid: boolean;

  company_permissions = { job_publisher_link: false, job_publisher_other_companies: false };
  adv_permissions = { job_publisher_link: false, job_publisher_other_companies: false };

  //for some reason, jquery not working well when initializing the form and hiding other-company div
  //with slide up method, so we used this variable
  hideOtherCompanyDiv = false;
  hideOtherCompanyWorkLocation = false;
  hideMyCompanyExternalLink = true;


  perPhotoSrc: string = "./assets/images/Capture.PNG";
  noProfilePicSet: string = "./assets/images/Capture.PNG";
  uploadLabelDisplay = true;
  imgError: string = null;
  uploadedFiles: any[] = [];
  image_code_to_send: { file: string, file_type: string, is_deleted: boolean, name: string } = { file: '', file_type: '', is_deleted: false, name: "" }
  existLogoLink = '';
  // logoImageNotFoundError = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    // private companyFormService: CompanyFormService,
    private messageService: MessageService,
    private postJobService: PostJobService,
    private managePost: ManagePostService,
    private translate: TranslateService,
    //  private get_img : ImageProcessingService,
    private imageProcessingService: ImageProcessingService,
    private title: Title,
    private lazyloadDropdownService: LazyloadDropdownService,
    private generalService: GeneralService,
    private cdRef: ChangeDetectorRef,
  ) {

    this.subscription = postJobService.Data.takeUntil(this.ngUnsubscribe).subscribe(val => {
      this.translated_data = val;
    });

    if (this.router.getCurrentNavigation().extras.state) {
      let state;
      state = this.router.getCurrentNavigation().extras.state;
      if (state.advOptions && state.advOptions.showAdvOptions)
        this.advHasNoAction = state.advOptions.showAdvOptions;
    }

    this.checklist = [
      { id: 0, value: 'is_mandatory_years_of_experience', isSelected: 0 },
      { id: 1, value: 'is_mandatory_gender', isSelected: 0 },
      { id: 2, value: 'is_mandatory_age', isSelected: 0 },
      { id: 3, value: 'is_mandatory_nationality', isSelected: 0 },
      { id: 4, value: 'is_mandatory_degree_level', isSelected: 0 },
      { id: 5, value: 'is_mandatory_education', isSelected: 0 },
      // {id: 6, value: 'is_mandatory_minor', isSelected: 0},
      { id: 6, value: 'is_mandatory_languages', isSelected: 0 },
      { id: 7, value: 'is_mandatory_skills', isSelected: 0 },
      { id: 8, value: 'is_mandatory_certification', isSelected: 0 },
      { id: 9, value: 'is_mandatory_current_location', isSelected: 0 },
      { id: 10, value: 'is_mandatory_driving_license', isSelected: 0 },
    ];

    this.types1 = [
      { label: 'Yearly', value: 'Yearly' },
      { label: 'Monthly', value: 'Monthly' },
      { label: 'Weekly', value: 'Weekly' },
      { label: 'Hourly', value: 'Hourly' }
    ];

    this.types1_1 = [
      { label: 'سنوي', value: 'Yearly' },
      { label: 'شهري', value: 'Monthly' },
      { label: 'أسبوعي', value: 'Weekly' },
      { label: 'ساعي', value: 'Hourly' }
    ];

    this.types2 = [
      { label: 'Male', value: 'Male' },
      { label: 'Female', value: 'Female' },
    ];

    this.types2_2 = [
      { label: 'غير محدد', value: 'Any' },
      { label: 'ذكر', value: 'Male' },
      { label: 'أنثى', value: 'Female' },
    ];
    this.postForm = this.fb.group({
      company_id: [''],
      translated_languages_id: [''],
      exp_field: ['', Validators.required],
      adv_title: ['', Validators.required],
      job_title: ['', Validators.required],
      employment_type_temp: ['', Validators.required],
      employment_types: [''],
      salary_from: ['', Validators.pattern("^[0-9]*$")],
      salary_to: ['', Validators.pattern("^[0-9]*$")],
      salary_type: [''],
      currency_id: [''],
      gender: [''],
      year_of_experience: [''],
      age: [''],
      age_from: [''],
      age_to: [''],
      job_description: ['', Validators.maxLength(7000)],
      nationality_temp_id: [''],
      nationalities: this.fb.array([]),

      degree_level: [''],
      degree_level_id: [''],
      exp_field_id: ['', Validators.required],
      educations: [''],

      languages_temp: [''],
      international_languages: this.fb.array([]),

      skills: [''],
      skill_types: this.fb.array([]),
      certification: [''],
      driving_license_temp: [''],
      driving_license: [''],

      opportunity_for: ['my_company'], // my_company , other_company
      company_locations: [''],
      company_location_id: [''],
      is_online: [''],
      show_company_name: [''],

      apply_on: ['cveek-inbox'],
      external_link: ['', UrlValidator.isValidUrlFormat],

      other_employer: this.fb.group({
        name: [''],
        // logo_name: new FormControl('', {
        //   validators: [],
        //   asyncValidators: [CompanyPostJobValidators.enteredLogoExistValidator(this.postJobService)],
        //   updateOn: 'blur'
        // }),
        logo_name: ['', [], CompanyPostJobValidators.enteredLogoExistValidator(this.postJobService)],
        company_industries: [[]],
        location_type: ['onsite'],
        full_work_location: [''],
        work_location: this.fb.group({
          'country': [''],
          'country_code': [''],
          'city': [''],
          'latitude': [''],
          'longitude': [''],
          'street_address': [''],
          'postal_code': [''],
        }),
        remote: false,
        logo: [{}],
        no_exist_logo_name: [false]
      }),

      //jobseeker current location
      location: [''],
      current_location: this.fb.group({
        'city': [''],
        'country': [''],
        'latitude': [''],
        'longitude': [''],
        'governorate': [''],
        'street_address': [''],
        'postal_code': [''],
        'country_code': ['']
      }),

      is_mandatory_years_of_experience: [''],
      is_mandatory_gender: [''],
      is_mandatory_age: [''],
      is_mandatory_nationality: [''],
      is_mandatory_degree_level: [''],
      is_mandatory_education: [''],
      is_mandatory_languages: [''],
      is_mandatory_skills: [''],
      is_mandatory_certification: [''],
      is_mandatory_current_location: [''],
      is_mandatory_driving_license: ['']
    }
      , { validator: CompanyPostJobValidators.validJobSeekerLocationValidator }
    );

    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
  }

  private createSkills() {

    return new FormGroup({
      'name': new FormControl(''),
      'skill_id': new FormControl('')
    });
  }


  onClickMe() {
    this.clickMessage = 'The Request is being Processed...';
    this.page_name = 'company_step';
  }
  sho(e: any) {
    this.canSee = true;
  }

  hid(e: any) {
    this.canSee = false;
  }

  get skills() {
    return this.postForm.get('skills') as FormArray;
  }

  get skill_types() {
    return this.postForm.get('skill_types') as FormArray;
  }

  get employment_types() {
    return this.postForm.get('employment_types') as FormControl;
  }

  get international_languages() {
    return this.postForm.get('international_languages') as FormArray;
  }

  get nationalities() {
    return this.postForm.get('nationalities') as FormArray;
  }

  get educationFieldsControl() {
    return (this.postForm.controls['educations'] as FormControl);
  }

  get jobTitleControl() {
    return (this.postForm.controls['job_title_s'] as FormGroup);
  }

  get degreeLevelControl() {
    return (this.postForm.controls['degree_level'] as FormControl);
  }

  get expFieldControl() {
    return (this.postForm.controls['exp_field'] as FormControl);
  }

  get currentLocationControl() {
    return (this.postForm.controls['current_location'] as FormGroup);
  }

  get otherEmployerControl() {
    return (this.postForm.controls['other_employer'] as FormGroup);
  }
  get otherCompanyLocationControl() {
    return (this.postForm.controls['other_employer'].controls['work_location'] as FormGroup);
  }

  selectJobTitle(event) {
    this.jobTitleControl.controls['id'].setValue(event.id);
  }

  setEducationFieldsOptions() {
    if (this.postForm.get('degree_level_id').value > 2) {
      this.educationFields = this.university_education_fields;
    }
    else if (this.postForm.get('degree_level_id').value === 2) {
      this.educationFields = this.school_education_fields;
    }
  }
  changeDegreeLevel() {
    this.postForm.get('degree_level_id').setValue(this.degreeLevelControl.value.degree_level_id);
    this.educationFieldsControl.setValue("");
    this.setEducationFieldsOptions();
  }

  changeExpField() {
    this.postForm.get('exp_field_id').setValue(this.expFieldControl.value.experience_field_id);
  }

  onKeyUPJobTitle() {
    this.jobTitleControl.get('id').setValue(-1);
  }

  setcompanyLanguage(languageId) {
    if (languageId && languageId === 1) {
      this.translate.setDefaultLang('en');

    } else if (languageId === 2) {
      this.translate.setDefaultLang('ar');

    } else {
      this.translate.setDefaultLang('en');
    }
  }

  hideShowCompIdentity(type) {
    if (type === 'show') {
      this.show = true;
      this.hide = false;
      //set company location validation required
      this.postForm.controls['company_locations'].setValidators([Validators.required]);
      this.postForm.controls['company_locations'].updateValueAndValidity();
    } else if (type === 'hide') {
      this.show = false;
      this.hide = true;
      //clear company location validation required
      this.postForm.controls['company_locations'].clearValidators();
      this.postForm.controls['company_locations'].updateValueAndValidity();
    }

  }

  /*--- handle the check buttons near the form elements "mandatory"
        to preaper the list of those checks to send with the
        submitted Data to the serve "backend" ---*/
  CheckItemList($event) {
    let list = this.checklist;
    let length = this.checklist.length;

    if ($event.target.checked === true) {
      //  let value = $event.path[0].value;
      let value = $event.target.value;
      for (let i = 0; i < length; i++) {
        if (list[i].value === value && list[i].isSelected !== 1) {
          list[i].isSelected = 1;
        }
      }
    } else {
      //let value = $event.path[0].value;
      let value = $event.target.value;
      for (let i = 0; i < length; i++) {
        if (list[i].value === value && list[i].isSelected === 1) {
          list[i].isSelected = 0;
        }
      }
    }
    this.checked2list = list;
  }

  /*--- get the checked mandatories ---*/
  getCheckedItemList() {
    this.checkedList = [];
    for (let i = 0; i < this.checked2list.length; i++) {
      this.checkedList.push(this.checked2list[i]);
    }
    /* this.checkedList = JSON.stringify(this.checkedList); */
    return this.checkedList;
  }


  minimize(type, moreInfo?) {
    if (type === 'more-values') {
      this.extraChecked = !this.extraChecked;

      if (!this.externalInfoVisible) {
        this.externalInfoVisible = true;
        $('.extraInformation').show('slow');
        $(".more").css("margin-top", "-10px")
      } else {
        this.externalInfoVisible = false;
        $('.extraInformation').hide('slow');
        $(".more").css("margin-top", "-10px");
        $(".more").css("margin-left", "80px")
      }
    }
    else if (type === 'salary') {
      this.extraCheckedSalary = !this.extraCheckedSalary;

      if (!this.externalSalaryInfoVisible) {
        this.externalSalaryInfoVisible = true;
        $('.extraInformationSalary').show('slow');
        $(".more").css("margin-top", "-10px");
      } else {
        this.externalSalaryInfoVisible = false;
        $('.extraInformationSalary').hide('slow');
        $(".more").css("margin-top", "-10px");
        $(".more").css("margin-left", "80px");
      }
    }
    else if (type === 'applyOn') {
      if (moreInfo === 'company-website') {
        this.postForm.controls['external_link'].setValidators([Validators.required, UrlValidator.isValidUrlFormat]);
        $('.company-apply-link').slideDown('slow');
      }
      else if (moreInfo === 'cveek-inbox') {
        this.postForm.controls['external_link'].clearValidators([Validators.required]);
        $('.company-apply-link').slideUp('slow');
        this.postForm.controls['external_link'].setValue("");
      }
      this.postForm.controls['external_link'].updateValueAndValidity();
    }
    else if (type === 'opportunityFor') {
      if (moreInfo === 'my_company') {
        this.postForm.controls['company_locations'].setValidators([Validators.required]);
        this.postForm.controls['company_locations'].updateValueAndValidity();
        $('.other-company').slideUp('slow');
        $('.my-company').slideDown('slow');

        //reset other company fields
        this.otherEmployerControl.reset();
        this.postForm.controls['external_link'].setValue("");
        this.otherEmployerControl.controls['location_type'].setValue('onsite');
        this.otherEmployerControl.controls['remote'].setValue(false);
        this.otherEmployerControl.controls['no_exist_logo_name'].setValue(false);
        this.otherEmployerControl.controls['logo_name'].setValue('');
        this.existLogoLink = '';

        //clear conditional validation in other company fields
        this.otherEmployerControl.controls['name'].clearValidators();
        this.otherEmployerControl.controls['company_industries'].clearValidators();
        this.otherEmployerControl.controls['remote'].clearValidators();
        this.otherCompanyLocationControl.controls['country_code'].clearValidators();
        this.otherEmployerControl.controls['full_work_location'].clearValidators();
        this.otherEmployerControl.controls['name'].updateValueAndValidity();
        this.otherEmployerControl.controls['company_industries'].updateValueAndValidity();
        this.otherEmployerControl.controls['remote'].updateValueAndValidity();
        this.otherCompanyLocationControl.controls['country_code'].updateValueAndValidity();
        this.otherEmployerControl.controls['full_work_location'].updateValueAndValidity();
        this.postForm.controls['external_link'].clearValidators([Validators.required]);
        this.postForm.controls['external_link'].updateValueAndValidity();
      }
      else if (moreInfo === 'other_company') {
        $('.my-company').slideUp('slow');
        $('.other-company').slideDown('slow');

        //add conditional validation to other company fields
        this.otherEmployerControl.controls['name'].setValidators([Validators.required]);
        this.otherEmployerControl.controls['company_industries'].setValidators([Validators.required]);
        this.otherEmployerControl.controls['remote'].setValidators([Validators.required]);
        this.otherCompanyLocationControl.controls['country_code'].setValidators([Validators.required]);
        this.otherEmployerControl.controls['full_work_location'].setValidators([Validators.required]);
        this.otherEmployerControl.controls['name'].updateValueAndValidity();
        this.otherEmployerControl.controls['company_industries'].updateValueAndValidity();
        this.otherEmployerControl.controls['remote'].updateValueAndValidity();
        this.otherCompanyLocationControl.controls['country_code'].updateValueAndValidity();
        this.otherEmployerControl.controls['full_work_location'].updateValueAndValidity();
        this.postForm.controls['external_link'].setValidators([Validators.required, UrlValidator.isValidUrlFormat]);
        this.postForm.controls['external_link'].updateValueAndValidity();
        //  this.otherEmployerControl.updateValueAndValidity();
        //reset my company values
        this.postForm.controls['company_locations'].setValue("");
        this.hideShowCompIdentity('show');
        if (this.company_permissions['job_publisher_link'] === true) {
          this.postForm.controls['apply_on'].setValue('cveek-inbox');
          $('.company-apply-link').slideUp('slow');
          this.postForm.controls['external_link'].setValue("");
        }
        this.postForm.controls['company_locations'].clearValidators([Validators.required]);
        this.postForm.controls['company_locations'].updateValueAndValidity();

      }

    }
    else if (type === 'locationType') {
      if (moreInfo === 'onsite') {
        this.otherEmployerControl.controls['full_work_location'].setValidators([Validators.required]);
        this.otherCompanyLocationControl.controls['country_code'].setValidators([Validators.required]);
        this.otherEmployerControl.controls['full_work_location'].updateValueAndValidity();
        this.otherCompanyLocationControl.controls['country_code'].updateValueAndValidity();
        this.otherEmployerControl.controls['remote'].setValue(false);
        $('.other-company-location').slideDown('slow');
      }
      else if (moreInfo === 'remote') {
        $('.other-company-location').slideUp('slow');
        this.otherEmployerControl.controls['full_work_location'].clearValidators();
        this.otherCompanyLocationControl.controls['country_code'].clearValidators();
        this.otherEmployerControl.controls['full_work_location'].updateValueAndValidity();
        this.otherCompanyLocationControl.controls['country_code'].updateValueAndValidity();
        this.otherEmployerControl.controls['remote'].setValue(true);
        this.otherEmployerControl.controls['full_work_location'].setValue("");
        this.otherCompanyLocationControl.reset();
      }
      // this.otherEmployerControl.controls['full_work_location'].updateValueAndValidity();
      //this.otherCompanyLocationControl.controls['country_code'].updateValueAndValidity();
    }
  }

  setFullLocation() {
    let country = this.postForm.controls['country'].value;
    let city = this.postForm.controls['city'].value;
    if (country !== '' && city !== '') {
      return city + ', ' + country;
    } else if (country !== '') {
      return country;
    } else if (city !== '') {
      return city;
    } else {
      return '';
    }
  }

  // for job seeker location - google auto complete -
  private getLocationPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if (navigator.userAgent.match(/Googlebot/i)) {
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googlelocationplaceRef.nativeElement,
      {
        types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components', 'geometry']  // 'name'
      });
    autocomplete.addListener('place_changed', (res) => {

      const place = autocomplete.getPlace();
      this.getLocationAddress(place);
    });

  }

  getLocationAddress(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place)
    this.currentLocationControl.controls['country'].setValue(data_location.country);
    this.currentLocationControl.controls['city'].setValue(data_location.city);
    this.currentLocationControl.controls['country_code'].setValue(data_location.country_code);
  }

  //for other company location - google auto complete -
  private getLocationOtherCompany() {
    //to stop bot traffic to google maps
    if (navigator.userAgent.match(/Googlebot/i)) {
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.otherCompanyLocationRef.nativeElement,
      {
        types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components', 'geometry']  // 'name'
      });
    autocomplete.addListener('place_changed', (res) => {

      const place = autocomplete.getPlace();
      this.getLocationAddressOtherCompany(place);
    });

  }

  getLocationAddressOtherCompany(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place);
    this.otherCompanyLocationControl.controls['country'].setValue(data_location.country);
    this.otherCompanyLocationControl.controls['city'].setValue(data_location.city);
    this.otherCompanyLocationControl.controls['country_code'].setValue(data_location.country_code);
    this.otherCompanyLocationControl.controls['street_address'].setValue(data_location.street_address);
    this.otherCompanyLocationControl.controls['postal_code'].setValue(data_location.postal_code);
    this.otherCompanyLocationControl.controls['latitude'].setValue(data_location.latitude);
    this.otherCompanyLocationControl.controls['longitude'].setValue(data_location.longitude);
  }

  /*--- fill up the form element's data */
  initializeFormData(data_get?) {
    this.postJobService.getPostFormData(this.current_language).subscribe(
      (res) => {
        if (res['company_profile_name'] !== null) {
          this.companyHaveProfile = true;
          if (res['company_profile_name'].company_logo !== null) {
            this.image_uploaded_url = this.imageProcessingService.getImagePath('companyLogo', 'med_thumbnail', res['company_profile_name'].company_logo);
            //   this.image_uploaded_url = this.get_img.getImage(res['company_profile_name'].company_logo,'storage/company/logo/','');
          }
          else {
            this.image_uploaded_url = './assets/images/Capture.PNG';
          }
        }
        else {
          this.companyHaveProfile = false;
          this.image_uploaded_url = './assets/images/Capture.PNG';
        }

        this.ability_to_post = res['waring_massage_comp_profile'];

        if (this.companyHaveProfile === false) {
          this.messageService.add({ key: "companyDontHaveProfile", severity: 'warn', summary: 'Warning Message', detail: res['waring_massage_comp_profile'], sticky: true });
        }

        if (res['company_profile_name'] !== null && res['company_profile_name'].company_profile_translations.length != 0) {
          this.company_name = res['company_profile_name'].company_profile_translations[0].name;
        } else {
          this.company_name = res['company_user_name'];
        }


        for (let i = 0; i < res['employment_types'].length; i++) {
          this.employmentTypeOpt[i] = {
            'employment_type_id': res['employment_types'][i]['id'],
            'name': res['employment_types'][i].emp_type_parent_translation[0].name
          };
        }

        for (let i = 0; i < res['nationalities'].length; i++) {
          if (res['nationalities'][i].nationality_translation[0] !== undefined) {
            this.nationalites[i] = {
              'nationality_id': res['nationalities'][i].nationality_translation[0].nationality_id,
              'name': res['nationalities'][i].nationality_translation[0].name
            };
          }
        }

        this.currency = res['currency'];
        //this.selectedCurrency = {id:39 , name:'United States dollar'}
        for (let i = 0; i < res['exp_field'].length; i++) {
          this.ExpField[i] = {
            'experience_field_id': res['exp_field'][i].experience_field_translation[0].experience_field_id,
            'name': res['exp_field'][i].experience_field_translation[0].name
          };
        }
        //  this.ExpField2 = this.ExpField;
        Object.assign(this.ExpField2, this.ExpField);
        this.ExpField.unshift({ 'experience_field_id': null, 'name': "" });
        this.YearExpValues = res['years_of_exp']
        this.YearExpValues.unshift({ 'year_of_exp_id': '', 'name': null });

        for (let i = 0; i < res['degree_level'].length; i++) {
          // tslint:disable-next-line:max-line-length
          this.degreeLevelValues[i] = {
            'degree_level_id': res['degree_level'][i].degree_level_translation[0].degree_level_id,
            'name': res['degree_level'][i].degree_level_translation[0].name
          };
        }
        this.degreeLevelValues.unshift({ 'degree_level_id': '', 'name': '' });

        this.school_education_fields = res['school_educations'];
        this.university_education_fields = res['university_educations'];
        this.educationFields = this.university_education_fields;

        for (let i = 0; i < res['languages'].length; i++) {
          // tslint:disable-next-line:max-line-length
          if (res['languages'][i].international_language_trans[0] !== undefined) {
            this.languages[i] = {
              'international_language_id': res['languages'][i].id,
              'name': res['languages'][i].international_language_trans[0].name,

            };
          }

        }

        if (res['company_location'].length > 0) {
          for (let i = 0; i < res['company_location'][0].company_locations.length; i++) {
            // tslint:disable-next-line:max-line-length
            this.company_locations[i] = {
              'company_location_id': res['company_location'][0].company_locations[i].id,
              'company_profile_id': res['company_location'][0].company_locations[i].company_profile_id,
              'company_name': res['company_location'][0].company_locations[i].name,
              'country': res['company_location'][0].company_locations[i].country,
              'city': res['company_location'][0].company_locations[i].city,
              'full_location': res['company_location'][0].company_locations[i].country +
                ' / ' + res['company_location'][0].company_locations[i].city
            };
          }
        }
        // company have profile but didn't enter location info
        else if (this.companyHaveProfile) {
          this.messageService.add({ key: 'companyDontHaveLocation', severity: 'warn', summary: 'Warning Message', detail: "Please enter the location of the company branches to show them in Location field", sticky: true });
        }
        this.company_locations.push({ 'company_location_id': -1, 'company_name': 'Remote' });
        //   this.company_locations.unshift({'company_location_id': '', 'company_name': ''});
        this.company_locations.unshift(null);
        //add company location validation required - because show company name is default value
        this.postForm.controls['company_locations'].setValidators([Validators.required]);
        this.postForm.controls['company_locations'].updateValueAndValidity();

        //initial values for radio buttons and visible options
        this.company_permissions = res['company_permissions'];
        if (this.company_permissions['job_publisher_other_companies'] === true) {
          this.postForm.controls['opportunity_for'].setValue('my_company');
          this.minimize('opportunityFor', 'my_company');
          this.hideOtherCompanyDiv = true;
        }

        // this.getLocationPlaceAutocomplete();
        // this.getLocationOtherCompany();

        if (data_get !== undefined) {
          this.buildFilledForm(data_get)
        }
        if (this.extraCheckedSalary === false) {
          this.externalSalaryInfoVisible = false;
          $('.extraInformationSalary').hide('slow');
          $(".more").css("margin-top", "-10px");
          $(".more").css("margin-left", "80px")
        }

      });
    if (this.current_language === 1) {
      this.payment_Date = this.types1;
      this.gender = this.types2;
    } else if (this.current_language === 2) {
      this.payment_Date = this.types1_1;
      this.gender = this.types2_2;
    }

    this.driving_license = this.data_map.driving_license
  }

  Currency(event) {
    let query = event.query;
    this.filteredCurrency = [];

    this.filteredCurrency = this.filterCurrency(query, this.currency);
  }

  filterCurrency(query, currencies: any[]) {
    let filtered: any[] = [];
    for (let i = 0; i < currencies.length; i++) {
      if (currencies[i] !== undefined) {
        let filter_currency = currencies[i];

        if (filter_currency.name.toLowerCase().indexOf(query.toLowerCase()) == 0) {
          filtered.push(filter_currency);
        }
      }
    }
    return filtered;
  }

  /*--- fill up the form's Data when we initialize the interface with advertisement's data came form
        manage-advs ---*/

  buildFilledForm(data_get) {
    let translated_advrs = data_get[0].translated_advrs[0];
    let shared_data = data_get[0].shared_data;
    this.status = shared_data['status'];
    let salary_from, salary_to, salary_type;
    let age_from, age_to;
    let gender;
    let jobSeeker_location = '';

    let certification;
    let company_location_id;
    this.postForm.controls['job_title'].patchValue(translated_advrs.translated_advr_data['job_titles']);
    this.selectedCurrency = translated_advrs.translated_advr_data['currency_code'];
    if (this.selectedCurrency === null) {
      this.selectedCurrency = { "id": 39, "name": "United States dollar" };
    }

    if (translated_advrs.translated_advr_data.job_adv_temp_trans) {
      this.postForm.controls['adv_title'].setValue(translated_advrs.translated_advr_data.job_adv_temp_trans.job_adv_title);
    } else {
      this.postForm.controls['adv_title'].setValue(translated_advrs.translated_advr_data.job_adv_trans.job_adv_title);
    }


    if (translated_advrs['exp_field'] !== null) {
      for (let i = 0; i < this.ExpField.length; i++) {
        if (this.ExpField[i].experience_field_id === translated_advrs['translated_advr_data'].exp_field.id) {
          this.postForm.controls['exp_field'].setValue(this.ExpField[i]);
          this.postForm.get('exp_field_id').setValue(this.postForm.controls['exp_field'].value.experience_field_id);
        }
      }
    }

    //start set values of step3: company information
    this.adv_permissions = shared_data['adv_permissions'];
    if (shared_data['opportunity_for'] === 'my_company') {
      this.postForm.controls['opportunity_for'].setValue('my_company');
      this.minimize('opportunityFor', 'my_company');
      this.hideOtherCompanyDiv = true;

      if (shared_data['company_location_id'] !== null) {
        company_location_id = shared_data['company_location_id'];
        for (let i = 1; i < this.company_locations.length; i++) {
          if (this.company_locations[i].company_location_id === shared_data['company_location_id']) {
            this.postForm.controls['company_locations'].setValue(this.company_locations[i]);
          }
        }
      }
      else if (shared_data['is_online'] === 1) {
        this.postForm.controls['company_locations'].setValue(this.company_locations[this.company_locations.length - 1]);
      }

      if (shared_data['show_company_name'] === 1) {
        this.show = true;
        this.hide = false;
        this.postForm.controls['company_locations'].setValidators([Validators.required]);
        this.postForm.controls['company_locations'].updateValueAndValidity();
      } else {
        this.hide = true;
        this.show = false;
        //clear company location validation required
        this.postForm.controls['company_locations'].clearValidators();
        this.postForm.controls['company_locations'].updateValueAndValidity();
      }

      if (shared_data['external_link'] !== null) {
        this.postForm.controls['apply_on'].setValue('company-website');
        this.postForm.controls['external_link'].setValue(shared_data['external_link']);
        this.minimize('applyOn', 'company-website');
        this.hideMyCompanyExternalLink = false;
      }
    }
    else if (shared_data['opportunity_for'] === 'other_company') {
      this.postForm.controls['opportunity_for'].setValue('other_company');
      this.minimize('opportunityFor', 'other_company');
      this.hideOtherCompanyDiv = false;

      let otherEmployer = translated_advrs.translated_advr_data.other_employer;
      this.otherEmployerControl.controls['name'].setValue(otherEmployer.name);
      this.otherEmployerControl.controls['no_exist_logo_name'].setValue(otherEmployer.no_exist_logo_name);
      if (this.otherEmployerControl.controls['no_exist_logo_name'].value === false) {
        this.otherEmployerControl.controls['logo_name'].setValue(otherEmployer.logo);
        this.existLogoLink = this.imageProcessingService.getImagePath('otherEmployerLogo', 'med_thumbnail', otherEmployer.logo);
      }
      //for unknown reason after setting the value it cause issue, that ican't delete any selected value,
      //and when i select any new value the id of this value is added with string instead of number!!!
      // but the original value of id in options array is numerical!!!
      // i converted it to string to solve the issue
      let industries_temp = [];
      for (let i = 0; i < otherEmployer.industries.length; i++) {
        industries_temp[i] = otherEmployer.industries[i].toString();
      }
      this.otherEmployerControl.controls['company_industries'].setValue(industries_temp);
      //   this.otherEmployerControl.controls['company_industries'].setValue(otherEmployer.industries);
      this.otherEmployerControl.controls['remote'].setValue(otherEmployer.remote);
      if (this.otherEmployerControl.controls['remote'].value === true) {
        this.otherEmployerControl.controls['location_type'].setValue('remote');
        this.minimize('locationType', 'remote');
        this.hideOtherCompanyWorkLocation = true;
      }
      else {
        this.hideOtherCompanyWorkLocation = false;
      }

      if (otherEmployer.work_location !== null && otherEmployer.work_location.country_code !== null) {
        this.otherEmployerControl.controls['location_type'].setValue('onsite');
        this.otherCompanyLocationControl.controls['country'].setValue(otherEmployer.work_location.country);
        this.otherCompanyLocationControl.controls['city'].setValue(otherEmployer.work_location.city);
        this.otherCompanyLocationControl.controls['country_code'].setValue(otherEmployer.work_location.country_code);
        this.otherCompanyLocationControl.controls['street_address'].setValue(otherEmployer.work_location.street_address);
        this.otherCompanyLocationControl.controls['postal_code'].setValue(otherEmployer.work_location.postal_code);
        this.otherCompanyLocationControl.controls['latitude'].setValue(otherEmployer.work_location.latitude);
        this.otherCompanyLocationControl.controls['longitude'].setValue(otherEmployer.work_location.longitude);
        let full_work_location = otherEmployer.work_location.country;
        if (otherEmployer.work_location.city !== null)
          full_work_location = full_work_location + ', ' + otherEmployer.work_location.city;
        if (otherEmployer.work_location.street_address !== null)
          full_work_location = full_work_location + ', ' + otherEmployer.work_location.street_address;
        this.otherEmployerControl.controls['full_work_location'].setValue(full_work_location);
      }

      if (shared_data['external_link'] !== null) {
        this.postForm.controls['external_link'].setValue(shared_data['external_link']);
      }
      //set other employer logo
      if (otherEmployer.logo !== null) {
        this.perPhotoSrc = this.imageProcessingService.getImagePath('otherEmployerLogo', 'med_thumbnail', otherEmployer.logo);
        //  this.perPhotoSrc = this.get_img.getImage(otherEmployer.logo,'storage/company/other_employer/','');
        this.uploadLabelDisplay = false;
      }
      else {
        this.perPhotoSrc = './assets/images/Capture.PNG';
        this.uploadLabelDisplay = true;
      }

    }
    //end set values of step3: company information

    if (translated_advrs.translated_advr_data['year_of_exp'] !== null) {
      for (let i = 0; i < this.YearExpValues.length; i++) {
        if (this.YearExpValues[i].year_of_exp_id === translated_advrs['translated_advr_data'].year_of_exp.id) {
          this.postForm.controls['year_of_experience'].setValue(this.YearExpValues[i]);
        }
      }
    }

    if (translated_advrs.translated_advr_data['international_languages']) {
      for (let i = 0; i < translated_advrs.translated_advr_data['international_languages'].length; i++) {
        this.value_lang[i] = translated_advrs.translated_advr_data['international_languages'][i]
      }
      this.postForm.controls['languages_temp'].setValue(this.value_lang);
    }

    if (translated_advrs.translated_advr_data['nationality']) {
      for (let i = 0; i < translated_advrs.translated_advr_data['nationality'].length; i++) {
        this.value_nation[i] = translated_advrs.translated_advr_data['nationality'][i]
      }
    }

    this.postForm.controls['nationality_temp_id'].setValue(this.value_nation);

    this.postForm.controls['skills'].setValue(translated_advrs.translated_advr_data['skill_types']);


    if (translated_advrs.translated_advr_data['employment_type_parent']) {
      for (let i = 0; i < translated_advrs.translated_advr_data['employment_type_parent'].length; i++) {
        this.value_emp[i] = translated_advrs.translated_advr_data['employment_type_parent'][i]
      }
    }

    if (translated_advrs.translated_advr_data['degree_level']) {
      for (let i = 0; i < this.degreeLevelValues.length; i++) {
        if (this.degreeLevelValues[i].degree_level_id === translated_advrs.translated_advr_data['degree_level'].id) {
          this.postForm.controls['degree_level'].setValue(this.degreeLevelValues[i]);
        }
      }
      this.postForm.controls['degree_level_id'].setValue(this.postForm.controls['degree_level'].value.degree_level_id);
    }

    this.postForm.controls['employment_type_temp'].setValue(this.value_emp);

    if (shared_data['salary_from'] !== null && shared_data['salary_to'] !== null) {
      salary_from = shared_data['salary_from'];
      salary_to = shared_data['salary_to'];
      salary_type = shared_data['salary_type'];
      this.postForm.controls['salary_from'].setValue(salary_from);
      this.postForm.controls['salary_to'].setValue(salary_to);
      this.selectedType_salary_type = salary_type;
      this.externalSalaryInfoVisible = true;
      $('.extraInformationSalary').show('slow');
      $(".more").css("margin-top", "-10px")
      this.extraCheckedSalary = true;
    }
    else {
      this.extraCheckedSalary = false;
    }
    /*  else return "hi"; */

    if (shared_data['age_from'] !== null) {
      age_from = shared_data['age_from'];
      age_to = shared_data['age_to'];
      this.minValue2 = age_from;
      this.maxValue2 = age_to;
    }

    if (shared_data['gender'] !== null) {
      gender = shared_data['gender'];
      this.selectedType_gender = gender;
    }

    let city;
    if (shared_data['city'] !== null) {
      city = shared_data['city'];
    } else {
      city = ''
    }

    if (shared_data['country'] !== null) {
      jobSeeker_location = city + ' ' + shared_data['country'];
      this.currentLocationControl.controls['country'].setValue(shared_data['country']);
      this.currentLocationControl.controls['city'].setValue(city);
      this.currentLocationControl.controls['country_code'].setValue(shared_data['country_code']);
    }

    this.postForm.controls['location'].setValue(jobSeeker_location);

    this.setEducationFieldsOptions();
    let educations = [];
    if (translated_advrs.translated_advr_data['educations']) {
      for (let i = 0; i < translated_advrs.translated_advr_data['educations'].length; i++) {
        educations.push(translated_advrs.translated_advr_data['educations'][i].id);
      }
    }
    this.educationFieldsControl.setValue(educations);

    if (shared_data['certification'] !== null) {
      certification = shared_data['certification']
      this.postForm.controls['certification'].setValue(certification)
    }

    if (shared_data['driving_license'] !== null) {
      for (let i = 0; i < this.driving_license.length; i++) {
        if (this.driving_license[i].label === shared_data['driving_license']) {
          this.postForm.controls['driving_license_temp'].setValue(this.driving_license[i]);
        }
      }
    }


    if (translated_advrs.translated_advr_data.job_adv_temp_trans) {
      this.postForm.controls['job_description'].setValue(translated_advrs.translated_advr_data.job_adv_temp_trans.job_description)
    } else {
      this.postForm.controls['job_description'].setValue(translated_advrs.translated_advr_data.job_adv_trans.job_description)
    }

    let mand_array = [];
    mand_array = shared_data['mandatory_job_advertisement'];
    for (let i = 0; i < mand_array.length; i++) {
      this.checklist[i].isSelected = mand_array[i];
    }
    this.checked2list = this.checklist;
  }
  openAiGenerator() {
    if (this.aiJobDescription) {
      this.aiJobDescription.open();
    } else {
      console.error('AI Job Description component not initialized');
    }
  }
  fillJobDataFromAi(jobData: any) {
    if (!jobData) return;
    // console.log('AI generated data:', jobData);

    // Basic text fields
    if (jobData.adv_title) {
      this.postForm.controls['adv_title'].setValue(jobData.adv_title);
    }

    if (jobData.job_description) {
      this.postForm.controls['job_description'].setValue(jobData.job_description);
    }

    // Experience field
    if (jobData.exp_field?.id) {
      for (let i = 0; i < this.ExpField.length; i++) {
        if (this.ExpField[i].experience_field_id === jobData.exp_field.id) {
          this.postForm.controls['exp_field'].setValue(this.ExpField[i]);
          this.postForm.controls['exp_field_id'].setValue(this.ExpField[i].experience_field_id);
          this.changeExpField();
          break;
        }
      }
    }

    // Job titles
    if (jobData.job_titles?.length > 0) {
      this.postForm.controls['job_title'].setValue(jobData.job_titles);
    }
    // Degree level
    if (jobData.degree_level?.id) {
      for (let i = 0; i < this.degreeLevelValues.length; i++) {
        if (this.degreeLevelValues[i].degree_level_id === jobData.degree_level.id) {
          this.postForm.controls['degree_level'].setValue(this.degreeLevelValues[i]);
          this.postForm.controls['degree_level_id'].setValue(this.degreeLevelValues[i].degree_level_id);
          this.changeDegreeLevel();
          break;
        }
      }
    }
    // Year of experience
    if (jobData.year_of_experience?.id) {
      for (let i = 0; i < this.YearExpValues.length; i++) {
        if (this.YearExpValues[i].year_of_exp_id === jobData.year_of_experience.id) {
          this.postForm.controls['year_of_experience'].setValue(this.YearExpValues[i]);
          break;
        }
      }
    }
    // Languages
    if (jobData.languages?.length) {
      const value_lang_ids = jobData.languages
        .map(lang => lang.id)
        .filter(id => id !== undefined);
      this.postForm.controls['languages_temp'].setValue(value_lang_ids);
    }

    // Nationalities
    if (jobData.nationalities?.length) {
      const value_nation_ids = jobData.nationalities
        .map(nation => nation.id)
        .filter(id => id !== undefined);

      this.postForm.controls['nationality_temp_id'].setValue(value_nation_ids);
    }

    // Skills
    if (jobData.skills?.length) {
      this.postForm.controls['skills'].setValue(jobData.skills);
    }

    if (jobData.salary_from) {
      this.postForm.controls['salary_from'].setValue(jobData.salary_from);
      this.salary_from_value = jobData.salary_from;
    }

    if (jobData.salary_to) {
      this.postForm.controls['salary_to'].setValue(jobData.salary_to);
      this.salary_to_value = jobData.salary_to;
    }

    if (jobData.currency_code?.id) {
      this.postForm.controls['currency_id'].setValue(jobData.currency_code.id);
      this.selectedCurrency = jobData.currency_code;
    }
    if (jobData.salary_type) {
      this.postForm.controls['salary_type'].setValue(jobData.salary_type);
      this.selectedType_salary_type = jobData.salary_type;

      // Show salary section
      this.externalSalaryInfoVisible = true;
      $('.extraInformationSalary').show('slow');
      $(".more").css("margin-top", "-10px");
      this.extraCheckedSalary = true;
    }
    this.externalInfoVisible = true;
    $('.extraInformation').show('slow');
    $(".more").css("margin-top", "-10px");
    $(".more").css("margin-left", "80px")
    this.extraChecked = true;
    // Employment types
    if (jobData.employment_types?.length) {
      const value_emp_ids = jobData.employment_types
        .map(emp => {
          const match = this.employmentTypeOpt.find(
            opt => opt.employment_type_id === emp.id
          );
          return match?.employment_type_id;
        })
        .filter(id => id !== undefined);

      if (value_emp_ids.length > 0) {
        this.postForm.controls['employment_type_temp'].setValue(value_emp_ids);
      }
    }

    // Force change detection to update UI
    this.cdRef.detectChanges();
  }

  clearjobseekerLocationData() {
    this.currentLocationControl.controls['country'].setValue('');
    this.currentLocationControl.controls['country_code'].setValue('');
    this.currentLocationControl.controls['city'].setValue('');
  }

  clearLocationData(control) {
    control.controls['country'].setValue('');
    control.controls['country_code'].setValue('');
    control.controls['city'].setValue('');
    control.controls['street_address'].setValue('');
    control.controls['postal_code'].setValue('');
    control.controls['latitude'].setValue('');
    control.controls['longitude'].setValue('');
  }

  companyInfoStepValidity() {
    if (this.postForm.controls['opportunity_for'].value === 'my_company') {
      if (this.postForm.controls['company_locations'].invalid || this.recieveCVsNotValid()) {
        this.valid_company = false;
      }
      else {
        this.valid_company = true;
      }
    }
    else if (this.postForm.controls['opportunity_for'].value === 'other_company') {
      if (this.otherEmployerControl.controls['name'].invalid || this.otherEmployerControl.controls['company_industries'].invalid
        || this.workLocationNotValid() || this.postForm.controls['external_link'].invalid || this.otherEmployerControl.controls['logo_name'].invalid) {
        this.valid_company = false;
      }
      else {
        this.valid_company = true;
      }
    }

    return this.valid_company;
  }

  recieveCVsNotValid() {
    if (this.postForm.controls['apply_on'].value === 'cveek-inbox')
      return false;
    else if (this.postForm.controls['apply_on'].value === 'company-website' && this.postForm.controls['external_link'].invalid) {
      return true;
    }
    else if (this.postForm.controls['apply_on'].value === 'company-website' && this.postForm.controls['external_link'].valid) {
      return false;
    }
    else return false;
  }

  workLocationNotValid() {
    if (this.otherEmployerControl.controls['location_type'].value === 'remote')
      return false;
    else if (this.otherEmployerControl.controls['location_type'].value === 'onsite' && (this.otherEmployerControl.controls['work_location'].invalid || this.otherEmployerControl.controls['full_work_location'].invalid)) {
      return true;
    }
    else if (this.otherEmployerControl.controls['location_type'].value === 'onsite' && (this.otherEmployerControl.controls['work_location'].valid || this.otherEmployerControl.controls['full_work_location'].valid)) {
      return false;
    }
    else return false;
  }


  /*--- submit data when all required data is ready but we handle here if
        this is an Edit status or an Add New Advertisement, also if it's a Template edit or
        any advertisement edit ---*/

  submit(form) {
    this.submitted = true;
    form.submitted = false;
    let CheckedItemList = this.getCheckedItemList();
    if (this.postForm.valid && this.salaryIsValid() === true) {
      this.submitWaiting = true;
      this.formValid = true;
      let sendData = this.postForm.value;
      sendData.company_id = this.companyId;
      sendData.translated_languages_id = this.current_language;

      if (CheckedItemList.length) {
        sendData.is_mandatory_years_of_experience = CheckedItemList[0].isSelected;
        sendData.is_mandatory_gender = CheckedItemList[1].isSelected;
        sendData.is_mandatory_age = CheckedItemList[2].isSelected;
        sendData.is_mandatory_nationality = CheckedItemList[3].isSelected;
        sendData.is_mandatory_degree_level = CheckedItemList[4].isSelected;
        sendData.is_mandatory_education = CheckedItemList[5].isSelected;
        sendData.is_mandatory_languages = CheckedItemList[6].isSelected;
        sendData.is_mandatory_skills = CheckedItemList[7].isSelected;
        sendData.is_mandatory_certification = CheckedItemList[8].isSelected;
        sendData.is_mandatory_current_location = CheckedItemList[9].isSelected;
        sendData.is_mandatory_driving_license = CheckedItemList[10].isSelected;
      }

      sendData = this.data_map.prepare_age_years(sendData, 'age')

      if (sendData.employment_type_temp.length) {
        sendData.employment_types = [];
        for (let i = 0; i < sendData.employment_type_temp.length; i++) {
          sendData.employment_types[i] = sendData.employment_type_temp[i]
        }
      }


      if (sendData.year_of_experience.length) {
        if (sendData.year_of_experience[0] !== 0 && sendData.year_of_experience[1] !== 30) {
          sendData.year_of_exp_from = sendData.year_of_experience[0];
          sendData.year_of_exp_to = sendData.year_of_experience[1];
        }

      }

      sendData.year_of_exp = sendData.year_of_experience.year_of_exp_id

      if (sendData.nationality_temp_id.length) {
        sendData.nationalities = [];
        for (let i = 0; i < sendData.nationality_temp_id.length; i++) {
          sendData.nationalities[i] = sendData.nationality_temp_id[i];
        }
      }

      if (sendData.job_title) {
        sendData.job_titles = [];
        for (let i = 0; i < sendData.job_title.length; i++) {
          sendData.job_titles[i] = sendData.job_title[i].id;
        }
      }
      if (sendData.skills) {
        sendData.skill_types = [];
        for (let i = 0; i < sendData.skills.length; i++) {
          sendData.skill_types[i] = sendData.skills[i].id;
        }
      }


      sendData.degree_level_id = sendData.degree_level.degree_level_id;

      if (sendData.languages_temp.length) {

        sendData.international_languages = [];
        for (let i = 0; i < sendData.languages_temp.length; i++) {
          sendData.international_languages[i] = sendData.languages_temp[i]
        }
      }

      sendData.driving_license = sendData.driving_license_temp.value;

      if (sendData.company_locations) {
        sendData.company_location_id = sendData.company_locations.company_location_id;

        if (sendData.company_locations.company_name === "Remote") {
          sendData.is_online = 1;
        } else {
          sendData.is_online = 0;
        }
      }
      else {
        sendData.is_online = 0;
        sendData.company_location_id = null;
      }

      if (this.extraCheckedSalary &&
        sendData.salary_from &&
        sendData.salary_to &&
        sendData.currency_id) {
        sendData.currency_id = sendData.currency_id.id;
      }
      else if (this.extraCheckedSalary === false) {
        sendData.salary_from = '';
        sendData.salary_to = '';
        sendData.currency_id = '';
      }
      else {
        sendData.currency_id = '';
      }

      if (this.show) {
        sendData.show_company_name = 1;
      } else {
        sendData.show_company_name = 0;
      }

      sendData = this.processStep4Checkbox(sendData);

      sendData.other_employer.logo = this.image_code_to_send;

      //fix status name to match status in advs manage
      if (this.status === 'publish') {
        this.status = 'published';
      }
      if (this.status === 'expiration') {
        this.status = 'expired';
      }
      //  if(this.translated_data.length !== 0)
      if (this.advId_edit !== undefined) {
        switch (this.edit_status) {
          case 'template': {
            this.managePost.editCompanyAdveTemplate(this.advId_edit, sendData).subscribe(
              (res) => {
                this.router.navigate(['/c', this.username, 'manage_advs', this.status]);
              }
            )
            break;
          }
          case 'edit_from_preview_template': {
            this.managePost.editCompanyAdveTemplate(this.advId_edit, sendData).subscribe(
              (res) => {
                let Advid = res['data']['job_advertisement_translation'][0].job_advertisement_template_id;
                let companyAdvId = res['data']['adv_template_id_by_company'];
                let AdvLang = res['data']['job_advertisement_translation'][0].translated_languages_id;
                this.changeAdvId(Advid, AdvLang);
                this.router.navigate(['/c', this.username, 'manage_advs', this.status]);
                //  this.router.navigate(['/c',this.username,'adv',companyAdvId]);
              }
            )
            break;
          }
          case 'edit_from_preview': {
            this.managePost.editCompanyAdvr(this.advId_edit, sendData).subscribe(
              (res) => {
                let Advid = res['job_advertisement_translation'][0].job_advertisement_id;
                let companyAdvId = res['adv_id_by_company'];
                let AdvLang = res['job_advertisement_translation'][0].translated_languages_id;
                this.changeAdvId(Advid, AdvLang);

                // it means after create adv , company didn't published it or saved it as draft or template
                // so after saving adv navigate company user to advr preview interface and display adv saving options
                if (this.advHasNoAction)
                  this.router.navigate(['/c', this.username, 'adv', companyAdvId]);
                else
                  this.router.navigate(['/c', this.username, 'manage_advs', this.status]);
              }
            )
            break;
          }
          default: {
            this.managePost.editCompanyAdvr(this.advId_edit, sendData).subscribe(
              (res) => {
                this.router.navigate(['/c', this.username, 'manage_advs', this.status])
              }
            )
            break;
          }
        }
        this.translated_data = [];
      } else {

        this.postJobService.addAdv(sendData).toPromise().then(
          (res) => {
            if (res['error']) {
              // this.submitWaiting = false;
            }
            else {
              let Advid = res['job_advertisement_translation'][0].job_advertisement_id;
              let companyAdvId = res['adv_id_by_company'];
              let AdvLang = res['job_advertisement_translation'][0].translated_languages_id;

              this.changeAdvId(Advid, AdvLang);
              this.router.navigate(['/c', this.username, 'adv', companyAdvId]);
            }
          });
      }
    } else {
      // this.submitted = false;
      form.submitted = true;
      this.formValid = false;
      //  this.submitWaiting = false;
    }
  }

  processStep4Checkbox(sendData) {
    if (sendData.year_of_experience === "" && sendData.is_mandatory_years_of_experience === 1) {
      sendData.is_mandatory_years_of_experience = 0;
    }
    if (sendData.year_of_experience && sendData.is_mandatory_years_of_experience === 1) {
      if (sendData.year_of_experience.name === null) {
        sendData.is_mandatory_years_of_experience = 0;
      }
    }
    if (sendData.gender === undefined && sendData.is_mandatory_gender === 1) {
      sendData.is_mandatory_gender = 0;
    }
    if (sendData.international_languages.length === 0 && sendData.is_mandatory_languages === 1) {
      sendData.is_mandatory_languages = 0;
    }

    if ((sendData.skills === null || (sendData.skills && sendData.skills.length === 0)) && sendData.is_mandatory_skills === 1) {
      sendData.is_mandatory_skills = 0;
    }

    if (sendData.current_location.country_code === "" && sendData.is_mandatory_current_location === 1) {
      sendData.is_mandatory_current_location = 0;
    }
    if (sendData.degree_level === "" && sendData.is_mandatory_degree_level === 1) {
      sendData.is_mandatory_degree_level = 0;
    }

    if (sendData.degree_level && sendData.is_mandatory_degree_level === 1) {
      if (sendData.degree_level.name === "") {
        sendData.is_mandatory_degree_level = 0;
      }
    }

    if (sendData.educations.length === 0 && sendData.is_mandatory_education === 1) {
      sendData.is_mandatory_education = 0;
    }
    if (sendData.nationalities.length === 0 && sendData.is_mandatory_nationality === 1) {
      sendData.is_mandatory_nationality = 0;
    }
    if (sendData.certification === "" && sendData.is_mandatory_certification === 1) {
      sendData.is_mandatory_certification = 0;
    }
    if ((sendData.driving_license === undefined || sendData.driving_license === "") && sendData.is_mandatory_driving_license === 1) {
      sendData.is_mandatory_driving_license = 0;
    }
    return sendData;
  }
  changeAdvId(Advid, AdvLang) {

    this.postJobService.changeAdvId_lang(Advid, AdvLang, 'post_job')
  }
  myAlert() {
    this.focusMessage = 'The Request is being Processed...';
  }


  /*--- here we handle if there is data coming from manage-advs to initialize the form  ---*/

  ngOnInit() {
    this.translate.use("en");

    // Initialize the job titles dropdown
    this.jobTitleDD = new LazyloadDropdownClass(
      this.lazyloadDropdownService,
      'job_titles',
      10,
      this.current_language
    );

    this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService, 'skills', 10, this.current_language);

    this.title.setTitle('Post jobs for free | CVeek');

    this.selectedType_salary_type = "Monthly";
    this.selectedCurrency = { "id": 39, "name": "United States dollar" };

    //  if(this.translated_data[0])
    if (this.translated_data[0] !== undefined && this.translated_data[0].shared_data !== undefined) {

      this.current_language = Number(this.translated_data[1]['translated_language_id']);

      if (this.translated_data[1].status) {
        this.edit_status = this.translated_data[1].status;
      }
      else if (this.translated_data[0].shared_data.status === 'template') {
        this.edit_status = 'edit_from_preview_template';
      }
      else {
        this.edit_status = 'edit_from_preview';
      }

      this.advId_edit = this.translated_data[0].shared_data.id;
      this.setcompanyLanguage(this.current_language);
      this.initializeFormData(this.translated_data);
      let data = [];
      data[0] = this.translated_data[1].status;
      data[1] = this.translated_data[1].page_number
      this.postJobService.send_Data(data);
    } else {
      this.setcompanyLanguage(this.current_language);
      this.initializeFormData();
    }

  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.getLocationPlaceAutocomplete();
      if (this.company_permissions['job_publisher_other_companies'] === true) {
        this.getLocationOtherCompany();
      }
    }, 2000);
  }

  ngOnDestroy() {
    this.generalService.notify('ResetCategory', 'post-job', 'contact', {});
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  step_exit(step_name) {
    this.page_name = step_name;
    this.no_data_entered = true;
    if (!this.postForm.controls['adv_title'].value) {
      this.no_data_entered = false

    } else if (this.postForm.controls['adv_title'].value) {

      this.no_data_entered = false

    } else {

      this.no_data_entered = true;
    }

    if (
      (!this.no_data_entered || this.submitted) &&
      (!this.postForm.controls['adv_title'].value || this.postForm.controls['exp_field_id'].value === '' ||
        !this.postForm.controls['job_title'].errors !== null || this.postForm.controls['employment_type_temp'].errors !== null
        || !this.salaryIsValid())
    ) {

      this.valid_advr = true;
    } else {
      this.valid_advr = false;
    }

    this.companyInfoStepValidity();
    // if (this.page_name === 'company_step' && !this.postForm.controls['company_locations'].value ||
    // this.submitted && !this.postForm.controls['company_locations'].value) {
    //   this.valid_company = true;
    // } else {
    //   this.valid_company = false;
    // }

  }

  salaryIsValid() {
    let valid = false;
    if (this.extraCheckedSalary === true && this.salary_from_value && this.salary_to_value
      && !this.postForm.controls['salary_from'].errors && !this.postForm.controls['salary_to'].errors
      && this.postForm.controls['currency_id'].value && Number(this.salary_to_value) > Number(this.salary_from_value)
    ) {
      if (this.postForm.controls['currency_id'].value.id !== undefined)
        valid = true;
      else valid = false;
    }
    else
      if (this.extraCheckedSalary === false || (this.extraCheckedSalary === true && this.postForm.controls['salary_from'].value === undefined && this.postForm.controls['salary_to'].value === undefined)) {
        valid = true;
      }

    return valid;
  }

  go_next_page() {
    this.no_data_entered = true;
  }

  go_previous_page() {
    this.no_data_entered = false;
  }

  requestNewValue(field) {
    this.generalService.notify('RequestNewValue', 'post-job', 'contact', { 'mainCategory': 'RequestNewValue', 'subCategory': field });
    this.generalService.notify('display Contact Modal', 'post-job', 'contact-us', { 'displayContactModal': true });
  }

  //start upload logo functions
  handleImageEditorPopup($event) {
    if ($event['editedImage'].file !== '') {
      this.image_code_to_send = $event['editedImage'];
      this.perPhotoSrc = this.image_code_to_send.file;
      this.uploadLabelDisplay = false;
    }
  }

  // onFileChanged(event: any) {
  //   let files = event.target.files;
  //   let result;

  //   if(files[0].size > 1048576 || (files[0].type !== 'image/jpeg' && files[0].type !== 'image/png') ){
  //     if(files[0].size > 1048576){
  //       this.imgError = "validationMessages.imageSizeBig";
  //     }
  //     else if(files[0].type !== 'image/jpeg' && files[0].type !== 'image/png'){
  //       this.imgError = "validationMessages.invalidImageType";
  //     }
  //   }

  //  else{
  //   this.imgError = null;
  //   const reader = new FileReader()
  //   for(let file of files) {
  //     this.uploadedFiles.push(file) ;
  //   }
  //   let file = this.uploadedFiles[0];
  //   this.data_map.upload_file(file).then(
  //     (res)=>{
  //       result = res;
  //       this.image_code_to_send = result;
  //       this.uploadLabelDisplay = false;
  //       this.uploadedFiles = [];
  //       this.cdRef.detectChanges();
  //     });

  //   reader.onload = () => {
  //     this.perPhotoSrc = reader.result as string;
  //   }
  //   reader.readAsDataURL(file)
  //  }
  // }

  deleteProfilePicture() {
    if (confirm(this.translate.instant("confirm.deleteLogo"))) {
      this.uploadLabelDisplay = true;
      this.perPhotoSrc = this.noProfilePicSet;
      this.image_code_to_send = { file: '', file_type: '', is_deleted: true, name: "" };
      this.generalService.notify('image-deleted', 'post-job', 'image-editor', {});
      this.cdRef.detectChanges();
    }
  }
  //end upload logo functions

  noExistLogoNameCheckboxChanged() {
    if (this.otherEmployerControl.controls['no_exist_logo_name'].value === true) {
      this.otherEmployerControl.controls['logo_name'].setValue('');
      this.perPhotoSrc = this.noUploadedImage;
      this.uploadLabelDisplay = true;
      // this.logoImageNotFoundError = false;
    }
  }

  logoLinkChanged() {
    let imageName = this.otherEmployerControl.controls['logo_name'].value;
    this.existLogoLink = this.imageProcessingService.getImagePath('otherEmployerLogo', 'med_thumbnail', imageName);
    this.otherEmployerControl.controls['no_exist_logo_name'].setValue(false);
    // this.logoImageNotFoundError = false;
    this.image_code_to_send = { file: '', file_type: 'url', is_deleted: false, name: imageName };
  }

  // enteredLogoNotFound(){
  //   let imageName = this.otherEmployerControl.controls['logo_name'].value;
  //   if(imageName !=="" && imageName !== null)
  //     this.logoImageNotFoundError = true;
  // }

  // for unknown reason there is an issue that when we select a value directly from the values, the added id type is string!!!
  // and when we filter a value, the added id type is number !! (the original array options id type is number!)
  // the issue appears when i want to delete a filtered value
  // it is not a good solution but we didn't figure out the reason for this issue
  companyIndustryChanged($event) {
    let industries_temp = [], industies_value = $event.value;

    for (let i = 0; i < industies_value.length; i++) {
      if (typeof (industies_value[i]) !== 'string')
        industries_temp[i] = industies_value[i].toString();
      else industries_temp[i] = industies_value[i]
    }
    this.otherEmployerControl.controls['company_industries'].setValue(industries_temp);
  }

}
