div.header {
    position: fixed;
    /* top: 67px; */
    left:0;
    font-size:15px;
    width: 100%;
    list-style: none;
    background: #3D7BCE;
    z-index: 999;
    text-align: center;
  }

  .search-input {
    width: calc(100% - 70px);
    font-size: 15px;
    color: #686F7A;
    border-radius: 1px;
    border: 1px solid #c5c5c5;
    box-sizing: border-box;
    margin: 8px 0 8px 15px;
    height: 32px;
    padding-left: 20px;
    padding-right: 20px;
    -webkit-appearance: none;
    font-weight: normal;
    transition: border .12s ease-in-out;
    background-color: white;
    display:inline-block;
  }

  .input-group {
    padding:0 20px;
    margin:5px 0;
    /* float: right !important; */
  }

.search-btn , .search-btn:active , .search-btn:focus , .search-btn:hover{
  display:inline-block;
  background: transparent;
  outline: 0;
  box-shadow: none;
}
.search-btn{
    margin-bottom: 0;
    padding-bottom: 0;
    padding-top: 0;
}
.search-btn i{
    color:#fff;
    font-size: 24px;
}

@media screen and (max-width: 767px) {
  .search-input{
    margin-left:0;
  }
}