/* Start page NAVBAR */

/* .page-navbar {
    font-size: 16px;
    width: 100%;
    list-style: none;
    background: #f2f2f2;
    z-index: 9999;
    text-align: center;
    border-bottom: 1px solid #ddd;
}
.page-navbar2{
    font-size: 16px;
    width: 100%;
    list-style: none;
    z-index: 9999;
    background: white;
    text-align: left;
    margin:20px;
    padding:30px;
    height: 1000px;
    border-right: 1px solid #ddd;
    border-bottom: none;
}


.page-navbar ul {
    margin-bottom: 0;
    padding: 6px 0px 10px 0px;
}

.page-navbar ul li {
    display: inline-block;
    padding: 18px 20px 10px;
    margin-right: 20px;
    
}
.page-navbar2 ul {
    margin-bottom: 0;
    padding: 6px 0px 10px 0px;
    width: 100%;
    height: 100%;
}

.page-navbar2 ul li {
    display: inline-block;
    padding: 18px 20px 10px;
    margin-right: 20px;
    width: 100%;
}


.page-navbar ul li::after {
    background-color: white;
}

.page-navbar ul li a {
    text-decoration: none;
    color: black;
    transition: all .4s ease;
}

.white {
    background:#f2f2f2;
}

.gray {
    background: #f2f2f2
}
 */
