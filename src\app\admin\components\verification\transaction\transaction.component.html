<!-- start of university form -->
<form *ngIf="!previewMode && userInput.type === 'UNIVERSITY'" [formGroup]="transactionForm">
  <div [formGroup]="verified_entry" class="form-group">
    <table class="table table-striped table-bordered">
      <thead>
        <tr>
          <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
          <td>{{ userInput.id }}</td>
          <th *ngIf="userInput.adv_id!=null;" class="text-center" colspan="(2 + languagesArray.length)/2">Adv ID :</th>
          <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>
          <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
          <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
        </tr>
        <tr>
          <th class="text-center" colspan="2">User Entry</th>
          <th class="text-center" colspan="2">Admin Entry </th>
        </tr>
        <tr>
          <th></th>
          <th class=""></th>
          <ng-container *ngFor="let lang of languagesArray">
            <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>Input Name</th>
          <td class=""><a id="search-university" (click)="search(0)" target="_blank"><i class="fa fa-search"
                title="click to search "></i></a>{{ userInput.name || "empty" }}</td>
          <ng-container formArrayName="item_type_trans" *ngFor="let lang of languagesArray; let i = index">
            <ng-container [formGroupName]="i">
              <td>
                <input type="text" formControlName="name" /><span class="alert alert-danger inline-alert"
                  *ngIf="item_type_trans.controls[i].controls.name.touched && item_type_trans.controls[i].controls.name.invalid">Name
                  is required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
        <tr>
          <th>Location</th>
          <td class="">{{ userInput.country+ ',' + userInput.city|| "empty" }}</td>
          <td colspan="2">
            <div> <input type="text" formControlName="location" #googlelocationplaceLocation
              (change)="changeLocation($event)">
            </div>
    
            <div>
              <span class="alert alert-danger inline-alert" *ngIf="location.touched && location.invalid">Location is
                required</span>
            </div>
          </td>

        </tr>
        <tr>
          <th>Country</th>
          <td class="">{{ userInput.country || "empty" }}</td>
          <td colspan="2">
            <input type="text" formControlName="country" readonly>
            <!-- <div> <input type="text" formControlName="country" #googlelocationplaceLocation
                (change)="changeLocation($event)">
            </div> -->
            <div>
              <span class="alert alert-danger inline-alert" *ngIf="country.touched && country.invalid">Location is
                required</span>
            </div>
          </td>

        </tr>
        <tr>
          <th>Country Code</th>
          <td class=""><a id="search-country-code" (click)="searchCountryCode()" target="_blank"><i class="fa fa-search"
                title="click to search for {{userInput.country }} country code "></i></a>{{ userInput.country_code ||
            "empty" }}</td>
          <td colspan="2">
            <input type="text" formControlName="country_code" readonly>
            <!-- <span class="alert alert-danger inline-alert" *ngIf="country_code.touched && country_code.invalid">Country code is required</span> -->
          </td>
        </tr>
        <tr>
        <tr>
          <th>City</th>
          <td class="">{{ userInput.city || "empty" }}</td>
          <td colspan="2">
            <input type="text" formControlName="city" readonly />
            <!-- <span class="alert alert-danger inline-alert" *ngIf="city.touched && city.invalid">City is required</span> -->
          </td>
        </tr>
        <tr>
          <th>Street Address</th>
          <td class="">{{ userInput.street_address || "empty" }}</td>
          <td colspan="2">
            <input type="text" formControlName="street_address" readonly />
            <!-- <span class="alert alert-danger inline-alert" *ngIf="street_address.touched && street_address.invalid">address is required</span> -->
          </td>
        </tr>
        <tr>
          <th>Website</th>
          <td class="">{{ userInput.url || "empty" }}</td>
          <td colspan="2">
            <input formControlName="url" type="text" /><span class="alert alert-danger inline-alert"
              *ngIf="url.touched && url.invalid">website is required</span>
          </td>
        </tr>
      </tbody>
    </table>


    <button type="submit" class="btn btn-success" [disabled]="transactionForm.invalid"
      (click)="saveTransaction(transactionForm.value)">Add / Save</button>
    <button type="button" class="btn btn-default" (click)="endTransaction()">End Transaction</button>

    <p>{{ transactionForm.value | json }}</p>
  </div>
</form>
<!-- end of university form -->




<!-- start of university preview -->
<table *ngIf="previewMode  && userInput.type === 'UNIVERSITY'" class="table table-striped table-bordered">
  <caption class="text-center table-caption">Values Details</caption>
  <thead>
    <tr>
      <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
      <td>{{ userInput.id }}</td>
      <th *ngIf="userInput.adv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">Adv ID :</th>
      <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

      <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
      <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
    </tr>
    <tr>
      <th class="text-center" colspan="2">User Entry</th>
      <th class="text-center" colspan="2">Admin Entry </th>
    </tr>
    <tr>
      <th></th>
      <th class=""></th>
      <ng-container *ngFor="let lang of languagesArray">
        <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
      </ng-container>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Input Name</th>
      <td class="">{{ userInput.name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.trans[i].name }}</span></td>
      </ng-container>
    </tr>
    <tr>
      <th>Country</th>
      <td class="">{{ userInput.country || "empty" }}</td>
      <td colspan="2"><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.country }}</span></td>
    </tr>
    <tr>
      <th>City</th>
      <td class="">{{ userInput.city || "empty" }}</td>
      <td colspan="2"><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.city }}</span></td>
    </tr>
    <tr>
      <th>Street Address</th>
      <td class="">{{ userInput.street_address || "empty" }}</td>
      <td colspan="2"><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.street_address }}</span>
      </td>
    </tr>
    <tr>
      <th>Website</th>
      <td class="">{{ userInput.url || "empty" }}</td>
      <td colspan="2"><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.url }}</span></td>
    </tr>

  </tbody>

</table>
<!-- end of university preview -->




<!-- start of skill form -->
<form *ngIf="!previewMode  && userInput.type === 'SKILL'" [formGroup]="transactionForm">
  <div [formGroup]="verified_entry" class="form-group">
    <table class="table table-striped table-bordered">
      <thead>
        <tr>
          <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
          <td>{{ userInput.id }}</td>

          <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">
            Adv ID :</th>
          <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

          <ng-template #tempCvId>
            <th *ngIf="userInput.cv_id!==null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
            <td *ngIf="userInput.cv_id!==null"> {{ userInput.cv_id }}</td>
          </ng-template>
        </tr>
        <tr>
          <th class="text-center" colspan="2">User Entry</th>
          <th class="text-center" colspan="2">Admin Entry </th>
        </tr>
        <tr>
          <th></th>
          <th class="border-right"></th>
          <ng-container *ngFor="let lang of languagesArray">
            <th class="text-center "><span class="badge badge-primary">{{ lang.name }}</span></th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>Input Skill</th>
          <td class="border-right"><a id="search-skill" (click)="search(3)" target="_blank"><i class="fa fa-search"
                title="click to search "></i></a>{{ userInput.name }}</td>
          <ng-container formArrayName="item_type_trans" *ngFor="let lang of languagesArray; let i = index">
            <ng-container [formGroupName]="i">
              <td>
                <input formControlName="name" type="text" />
                <span class="alert alert-danger inline-alert"
                  *ngIf="item_type_trans.controls[i].controls.name.touched && item_type_trans.controls[i].controls.name.invalid">Name
                  is required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
        <tr>
          <th>Parent Job Title</th>
          <td class="border-right">----</td>
          <ng-container *ngFor="let lang of languagesArray; let i = index">
            <td>
              <p-multiSelect [options]="skillParentArr[lang.id - 1 ]" [(ngModel)]="selectedParents"
                formControlName="job_titles" [panelStyle]="{minWidth:'13em'}" [required]="true">
                <ng-template let-parent pTemplate="item">
                  <div style="font-size:14px;float:right;margin-top:4px">{{parent.label}}</div>
                </ng-template>
              </p-multiSelect>
              <!-- <p>Selected parents: {{job_titles.value }}</p> -->
              <div class="alert alert-danger " *ngIf="job_titles.touched && job_titles.invalid">Parent Job Title is
                required</div>
            </td>
          </ng-container>
        </tr>
        <tr>
          <th><i class="fa fa-plus" title="add new value" data-toggle="modal" data-target="#addValueModal"
              (click)="addNewCategory()"></i>Category</th>
          <td class="border-right">----</td>
          <ng-container *ngFor="let lang of languagesArray; let i = index">
            <td>
              <p-dropdown [options]="skillCatsArr[lang.id - 1]" #skillCat [(ngModel)]="selectedCat"
                formControlName="skill_category_id" [required]="true" [filter]="true">
                <ng-template let-category pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                    <div style="font-size:12px;float:left;margin-top:4px">{{category.label}}</div>
                  </div>
                </ng-template>
              </p-dropdown>
              <span class="alert alert-danger " *ngIf="skill_category_id.touched && skill_category_id.invalid">category
                is required</span>
            </td>
          </ng-container>
        </tr>
      </tbody>
    </table>


    <button type="submit" class="btn btn-success" [disabled]="transactionForm.invalid"
      (click)="saveTransaction(transactionForm.value)">Add / Save</button>
    <button type="button" class="btn btn-default" (click)="endTransaction()">End Transaction</button>


    <!-- <p>{{ transactionForm.value | json }}</p> -->
  </div>
</form>
<!-- end of skill form -->




<!-- start of skill preview -->
<table *ngIf="previewMode && userInput.type === 'SKILL'" class="table table-striped table-bordered">
  <caption class="text-center table-caption">Values Details</caption>
  <thead>
    <tr>
      <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
      <td>{{ userInput.id }}</td>

      <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">Adv
        ID :</th>
      <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

      <ng-template #tempCvId>
        <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
        <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
      </ng-template>
    </tr>
    <tr>
      <th class="text-center" colspan="2">User Entry</th>
      <th class="text-center" colspan="2">Admin Entry </th>
    </tr>
    <tr>
      <th></th>
      <th class="border-right"></th>
      <ng-container *ngFor="let lang of languagesArray">
        <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
      </ng-container>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Input Skill</th>
      <td class="border-right">{{ userInput.name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.trans[i].name }}</span></td>
      </ng-container>
    </tr>
    <tr>
      <th><i class="fa fa-plus" title="add new value"></i>Parent</th>
      <td class="border-right">----</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'"><span
              *ngFor="let parent of  adminEntry.skill_parents">{{ parent.job_title_translation[i].name + ', ' }}
            </span></span>
        </td>
      </ng-container>
    </tr>
    <tr>
      <th>Category</th>
      <td class="border-right">{{ userInput.skill_category }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.skill_category_trans[i].name
            }}</span>
        </td>
      </ng-container>
    </tr>
  </tbody>
</table>
<!-- end of skill preview -->




<!-- start of major form -->
<form *ngIf="!previewMode && userInput.type === 'MAJOR'" [formGroup]="transactionForm">
  <div [formGroup]="verified_entry" class="form-group">

    <table class="table table-striped table-bordered">
      <thead>
        <tr>
          <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
          <td>{{ userInput.id }}</td>

          <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">
            Adv ID :</th>
          <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>
  <ng-template #tempCvId>
    <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
    <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
  </ng-template>
  </tr>
  <tr>
    <th class="text-center" colspan="2">User Entry</th>
    <th class="text-center" colspan="2">Admin Enry </th>
  </tr>
  <tr>
    <th></th>
    <th class="border-right"></th>
    <ng-container *ngFor="let lang of languagesArray">
      <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
    </ng-container>
  </tr>
  </thead>
  <tbody>
    <tr>
      <th>Input Major</th>
      <td class="border-right"><a id="search-major" (click)="search(1)" target="_blank"><i class="fa fa-search"
            title="click to search "></i></a>{{ userInput.name || "empty" }}</td>
      <ng-container formArrayName="item_type_trans" *ngFor="let lang of languagesArray; let i = index">
        <ng-container [formGroupName]="i">
          <td><input type="text" formControlName="name" />
            <span class="alert alert-danger inline-alert"
              *ngIf="item_type_trans.controls[i].controls.name.touched && item_type_trans.controls[i].controls.name.invalid">Name
              is required</span>
          </td>
        </ng-container>
      </ng-container>
    </tr>
    <!-- <tr>
      <th><i class="fa fa-plus" title="add new value" data-toggle="modal" data-target="#addValueModal"
          (click)="AddNewParent()"></i>Parent</th>
      <td class="border-right">{{ userInput.parent_id || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <p-dropdown [options]="majorParentArr[lang.id - 1]" [(ngModel)]="selectedParent"
            formControlName="major_parent_id" [required]="true" [filter]="true">
            <ng-template let-parent pTemplate="item">
              <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
              </div>
            </ng-template>
          </p-dropdown>
          <span class="alert alert-danger " *ngIf="major_parent_id.touched && major_parent_id.invalid">Parent is
            required</span>
        </td>
      </ng-container>
    </tr> -->
  </tbody>
  </table>


  <button type="submit" class="btn btn-success" [disabled]="transactionForm.invalid"
    (click)="saveTransaction(transactionForm.value)">Add / Save</button>
  <button type="button" class="btn btn-default" (click)="endTransaction()">End Transaction</button>


  <p>{{ transactionForm.value | json }}</p>
  </div>
</form>
<!-- end of major form -->




<!-- start of major preview -->
<table *ngIf="previewMode && userInput.type === 'MAJOR'" class="table table-striped table-bordered">
  <caption class="text-center table-caption">Values Details</caption>
  <thead>
    <tr>
      <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
      <td>{{ userInput.id }}</td>

      <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">Adv
        ID :</th>
      <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

      <ng-template #tempCvId>
        <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
        <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
      </ng-template>
    </tr>
    <tr>
      <th class="text-center" colspan="2">User Entry</th>
      <th class="text-center" colspan="2">Admin Enry </th>
    </tr>
    <tr>
      <th></th>
      <th class="border-right"></th>
      <ng-container *ngFor="let lang of languagesArray">
        <th class="text-center "><span class="badge badge-primary">{{ lang.name }}</span></th>
      </ng-container>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Input Major</th>
      <td class="border-right">{{ userInput.name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.trans[i].name }}</span></td>
      </ng-container>
    </tr>
    <!-- <tr>
      <th><i class="fa fa-plus" title="add new value"></i>Parent</th>
      <td class="border-right">___</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.parent.name }}</span></td>
      </ng-container>
    </tr> -->
  </tbody>
</table>
<!-- end of major preview -->




<!-- start minor form -->
<form *ngIf="!previewMode && userInput.type === 'MINOR'" [formGroup]="transactionForm">
  <div [formGroup]="verified_entry" class="form-group">

    <table class="table table-striped table-bordered">
      <thead>
        <tr>
          <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
          <td>{{ userInput.id }}</td>

          <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">
            Adv ID :</th>
          <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

          <ng-template #tempCvId>
            <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
            <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
          </ng-template>
        </tr>
        <tr>
          <th class="text-center" colspan="2">User Entry</th>
          <th class="text-center" colspan="2">Admin Enry </th>
        </tr>
        <tr>
          <th></th>
          <th class="border-right"></th>
          <ng-container *ngFor="let lang of languagesArray">
            <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>Minor Name</th>
          <td class="border-right"><a id="search-minor" (click)="search(2)" target="_blank"><i class="fa fa-search"
                title="click to search "></i></a>{{ userInput.name || "empty" }}</td>
          <ng-container formArrayName="item_type_trans" *ngFor="let lang of languagesArray; let i = index">
            <ng-container [formGroupName]="i">
              <td><input type="text" formControlName="name" />
                <span class="alert alert-danger inline-alert"
                  *ngIf="item_type_trans.controls[i].controls.name.touched && item_type_trans.controls[i].controls.name.invalid">Name
                  is required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
        <tr>
          <th>Major</th>
          <td class="border-right">{{ userInput.major_name || "empty" }}
            <span *ngIf="!userInput.major_verified">
              (unverified) 
              <!-- <i class="fa fa-info" title="verify" (click)="verifyMajor()"></i> -->
            </span>
          </td>
         
          <ng-container *ngFor="let lang of languagesArray; let i = index">
            <td>
              <p-dropdown [options]="majorArr[lang.id - 1]" [(ngModel)]="selectedMajor" formControlName="major_education_field_id"
                [required]="true" [filter]="true" (onChange)="changeMajorId($event)">
                <ng-template let-parent pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                    <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                  </div>
                </ng-template>
              </p-dropdown>
              <!-- <span class="alert alert-danger " *ngIf="major_education_field_id.touched && major_education_field_id.invalid">Major is required</span> -->
            </td>
          </ng-container>
        </tr>
        <!-- <tr>
          <th>Major Parent</th>
          <td class="border-right">----</td>
          <ng-container *ngFor="let lang of languagesArray; let i = index">
            <td class="border-right" *ngIf="majorParentBySelectedMajor.length>0">
              {{ majorParentBySelectedMajor[lang.id - 1]['label'] }}
            </td>
          </ng-container>
        </tr> -->

      </tbody>
    </table>

    <button type="submit" class="btn btn-success" [disabled]="transactionForm.invalid"
      (click)="saveTransaction(transactionForm.value)">Add / Save</button>
    <button type="button" class="btn btn-default" (click)="endTransaction()">End Transaction</button>

    <p>{{ transactionForm.value | json }}</p>
  </div>
</form>
<!-- end of minor form -->


<!-- start of minor preview -->
<table *ngIf="previewMode && userInput.type === 'MINOR'" class="table table-striped table-bordered">
  <caption class="text-center table-caption">Values Details</caption>
  <thead>
    <tr>
      <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
      <td>{{ userInput.id }}</td>

      <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">Adv
        ID :</th>
      <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

      <ng-template #tempCvId>
        <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
        <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
      </ng-template>
    </tr>
    <tr>
      <th class="text-center" colspan="2">User Entry</th>
      <th class="text-center" colspan="2">Admin Enry </th>
    </tr>
    <tr>
      <th></th>
      <th class="border-right"></th>
      <ng-container *ngFor="let lang of languagesArray">
        <th class="text-center "><span class="badge badge-primary">{{ lang.name }}</span></th>
      </ng-container>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Minor Name</th>
      <td class="border-right">{{ userInput.minor_trans[0].name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.minor_trans[i].name }}</span>
        </td>
      </ng-container>
    </tr>
    <tr>
      <th>Major</th>
      <td class="border-right">{{ userInput.major_trans[0].name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.major_trans[i].name}}</span>
        </td>
      </ng-container>
    </tr>
    <!-- <tr>
      <th>Major Parent</th>
      <td class="border-right">----</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td><span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.major_parent }}</span></td>
      </ng-container>
    </tr> -->
  </tbody>
</table>
<!-- end of minor preview -->


<!-- start job_title form -->
<form *ngIf="!previewMode && userInput.type === 'JOB_TITLE'" [formGroup]="transactionForm">
  <div [formGroup]="verified_entry" class="form-group">

    <table class="table table-striped table-bordered">
      <thead>
        <tr>
          <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
          <td>{{ userInput.id }}</td>

          <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">
            Adv ID :</th>
          <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

          <ng-template #tempCvId>
            <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
            <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
          </ng-template>
        </tr>
        <tr>
          <th class="text-center" colspan="2">User Entry</th>
          <th class="text-center" colspan="2">Admin Enry </th>
        </tr>
        <tr>
          <th></th>
          <th class="border-right"></th>
          <ng-container *ngFor="let lang of languagesArray">
            <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>Job title Name</th>
          <td class="border-right"><a id="search-job-title" (click)="search(4)" target="_blank"><i class="fa fa-search"
                title="click to search "></i></a>{{ userInput.name || "empty" }}</td>
          <ng-container formArrayName="item_type_trans" *ngFor="let lang of languagesArray; let i = index">
            <ng-container [formGroupName]="i">
              <td><input type="text" formControlName="name" />
                <span class="alert alert-danger inline-alert"
                  *ngIf="item_type_trans.controls[i].controls.name.touched && item_type_trans.controls[i].controls.name.invalid">Name
                  is required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
        <tr>
          <th><i class="fa fa-plus" title="add new value" data-toggle="modal" data-target="#addValueModal"
              (click)="addNewField()"></i>Experience Fields</th>
          <td class="border-right">
            <ng-container *ngFor="let field of userInput.experience_fields; let i = index">
              {{ field.experience_field_translation[0].name + ',' }}
            </ng-container>
          </td>
          <ng-container *ngFor="let lang of languagesArray; let i = index">
            <td>
              <p-multiSelect [options]="experienceFields[lang.id - 1 ]" [(ngModel)]="selectedFields"
                formControlName="experience_fields" [panelStyle]="{minWidth:'13em'}" [required]="true">
                <ng-template let-field pTemplate="item">
                  <div style="font-size:14px;float:right;margin-top:4px">{{field.label}}</div>
                </ng-template>
              </p-multiSelect>
              <span class="alert alert-danger " *ngIf="experience_fields.touched && experience_fields.invalid">field is
                required</span>
            </td>
          </ng-container>
        </tr>
        <!-- <tr>
          <th>Major Parent</th>
          <td class="border-right">----</td>
          <ng-container *ngFor="let lang of languagesArray; let i = index"><td>
            {{  userInput.major_parent_name }}
          </td></ng-container>
        </tr> -->
      </tbody>
    </table>


    <button type="submit" class="btn btn-success" [disabled]="transactionForm.invalid"
      (click)="saveTransaction(transactionForm.value)">Add / Save</button>
    <button type="button" class="btn btn-default" (click)="endTransaction()">End Transaction</button>


    <p>{{ transactionForm.value | json }}</p>
  </div>
</form>
<!-- end of job_title form -->




<!-- start of job_title preview -->
<table *ngIf="previewMode && userInput.type === 'JOB_TITLE'" class="table table-striped table-bordered">
  <caption class="text-center table-caption">Values Details</caption>
  <thead>
    <tr>
      <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
      <td>{{ userInput.id }}</td>

      <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">Adv
        ID :</th>
      <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

      <ng-template #tempCvId>
        <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
        <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
      </ng-template>
    </tr>
    <tr>
      <th class="text-center" colspan="2">User Entry</th>
      <th class="text-center" colspan="2">Admin Enry </th>
    </tr>
    <tr>
      <th></th>
      <th class="border-right"></th>
      <ng-container *ngFor="let lang of languagesArray">
        <th class="text-center "><span class="badge badge-primary">{{ lang.name }}</span></th>
      </ng-container>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Job Title Name</th>
      <td class="border-right">{{ userInput.name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.job_title_trans[i].name }}</span>
        </td>
      </ng-container>
    </tr>
    <tr>
      <th>Experience Fields</th>
      <td class="border-right">
        <ng-container *ngFor="let field of userInput.experience_fields; let i = index">
          {{ field.experience_field_translation[0].name + ',' }}
        </ng-container>
      </td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">
            <ng-container *ngFor="let field of adminEntry.experience_fields; let j = index">
              {{ field.experience_field_translation[i].name + ',' }}
            </ng-container>
          </span>
        </td>
      </ng-container>
    </tr>
    <!-- <tr>
      <th>Major Parent</th>
      <td class="border-right">----</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index"><td><span  *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.major_parent  }}</span></td></ng-container>
    </tr> -->
  </tbody>
</table>
<!-- end of job_title preview -->



<!-- start job_title_synonym form -->
<form *ngIf="!previewMode && userInput.type === 'JOB_TITLE_SYNONYMS'" [formGroup]="transactionForm">
  <div [formGroup]="verified_entry" class="form-group">

    <table class="table table-striped table-bordered">
      <thead>
        <tr>
          <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
          <td>{{ userInput.id }}</td>

          <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">
            Adv ID :</th>
          <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

          <ng-template #tempCvId>
            <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
            <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
          </ng-template>
        </tr>
        <tr>
          <th class="text-center" colspan="2">User Entry</th>
          <th class="text-center" colspan="2">Admin Enry </th>
        </tr>
        <tr>
          <th></th>
          <th class="border-right"></th>
          <ng-container *ngFor="let lang of languagesArray">
            <th class="text-center"><span class="badge badge-primary">{{ lang.name }}</span></th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>Synonym</th>
          <td class="border-right"><a id="search-job-title-synonyms" (click)="search(5)" target="_blank"><i
                class="fa fa-search" title="click to search "></i></a>{{ userInput.name || "empty" }}</td>
          <ng-container formArrayName="item_type_trans" *ngFor="let lang of languagesArray; let i = index">
            <ng-container [formGroupName]="i">
              <td><input type="text" formControlName="name" />
                <span class="alert alert-danger inline-alert"
                  *ngIf="item_type_trans.controls[i].controls.name.touched && item_type_trans.controls[i].controls.name.invalid">Name
                  is required</span>
              </td>
            </ng-container>
          </ng-container>
        </tr>
        <tr>
          <th>Job Title</th>
          <td class="border-right">{{ userInput.job_title_name || "empty" }}<span *ngIf="!userInput.job_title_verified">
              (unverified) <i class="fa fa-info" title="verify" (click)="verifyJobTitle()"></i></span></td>
          <ng-container *ngFor="let lang of languagesArray; let i = index">
            <td>
              <p-dropdown [options]="jobTitles[lang.id - 1]" [(ngModel)]="selectedMajor" formControlName="job_title_id"
                [required]="true" [filter]="true">
                <ng-template let-job pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                    <div style="font-size:12px;float:left;margin-top:4px">{{job.label}}</div>
                  </div>
                </ng-template>
              </p-dropdown>
              <span class="alert alert-danger " *ngIf="job_title_id.touched && job_title_id.invalid">Job title is
                required</span>
            </td>
          </ng-container>
        </tr>
        <tr>
          <!-- <th>Major Parent</th>
          <td class="border-right">----</td>
          <ng-container *ngFor="let lang of languagesArray; let i = index"><td>
            {{  userInput.major_parent_name }}
          </td></ng-container> -->
          <!-- <ng-container *ngFor="let lang of languagesArray; let i = index"><td>
              <p-dropdown [options]="majorParentArr[lang.id - 1]" [(ngModel)]="selectedParent" formControlName="major_parent_id" [required]="true" [filter]="true">
                <ng-template let-parent pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                        <div style="font-size:12px;float:left;margin-top:4px">{{parent.label}}</div>
                    </div>
                </ng-template>
              </p-dropdown>
              <span class="alert alert-danger " *ngIf="major_parent_id.touched && major_parent_id.invalid">Parent is required</span>
          </td></ng-container> -->
        </tr>

      </tbody>
    </table>


    <button type="submit" class="btn btn-success" [disabled]="transactionForm.invalid"
      (click)="saveTransaction(transactionForm.value)">Add / Save</button>
    <button type="button" class="btn btn-default" (click)="endTransaction()">End Transaction</button>


    <p>{{ transactionForm.value | json }}</p>
  </div>
</form>
<!-- end of job_title_synonym form -->




<!-- start of job_title_synonym preview -->
<table *ngIf="previewMode && userInput.type === 'JOB_TITLE_SYNONYMS'" class="table table-striped table-bordered">
  <caption class="text-center table-caption">Values Details</caption>
  <thead>
    <tr>
      <th class="text-center" colspan="(2 + languagesArray.length)/2">Transaction ID:</th>
      <td>{{ userInput.id }}</td>

      <th *ngIf="userInput.adv_id!=null else tempCvId" class="text-center" colspan="(2 + languagesArray.length)/2">Adv
        ID :</th>
      <td *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</td>

      <ng-template #tempCvId>
        <th *ngIf="userInput.cv_id!=null" class="text-center" colspan="(2 + languagesArray.length)/2">CV ID :</th>
        <td *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</td>
      </ng-template>
    </tr>
    <tr>
      <th class="text-center" colspan="2">User Entry</th>
      <th class="text-center" colspan="2">Admin Enry </th>
    </tr>
    <tr>
      <th></th>
      <th class="border-right"></th>
      <ng-container *ngFor="let lang of languagesArray">
        <th class="text-center "><span class="badge badge-primary">{{ lang.name }}</span></th>
      </ng-container>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Synonym</th>
      <td class="border-right">{{ userInput.job_title_synonyms_trans[0].name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.job_title_synonyms_trans[i].name
            }}</span>
        </td>
      </ng-container>
    </tr>
    <tr>
      <th>Job Title</th>
      <td class="border-right">{{ userInput.job_title_name || "empty" }}</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index">
        <td>
          <span *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.job_title_trans[i].name}}</span>
        </td>
      </ng-container>
    </tr>
    <!-- <tr>
      <th>Major Parent</th>
      <td class="border-right">----</td>
      <ng-container *ngFor="let lang of languagesArray; let i = index"><td><span  *ngIf="adminEntry.operation !== 'END_TRANSACTION'">{{ adminEntry.major_parent  }}</span></td></ng-container>
    </tr> -->
  </tbody>
</table>
<!-- end of job_title_synonym preview -->





<div *ngIf="previewMode" class="row action">
  <div class="col-md-6"><b>Admin Action:</b></div>
  <div class="col-md-6"
    *ngIf="adminEntry.operation === 'UNIVERSITY' || adminEntry.operation === 'SKILL' || adminEntry.operation === 'MAJOR'
   || adminEntry.operation === 'MINOR' || adminEntry.operation === 'JOB_TITLE' || adminEntry.operation === 'JOB_TITLE_SYNONYMS' || adminEntry.operation === 'SUGGEST_VALUE'">
    <span class="badge badge-primary">Save</span>
  </div>
  <div class="col-md-6" *ngIf="adminEntry.operation === 'END_TRANSACTION'"><span class="badge badge-default">End</span>
  </div>
</div>


<!-- start of action table-->
<table *ngIf="previewMode" class="table table-bordered">
  <caption class="text-center table-caption">Actions Details</caption>
  <thead>
    <tr>
      <th></th>
      <th>Action</th>
      <th>Date</th>
      <th>Time</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>
        <span *ngIf="userInput.cv_id!=null">CV ID( {{ userInput.cv_id }} )</span>
        <span *ngIf="userInput.adv_id!=null">Adv ID( {{userInput.adv_id }} )</span>
        <!-- <span *ngIf=" userInput.type !== 'JOB_TITLE' && userInput.type !== 'JOB_TITLE_SYNONYMS'">CV ID( {{  userInput.cv_id  }} )</span>
        <span *ngIf=" userInput.type === 'JOB_TITLE' || userInput.type === 'JOB_TITLE_SYNONYMS'">Adv ID( {{userInput.adv_id }} )</span> -->
      </th>
      <td>{{ userInput.type.toLowerCase()}}</td>
      <td>{{ userInput.date | date: 'mediumDate' }}</td>
      <td>{{ userInput.time }}</td>
    </tr>
    <tr>
      <th>Admin ( {{ adminEntry.admin }} )</th>
      <td>
        <span
          *ngIf="adminEntry.operation=== 'UNIVERSITY' || adminEntry.operation === 'SKILL' || adminEntry.operation === 'MAJOR'
        || adminEntry.operation === 'MINOR' || adminEntry.operation === 'JOB_TITLE' || adminEntry.operation === 'JOB_TITLE_SYNONYMS'">Save</span>
        <span *ngIf="adminEntry.operation === 'SUGGEST_VALUE'">Suggest Value</span>
        <span *ngIf="adminEntry.operation === 'END_TRANSACTION'">End</span>
      </td>
      <td>{{ adminEntry.date | date: 'mediumDate' }}</td>
      <td>{{ adminEntry.date | date: 'mediumTime' }}</td>
    </tr>
    <tr>
      <th>

        <span *ngIf="userInput.adv_id!=null"> {{ userInput.adv_id }}</span>

        <span *ngIf="userInput.cv_id!=null"> {{ userInput.cv_id }}</span>


        <!-- <span *ngIf=" userInput.type !== 'JOB_TITLE' && userInput.type !== 'JOB_TITLE_SYNONYMS'">CV ID( {{  userInput.cv_id  }} )</span>
        <span *ngIf=" userInput.type === 'JOB_TITLE' || userInput.type === 'JOB_TITLE_SYNONYMS'">Adv ID( {{userInput.adv_id }} )</span> -->
      </th>
      <td></td>
      <td></td>
      <td></td>
    </tr>
  </tbody>

</table>
<!-- end of action table -->