import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from "@angular/common/http";
import { ExportUrlService } from "shared/shared-services/export-url.service";

@Injectable({
  providedIn: 'root'
})
export class CvsPreviewService {
  url = '';
  fav = '';
  read = '';
  move_to = '';
  interviewSet = '';
  advFolders = '';
  constructor(private http: HttpClient ,
              private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url = data + 'emp_app/search';
      this.fav = data + 'emp_app/favourite';
      this.read = data + 'emp_app/read';
      this.move_to = data + 'emp_app/move_to_folder';
      this.interviewSet = data + 'emp_app/interview';
      this.advFolders = data + 'emp_app/data';
    });
  }


  jsEncode(param: string){
    return encodeURIComponent(param);
  }

  jsDecode(param: string){
    return decodeURIComponent(this.jsEncode(param));
  }

  //commented have_data param
  getFilterData(pgsize, pgnum, lang, filters, columns?) {
    let params = new HttpParams();
    let body_req = [];
    params = params.append('pgsize', pgsize);
    params = params.append('pgnum', pgnum);
    params = params.append('lang', lang);
    if(columns && columns.length > 0) {
      let ColList = columns
      body_req = filters;
      ColList.forEach((ColName:string) =>{
        params = params.append(`requirements[]`, ColName);
      });
    //  params = params.append('have_data',fitlerFormData)
      return this.http.post(this.url,body_req,{ params: params})
     }else{
      let encodedName = this.jsDecode('?requirements=[]');
    //  params = params.append('have_data',fitlerFormData)
      body_req = filters;
      return this.http.post(this.url+encodedName,body_req,{ params: params})
    }
  }

  addFavCv(send_fav_data) {
    return this.http.post(this.fav,send_fav_data)
  }

  MoveCv(emp_ids,from,to) {
    let sendData = {};
    sendData['emp_ids'] = [];
    sendData['emp_ids'] = emp_ids;
    sendData['from_folder'] = from;
    sendData['to_folder'] = to
    return this.http.post(this.move_to,sendData)
  }

  MarkasRead(send_read_data) {
    return this.http.post(this.read,send_read_data)
  }

  setInterviewTime(emp_ids,date) {
    let sendData = {};
    sendData['emp_ids'] = [];
    sendData['emp_ids'].push(emp_ids) ;
    sendData['date'] = date;

    return this.http.post(this.interviewSet, sendData);
  }

  getAdvsTitlesFolders(){
    return this.http.get(this.advFolders);
  }

}
