<app-pre-loader [show]="subCat.length === 0 && mainCat.length === 0 && helpTopics.length === 0 && openedFromHelpTopic">
</app-pre-loader>
<app-help-header></app-help-header>

<div style="min-height:400px;">

<div class="container-padding" *ngIf="subCat.length !== 0 && mainCat.length !== 0 && helpTopics.length !== 0 ">

<!-- class="container-fluid" -->
  <div>
      <!-- class="row content-row" -->
    <div>
        <!-- class="col-md-10 col-sm-10 col-md-offset-1 col-sm-offset-1 col-xs-12 cat-col" -->
      <div class="cat-col">
        <div class="card">
          <!-- <div class="row content-row"> -->
          <div *ngIf="openedFromHelpTopic">
          </div>
          <!-- <div class="col-md-10 col-sm-10 col-md-offset-1 col-sm-offset-1 col-xs-12"> -->
          <!-- <div class="card"> -->
          <div>
            <ol class="breadcrumb">
              <li *ngIf="!openedFromHelpTopic" class="active"><a (click)="displayMainCats()"><i class="fa fa-home"
                    title="home page of help"></i></a></li>
              <li *ngIf="openedFromHelpTopic" class="active"><a [routerLink]="['/i/' + '/help']"><i class="fa fa-home"
                    title="home page of help"></i></a></li>
              <li class="active badge badge-primary category-badge">
                  <!-- [routerLink]="['/i/' + '/help/' + mainCat[currentLangId - 1].name + '/' + mainCat[currentLangId - 1].id]" -->
                <a
                  (click)="navigate('main',mainCat[currentLangId - 1].name,mainCat[currentLangId - 1].id)"
                  >{{mainCat[currentLangId - 1].name }}</a></li>
              <li class="active badge badge-primary category-badge">{{ subCat[currentLangId - 1].name}}</li>
            </ol>
          </div>

          <h1 class="subcat-title">{{ subCat[currentLangId - 1].name}}</h1>
          <ul class="list-group sub-group-list">
            <!-- <div class="badge badge-primary category-badge">{{ subCat[currentLangId - 1].name }}</div> -->
            <div *ngIf="helpTopics[currentLangId - 1].length === 0">
              <p class=" note" translate>help.EmptySubCat </p>
            </div>
            <div *ngFor="let  h of helpTopics[currentLangId - 1]; let i = index">
              <li class="list-group-item badge badge-primary category-badge ">
                  <!-- [routerLink]="[ '/i/' + '/help/' +  mainCat[currentLangId - 1].name + '/' + subCat[currentLangId - 1].name , h.id, h.slug ]" -->
                <h2>
                  <a class="help-topic"
                  (click)="navigate('slug',mainCat[currentLangId - 1].name ,null,subCat[currentLangId - 1].name, h.id, h.slug)"
                  >
                    {{h.title }}
                  </a>
                </h2>
              </li>
            </div>

          </ul>

        </div>
      </div>

    </div>
  </div>
</div>


</div>
