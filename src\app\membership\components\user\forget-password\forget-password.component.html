<div class="home-page-content">
  <div class="container">
    <div>
      <form  [formGroup]="forgetPasswordForm"
             #form="ngForm"
             class="form-horizontal validate-form"
             (ngSubmit)="forgetPassword()">
        <br>
        <div class="row">
          <h1>Enter your email</h1>
        </div>
        
        <br>

        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && !forgetPasswordForm.controls['email'].valid) || resetMessage }">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':forgetPasswordForm.controls['email'].value}">
            <input  type="email" formControlName="email" class="form-control" id="email" (keydown)="resetMessage=null">
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Enter Your Email</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && forgetPasswordForm.controls['email'].errors?.required">Required</span>
            <span class="error-message " *ngIf="form.submitted && forgetPasswordForm.controls['email'].errors?.invalidEmailError" translate>{{forgetPasswordForm.controls['email'].errors?.invalidEmailError}}</span>
            <span class="error-message " *ngIf="resetMessage">{{resetMessage}}</span>
          </div>
        </div>
       
        <div class="text-center">
          <button type="submit" class="btn btn-success">Send</button>
        </div>
      </form>
    </div>
  </div>
</div>

