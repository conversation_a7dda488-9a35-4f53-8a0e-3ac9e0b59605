<page-navbar [navType]="'empty'"></page-navbar>

<div class="article-container">
    <h1>CV Templates</h1>

    <div class="article-img text-center">
        <img src="./assets/images/footer-pages/cv-templates.webp" class="img-responsive">
    </div>

    
    <div class="section">
        <h2>Choose Your Perfect Resume Template &ndash; Make a Lasting Impression</h2>

        <p>At <strong>CVeek</strong>, we offer a curated collection of <strong>professional resume templates</strong> &mdash; all <strong>completely free</strong>, and designed to match your <strong>industry, career level</strong>, and even <strong>personal visual style</strong>.</p>
        
        <p>✔️ Whether you&#39;re looking for a <strong>clean and modern resume layout</strong>, or a <strong>standout design that highlights your skills and achievements</strong>, we&#39;ve got the perfect fit.</p>
        
        <p>✔️ Each template is crafted to <strong>catch attention</strong> and present your information in a clear, organized, and professional format.</p>
        
        <p>✔️ <strong>Apply any template in just one click</strong> &mdash; instantly transform your resume&rsquo;s appearance.</p>
        
        <p>✔️ <strong>Download a polished PDF version</strong>, ready to print or send digitally &mdash; at any time, to any employer.</p>
        
        <p><strong>Try our free resume templates today</strong> and let your resume speak for your potential.</p>
    </div>

    <div class="section text-center page-link">
        <h2>Check our Resume Builder page then choose your favourite template</h2>
        <a *ngIf="username ===null" routerLink="/m/user/login">Resume Builder</a>
        <a *ngIf="username !==null" [routerLink]="['/u', username, 'resumes']">Resume Builder</a>
    </div>
    
</div>
