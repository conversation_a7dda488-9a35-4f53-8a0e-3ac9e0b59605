import { ContactService } from '../../services/contact.service';
import { LanguageService } from '../../../admin/services/language.service';
import { FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng';
import { Title } from '@angular/platform-browser';


@Component({
  selector: 'app-contact-form',
  templateUrl: './contact-form.component.html',
  styleUrls: ['./contact-form.component.css']
})
export class ContactFormComponent implements OnInit, OnDestroy {
  contactForm;
  @Input('isAReportMsg')isAReportMsg = false;
  @Output('closeContactForm')closeContactForm = new EventEmitter();
  role = '';
  private ngUnsubscribe: Subject<any> = new Subject();
  languagesArray: any = [];
  currentLangId = 1;
  mainCat: {'label': string, 'value': number }[][] = [];
  subCat: {'label': string, 'value': number , 'main_cat_id': number}[][] = [];
  filteredSubCats: {'label': string, 'value': number , 'main_cat_id': number}[][] = [];
  displayAlert = false;
  msg = '';
  severity = 'success';
  visitor = false;
  userEmail = '';
  name = '';
  main_cat_id: number = null;

  constructor(private fb: FormBuilder,
              private languageService: LanguageService,
              private translate: TranslateService,
              private contactService: ContactService,
              private messageService: MessageService,
              private title: Title
              ) {
                translate.addLangs(['en', 'ar']);
                translate.setDefaultLang('en');
                const browserLang = translate.getBrowserLang();

                // title.setTitle('Contact us | CEVAST');
                if (localStorage.getItem('defaultLang')) {
                  this.currentLangId = +localStorage.getItem('defaultLang');
                  if (this.currentLangId === 2) {
                    this.languageService.changeDirection('app-contact-form', 2,
                     ['.wrap-input100 textarea.input100 ',  'p-dropdown label.ui-dropdown-label', 'form']);
                  }
                }
                translate.use( this.languageService.getLangAbbrev(this.currentLangId));
              }

  ngOnInit() {
    if (localStorage.getItem('role')) {
      this.role = localStorage.getItem('role');
      this.userEmail = JSON.parse(localStorage.getItem('id_token_claims_obj')).user_info.email;
      this.name = JSON.parse(localStorage.getItem('id_token_claims_obj')).user_info.first_name;
    } else {
      this.visitor = true;
      this.role = '';
    }

    if (this.isAReportMsg) {
      this.main_cat_id = 2;
    }

    this.buildEmptyForm();
    // this.getCategories();
  }

  buildEmptyForm() {
    this.contactForm = this.fb.group({
      'language_id': [this.currentLangId, Validators.required],
      'email'     : [this.userEmail, [ Validators.required, Validators.email ]],
      'email_role'      : [ this.role, Validators.required],
      'contact_main_cat_id': [this.main_cat_id, Validators.required],
      'contact_sub_cat_id': [null],
      'message'      : ['', Validators.required]
    });
   
    this.getLanguages();
  }

  getCategories() {
    for (let i = 0; i < this.languagesArray.length; i++) {
      this.contactService.getAllCategories(i + 1).takeUntil(this.ngUnsubscribe).subscribe(res => {
       
        this.mainCat[i] = [];
        this.subCat[i] = [];
        this.filteredSubCats[i] = [];

        // filling main_cat array
        let mainCatsTemp = res['ContactMainCat'];
        for (let category of mainCatsTemp) {
          if (category.contact_main_cat_translation.length !== 0) {
            this.mainCat[i].push({
              'label': category.contact_main_cat_translation[0].name,
              'value':  category.id,
            });
          }

        }
        this.mainCat[i].unshift({ 'label': '' , 'value': null});

        // filling sub_cat array
        let subCatsTemp = res['ContactSubCat'];
        for (let category of subCatsTemp) {
          if (category.contact_sub_cat_translation.length !== 0) {
            this.subCat[i].push({
              'label': category.contact_sub_cat_translation[0].name,
              'value': category.id,
              'main_cat_id': category.contact_main_cat_id
            });
          }
        }
        this.subCat[i].unshift({ 'label': '' , 'value': null, 'main_cat_id': null});

      });

    }

  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
      
      let temp = res['data'];
      for ( let lang of temp) {
        this.languagesArray.push({
          'id'  : lang.id,
          'name': lang.name
        });

      }
      
     this.getCategories();
  });
 }

//  changeLang( langId: number) {
//     this.translate.use(this.languageService.getLangAbbrev(langId));
//     this.currentLangId = langId;
//   }





  filter() {
    for (let j = 0; j < this.languagesArray.length; j++) {
      this.filteredSubCats[j] = [];
      for (let i = 0; i < this.subCat[j].length; i++) {
        if (this.subCat[j][i].main_cat_id === this.contact_main_cat_id.value) {
          this.filteredSubCats[j].push({
            'value': this.subCat[j][i].value,
            'label': this.subCat[j][i].label,
            'main_cat_id': this.subCat[j][i].main_cat_id
          });
        }
      }
      this.filteredSubCats[j].unshift({ 'label': '' , 'value': null, 'main_cat_id': null});

    }
    
   
    if (this.filteredSubCats[0].length <= 1 ) {
      this.contact_sub_cat_id.setValue(null);
      this.contact_sub_cat_id.setErrors(null);
      this.contact_sub_cat_id.errors = null;
    }
   
  }




  send(data) {
   
    if (this.contactForm.valid) {
        let dataToSend = this.contactForm.value;
        this.contact_main_cat_id.touched = false;
        this.contact_sub_cat_id.touched  = false;
        this.message.touched             = false;
        this.contactForm.touched         = false;
        this.contact_main_cat_id.setValue(null);
        this.contact_sub_cat_id.setValue(null);
        this.message.setValue('');
        if ( dataToSend.contact_main_cat_id !== null  ) {
            dataToSend.language_id = this.currentLangId;
            this.contactService.sendMessage(dataToSend).subscribe( (res: {'success': boolean, 'email': object }) => {
                
                if (res.success ) {
                  this.showSuccess();
                } else {
                  this.showError();
                }
                setTimeout(() => {  this.hideAlert(); }, 6000);
                this.closeContactForm.emit();
            }, (erorr: Error) => {
              this.showError();
              setTimeout(() => {  this.hideAlert(); }, 6000);
            }
            );
        } else {
             alert('please enter valid values');
        }

    }

  }


 showSuccess() {
    this.severity = 'success';
    this.displayAlert = true;
    this.msg = 'MsgSentSuccessfully';
 }

 showError() {
    this.severity = 'error';
    this.displayAlert = true;
    this.msg = 'FailToSendMsg';
 }

 hideAlert() {
  this.displayAlert = false;
 }


  get email() {
    return this.contactForm.get('email');
  }

  get email_role() {
    return this.contactForm.get('email_role');
  }

  get contact_main_cat_id() {
    return this.contactForm.get('contact_main_cat_id');
  }

  get contact_sub_cat_id() {
    return this.contactForm.get('contact_sub_cat_id');
  }

  get message() {
    return this.contactForm.get('message');
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
   }
}



