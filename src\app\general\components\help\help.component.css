a {
  cursor: pointer;
}
/* h2 {
  color: white;
  padding-top: 30px;
  padding-left: 30px;
} */
.badge.category-badge {
  padding: 5px 10px;
  font-size: 16px;
  margin-top: 12px;
  margin-bottom: 12px;
  color:#4876BA;
  /* color:#005b9f; */
  background-color: transparent;
  cursor: pointer;
  display: block;
}

div, table, td,th,p,h2,form ,ul, li, ol {
  font-family: 'Exo2-Regular', sans-serif;
}

.container {
  width: 100%;
  height: 100%;

}
.title{
  color:#4876BA;
  /* color: #005b9f; */
  font-size:30px;
  padding-top: 30px;
  padding-left: 26px;
}
.container {
  width: 100%;
  height: 100%;
}
input.form-control {
  height: 34px;
  width: calc(50%);
  transition: all 0.5s ease-in-out;
}

span.languages button.btn {
  float: right;
  margin-top: 30px;;
}

/* Start Responsive  */
.container-fluid{
  padding:4px;
}
/* @media screen and (max-width: 1024px ){
  .container-fluid{
    padding:0 30px;
  }
} */

@media screen and (max-width: 850px ) {
  h2 {
    margin-top: -40px !important;
  }
}

@media screen and (min-width: 851px) and (max-width: 1024px ) {
  h2 {
    margin-top: -30px !important;
  }
}

@media screen and (min-width: 1400px)  {
  h2 {
    margin-top: 0px !important;
  }
}
@media screen and (max-width: 767px)  {
  .container-fluid{
    padding: 0 20px 30px 20px;;
}
  .title{
    font-size:25px;
    padding-left:4px;
  }
  /* h2{
    padding-left: 6px;
    padding-right: 6px;
  } */
}

a {
  display: block;
}
a:hover {
 text-decoration: none;
}
a.show {
  padding-bottom: 10px;
}

div.lang-col {
    margin-top: 17px;
    padding-right: 0px;
}

.card {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius:2%;
  margin-bottom: 10px;
  transition: all 0.6s ease-in-out;
}
.card:hover {
  box-shadow:  1px 2px 10px #686F7A;
}

.content-row{
  margin-top: 10px;
}

.cat-col{
  padding-right: 10px;
  padding-left: 10px;
}
h4, h5 {
  padding-left: 15px;
  font-weight: bold;
}

p.no-matches {
  background-color: white;
  padding: 10px;
  padding-left: 30px;
  margin: 10px;
}

h5 {
  padding-left: 20px;
}
:host ::ng-deep p-progressSpinner svg circle {
  fill:transparent;
}
