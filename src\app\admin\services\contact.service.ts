import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable()
export class ContactService {
  url = '';
  tempUrl = '';
  archUrl = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.baseUrl = data + 'admin';
          this.url     = data + 'admin/contact';
          this.tempUrl = data + 'admin/template_massage';
          this.archUrl = data + 'admin/archive_massage';
    });
  }

  getMsgLog(msgId) {
      let headers = new HttpHeaders().set('Content-Type', 'application/json');
      return this.http.get(this.baseUrl + '/received_email_activity_log/' + msgId, { headers });
  }


  // messages requests
  getMessages() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url, { headers });
  }

  addReply(reply) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/replay', reply, { headers });
  }

  addComment(comment) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/comment', comment, { headers });
  }

  assignTo(admin) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/assign', admin, { headers });
  }

  getAdmins() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + '/get_admin_users', { headers });
  }

  setAsOpen(msgId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/open/' + msgId, { headers });
  }

  setAsDone(msgId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/done/' + msgId, { headers });
  }

  setAsNotDone(msgId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/not_done/' + msgId, { headers });
  }

  deleteMultiMsgs(msgIds) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/multi_delete_for_messages' , msgIds, { headers });
  }

   // http://localhost:8000/admin/contact/template_titles


  // template APIs:
  getMsgTemplates() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.tempUrl + '/get_template', { headers });
  }



  addTemplate(template) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.tempUrl + '/add', template, { headers });
  }

  editTemplate(tempId, template) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.tempUrl + '/' + tempId, template, { headers });
  }

  removeTemplate(tempId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.delete(this.tempUrl + '/' + tempId, { headers });
  }



 // archive APIs:
  getMsgArchive() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.archUrl + '/get_massages', { headers });
  }

  restoreMultiMsgs(msgsIds) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.archUrl + '/restore_multi_messages', msgsIds, { headers });
    // { "received_messages_ids":[2] }
  }

  deleteMultiArchivedMsgs(msgsIds) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.archUrl + '/delete_multi_messages', msgsIds, { headers });
  // {  	"received_messages_ids":[3]  }
  }





}
