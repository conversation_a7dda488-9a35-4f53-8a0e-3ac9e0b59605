.cus-navbar{
  border: 0;
  /* border-bottom: 1px solid #ddd; */
  position: fixed;
  z-index: 1000;
  /* z-index: 10000; */
  top: 0;
  width: 100%;
  background: #ffffff;
}
.nav-container{
  padding:0 20px;
  display: flex;
}
nav {
  transition: all 0.3s ease;
}
.nav-header{
  width:27%;
  display: flex;
  align-items: center;
}
.nav-header , .nav-menu{
  display: flex;
  align-items: center;
}
.nav-header .logo-img{
  display:inline-block;
}
.nav-header .logo-img img{
  height:70px;
  padding: 9px 0;
  /* width:70px; */
}
.nav-header .site-title{
  position: absolute;
  clip: rect(1px, 1px, 1px, 1px);
  -webkit-clip-path: inset(0px px 0px 99.9% 99.9%);
  clip-path: inset(0px 0px 99.9% 99.9%);
  overflow: hidden;
  height: 1px;
  width: 1px;
  padding: 0;
  border: 0;
}
/* .nav-header .site-title{
  padding:0 10px;
  color:#3D7BCE;
  font-family: 'Exo2';
  font-size: 1.6rem;
} */

.nav-menu1{
  width:45%;
  justify-content: center;
}
.nav-menu2{
  width:28%;
  justify-content: flex-end;
}
.nav-menu ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.nav-menu ul li{
  float:left;
}
.nav-menu ul li a{
  display: inline-block;
  padding: 10px;
  cursor: pointer;
}
.nav-menu ul li a svg{
  /* width:50px; */
  height:41px;
}
.nav-menu1 li{
  margin:0 12px;
}
.cus-toggle-btn{
  padding: 6px;
  margin-top: 3px;
  width: 35px;
  height: 35px;
  opacity: 1;
  border-radius: 50%;
  cursor: pointer;
}
.cus-toggle-btn:hover{
  background-color: rgba(60,64,67,0.08);
  transition:all .3s ease;
}
.cus-toggle-btn svg{
  width: 24px;
  height: 24px;
  color: #999;
  opacity: 1;
  fill: currentColor;
}
.top-navbar-profile-li{
  width: 122px;
  text-align: center;
}
.top-navbar-profile{
  display: inline-block;
  padding: 0 0 0 12px;
  color: #999;
  cursor: pointer;
}
.home-nav .top-navbar-profile{
  color:#fff;
}
.scrolled .top-navbar-profile{
  color:#999 !important;
}
.img-div{
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
  /* display: inline-block;
  vertical-align: middle; */
  /* width: 35px; */
  max-width:100%;
  height: 35px;
}
.img-div img{
  border-radius: 50%;
  max-height: 100%;
}
.top-navbar-profile .profile-name{
  padding: 0 8px;
  white-space: nowrap;
}
.top-navbar-profile .fa {
  font-size: 1.2rem;
}
.top-navbar-profile-li .dropdown-menu{
  left:unset;
  right:0;
}
.top-navbar-profile-li .dropdown-menu .dropdown-item {
  display: block;
  text-decoration: none;
  color: #999;
  padding: 5px 10px;
  transition: all .2s ease;
}
.top-navbar-profile-li .dropdown-menu .dropdown-item:hover{
  color:#555;
  background: #f9f9f9;
}
.toggle-menu .dropdown-menu .dropdown-item{
  text-decoration: none;
  color: #555;
  width: 31%;
  margin: 8px 2px;
  padding: 0;
}
.toggle-menu .dropdown-menu .dropdown-item img {
  display:block;
  margin: auto;
  width: 35px;
  height: 35px;
  border-radius: 4px;
}
.toggle-menu .dropdown-menu .dropdown-item span{
  display:block;
  font-size: 11px;
  color:#777;
}
.toggle-menu .dropdown-menu{
  transform: translate(-75%,0px);
  width: 300px;
  padding: 10px;
}

.job-search{
  display: none;
}

.search-state .nav-menu1{
  display:none;
}
.search-state .job-search{
  /* display:inline-block; */
  display: flex;
  align-items: center;
  width:45%;
}
.search-state .job-search job-search-form-top-bar{
  width:100%;
}
.search-state .country-link-li{
  display:none;
}
.toggle-menu .aditional-links , .toggle-menu .mobile-links , .toggle-menu .mobile-links-block{
  display:none !important;
}
.home-nav .toggle-menu .aditional-links , .home-nav .toggle-menu .mobile-links{
  display:inline-block !important;
}

.search-state .toggle-menu .aditional-links{
  display:inline-block !important;
}
.search-state .countryDD-home{
  display:none;
}
.search-container .search-btn , .search-container .search-btn:active , .search-container .search-btn:focus , .search-container .search-btn:hover{
  background: transparent;
  outline: 0;
  box-shadow: none;
}
.search-container .search-btn{
  margin-bottom: 0;
  padding-bottom: 0;
  padding-top: 0;
}
.search-container .search-btn i{
  color:#30457c;
  font-size: 24px;
  margin-bottom: 15px;
  margin-left: 8px;
}
.search-container .form-group{
  margin-bottom:12px;
}
.search-container .form-horizontal{
  margin-top:18px;
}


.search-div{
  display:inline-block;
  width: calc(100% - 110px);
}

.focus-no-padding .custom-underline{
  width: calc(100% - 125px) !important;
  bottom:17px !important; 
}
.country-div{
  display:inline-block;
}
.search-btn-div{
  display:inline-block;
  width:50px;
}

.country-link{
  width: 16px;
  height: 16px;
  border-radius: 3px;
}
/* start nav icons styling */
.nav-post-job .cls-1,.nav-post-job .cls-4,.nav-post-job .cls-6{fill:none;}
.nav-post-job .cls-2{fill:#f39a22;}
.nav-post-job .cls-3{
  /* fill:url(#New_Pattern_Swatch_3); */
  fill:#f39a22;
}
.nav-post-job .cls-4,.nav-post-job .cls-6{
  stroke-miterlimit:10;
  /* stroke:url(#New_Pattern_Swatch_3); */
  stroke:#f39a22;
}
.nav-post-job .cls-4{stroke-width:6px;}
.nav-post-job .cls-5{
  clip-path:url(#clip-path);
}
.nav-post-job .cls-6{stroke-width:25px;}

.nav-inbox .cls-1{fill:#324572;}
.nav-inbox .cls-2,.nav-inbox .cls-4,.nav-inbox .cls-5,.nav-inbox .cls-6{fill:none;stroke-miterlimit:10;}
.nav-inbox .cls-2,.nav-inbox .cls-4,.nav-inbox .cls-5{stroke:#5291cd;}
.nav-inbox .cls-2{stroke-width:25px;}
.nav-inbox .cls-3{fill:#5291cd;}
.nav-inbox .cls-4{stroke-width:6px;}
.nav-inbox .cls-5{stroke-linecap:round;stroke-width:10px;}
.nav-inbox .cls-6{stroke:#324572;stroke-width:30px;}

.nav-advertisement .cls-1,.nav-advertisement .cls-3{fill:none;}
.nav-advertisement .cls-2{fill:#3a5f8c;}
.nav-advertisement .cls-3{
  stroke-miterlimit:10;
  stroke-width:30px;
  stroke:#3a5f8c;
  /* stroke:url(#New_Pattern_Swatch_5); */
}


.nav-home .cls-1 , .nav-home .cls-2{
  fill:none;
  stroke:#2e3871;
  stroke-miterlimit:10;
}
.nav-home .cls-1{
  stroke-linecap:round;
  stroke-width:40px;
}
.nav-home .cls-2{
  stroke-width:30px;
}
.nav-home .cls-3{
  fill:#2e3871;
}

/* .nav-articles .cls-1, .nav-articles .cls-2, .nav-articles .cls-3{
  fill:none;
}
.nav-articles .cls-2, .nav-articles .cls-3, .nav-articles .cls-5{
  stroke:#b69963;stroke-miterlimit:10;
}
.nav-articles .cls-2, .nav-articles .cls-5{
  stroke-width:11px;
}
.nav-articles .cls-3{
  stroke-width:25px;
}
.nav-articles .cls-4{
  fill:#f3cf71;
}
.nav-articles .cls-5{
  fill:url(#Unnamed_Pattern_4);
}
.nav-articles .cls-6{
  font-size:116.39px;fill:#fff;font-family:ArialMT, Arial;
}

.nav-search-people .cls-1{
  fill:#324572;
}
.nav-search-people .cls-2{
  fill:#5291cd;
} */

.top-navbar-links a{
  transition:all .3s ease;
}

.top-navbar-links a svg{
  transition:all .3s ease;
}
.top-navbar-links a svg path{
  transition:all .3s ease;
}
svg .ani-last {
  transition:all 1s ease !important;
}
.top-navbar-links a.nav-home.active , .top-navbar-links a.nav-home:hover {
  background: #2e3871;
}
.top-navbar-links a.nav-search-job.active , .top-navbar-links a.nav-search-job:hover {
  background: rgb(53,181,88);
}
.top-navbar-links a.nav-post-job.active, .top-navbar-links a.nav-post-job:hover {
  background: #f39a22;
}
.top-navbar-links a.nav-inbox.active, .top-navbar-links a.nav-inbox:hover {
  background:  #5291cd;
}
.top-navbar-links a.nav-advertisement.active, .top-navbar-links a.nav-advertisement:hover {
  background:  #3a5f8c;;
}
.top-navbar-links a.nav-search-people.active, .top-navbar-links a.nav-search-people:hover {
  background: #5291cd;
}


.nav-search-job svg{
  width: 66px;
}
.nav-home svg{
  width: 46px;
}

.nav-search-job .cls-1, .nav-search-job .cls-4{
  fill:none;
}
.nav-search-job .cls-2 , .nav-search-job .cls-6{
  fill:#2fb256;
}
.nav-search-job .cls-3{
  fill:url(#New_Pattern_Swatch_4);
}
.nav-search-job .cls-4{
  stroke-miterlimit:10;
  stroke-width:6px;
  stroke:url(#New_Pattern_Swatch_4);
}


.nav-write-cv .cls-1, .nav-write-cv  .cls-3,.nav-write-cv  .cls-5,.nav-write-cv  .cls-6, .nav-write-cv  .cls-7{
    fill:none;
  }
  .nav-write-cv  .cls-2{
      fill:#628db6;
  }
  .nav-write-cv  .cls-3,.nav-write-cv  .cls-6{
      stroke-linecap:round;
  }
  .nav-write-cv  .cls-3 , .nav-write-cv .cls-5, .nav-write-cv  .cls-6,.nav-write-cv  .cls-7{
      stroke-miterlimit:10;
      stroke: #628db6;
  }
  .nav-write-cv  .cls-3{
      stroke-width:30px;
  }
  .nav-write-cv  .cls-4{
      fill:#628db6;
  }
  .nav-write-cv  .cls-5{
      stroke-width:5px;
  }
  .nav-write-cv .cls-6{
      stroke-width:12px;
  }
  .nav-write-cv .cls-7{
      stroke-width:6px;
  }


.top-navbar-links a.active svg , .top-navbar-links a:hover svg{
  fill:#fff;
}
.top-navbar-links a.active svg path , .top-navbar-links a:hover svg path{
  stroke:#fff;
} 
.top-navbar-links a.nav-home.active .cls-3 , .top-navbar-links a.nav-home:hover .cls-3{
  fill:#fff;
}
/* .top-navbar-links a.nav-search-people.active .cls-1 , .top-navbar-links a.nav-search-people:hover .cls-1 , 
.top-navbar-links a.nav-search-people.active .cls-2 , .top-navbar-links a.nav-search-people:hover .cls-2{
  fill:#fff;
} */

.top-navbar-links a.nav-post-job.active .cls-2,.top-navbar-links a.nav-post-job.active .cls-3 ,
.top-navbar-links a.nav-post-job:hover .cls-2,.top-navbar-links a.nav-post-job:hover .cls-3{
  fill:#fff;
}
.top-navbar-links a.nav-post-job.active .cls-4,.top-navbar-links a.nav-post-job.active .cls-6 ,
.top-navbar-links a.nav-post-job:hover .cls-4,.top-navbar-links a.nav-post-job:hover .cls-6{
  stroke:#fff;
}

.top-navbar-links a.nav-inbox.active .cls-2,.top-navbar-links a.nav-inbox.active .cls-4 ,
.top-navbar-links a.nav-inbox.active .cls-5,.top-navbar-links a.nav-inbox.active .cls-6 ,
.top-navbar-links a.nav-inbox:hover .cls-2,.top-navbar-links a.nav-inbox:hover .cls-4,
.top-navbar-links a.nav-inbox:hover .cls-5,.top-navbar-links a.nav-inbox:hover .cls-6{
  stroke:#fff;
}
.top-navbar-links a.nav-inbox.active .cls-1,.top-navbar-links a.nav-inbox.active .cls-3 ,
.top-navbar-links a.nav-inbox:hover .cls-1,.top-navbar-links a.nav-inbox:hover .cls-3{
  fill:#fff;
}

.top-navbar-links a.nav-advertisement.active .cls-3,
.top-navbar-links a.nav-advertisement:hover .cls-3{
  stroke:#fff;
}

.top-navbar-links a.nav-search-job.active .cls-3 , .top-navbar-links a.nav-search-job:hover .cls-3 {
  fill: #fff;
}

.top-navbar-links a.nav-search-job.active .cls-4 , .top-navbar-links a.nav-search-job:hover .cls-4  {
  stroke: #fff;
  fill:#fff;
}
.top-navbar-links a.nav-search-job:hover svg path{
  stroke: #fff;
  fill: #fff;
}

.nav-help .cls-1{
    fill:none;stroke:#7f1416;stroke-miterlimit:10;stroke-width:46px;
  }
.nav-help .cls-2{
    fill:#7f1416;
  }

  .nav-faqs .cls-1{
    fill:#6298d1;
  }

/* .toggle-menu .dropdown-menu a svg{
  height: 29px !important;
} */
.toggle-menu .dropdown-menu .aditional-links svg , .toggle-menu .dropdown-menu .mobile-links svg,
.toggle-menu .dropdown-menu .fixed-toggle-links svg{
  height: 29px !important;
}
.toggle-menu .dropdown-menu .nav-search-job svg{
  width: 42px !important;
}
.toggle-menu .dropdown-menu .nav-search-job .cls-3{
  fill:#2fb256;
}
.toggle-menu .dropdown-menu .nav-faqs svg{
  width: 42px !important;
}
.toggle-menu .dropdown-menu a{
    cursor: pointer;
}

.notification-icon{
  height: 20px;
}
.notification-icon path{
 fill:#999;
}
.dropdown:hover .notification-icon path{
 fill:#3D7BCE;
}

/* 
.notif-link{
    position: relative;
}
.notif-link i{
  font-size: 19px;
}
.notif-count{
  position: absolute;
  top: 0px;
  right: 5px;
  width: 15px;
  padding: 4px;
  font-size: 10px;
  line-height: 6px;
  height: 15px;
  border-radius: 50%;
  background: red;
  color: #fff;
  border: 1px solid #fff;
  font-weight: bold;
} */
/* end nav icons styling */

.country-dropdown .country-img{
  width:30px;
  margin-top:4px;
  float:right;
  border-radius: 2px;
}

.topbar-icons svg path , .topbar-icons .st1 , .topbar-icons polygon{
  fill:#3D7BCE;
  opacity: 0.7;
  transition: all .1s ease;
}

.topbar-icons:hover path , .topbar-icons:hover svg .st1 , .topbar-icons:hover polygon{
  opacity: 1  !important;
}

.no-profile{
  width:35px;
  height: 100%;
  border-radius: 50%;
  background: #F9B21C;
}
.no-profile-span{
  display: inline-block;
  padding: 6px;
  font-weight: bold;
  font-size: 17px;
  color: #fff;
}

@media screen  and (max-width:1160px){
  .nav-menu ul li a svg{
      /* width: 44px; */
      height: 31px;
  }
  .nav-search-job svg{
      width: 55px;
  }
  .nav-home svg{
      width: 38px;
  }
  /* .nav-header .logo-img img {
      width: 72px;
  } */
  .nav-header .site-title {
      font-size: 1.4rem;
  }
  .search-container .form-group {
      margin-bottom: 3px;
  }
  .nav-header .logo-img img {
    padding: 15px 0;
  }
}

@media screen  and (max-width:992px){
  /* .nav-menu ul li a svg{
      height: 37px;
  } */
  .nav-search-job svg{
      width: 44px;
  }
  .nav-home svg{
      width: 31px;
  }
}

@media screen  and (max-width:905px){
  .nav-header .logo-img img {
    height:60px;
      /* width: 60px; */
  }
  .nav-header .site-title {
      font-size: 1.2rem;
  }
  .nav-header{
      width:23%;
  }
  .nav-menu2{
      width:23%;
  }
  .nav-menu1 , .job-search{
      width:54% !important;
  }
  .top-navbar-profile .profile-name {
      display: none;
  }
  .top-navbar-profile .fa {
      display: none;
  }
  .top-navbar-profile-li{
      width: 47px;
  }
  .search-container .form-group {
      margin-bottom: 0px;
  }
  .search-container .form-horizontal{
      margin-top: 8px;
  }
}

@media screen and (max-width: 860px){
  .nav-menu ul li a svg{
      height: 29px;
  }
}
@media screen  and (max-width:767px){
  .nav-header .logo-img img {
    height:56px;
    padding: 10px 0;
    /* width: 56px; */
  }
  .mobile-view .nav-menu1{
      display:none;
  }
  .mobile-view .toggle-menu .aditional-links , .mobile-view .toggle-menu .mobile-links{
      display:inline-block !important;
  }
  .mobile-view .mobile-hide{
      display:none;
  }
  .mobile-view .toggle-menu .dropdown-menu{
      transform: translate(-93%,0px);
  }
  .mobile-view  .mobile-links-block{
      display:block !important;
      width:100% !important;
  }
  .mobile-view .list-style{
    display:flex !important;
    align-items: center;
  }
  .mobile-view .list-style:hover span{
    color:#3D7BCE !important;
  }
  .mobile-view .list-style img {
      display:inline-block !important;
      margin-right: 8px !important;
      width: 18px !important;
      height: 18px !important;
  }
  .mobile-view .list-style svg {
    display:inline-block !important;
    margin-right: 8px !important;
    height: 18px !important;
  }
  .mobile-view .list-style span{
      display:inline-block !important;
      font-size:12px !important;
  }
  .profile-mobile{
      margin-top:12px;
  }
  .profile-mobile img{
      max-width:30px;
      border-radius: 50%;
      margin-right: 3px;
  }
  .profile-mobile .no-profile{
    display:inline-block;
    width: 30px;
    height: 30px;
    margin-right: 3px;
  }
  .profile-mobile .no-profile .no-profile-span{
      padding: 3px 0 0 9px;
  }

  .job-search{
      display: inline-block;
      visibility: hidden;
  }
  .search-state .job-search{
      visibility: visible;
      padding-left: 20px;
  }
  .search-container .form-horizontal{
      margin-top: 18px;
  }
  /* .job-search{
      width: 57% !important;
  }
  .nav-menu2{
      width: 20%;      
  }
  .nav-header .site-title {
      font-size: 1rem;
  } */
  .country-dropdown .country-img{
      width:23px;
  }
  .search-container .search-btn i{
      font-size: 21px;
  }
  .focus-no-padding .custom-underline {
      bottom: 15px !important;
  }

  /* start fix nav items width */
  .nav-container{
    padding: 0 10px;
  }
  .search-state .nav-header{
    width:20% !important;
  }
  .nav-header{
    width:62% !important;
  }
  .job-search , .search-state .job-search{
    width: 76% !important;
  }
  .nav-menu2{
    width: 7%;      
  }
    /* end fix nav items width */
}

/* max-width:600 */
/* @media screen  and (max-width:767px){
  .nav-menu2{
      width: 25%;      
  }
  .job-search{
      width: 48% !important;
  }
  .nav-header{
      width:41% !important;
  }

  .search-state .nav-menu2{
      width: 17%;      
  }
  .search-state .job-search{
      width: 71% !important;
  }
  .search-state .nav-header{
      width:12% !important;
  }
  .search-state .nav-header .site-title {
      display:none;
  }
  .nav-header .logo-img img {
    padding: 10px 0;
  }
} */
@media screen  and (max-width:400px){
  .cus-toggle-btn{
      padding: 3px;
      margin-top: 5px;
      width: 25px;
      height: 25px;
  }
  .cus-toggle-btn svg {
      width: 20px;
      height: 20px;
  }
}