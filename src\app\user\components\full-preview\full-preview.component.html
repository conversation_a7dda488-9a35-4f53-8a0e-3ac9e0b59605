<page-navbar
    *ngIf="templateId !== undefined"
    [username]="username"  [userResumeId]="userResumeId"
    [navType]="'fullPreview'"  [templateId]="templateId"
  >
  </page-navbar>

<div class="preview-all-wrapper">
  <div class="side-options-bar" *ngIf="displaySideOptionsBar">
    <button class="btn" [routerLink]="['/u',username,userResumeId]" pTooltip="Edit">
        <img src="./assets/images/secondbar/edit.svg">
    </button>
    <button class="btn" (click)="downloadPdf()" pTooltip="Download" [disabled]="resumeHaveNoData">
        <img src="./assets/images/secondbar/download.svg">
    </button>
    <button class="btn" (click)="downloadAtsPdfFile()" pTooltip="Download ATS Format" [disabled]="resumeHaveNoData">
        <img src="./assets/images/secondbar/download.svg">
    </button>
  </div>
  <!-- <div class="side-options-bar">
    <a [routerLink]="['/u',username,userResumeId]">
        <div><img src="./assets/images/secondbar/edit.svg"></div>
        <span>Edit</span>
    </a>
    <a (click)="downloadPdf()">
        <div><img src="./assets/images/secondbar/download.svg"></div>
        <span>Download</span>
    </a>
  </div> -->

  <div class="resume-preview" *ngIf="resumeId != null">
    <div class="page-title">
        <span>CVeek Preview</span>
    </div>

    <div *ngIf="resumeHaveNoData">
        <div class="alert alert-warning" role="alert">
            {{errorOjb['error']}}
            <br>
            <a *ngIf="errorOjb['type'] === 'empty_resume'" [routerLink]="['/u',username,userResumeId]">Start adding CV information</a>
        </div>
    </div>

    <!-- *ngIf="resumeId != null && !resumeHaveNoData" -->
    <resume-preview *ngIf="!resumeHaveNoData" [resumeId]="resumeId" [username]="username"  [userResumeId]="userResumeId">
        <ng-container class="summary-edit">
            <a routerLink="/u/{{username}}/{{userResumeId}}/summary/" style="float:left;margin-right:15px;font-size:18px;color:#fffd;">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="objective-edit">
            <a routerLink="/u/{{username}}/{{userResumeId}}/objective/" style="float:left;margin-right:15px;font-size:18px;color:#fffd;">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="personalInfo-edit">
            <a class="edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/personal-info/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="contactInfo-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/contact-info/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="education-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/education/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="wordExp-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/work-experience/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="skills-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/skills/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="languages-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/languages/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="driving-edit">
            <a class="edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/driving-license/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="achievements-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/achievements/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="certifications-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/certifications/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="portfolios-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/portfolios/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="publications-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/publications/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="training-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/training/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="projects-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/projects/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="memberships-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/memberships/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="volunteers-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/volunteers/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="conferences-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/conferences-workshop-seminar/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="hobbies-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/hobbies-interests/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
        <ng-container class="references-edit">
            <a class=" edit-btn" routerLink="/u/{{username}}/{{userResumeId}}/references/">
                <i class="fa fa-pencil-square-o"></i>
            </a>
        </ng-container>
    </resume-preview>

  </div> <!-- End of resume-preview div -->

</div> <!-- End of preview-all-wrapper div -->
    <div class="modal fade" id="cvTemplateModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModal2Label">CV Templates</h4>
                </div>
                <cv-template-modal *ngIf="resumeId != null" [resumeId]="resumeId" [username]="username" [userResumeId]="userResumeId" [templateId]="templateId">
                </cv-template-modal>
            </div>
        </div>
    </div>
