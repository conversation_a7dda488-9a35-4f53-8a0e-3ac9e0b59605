import { LanguageService } from './../../../../services/language.service';
import { HelpTopic } from 'app/admin/models/help-topic';
import { Component, OnInit, OnDestroy, Input, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HelpTopicService } from 'app/admin/services/help-topic.service';
import { Subject } from 'rxjs/Subject';
import { Language } from 'app/admin/models/language';
import { Table } from 'primeng/table';
declare var $: any;

@Component({
  selector: 'app-admin-help-topics',
  templateUrl: './admin-help-topics.component.html',
  styleUrls: ['./admin-help-topics.component.css']
})
export class AdminHelpTopicsComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  displayModal = false;
  hTopic;
  hTopicIdToDelete;
  // hTopicToPreview;
  hTopicsArray = [];
  filteredHTopics = [];
  topicsCount: number;
  currentLangId;
  languagesArray: Language[] = [];
  main_categories: {'user_main_categories': { 'value': number, 'label': string}[][],
  'company_main_categories': { 'value': number, 'label': string}[][]}
 = { 'user_main_categories': [], 'company_main_categories': []};
sub_categories: {'user_sub_categories': { 'value': number, 'label': string, 'main_cat_id': number}[][],
  'company_sub_categories': { 'value': number, 'label': string, 'main_cat_id': number}[][]}
 = { 'user_sub_categories': [], 'company_sub_categories': []};
private ngUnsubscribe: Subject<any> = new Subject();
types = [ {'label': '', 'value': null},
{'label': 'User', 'value': 'user help'},
{'label': 'Company', 'value': 'company help'}
];
statuses = [
{'label': '', 'value': null},
{'label': 'active', 'value': 1},
{'label': 'inactive', 'value': 0}
];
loading = true;
  mode: string;
type;
status;
main_cat;
sub_cat;
main = [];
sub = [];
constructor(private translate: TranslateService,
            private htopicService: HelpTopicService,
            private languageService: LanguageService ) {
  translate.addLangs(['en', 'ar']);
  translate.setDefaultLang('en');
  const browserLang = translate.getBrowserLang();
  translate.use(browserLang.match(/en|ar/) ? browserLang : 'en');
  if (this.translate.currentLang === 'en') {this.currentLangId = 1;
  } else { this.currentLangId = 2; }
}

ngOnInit() {
  this.getQuestions();
  this.getQuestionFromSidebar();
  this.getLanguages();

}

getQuestions() {
  // getting questions
  this.htopicService.getHelpTopics().takeUntil(this.ngUnsubscribe).subscribe(res => {
    console.log('res', res);
    let htopicsTemp = res['help_center'];
    for (let ht of htopicsTemp) {
               this.hTopicsArray.push({
                'id'              : ht.id,
                'main_cat_id'     : ht.help_center_main_cat_id,
                'sub_cat_id'      : (ht.help_center_sub_cat === null) ? 'null' : ht.help_center_sub_cat_id,
                'main_cat'        : ht.help_center_main_cat.help_center_main_cat_trans[0].name,
                'sub_cat'         : (ht.help_center_sub_cat === null) ? '----' : ht.help_center_sub_cat.help_center_sub_cat__trans[0].name,
                'order'           : ht.order,
                'active'          : ht.active,
                // 'active_label'    : (ht.active === 0 ) ? 'inactive' : 'active',
                'type'            : ht.help_center_type.toLowerCase(),
                'title'           : (ht.help_center_trans.length === 0) ? '' : ht.help_center_trans[0].title,
                'description'     : (ht.help_center_trans.length === 0) ? '' : ht.help_center_trans[0].description,
                'slug'            : (ht.help_center_trans.length === 0) ? '' : ht.help_center_trans[0].slug,
                'page_title'      : (ht.help_center_trans.length === 0) ? '' : ht.help_center_trans[0].page_title,
                'meta_description': (ht.help_center_trans.length === 0) ? '' : ht.help_center_trans[0].meta_description,
                'meta_keywords'   : (ht.help_center_trans.length === 0) ? '' : ht.help_center_trans[0].meta_keywords,
                'langId'          : (ht.help_center_trans.length === 0) ? 1 : ht.help_center_trans[0].translated_languages_id,
                'display'         : false

              });

    }
    console.log('hTopicsArray', this.hTopicsArray);
    this.loading = false;
    this.table.filter(1, 'id', 'startsWith');
    this.table.filter(null, 'id', 'startsWith');
  });
  // this.filteredHTopics = this.hTopicsArray;

}


getQuestionFromSidebar() {
   // getting new question from the createModal in the admin
   this.htopicService.newHelpTopic.takeUntil(this.ngUnsubscribe)
   .subscribe(ht => {
       if ( ht.id !== null ) {
         console.log('new help topic from sidebar was added to the table ', ht);
         let res = {
           'data': {
             'id'                          : ht.id,
             'order'                       : ht.order,
             'active'                      : ht.active,
             'help_center_type'            : ht.type,
             'help_center_main_cat_id': ht.main_cat_id,
             'help_center_sub_cat_id' : ht.sub_cat_id,
             'help_center_main_cat': { 'help_center_main_cat_trans': [ { 'name': ht.main_cat }]},
             'help_center_sub_cat' : { 'help_center_sub_cat__trans': [ { 'name': ht.main_cat }]},
             'help_center_trans'   : [{ 'translated_languages_id': ht.langId , 'title': ht.title,
                                        'description': ht.description, 'page_title': ht.page_title, 'slug': ht.slug,
                                        'meta_description': ht.meta_description, 'meta_keywords': ht.meta_keywords }]
           }
         };
         this.showNewHelpTopicInTable(res);
       }

   });

}



getLanguages() {
  this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
    console.log(res);
    let temp = res['data'];
    for ( let lang of temp) {
      this.languagesArray.push({
        'id'  : lang.id,
        'name': lang.name
      });

    }

    console.log('languages array', this.languagesArray);
    console.log('lang arr length', this.languagesArray.length);
    this.getCategories();
});
}

getCategories() {
  for (let i = 0; i < this.languagesArray.length; i++) {
    console.log('inside cat loop', i);
    this.htopicService.getHTCategories(i + 1).takeUntil(this.ngUnsubscribe).subscribe( categoriesRes => {
      console.log('cat res', i + 1, categoriesRes);
      // filling user_main_cat array
      let categoriesTemp = categoriesRes['user_category'];
      this.main_categories.user_main_categories[i] = [];
      this.sub_categories.user_sub_categories[i] = [];
      for (let k = 0; k < categoriesTemp.length; k++) {
        this.main_categories.user_main_categories[i].push({
              'label': categoriesTemp[k].help_center_main_cat__trans[0].name,
              'value':  categoriesTemp[k].id,
        });
        // filling user_sub_cat array
        let userSubCategories = categoriesTemp[k].help_center_sub_cat;
        // console.log('userSubCategories', i, userSubCategories);
        for (let m = 0; m < userSubCategories.length; m++) {
          this.sub_categories.user_sub_categories[i].push({
                'label': userSubCategories[m].help_center_sub_cat__trans[0].name,
                'value':  userSubCategories[m].id,
                'main_cat_id': userSubCategories[m].help_center_main_cat_id,
          });
        }
        // this.sub_categories.user_sub_categories[i].unshift({ 'label': ' ', 'value': null, main_cat_id: null });
        console.log('user sub category[' + i + '] ', this.sub_categories.user_sub_categories[i]);
      }
      this.main_categories.user_main_categories[i].unshift({ 'label': ' ', 'value': null });
      console.log('user main category[' + i + '] ', this.main_categories.user_main_categories[i]);

      // filling company_main_cat array
      let categoriesTemp2 = categoriesRes['company_category'];
      this.main_categories.company_main_categories[i] = [];
      this.sub_categories.company_sub_categories[i] = [];
      for (let j = 0; j < categoriesTemp2.length; j++) {
        this.main_categories.company_main_categories[i].push({
              'label': categoriesTemp2[j].help_center_main_cat__trans[0].name,
              'value':  categoriesTemp2[j].id,
        });
        // filling company sub_cat array
        let comSubCategories = categoriesTemp2[j].help_center_sub_cat;
        // console.log('comSubCategories', i, comSubCategories);
        for (let n = 0; n < comSubCategories.length; n++) {
          this.sub_categories.company_sub_categories[i].push({
                'label': comSubCategories[n].help_center_sub_cat__trans[0].name,
                'value':  comSubCategories[n].id,
                'main_cat_id': comSubCategories[n].help_center_main_cat_id
          });
        }
        console.log('company sub category[' + i + '] ', this.sub_categories.company_sub_categories[i]);
      }
      this.sub_categories.company_sub_categories[i].unshift({ 'label': ' ', 'value': null, 'main_cat_id': null });
      this.main_categories.company_main_categories[i].unshift({ 'label': ' ', 'value': null });
      console.log('company main category[' + i + '] ', this.main_categories.company_main_categories[i]);
      this.main = this.main_categories.company_main_categories[0].concat(this.main_categories.user_main_categories[0]);
      this.sub = this.sub_categories.company_sub_categories[0].concat(this.sub_categories.user_sub_categories[0]);
    });

  }

}


activateHtopic(ht) {
    const body = {
      'active':  (ht.active) ? 0 : 1
    };
    this.htopicService.activateHtopic(body, ht.id).subscribe(res => {
      console.log('activate res', res);
      let index = this.hTopicsArray.indexOf(ht);
      this.hTopicsArray[index].active =  (res['data'] !== undefined) ? res['data'].active : false;
      if ((!ht.active) && res['data'] === undefined) {
        alert(res['error']);
      }
    });

}

showNewHelpTopicInTable(event) {
  let temp = event['data'];
  console.log('temp', temp);
  for (let i = 0; i < temp.help_center_trans.length; i++) {
    if (temp.help_center_trans[i].translated_languages_id === 1) {
        let ht = {
          'id'              : temp.id,
          'order'           : temp.order,
          'active'          : temp.active,
          'type'            : temp.help_center_type.toLowerCase(),
          'main_cat_id'     : temp.help_center_main_cat_id,
          'sub_cat_id'      : temp.help_center_sub_cat_id,
          'main_cat'        : temp.help_center_main_cat.help_center_main_cat_trans[0].name,
          'sub_cat'         :temp.help_center_sub_cat? temp.help_center_sub_cat.help_center_sub_cat__trans[0].name: '----',
          'title'           : temp.help_center_trans[0].title,
          'description'     : temp.help_center_trans[0].description,
          'slug'            : temp.help_center_trans[0].slug,
          'page_title'      : temp.help_center_trans[0].page_title,
          'meta_description': temp.help_center_trans[0].meta_description,
          'meta_keywords'   : temp.help_center_trans[0].meta_keywords,
          'langId'          : temp.help_center_trans[0].translated_languages_id,
          'display'         : false

        };
        this.hTopicsArray.splice(0, 0, ht);
        console.log('newHelpTopicninTable', ht);
        this.table.filter(null, 'id', 'startsWith');


    }
  }

  this.closeModal();

}


showUpdatedHelpTopicInTable(event) {
  let index = this.gethTopicIndex(event['old']);
  console.log(index);
  let temp = event['new'];
  for (let i = 0; i < temp.help_center_trans.length; i++) {
    if (temp.help_center_trans[i].translated_languages_id === 1) {

      let updatedHTopic = {
        'id'              : temp.id,
        'order'           : temp.order,
        'active'          : temp.active,
        'type'            : temp.help_center_type.toLowerCase(),
        'main_cat_id'     : temp.help_center_main_cat_id,
        'sub_cat_id'      : temp.help_center_sub_cat_id,
        'main_cat'        : temp.help_center_main_cat.help_center_main_cat_trans[0].name,
        'sub_cat'         : temp.help_center_sub_cat_id?temp.help_center_sub_cat.help_center_sub_cat__trans[0].name:'',
        'title'           : temp.help_center_trans[0].title,
        'description'     : temp.help_center_trans[0].description,
        'slug'            : temp.help_center_trans[0].slug,
        'page_title'      : temp.help_center_trans[0].page_title,
        'meta_description': temp.help_center_trans[0].meta_description,
        'meta_keywords'   : temp.help_center_trans[0].meta_keywords,
        'langId'          : temp.help_center_trans[0].translated_languages_id,
        'display'         : false
      };

      console.log('updated En help topic', this.hTopicsArray[index], updatedHTopic);
      this.hTopicsArray.splice(index, 1, updatedHTopic);

    }

  }

  this.closeModal();

}


removeHelpTopicFromTable(event) {
  console.log('delete help topic', event['data']);
  let temp = event['data'];
  if ( temp !== null  || temp !== {}) {
    let index = this.gethTopicIndex(this.hTopicIdToDelete);
    console.log('deleted help topic', this.hTopicIdToDelete, 'index', index);
      this.hTopicsArray.splice(index, 1);
    }
  this.closeModal();
  this.table._totalRecords = this.hTopicsArray.length;
}



displayPreviewModal(ht) {
  this.mode = 'preview';
  this.hTopic = {
    'id'              : ht.id,
    'order'           : ht.order,
    'active'          : ht.active,
    'type'            : ht.type,
    'slug'            : ht.slug,
    'main_cat_id'     : ht.main_cat_id,
    'sub_cat_id'      : ht.sub_cat_id,
    'main_cat'        : ht.main_cat,
    'sub_cat'         : ht.sub_cat,
    'title'           : ht.title,
    'description'     : ht.description,
    'page_title'      : ht.page_title,
    'meta_description': ht.meta_description,
    'meta_keywords'   : ht.meta_keywords,
    'langId'          : ht.langId,
  };
  this.displayModal = true;

}

closeModal() {
  this.displayModal = false;
  $('#topicModal').hide();
  $('body').removeClass('modal-open');
  $('body').removeAttr('style');
  $('div.modal-backdrop.fade.in').remove();
}


displayCreateModal() {
  this.mode = 'create';
  this.displayModal = true;

}


displayEditFormModal(ht) {
  this.mode = 'edit';
  this.hTopic = {
    'id'              : ht.id,
    'order'           : ht.order,
    'active'          : ht.active,
    'main_cat_id'     : ht.main_cat_id,
    'sub_cat_id'      : ht.sub_cat_id,
    'main_cat'        : ht.main_cat,
    'sub_cat'         : ht.sub_cat,
    'type'            : ht.type,
    'slug'            : ht.slug,
    'title'           : ht.title,
    'description'     : ht.description,
    'page_title'      : ht.page_title,
    'meta_description': ht.meta_description,
    'meta_keywords'   : ht.meta_keywords,
    'langId'          : ht.langId,
  };
  console.log('ht t up', this.hTopic);
  this.displayModal = true;
}


displayDeleteAlert(ht) {
  this.mode = 'delete';
  this.displayModal = true;
  console.log('display delete alert ht', ht);
  this.hTopicIdToDelete =  ht.id;
}




closePreviewOpenEdit() {
  // this.hTopic = {
  //     'id'              : this.hTopic.id,
  //     'order'           : this.hTopic.order,
  //     'active'          : this.hTopic.active,
  //     'main_cat_id'     : this.hTopic.main_cat_id,
  //     'sub_cat_id'      : this.hTopic.sub_cat_id,
  //     'main_cat'        : this.hTopic.main_cat,
  //     'sub_cat'         : this.hTopic.sub_cat,
  //     'type'            : this.hTopic.type,
  //     'slug'            : this.hTopic.slug,
  //     'title'           : this.hTopic.title,
  //     'description'     : this.hTopic.description,
  //     'page_title'      : this.hTopic.page_title,
  //     'meta_description': this.hTopic.meta_description,
  //     'meta_keywords'   : this.hTopic.meta_keywords,
  //     'langId'          : this.hTopic.langId,
  // };
  this.mode = 'edit';

}


gethTopicIndex(htopic): number {
  let index: number = null;
  for (let ht of this.hTopicsArray) {
    if (ht.id === htopic.id) {
      index = this.hTopicsArray.indexOf(ht);
    }
  }
  return index;
}

clearAll() {
  this.table.filter(null, 'id', 'startsWith');
  this.table.filter(null, 'title', 'contains');
  this.table.filter(null, 'type', 'equals');
  this.table.filter(null, 'order', 'contains');
  this.table.filter(null, 'main_cat_id', 'equals');
  this.table.filter(null, 'sub_cat_id', 'equals');
  this.table.filter(null, 'active', 'equals');
  this.table.filterGlobal(null, 'contains');
  $('.ui-table-globalfilter-container input').val(null);
  console.log($('.ui-column-filter').val());
  $('.ui-column-filter').val(null);
   this.status = '';
   this.type = '';
   this.main_cat = '';
   this.sub_cat = '';
 }

ngOnDestroy(): void {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();

}


}





