
import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {AbstractControl, FormBuilder, FormControl, Validators} from '@angular/forms';
import { CompanyFormService } from '../../services/company-form.service';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import { CompanyWrapperService } from '../../services/company-wrapper.service';
import { DataMap } from "shared/Models/data_map";
import { GeneralService } from '../../../general/services/general.service';

declare var $: any;

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'form-edit',
  templateUrl: './form-edit.component.html',
  styleUrls: ['./form-edit.component.css']
})
export class FormEditComponent implements OnInit {

  // @Input('submitEdit') submitEdit ;
  // sendData = this.submitEdit;
  username = '';
  @Input('profileId') profileId;
  data_map = new DataMap();
  subscription: Subscription;
  sendData;
  @Output() closeModalPopup = new EventEmitter();
  lang_passed;
  loader = false;
  constructor(private companyFormService: CompanyFormService,
    private router: Router,
    private route: ActivatedRoute,
    private generalService: GeneralService
  ) {

/*---- here we get the data which has been sent from company-form ---*/

    this.subscription =  companyFormService.Data.subscribe(val => {
      this.sendData = val;
      if(this.sendData.language_passed !== undefined) {
        this.lang_passed = this.sendData.language_passed
      }
      });

      this.route.parent.parent.params.subscribe(res => {
        this.username = res['username'];
      });
   }

  ngOnInit() {
  }

  close() {
    this.sendData = [];
    $('#companyedit').modal('hide');
  }

  /*--- this the OK button in the popup interface which represents the Alert Message when the
        company edit it's profile  so when click OK the data will submit---*/
  submit() {
    this.loader = true;
    let logo = this.sendData.logo;
    this.companyFormService.editCompanyForm(this.sendData, this.profileId).toPromise().then(
      (res) =>  {
        if(logo.file !== "" && logo.is_deleted === false){
          this.generalService.notify('profilePictureChanged' , 'companyProfile','navbar' , {'profile_picture':res['translation'][1].path_company_imagelogo});
          localStorage.setItem("pic",res['translation'][1].path_company_imagelogo);
        }
        //company logo deleted , so remove company logo in navigation bar
        else if(logo.file === "" && logo.is_deleted === true){
          this.generalService.notify('profilePictureRemoved' , 'companyProfile','navbar' , {'profile_picture':"none"});
          localStorage.setItem("pic","none");
        }

        let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
        if(this.lang_passed  !== undefined) {
          send_Data_to_preview['language_passed'] = this.lang_passed;
        }
        this.companyFormService.send_Data(send_Data_to_preview);
        this.loader = false;
        $('#companyedit').modal('hide');
        this.router.navigate(['/c',this.username,'profile','preview'])
      }
    );
    this.sendData = [];
  }

}
