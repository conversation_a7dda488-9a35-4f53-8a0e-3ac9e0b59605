<app-pre-loader  [show]="filteredHelpTopics.length === 0"></app-pre-loader>
 <!-- Start of header -->
 <app-help-header></app-help-header>
 <!-- End of header -->

 <div class="container-fluid" style="min-height:400px;">
   <div style="background:white !important;">
  <h1 class="title" translate>help.HelpCenterTitle</h1>
  <!-- class="container" -->
  <div>
    <app-pre-loader *ngIf="mainCategories.length === 0"></app-pre-loader>

<app-main-help *ngIf="displayMainCats" [mainCategories]="mainCategories" [mainCat]="mainCat"  [openedFromHelpTopic]="false" [display]="display" [displayCats]="displayCats" [displayOneMainCat]="displayOneMainCat" [currentLangId]="currentLangId" [languagesArray]="languagesArray" (subCatClicked)="displaySubHelp($event)" (displayHomeClicked)="displayHelpHome()" ></app-main-help>

<app-sub-help *ngIf="displaySubCats" [helpTopics]="selectedHelpTopics" [mainCat]="selectedMainCat" [subCat]="selectedSubCat" [openedFromHelpTopic]="false" (helpHomeClicked)="displayHelpHome()"   [currentLangId]="currentLangId"></app-sub-help>

<!-- <app-help-topic *ngIf="displayhTopic" [helpTopic]="selectedHelpTopic" [helpTopicTrans]="helpTopicTrans" [languagesArray]="languagesArray"  (displayHomeClicked)="displayHomeFromhTopic()" [currentLangId]="currentLangId" ></app-help-topic> -->

</div>
</div>
</div>

