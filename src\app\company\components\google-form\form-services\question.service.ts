import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';

import 'rxjs/add/operator/map';
import {Observable} from 'rxjs/Observable';
import { Question } from "app/company/components/google-form/Models/question";
// tslint:disable-next-line:import-blacklist
import { BehaviorSubject } from 'rxjs';
import { ExportUrlService } from "shared/shared-services/export-url.service";

@Injectable()
export class QuestionService {
  /* baseUrl =  'https://cre-s.com/' ;
  version = 'v1.1.1' ;
  private url = new BehaviorSubject<any>(this.baseUrl + this.version + '/api/' ) ;
  private url = 'http://localhost:8000'; */

  /* baseUrl =  'https://cre-s.com/' ;
  version = 'v1.2.48' ;
  private url = new BehaviorSubject<any>(this.baseUrl + this.version + '/api/' ) ;
  private url = new BehaviorSubject<any>('http://localhost:8000/') ; */
  url = '';
  constructor(private http: HttpClient , private privateSharedURL: ExportUrlService) { 
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url = data
    })
   }


  public GetResponseByUser(formId?:number, userId?: number){
    if(!formId) formId=1;
    if(!userId) userId=3;
    return this.http.get(this.url+'userReplies/'+formId+'/'+userId);
  }

  public GetResponse(){
    return this.http.get(this.url+'getReplies/'+1);
  }

  getAllQuestions(form_id?: number): Observable<Question[]> {
    if(!form_id) form_id=1;
    return this.http
      .get <Question[]>(this.url + `forms/${form_id}/questions`);
  }

  addQuestion(question: Question) {
    const body = JSON.stringify({
        'form_id':1,
        'label': question.label,
        'controlType': question.controlType,
        'isRequired': question.isRequired,
        'order': question.order,
        'choices': question.choices,
      }
    );
    //console.log(body);
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + 'forms/'+question.form_id+'/questions', body, {headers});
  }

  addResponse(responses,form_id?:number, user_id?:number) {
    if(!form_id) form_id=1;
    if(!user_id) user_id=3;

    const body = JSON.stringify({
          'user_id': user_id,
          'responses': responses
        }
    );
    //console.log(body);
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + `forms/${form_id}/responses`, body, {headers});
  }

  updateQuestion(question: any, id: any,formId?:number) {
    if(!formId) formId=1;
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + 'forms/'+formId+'/questions/' + id, JSON.stringify(question), {headers});
  }

  deleteQuestion(id: any,formId?:number) {
    if(!formId) formId=1;
    return this.http.delete(this.url + 'forms/'+formId+'/questions/'+ id);
  }

  reOrderQuestion(data: any) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    const body = JSON.stringify( {'questions': data} );
    return this.http.post(this.url + 'reorder', body, {headers});
  }

  getAllQuestionsChart(formId?:number){
  if(!formId) formId=1;
    return this.http.get(this.url +'allcharts/'+formId)
  }

}
