import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChange, SimpleChanges } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
declare var $: any;
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'app-cols-manager',
  templateUrl: './cols-manager.component.html',
  styleUrls: ['./cols-manager.component.css']
})

export class ColsManagerComponent implements OnInit , OnChanges  {
@Input() allItems ;
@Input() selectedItems ;
@Input() showModal ;
@Input() folder ;
@Input() maxColAllowed;
arr1: any;
temp1: any;
arr2: any;
temp2: any;
remainingColsToAdd: number;
standardItems: any;
  constructor(private generalService: GeneralService) {

   }

  ngOnInit(): void {
    this.arr1 = [...this.allItems];
    this.temp1 = [...this.selectedItems];
    this.arr2 = [];
    this.temp2 = [];
    this.standardItems = [] ;
    this.remainingColsToAdd = this.maxColAllowed - this.arr2.length;
  }

  ngOnChanges(changes: SimpleChanges) {
      if (changes['selectedItems'] && changes['selectedItems']['currentValue']) {
        this.arr2 = [];
        this.temp2 = [];
        this.temp1 = changes['selectedItems']['currentValue'];
        this.exchangeItems('1', 'arr1', 'arr2');
      }

  }

  exchangeItems(temp,src,dist, type= '') {
     if(this.remainingColsToAdd  > 0 && this.arr2.length  === 0)
     {
      this[dist] = this[dist].concat(this['temp' + temp]);
      for ( let i = 0 ; i < this['temp' + temp].length; i++) {
         this[src] =   this[src].filter((el) => el['field'] !== this['temp' + temp][i]['field']);
      }
      this['temp' + temp] = [];
      this.standardItems = this.arr2.filter((el) => el.standard);
      this.remainingColsToAdd = this.maxColAllowed - this.arr2.length;
      if (this.standardItems) {
        this.remainingColsToAdd += this.standardItems.length;
      }
     }
  }


  onCheckboxChange(e, item , dist) {
     if (e.target.checked) {
      var El = {
        'title' : item['title'],
         'field' : item['field'],
         'selected' : false,
     };
     this[dist].push(El);
     } else {
      this[dist] =   this[dist].filter((el) => el['field'] !== item['field']);
     }
  }

  save() {
    /// because we don't  have more time  we didn't change any thing on backend
    //// so we will make the following processing
    //// because  requirements needs to be = ['field1' , 'field2', .... etc]
     // tslint:disable-next-line:prefer-const
     let requirements = [];
         this.arr2.forEach(element => {
            requirements.push(element['field']);
         });
    //
    this.generalService.notify('folderChanged' , 'cols-manager' , 'cvs-table' ,
     {'folder' : this.folder , 'requirements' : requirements}) ;
     $('#ColsModal').modal('toggle');
  }


  cancel(event) {
    $('#ColsModal').modal('hide');
  }
}
