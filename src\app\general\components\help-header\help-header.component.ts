import { Component, OnInit, Input } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { GeneralService } from '../../services/general.service';

@Component({
  selector: 'app-help-header',
  templateUrl: './help-header.component.html',
  styleUrls: ['./help-header.component.css']
})
export class HelpHeaderComponent implements OnInit {
  searchQuery = '';
  @Input('inSearchInterface') inSearchInterface: boolean;

  constructor( private router: Router,
    private generalService: GeneralService,
    private route: ActivatedRoute,
    ) { }

  ngOnInit(): void {
    if(this.inSearchInterface){
      this.route.params.subscribe(res => {
        this.searchQuery = res['query'];
      });
    }
  }

  search(){
    if(this.inSearchInterface){
      this.generalService.notify('inHelpSearchInterface' , 'help-header' , 'help-search' , {"searchQuery":this.searchQuery}) ;
    }
    else {
      this.router.navigate(['i/','help-search' , this.searchQuery]);
    }
  }

}
