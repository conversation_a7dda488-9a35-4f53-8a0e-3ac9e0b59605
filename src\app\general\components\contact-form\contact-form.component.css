input, label, h1, p, button, div {
  font-family: 'Exo2-Regular', sans-serif;
}

label[for="type"] {
  padding-left: 10px;
  margin-top: 5px;
}

div.form-group.type-form-group {
  margin-top: 10px;
  margin-bottom: 10px;
}

.dropdown-input.wrap-input100.validate-input {
  padding: 5px 15px;
  background-color:#fff;
  margin-top: 30px;
  margin-bottom: 30px;
}

.alert.alert-danger {
  /* padding: 5px; */
  border: none;
  padding-left: 25px;
  background-color: transparent;

}

.form-group.type-form-group .row {
  padding-left: 80px;
  margin-top: 30px;
}


.container-contact100 {
  width: 100%;
  min-height: 100vh;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 15px;
  position: relative;
  background-color: #f2f2f2;
}


.input100 {
  position: relative;
  display: block;
  width: 100%;
  background: #fff;
  border-radius: 31px;

  font-family: 'Exo2-Regular', sans-serif;
  font-size: 18px;
  color: #8f8fa1;
  line-height: 1.2;
}

.validate-input {
  position: relative;
}
.wrap-input100 {
  width: 100%;
  /* background-color: #fff; */
  border-radius: 31px;
  margin-bottom: 16px;
  position: relative;
  /* z-index: 1; */
}

* {
	margin: 0px;
	padding: 0px;
	box-sizing: border-box;
}


/*---------------------------------------------*/
a {
	font-family: 'Exo2-Regular', sans-serif;
	font-size: 14px;
	line-height: 1.7;
	color: #666666;
	margin: 0px;
	transition: all 0.4s ease-in-out;
	-webkit-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
}


/*---------------------------------------------*/
h1,h2,h3,h4,h5,h6 {
	margin: 0px;
}

p {
	font-family: 'Exo2-Regular', sans-serif;
	font-size: 14px;
	line-height: 1.7;
	color: #666666;
	margin: 0px;
}

ul, li {
	margin: 0px;
	list-style-type: none;
}


/*---------------------------------------------*/
input {
	outline: none;
	border: none;
}



textarea {
  outline: none;
  border: none;
  overflow-y: hidden !important;
}

textarea:focus, input:focus {
  /* border-color: transparent !important; */
  border: 1px solid !important;

}

input::-webkit-input-placeholder { color: #bdbdd3;}
input:-moz-placeholder { color: #bdbdd3;}
input::-moz-placeholder { color: #bdbdd3;}
input:-ms-input-placeholder { color: #bdbdd3;}

textarea::-webkit-input-placeholder { color: #bdbdd3;}
textarea:-moz-placeholder { color: #bdbdd3;}
textarea::-moz-placeholder { color: #bdbdd3;}
textarea:-ms-input-placeholder { color: #bdbdd3;}

/*---------------------------------------------*/
button {
	outline: none !important;
	border: none;
	background: transparent;
}

button:hover {
	cursor: pointer;
}

iframe {
	border: none !important;
}


/*---------------------------------------------*/
.container {
	max-width: 1200px;
}




/*//////////////////////////////////////////////////////////////////
[ Contact ]*/

.container-contact100 {
  width: 100%;
  min-height: 100vh;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 15px;
  position: relative;
  /* background-color: #f2f2f2; */
  /* background: -webkit-linear-gradient(top, rgb(21, 14, 84) 0%, #276ea4 100%); */
  margin-top: -50px;
  /* background-image: url(assets/images/44.jpg); */
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
  background-blend-mode: luminosity;
  background-color: #fff;
}

.wrap-contact100 {
  width: 550px;
  background: transparent;
  padding: 0px 0px 15px 0px;
}


/*==================================================================
[ Form ]*/

.contact100-form {
  width: 100%;
}

.contact100-form-title {
  display: block;
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 30px;
  color: #403866;
  line-height: 1.2;
  text-transform: uppercase;
  text-align: center;
  padding-bottom: 49px;
  padding-top: 49px;
}

/*------------------------------------------------------------------
[ Input ]*/

.wrap-input100 {
  width: 100%;
  /* background-color: #fff; */
  border-radius: 31px;
  margin-bottom: 16px;
  position: relative;
  /* box-shadow: 2px 1px 2px 0px; */
  /* z-index: 1; */
}

.input100 {
  position: relative;
  display: block;
  width: 100%;
  background: #fff;
  border-radius: 31px;
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 18px;
  color: #8f8fa1;
  line-height: 1.2;
}


/*---------------------------------------------*/
input.input100 {
  height: 62px;
  padding: 0 35px 0 35px;
}


textarea.input100 {
  min-height: 169px;
  padding: 19px 35px 0 35px;
}


/*------------------------------------------------------------------
[ Button ]*/
.container-contact100-form-btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding-top: 10px;

}

.contact100-form-btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  min-width: 150px;
  height: 62px;
  background-color: #0000ff8f;
  border-radius: 31px;
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 16px;
  color: #fff;
  line-height: 1.2;
  text-transform: uppercase;

  /* -webkit-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out; */
  position: relative;
  /* z-index: 1; */

}



.contact100-form-btn:hover {
  background-color: #403866 !important;
}

.contact100-form-btn[disabled] {
  background-color: #1212db41;
  cursor: not-allowed !important;
}

.contact100-form-btn[disabled]:hover {
  background-color: #0000ff00;
  cursor: not-allowed !important;
}
.m-r-6 {margin-right: 6px;}



/*------------------------------------------------------------------
[ Alert validate ]*/

.validate-input {
  position: relative;
}

.alert-validate::before {
  content: attr(data-validate);
  position: absolute;
  z-index: 1000;
  max-width: 70%;
  background-color: #fff;
  border: 1px solid #c80000;
  border-radius: 14px;
  padding: 4px 25px 4px 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 10px;
  pointer-events: none;

  font-family: 'Exo2-Regular', sans-serif;
  color: #c80000;
  font-size: 13px;
  line-height: 1.4;
  text-align: left;

  visibility: hidden;
  opacity: 0;

  -webkit-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  transition: opacity 0.4s;
}

.alert-validate::after {
  content: "\f06a";
  font-family: FontAwesome;
  display: block;
  position: absolute;
  z-index: 1100;
  color: #c80000;
  font-size: 16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 16px;
}


:host ::ng-deep p-dropdown .ui-dropdown.ui-widget.ui-state-default.ui-corner-all
     .ui-dropdown-label.ui-inputtext.ui-corner-all {
      font-family: 'Exo2-Regular', sans-serif;
      font-size: 18px;
      color: #8f8fa1;
}


 :host ::ng-deep  p-messages[severity=success] .ui-messages.ui-widget.ui-corner-all.ui-messages-success {
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 1.4em;
  border-radius: 10px;
  border: none;
  width: 550px;
  height: 70px;
  background-color: #00800017;
  color: #028802;
  margin: auto;
  margin-top: 15px;
  vertical-align: middle;
  text-align: center;
  padding-top: 20px;
 }

 :host ::ng-deep  p-messages[severity=error] .ui-messages.ui-widget.ui-corner-all.ui-messages-error{
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 1.4em;
  background-color: transparent;
  color: #980707;
  border-radius: 10px;
  border: none;
  width: 550px;
  height: 70px;
  margin: auto;
  margin-top: 15px;
  vertical-align: middle;
  text-align: center;
  padding-top: 20px;
}


:host ::ng-deep  p-messages .ui-messages .ui-messages-icon.pi.pi-check {
  padding-left: 100px;
  color: green;
  margin-top: -5px;
}
ui-messages-icon pi ng-tns-c128-15 pi-check

:host ::ng-deep  p-messages .ui-messages .custom-message {
  padding-top: 5px;;
}

:host ::ng-deep  p-messages .ui-messages  .ui-messages-close {
  margin-top: 15px;
}

:host ::ng-deep  p-messages .ui-messages.ui-messages-success .ui-messages-icon {
  color: green;
  margin-top: -5px;
}

:host ::ng-deep  p-messages .pi.pi-times {
  font-size: 0.7em;
  color: darkgray;
}

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */


.ui-state-disabled, .ui-widget:disabled {
  opacity: 1;
}


:host ::ng-deep p-progressSpinner svg circle {
  fill:transparent;
}
