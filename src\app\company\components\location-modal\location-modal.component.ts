import { Component, OnInit, ViewChild, ElementRef, OnDestroy, Input, EventEmitter, Output } from '@angular/core';
// import { MapToFormService } from '../../../user/cv-services/map-to-form.service';
import { Place } from 'shared/Models/place';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// import { ActivatedRoute, Router } from '@angular/router';
import { Subject, BehaviorSubject } from 'rxjs';
import { CompanyLocationService } from './../../services/company-location.service';
import {LangChangeEvent, TranslateService} from '@ngx-translate/core';
import { DataMap } from 'shared/Models/data_map';
declare var $: any;
@Component({
  selector: 'location-modal',
  templateUrl: './location-modal.component.html',
  styleUrls: ['./location-modal.component.css']
})

export class LocationModalComponent implements OnInit {
  currentLng: any;
  currentLat: any;
  display: boolean;
  companyLocation: FormGroup;
  private ngUnsubscribe: Subject<any> = new Subject();
  @ViewChild('googleplace') googleplace: any;
  @ViewChild('googleplace') public googleplaceRef: ElementRef;
  @ViewChild('googlelocationplace') googlelocationplace: any;
  @ViewChild('googlelocationplace') public googlelocationplaceRef: ElementRef;
  currentType: string = 'currentLocation';
  currentLocation: Place = new Place();
  currentSub: BehaviorSubject<any> = new BehaviorSubject('');
  @Input('location_item') location_item: any;
  city: string;
  country: string;
  country_code: any;
  streetAddr: any;
  check_name: boolean;
  check_location: boolean = false;
  companyId = Number (localStorage.getItem('company_id'));
  currentLanguage = Number (localStorage.getItem('current_company_language'));
  @Output() closeModalPopup = new EventEmitter();

  data_map = new DataMap()
  
  constructor(
    // private mapToFormService: MapToFormService,
    private fb: FormBuilder,
    // private router: Router,
    // private route: ActivatedRoute,
    private companyLocationService: CompanyLocationService,
    private translate: TranslateService
  ) {

    this.companyLocation = this.fb.group({
      'name': ['',Validators.required],
      'company_id': [''],
      'location': ['',Validators.required],
      'latitude': [''],
      'longitude': [''],
      'country': [''],
      'country_code': ['',Validators.required],
      'city': [''],
      'postal_code': [''],
      'street_address': [''],
      'verified_by_google': [''],
      'location_id': ['']
  });
   }

  //  initializePlaceData() {
  //   this.mapToFormService.personalMapData.skip(1).takeUntil(this.ngUnsubscribe).subscribe(
  //     (place: Place) => {

  //       if (place.type === 'currentLocation') {
  //         this.currentLocation = place;
  //       }
  //       this.setCurrentLocationControls(this.currentLocation);
  //     }
  //   );
  // }

  setcompanyLanguage() {
    if (this.currentLanguage && this.currentLanguage === 1) {
      this.translate.setDefaultLang('en');
    } else if (this.currentLanguage === 2) {
      this.translate.setDefaultLang('ar');
    } else {
      this.translate.setDefaultLang('en');
    }
}


// setCurrentLocationControls(place: Place) {
//   this.companyLocation.controls['country'].setValue(place.country);
//   this.companyLocation.controls['city'].setValue(place.city);
//   this.companyLocation.controls['postal_code'].setValue(place.postalCode);
//   this.companyLocation.controls['street_address'].setValue(place.streetAddress);
//   this.companyLocation.controls['latitude'].setValue(place.latitude);
//   this.companyLocation.controls['longitude'].setValue(place.longitude);
//   this.companyLocation.controls['country_code'].setValue(place.countryCode)
//   if (place.full == 'Dragging') {
//     this.companyLocation.controls['location'].setValue(this.companyLocation.controls['country'].value + ',' +this.companyLocation.controls['city'].value + ',' + this.companyLocation.controls['street_address'].value);
//   } else {
//     this.companyLocation.controls['location'].setValue(this.companyLocation.controls['country'].value + ',' +this.companyLocation.controls['city'].value + ',' + this.companyLocation.controls['street_address'].value);
//   }
// }

// notifyCurrentMap(val) {
//     this.currentSub.next(val);
// }
  

/* getAddress(place: object) {
  this.city = this.getCity(place);
  this.country = this.getCountry(place);
  this.companyLocation.controls['country'].setValue(this.country);
  this.companyLocation.controls['city'].setValue(this.city);
  this.companyLocation.controls['location'].setValue(this.setFullLocation());
  $('#location').focus();
} */
  

private getLocationPlaceAutocomplete() {
  //to stop bot traffic to google maps
  if(navigator.userAgent.match(/Googlebot/i)){
    return;
  }
  const autocomplete = new google.maps.places.Autocomplete(this.googlelocationplace.nativeElement,
    {
      types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
      fields: ['address_components','geometry']
    });
  autocomplete.addListener('place_changed', () => {
    const place = autocomplete.getPlace();
    this.getLocationAddress(place); 
  });
}
  
// getLocationAddress(place: object) {
//   this.city = this.getCity(place);
//   this.country = this.getCountry(place);
//   this.companyLocation.controls['country'].setValue(this.country);
//   this.companyLocation.controls['city'].setValue(this.city);
//   this.companyLocation.controls['name'].setValue(this.companyLocation.controls['name'].value);
// } 

getLocationAddress(place: object) {
  let data_location = this.data_map.getLocationDataFromGoogleMap(place)
  
  this.city = data_location.city;
  this.country = data_location.country;
  this.country_code = data_location.country_code;
  this.streetAddr = data_location.street_address;
  this.companyLocation.controls['street_address'].setValue(this.streetAddr);
  this.companyLocation.controls['country'].setValue(this.country);
  this.companyLocation.controls['city'].setValue(this.city);
  this.companyLocation.controls['country_code'].setValue(this.country_code);
  this.companyLocation.controls['name'].setValue(this.companyLocation.controls['name'].value);
  this.companyLocation.controls['latitude'].setValue(data_location.latitude);
  this.companyLocation.controls['longitude'].setValue(data_location.longitude);
}

clearLocationData() {
  this.companyLocation.controls['country'].setValue('');
  this.companyLocation.controls['country_code'].setValue('');
  this.companyLocation.controls['city'].setValue('');
  this.companyLocation.controls['street_address'].setValue('');
  this.companyLocation.controls['latitude'].setValue('');
  this.companyLocation.controls['longitude'].setValue('');
}

initializeFormData() {
  this.companyLocation.controls['country'].setValue(this.location_item.country);
  this.companyLocation.controls['city'].setValue(this.location_item.city);
  this.companyLocation.controls['postal_code'].setValue(this.location_item.postalCode);
  this.companyLocation.controls['latitude'].setValue(this.location_item.latitude);
  this.companyLocation.controls['longitude'].setValue(this.location_item.longitude);
  this.companyLocation.controls['country_code'].setValue(this.location_item.country_code);

  if(this.location_item.city){
    if(this.location_item.street_address){
      this.companyLocation.controls['street_address'].setValue(this.location_item.street_address);
      this.companyLocation.controls['location'].setValue(this.companyLocation.controls['country'].value + ',' +this.companyLocation.controls['city'].value + ',' + this.companyLocation.controls['street_address'].value);
    }
    else 
      this.companyLocation.controls['location'].setValue(this.companyLocation.controls['country'].value + ',' +this.companyLocation.controls['city'].value);
  }

  else
    this.companyLocation.controls['location'].setValue(this.companyLocation.controls['country'].value);
  
  this.companyLocation.controls['verified_by_google'].setValue(this.location_item.verified_by_google);
  this.companyLocation.controls['name'].setValue(this.location_item.name);


 
    
    // $('#location').focus();
}

/* changAutoComplete($event?) {

  if ($event && $event.target.checked === true) {
    this.getLocationPlaceAutocomplete();
    this.check_location = true;
    this.currentLat = this.location_item.latitude;
    this.currentLng = this.location_item.longitude;
    if (this.currentLat && this.currentLng) {
  
      let currentCoords = {
        lat: this.currentLat,
        lng: this.currentLng
      };
      this.notifyCurrentMap(currentCoords);
    }
    
  } else {
    this.check_location = false;

    google.maps.event.clearInstanceListeners(this.googlelocationplace.nativeElement);
  }
} */

ngAfterViewInit() {
  setTimeout(() => {
    this.getLocationPlaceAutocomplete();
    this.check_location = true;
    this.currentLat = this.location_item.latitude;
    this.currentLng = this.location_item.longitude;
    if (this.currentLat && this.currentLng) {
  
      let currentCoords = {
        lat: this.currentLat,
        lng: this.currentLng
      };
      // this.notifyCurrentMap(currentCoords);
    }
  }, 1);
 
  
}

ngOnInit() {
  this.setcompanyLanguage();
  this.initializeFormData();

  // this.initializePlaceData();
  //   this.companyLocation.controls['location'].valueChanges.takeUntil(this.ngUnsubscribe).subscribe(
  //     () => {
  //       if (this.currentLocation.latitude === null && this.currentLocation.longitude === null) {
  //         this.companyLocation.controls['location'].setErrors({'incorrect': true});

  //       } else {
  //         this.companyLocation.controls['location'].setErrors(null);

  //       }
  //     }
  //   );
  
  this.companyLocation.controls['company_id'].setValue(this.companyId);
 /*  this.companyLocationService.getCompanyName(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
    (res) => {

      this.companyLocation.controls['name'].setValue(res['company_name']);
    }); */
}

submit() {
  let sendLocation;
 
  if (this.companyLocation.valid) {
    if ($('#check-verify').prop("checked") == true) {
      this.companyLocation.controls['verified_by_google'].setValue(true);
    } else {
      this.companyLocation.controls['verified_by_google'].setValue(false);
    }
    sendLocation = this.companyLocation.value;
    sendLocation.location_id = this.location_item.id;

    this.companyLocationService.editCompanyLocationData(sendLocation).subscribe(res => {

      this.closeModalPopup.emit({'new': res['data'], 'old': this.location_item});
      $('#locationModal').modal('hide');
   });
  }
}


}
