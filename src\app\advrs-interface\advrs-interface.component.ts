/// <reference types="@types/googlemaps" />
import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { HttpClient } from "@angular/common/http";
import { Options, LabelType } from "ng5-slider";
import { SelectItem, MessageService, Message, } from "primeng/api";
import { AdvrsViewService } from "app/general/services/advrs-view.service";
import { FormBuilder, FormControl, NgForm, FormGroup } from "@angular/forms";
// import { OverlayPanel } from "primeng/overlaypanel";
import { Subject, BehaviorSubject } from "rxjs";
import { DataMap } from "shared/Models/data_map";
import { MapToFormService } from "app/user/cv-services/map-to-form.service";
import { Place } from "shared/Models/place";
import { Router, ActivatedRoute } from "@angular/router";
import { PostJobService } from "app/company/services/post-job.service";
import { ImageProcessingService } from "shared/shared-services/image-processing.service";

import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from 'app/general/services/general.service';
import { SearchJobContactsService } from '../general/services/search-job-contacts.service';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
import { Paginator } from 'primeng/paginator';

declare var $: any;

@Component({
  selector: 'app-advrs-interface',
  templateUrl: './advrs-interface.component.html',
  styleUrls: ['./advrs-interface.component.scss'],
  providers: [MessageService]
})

export class AdvrsInterfaceComponent implements OnInit {
  firstLoad: boolean = true;
  currentLangId:number;
  skillsDD:any;
  year_of_exp: any;
  displayBasic2: boolean;
  displayBasic1: boolean;
  clearAll: boolean = false;

  choosen_country;
  job_Title_from_user_topBar = { 'id': '', 'name': '' };
  // currentCoords_from_user_top_Bar: { lat: number; lng: number; };
  display: boolean = false;
  AdvrId: any;
  currentCoords: { lat: number; lng: number; };
  send_filter_data;
  standardCol: { field: string; header: string; data: string; style: string }[];
  global_col: { field: string; header: string; data: string; style: string, selected: boolean }[];

  tableCols: any;
  published_dates: any[] = [];
  image_uploaded_url: any[] = [];
  image_code_url: any[] = [];
  jobTitleForm;
  data_map = new DataMap();

  location_init: boolean = false;
  private ngUnsubscribe: Subject<any> = new Subject();

  need_data = false;
  data_type: any[][] = [[]];

  data_Source = [];
  public page_number: number = 1;
  totalRecords;
  rows = 100;
  cols: any[];
  role;
 
  _selectedColumns: any[];
  selectedColumnsWithoutStandardCols: any[];
  colsReadyPassToModal = false;
  sendColumns: any[] = [];
  filterColumns: any[] = [];
  selected_filters: { filter_name: string; filter_value: any; filter_label: string }[];
  selected_filters_backup = [];
  selected_three_filters: { filter_name: string; filter_value: any; form_control_name: string }[];
  minValue2: number = 1000;
  minValue: number = 18;
  maxValue2: number = 80000;
  maxValue: number = 80;
  driving_license = this.data_map.driving_license;
  options: Options = {
    floor: 18,
    ceil: 80,
    step: 1,
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return '' + value;
        case LabelType.High:
          return '' + value;
        default:
          return '' + value;
      }
    }
  };
  options2: Options = {
    floor: 1000,
    ceil: 80000,
    step: 100,
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return '' + value;
        case LabelType.High:
          return '' + value;
        default:
          return '' + value;
      }
    }
  };
  types2: SelectItem[];
  gender: SelectItem[];
  data: any;
  activeIndex: number = 0;
  firstName: string;
  lastName: string;
  address: string;
  msgs: Message[] = [];
  filtersForm;
  filtersFormLocation;
  ExpField = [];
  emp_type = [];
  com_industries = [];
  driving_licen = [];
  nationality = [];
  langs = [];
  skills = [];
  degreeLevel = [];
  filteredJobTitle = [];
  education_fields = [];
  jobTitles = [];
  @ViewChild('googlelocationplaceCountry') public googlelocationplaceCountryRef: any;
  @ViewChild('googlelocationplaceCity') public googlelocationplaceCityRef: any;
  @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: any;
  currentSub: BehaviorSubject<any> = new BehaviorSubject('');
  currentType: string = 'currentLocation';
  currentLocation: Place = new Place();
  public remoteFields1: Object = { text: 'name', value: 'id' };
  public remoteFields2: Object = { text: 'name', value: 'international_language_id' };
  public remoteFields3: Object = { text: 'name', value: 'nationality_id' };
  distanceTool = [
    { 'value': '', 'label': '' },
    { 'value': 'km', 'label': 'KM' },
    { 'value': 'mi', 'label': 'Miles' }
  ];
  apply_show: boolean;
  multi_Select_names;

  // ngModel control names
  Experience_Field;
  Employment_Types;
  Company_Industries;
  Nationality;
  Languages;
  Skills;
  Driving_License = '';
  Degree_Level = '';
  Age = [18, 80];
  Salary = [1000, 80000];
  Gender;
  certification;
  company_name;
  educations;
  Year_of_experience;
  Verified_Companies = true;
  remote = false;
  applyWaiting = false;

  test_filter_code = `<p-dropdown [options]="skills" (onChange)="dt.filter($event.value, 'skill', 'equals')" styleClass="ui-column-filter"
  [filter]="true" placeholder="" [showClear]="false">
  </p-dropdown>`
  clear_all_boolean;

  allFiltersVisibility = false;
  locationFilterVisibility = false;
  isMobileLayout = false;
  isMobileData = false;
  //     final object to send to backend
  appliedFilters = {};

  //       used to clear filters in all filters interface
  allFiltersControls = [
    'Experience_Field', 'Company_Industries', 'Salary', 'certification', 'company_name', 'Driving_License',
    'Age', 'Gender', 'Nationality', 'Languages', 'Degree_Level', 'educations'
  ]
  //      used to clear all filters + additional filters
  allAllFiltersControls = [
    'Experience_Field', 'Company_Industries', 'Salary', 'certification', 'company_name', 'Driving_License',
    'Age', 'Gender', 'Nationality', 'Languages', 'Degree_Level', 'educations',
    'job_title', 'country', 'Employment_Types', 'Skills', 'Year_of_experience', 'Verified_Companies', 'remote'
  ]
  pageTitle = 'Jobs';
  metaTitle = '';
  metaDescription = '';
  currentDate = (new Date().getMonth() + 1).toString() + '-' + new Date().getFullYear().toString();
  // we add to the array every control name of the controls in all filters interface that have been changed
  // so if the user click cancel we can reset selected_filters array and controls in this interface to the
  //previous state before choosing values and cancel from the interface
  currentChangedControlsAllFilter = [];
  // showModal = false;
  currentAdvrInfo;
  showLoader: boolean;
  actionAfterLogin = null;
  countriesFromDB = [];

  @ViewChild('p', {static: false}) paginator: Paginator;
  changingPaginationProgrammatically = false;

  //to initialize objects of lazy dropdown to use get original value function
  jobTitleDD:any;
  countriesDD:any;
  expFieldDD:any;

  constructor(private http: HttpClient,
    private advrsView: AdvrsViewService,
    private messageService: MessageService,
    private fb: FormBuilder,
  //  private mapToFormService: MapToFormService,
    private postJobService: PostJobService,
    private imageProcessingService: ImageProcessingService,
    private router: Router,
    private route: ActivatedRoute,
    private title: Title,
    private meta:Meta,
    private generalService: GeneralService,
    private searchJobContactsService: SearchJobContactsService,
    private lazyloadDropdownService:LazyloadDropdownService
  ) {

  }
  ngOnInit(): void {
     //get default language
    if (localStorage.getItem('defaultLang')) {
      this.currentLangId = +localStorage.getItem('defaultLang');
    } else {
      this.currentLangId = 1;
    }

    this.driving_license = this.driving_license.slice(1);
    this.showLoader = true;
    this.initFormsAndTypes();
    this.initAppliedFilters();
    this.initSelectedFilters();

    //////////  get Activated params
    let searchtype = this.route.snapshot.params['search-type'];
    let searchquery = this.route.snapshot.params['search-query'];
    ///
    ////
    let country = '';
    let jobTitle = '';
    let searchQueryArray = [];
    ///////

    let getOriginal: {paramName:string , paramValue:any} = {paramName:"get_original" , paramValue:1}; 

    // if url is  job title-jobs-country
    if (searchtype === 'p') {
      if (searchquery !== undefined) {
        if (searchquery.includes('-jobs-')) {
          searchQueryArray = searchquery.split('-jobs-');
          country = searchQueryArray[1];
          if(country !==undefined)
            country = this.replaceDashWithSpace(country);
    
          else
            country = '';

        } else {
          searchQueryArray.push(searchquery);
        }
        jobTitle = searchQueryArray[0];
        jobTitle = this.replaceDashWithSpace(jobTitle);
      }

      // here we should get original value of job title & country after getting it from url
      // because job title & country values in url are modified to be url fiendly
      if (jobTitle !== '' && country !== '') {
        
        this.jobTitleDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_titles',1,this.currentLangId,getOriginal);
        this.countriesDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'countries',1,this.currentLangId,getOriginal);
        this.jobTitleDD.getOriginalValue(jobTitle).switchMap(res1 => {
          if(res1['data'].length > 0)
            jobTitle = res1['data'][0].name;

          this.job_Title_from_user_topBar.name = jobTitle;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'] = jobTitle;
          this.appliedFilters['job_title'] = jobTitle;
          
          return this.countriesDD.getOriginalValue(country);
        }).subscribe(res2=>{
          if(res2['data'].length > 0)
            country = res2['data'][0].name;

          this.choosen_country = country;
          this.ControlsCountryCity.controls['country'].setValue(country);
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = country;
          this.appliedFilters['country'] = country;
  
          this.pageTitle = this.job_Title_from_user_topBar.name + ' jobs in ' + this.choosen_country;
          this.metaDescription = this.pageTitle + ' ' + this.currentDate +' | Find the latest '+ this.job_Title_from_user_topBar.name + ' job vacancies and employment opportunities in '+ this.choosen_country;

          this.getSearchQueryResults();
        });

      }
      else if (jobTitle !== '') {
        this.jobTitleDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_titles',1,this.currentLangId,getOriginal);
        this.jobTitleDD.getOriginalValue(jobTitle).subscribe(res=>{
          if(res['data'].length > 0)
            jobTitle = res['data'][0].name;

          this.job_Title_from_user_topBar.name = jobTitle;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'] = jobTitle;
          this.appliedFilters['job_title'] = jobTitle;

          this.pageTitle = this.job_Title_from_user_topBar.name + ' jobs';
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate +' | Find the latest '+ this.job_Title_from_user_topBar.name + ' job vacancies and employment opportunities';

          this.getSearchQueryResults();
        });
      }
      else if (country !== '') {
        this.countriesDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'countries',1,this.currentLangId,getOriginal);
        this.countriesDD.getOriginalValue(country).subscribe(res=>{
          if(res['data'].length > 0)
            country = res['data'][0].name;

          this.choosen_country = country;
          this.ControlsCountryCity.controls['country'].setValue(country);
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = country;
          this.appliedFilters['country'] = country;

          this.pageTitle = 'Jobs in ' + this.choosen_country;
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate + ' | Find the latest job vacancies and employment opportunities in '+this.choosen_country;

          this.getSearchQueryResults();
        });
        
      } else {
        this.pageTitle = 'Jobs';
        this.getSearchQueryResults();
      }
    }

// if url is  experience field-jobs-country
    let field = '';
    if (searchtype === 'f') {
      if (searchquery !== undefined) {
        if (searchquery.includes('-jobs-')) {
          searchQueryArray = searchquery.split('-jobs-');
          country = searchQueryArray[1];
          country = this.replaceDashWithSpace(country);
          this.choosen_country = country;
        } else {
          searchQueryArray.push(searchquery);
        }
        field = searchQueryArray[0];
        field = this.replaceDashWithSpace(field);
      }


      if (field !== '' && country !== '') {
        this.expFieldDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'exp_fields',1,this.currentLangId,getOriginal);
        this.countriesDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'countries',1,this.currentLangId,getOriginal);
        this.expFieldDD.getOriginalValue(field).switchMap(res1=>{
          if(res1['data'].length > 0)
            field = res1['data'][0].name;
         
          let ExpFieldObject = res1['data'];
          if(ExpFieldObject.length !== 0){
            this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'Experience_Field')]['filter_value'] = ExpFieldObject;
            this.Experience_Field = ExpFieldObject;
            this.appliedFilters['exp_field'] = this.convertToIdsArr(this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'Experience_Field')]['filter_value']);
          }
          return this.countriesDD.getOriginalValue(country);
        }).subscribe(res2=>{       
          if(res2['data'].length > 0)  
            country = res2['data'][0].name;
      
          this.choosen_country = country;
          this.ControlsCountryCity.controls['country'].setValue(country);
          this.appliedFilters['country'] = country;

          this.pageTitle = field + ' jobs in ' + this.choosen_country;
          this.metaDescription = this.pageTitle + ' ' + this.currentDate  + ' | Find the latest '+ field + ' job vacancies and employment opportunities in '+ this.choosen_country;

          this.getSearchQueryResults();
        });
      }
      else if (field !== '') {
        this.expFieldDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'exp_fields',1,this.currentLangId,getOriginal);
        
        this.expFieldDD.getOriginalValue(field).subscribe(res=>{
          if(res['data'].length > 0)
            field = res['data'][0].name;
         
          let ExpFieldObject = res['data'];
          if(ExpFieldObject.length !== 0){
            this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'Experience_Field')]['filter_value'] = ExpFieldObject;
            this.Experience_Field = ExpFieldObject;
            this.appliedFilters['exp_field'] = this.convertToIdsArr(this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'Experience_Field')]['filter_value']);
          }

          this.pageTitle = field + ' jobs';
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate + ' | Find the latest '+ field + ' job vacancies and employment opportunities';

          this.getSearchQueryResults();
        });
      }
      else if (country !== '') {
        this.countriesDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'countries',1,this.currentLangId,getOriginal);

        this.countriesDD.getOriginalValue(country).subscribe(res=>{
          if(res['data'].length > 0)
            country = res['data'][0].name;
      
          this.choosen_country = country;
          this.ControlsCountryCity.controls['country'].setValue(country);
          this.appliedFilters['country'] = country;

          this.pageTitle = 'Jobs in ' + this.choosen_country;
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate + ' | Find the latest job vacancies and employment opportunities in '+this.choosen_country;
          
          this.getSearchQueryResults();
        });  
      }
      else {
        this.pageTitle = 'Jobs';
        this.getSearchQueryResults();
      }
  
    }
    // For example /////////////) ["web", "jobs", "syria"]
    // 0: "web"
    // 1: "jobs"
    // 2: "syria"
    ///
    /////
    //////////
    
    if (searchtype !== 'f' && searchtype !== 'p'){
      this.pageTitle = 'Jobs';
      this.getSearchQueryResults();
    }

    this.checkIfMobile();

    this.check_Selected_filters_for_option_clearAll();
    if (localStorage.getItem("role")) {
      this.role = localStorage.getItem("role");
    }

  //  this.initializePlaceData();
    this.tableCols = this.global_col;
    this.tableCols.splice(0, 3);

    // if(this.job_Title_from_user_topBar.name !== ''){
    //   let jobTitleParam =  { paramName: 'job_title', paramValue: this.job_Title_from_user_topBar.name};
    //   this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,this.currentLangId,jobTitleParam);
    // }
    // else{
     
      this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,this.currentLangId);
   // }
   
   // this.getSearchQueryResults();

    //  this.FiltersFormData_getAdvrs(null, null,this.appliedFilters);

    this.generalService.internalMessage.subscribe((data) => {
      // case when unauth user want to apply for job , it open login modal ,
      //login then it applies automatically for the same job
      if (data['src'] === 'login' && this.actionAfterLogin !==null) {
        if (data['message'] === 'loginSuccess' && this.actionAfterLogin === 'apply' && this.currentAdvrInfo.advId!==undefined) {
          $('#authModal').modal('hide');
          this.role = localStorage.getItem('role');
          this.apply_for_job(this.currentAdvrInfo.advId, this.currentAdvrInfo.company_name, this.currentAdvrInfo.job_title);
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
        }
        else if (data['message'] === 'loginSuccess' && this.actionAfterLogin === 'applyOnCompanySite' && this.currentAdvrInfo.url!==undefined) {
          $('#authModal').modal('hide');
          this.role = localStorage.getItem('role');
          this.apply_on_company_site(this.currentAdvrInfo.url);
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
        }
        else if (data['message'] === 'loginSuccess' && this.actionAfterLogin === 'changeCols') {
          $('#authModal').modal('hide');
          this.role = localStorage.getItem('role');
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
          this.displayColModal();
        }
      }
      
    });

    // show message for expired adv 
    //Note: for some reason this interface isn't getting a notify from generalService in interceptor.ts 
    //so we used localstorage here to get the notification from interceptor.ts
    // if(localStorage.getItem("expAdvMsg")){
    //   let msg = localStorage.getItem("expAdvMsg")
    //   setTimeout(() => {
    //     this.messageService.add({
    //       severity: "warn",
    //       detail: msg,
    //       life:12000
    //     });
    //   }, 3000);
    //   localStorage.removeItem('expAdvMsg');
    // }
    
    this.searchJobContactsService.sharedMessage.subscribe((data) => {
      
      if (data['message'] === 'inJobSearch') {
        let para = data['mData'].queryParams;
        this.job_Title_from_user_topBar = { 'id': '', 'name': '' };
        if (para.jtn && para.cnt) {
          if (para.jti) {
            this.job_Title_from_user_topBar.id = para.jti;
          }
        //  para.jtn = this.replaceDashWithSpace(para.jtn); 
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'] = para.jtn;
          this.appliedFilters['job_title'] = para.jtn;

          this.job_Title_from_user_topBar.name = para.jtn;
         
        // check if country changed set the new country and clear old city value
        if(this.ControlsCountryCity.controls['country'].value !== para.cnt ){
          this.ControlsCountryCity.controls['country'].setValue(para.cnt);
          this.choosen_country = para.cnt;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = para.cnt;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = "";
          this.appliedFilters['country'] = para.cnt;
          this.appliedFilters['city'] = "";
          this.ControlsCountryCity.controls['city'].setValue("");
          
          this.removeNg5SliderFilterValue('remote');
          // this.currentCoords_from_user_top_Bar = {
          //   lat: Number(para.lat),
          //   lng: Number(para.lng)
          // };
        }
          
          this.clearAll = true;

          this.pageTitle = para.jtn + ' jobs in ' + this.ControlsCountryCity.controls['country'].value;
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate + ' | Find the latest '+ para.jtn + ' job vacancies and employment opportunities in '+ this.ControlsCountryCity.controls['country'].value;
          // currently they don't want relation between job title and skills,so send haveData=0
          //old business
          // havedata = 2  means send skills that corresponde to job title
          //previously it was send skills and experience field data that corresponde to job title
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters, 0);
        }
        else if (para.jtn) {
          if (para.jti) {
            this.job_Title_from_user_topBar.id = para.jti;
          }
        //  para.jtn = this.replaceDashWithSpace(para.jtn); 
          this.job_Title_from_user_topBar.name = para.jtn;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'] = para.jtn;
          this.appliedFilters['job_title'] = para.jtn;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';
          this.appliedFilters['country'] = '';
          this.clearAll = true;

          this.pageTitle = para.jtn + ' jobs';
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate + ' | Find the latest '+ para.jtn + ' job vacancies and employment opportunities';
          // currently they don't want relation between job title and skills,so send haveData=0
          //old business
          //havedata = 2  means send skills and experience field data that corresponde to job title
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters, 0);
        }
        else if (para.cnt) {
          this.choosen_country = para.cnt;
          // this.currentCoords_from_user_top_Bar = {
          //   lat: Number(para.lat),
          //   lng: Number(para.lng)
          // };
          this.ControlsCountryCity.controls['country'].setValue(para.cnt);
          this.ControlsCountryCity.controls['city'].setValue("");
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = para.cnt;
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = "";
          this.appliedFilters['country'] = para.cnt;
          this.appliedFilters['city'] = "";
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'] = '';
          this.appliedFilters['job_title'] = '';
          this.clearAll = true;

          this.pageTitle = 'Jobs in ' + this.choosen_country;
          this.metaDescription = this.pageTitle +  ' ' + this.currentDate + ' | Find the latest job vacancies and employment opportunities in '+this.choosen_country;

          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
          
          this.removeNg5SliderFilterValue('remote');
        }
        else {
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'] = '';
          this.appliedFilters['job_title'] = '';
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';
          this.appliedFilters['country'] = '';
          this.appliedFilters['city'] = "";

          this.pageTitle = 'Jobs';
          // currently they don't want relation between job title and skills,so send haveData=0
           //in old business we sent haveData=2
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters, 0);
        }
      }

      if(this.pageTitle === 'Jobs'){
        this.metaTitle = 'CVeek | Best Jobs in the world 2024';
      }
      else{
        this.metaTitle = 'CVeek 2024 | ' + this.pageTitle;
      }
      this.title.setTitle(this.metaTitle);
      this.meta.updateTag({ property: 'og:title', content: this.metaTitle });

      if(this.pageTitle === 'Jobs'){
        this.meta.updateTag({ name: 'description', content: 'CVeek | Find Best jobs in world ' + this.currentDate  +', online Jobs with high Salary.  Also Best Jobs in Qatar, Gulf, Middle East, around world and best Jobs for futures' });
        this.meta.updateTag({ property: 'og:description', content: 'CVeek | Find Best jobs in world ' + this.currentDate  +', online Jobs with high Salary.  Also Best Jobs in Qatar, Gulf, Middle East, around world and best Jobs for futures' });
      }
      else{
        this.meta.updateTag({ name: 'description', content: this.metaDescription });
        this.meta.updateTag({ property: 'og:description', content: this.metaDescription });
      }
    });


    if(this.pageTitle === 'Jobs'){
      this.metaTitle = 'CVeek | Best Jobs in the world 2024';
    }
    else{
      this.metaTitle = 'CVeek 2024 | ' + this.pageTitle;
    }
    this.title.setTitle(this.metaTitle);
    this.meta.updateTag({ property: 'og:title', content: this.metaTitle });

    if(this.pageTitle === 'Jobs'){
      this.meta.updateTag({ name: 'description', content: 'CVeek | Find Best jobs in world '+ this.currentDate  +', online Jobs with high Salary.  Also Best Jobs in Qatar, Gulf, Middle East, around world and best Jobs for futures' });
      this.meta.updateTag({ property: 'og:description', content: 'CVeek | Find Best jobs in world '+ this.currentDate  +', online Jobs with high Salary.  Also Best Jobs in Qatar, Gulf, Middle East, around world and best Jobs for futures' });
    }
    else{
      this.meta.updateTag({ name: 'description', content: this.metaDescription });
      this.meta.updateTag({ property: 'og:description', content: this.metaDescription });
    }

    // recieve message from advr preview tab to update it's state
    const bc = new BroadcastChannel('searchJob-advrPreview');
    bc.onmessage = (ev) => {
      if (ev.data == "loginAndNotApplied") {
        if (localStorage.getItem("role")) {
          this.role = localStorage.getItem("role");
        }
      }
      else if (ev.data == "loginAndApplied") {
        if (localStorage.getItem("role")) {
          this.role = localStorage.getItem("role");
        }
        this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
      }
    };

  }


////////////////////////////////////////////
  initFormsAndTypes() {
   
    this.filtersFormLocation = this.fb.group({
      location_radio: ['country-city'],
      country_city: this.fb.group({
        city: [''],
        country: [''],
        latitude: [''],
        longitude: ['']
      }),

      location: this.fb.group({
        distance: [''],
        unit_distance: [''],
        latitude: [''],
        longitude: [''],
        location: [''],
        city: [''],
        country: ['']
      })

    });

    this.types2 = [
      { label: 'Any', value: 'Any' },
      { label: 'Male', value: 'male' },
      { label: 'Female', value: 'female' },
    ];

    this.gender = this.types2;
    this.multi_Select_names = ['emp_type', 'exp_field', 'com_industries', 'nationality', 'langs', 'skills']

    this.standardCol = [
      { field: 'company_logo', header: 'Logo', data: 'company_logo', style: 'logo' },
      { field: 'company_name', header: 'Company Name', data: 'company_name', style: 'name' },
      { field: 'job_title', header: 'Advertisement Title', data: 'job_title', style: 'test' },
    //  { field: 'publish_date', header: 'Publish Date', data: 'publish_date', style: 't' }
      { field: 'publish_data', header: 'Date', data: 'publish_date', style: 't' }
    ];

    this.global_col = [
      { field: 'company_logo', header: 'Logo', data: 'company_logo', style: '', selected: false },
      { field: 'company_name', header: 'Name', data: 'company_name', style: '', selected: false },
      { field: 'job_title', header: 'Advertisement Title', data: 'job_title', style: '', selected: false },
      { field: 'year_of_exp', header: 'Experience Years', data: 'year_of_exp', style: '', selected: false },
    //  { field: 'employment_type', header: 'Employment Type', data: 'employment_type', style: '', selected: false },
      { field: 'emp_type', header: 'Job Type', data: 'emp_type', style: '', selected: false },
      { field: 'salary', header: 'Salary', data: 'salary', style: '', selected: false },
      { field: 'country', header: 'Country', data: 'country', style: '', selected: false },
      { field: 'city', header: 'City', data: 'city', style: '', selected: false },
      { field: 'company_location', header: 'Location', data: 'company_location', style: '', selected: false },
      { field: 'degree_level', header: 'Degree Level', data: 'degree_level', style: '', selected: false },
      { field: 'educations', header: 'Education', data: 'educations', style: '', selected: false },
      // { field: 'minor', header: 'Minor', data: 'minor', style: '', selected: false },
      { field: 'certification', header: 'Certification', data: 'certification', style: '', selected: false },
      { field: 'skill', header: 'Skills', data: 'skill', style: 'skills', selected: false },
      { field: 'languages', header: 'Languages', data: 'languages', style: 'languages', selected: false },
      { field: 'nationality', header: 'Nationality', data: 'nationality', style: 'nationality', selected: false },
      { field: 'gender', header: 'Gender', data: 'gender', style: '', selected: false },
      { field: 'age', header: 'Age', data: 'age', style: '', selected: false },
      { field: 'driving_license', header: 'Driving License', data: 'driving_license', style: '', selected: false },
      { field: 'exp_field', header: 'Experience Field', data: 'exp_field', style: '', selected: false },
      { field: 'company_industry', header: 'Company Industry', data: 'company_industry', style: 'industry', selected: false }
    ];

  }
  showBasicDialog2() {
    this.displayBasic2 = true;
  }
  showBasicDialog1() {
    this.displayBasic1 = true;
  }

  private getLocationPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete_country = new google.maps.places.Autocomplete(this.googlelocationplaceCountryRef.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']  // 'name'
      });

    autocomplete_country.addListener('place_changed', (res) => {
      let place_country = autocomplete_country.getPlace();
      this.getLocationAddress(place_country);
    });

    const autocomplete_city = new google.maps.places.Autocomplete(this.googlelocationplaceCityRef.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']
      });

    autocomplete_city.addListener('place_changed', (res) => {
      const place_city = autocomplete_city.getPlace();
      this.getLocationAddress(place_city);
    });
    this.getLocationPlacefromLocationAutoComplete();
  }

  private getLocationPlacefromLocationAutoComplete() {
    const autocomplete_location = new google.maps.places.Autocomplete(this.googlelocationplaceLocationRef.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']
      });

    autocomplete_location.addListener('place_changed', (res) => {
      const place_location = autocomplete_location.getPlace();
      this.getLocationAddressInfo(place_location);
    });

    this.location_init = true;
  }

// to call in country & city autocomlete
  getLocationAddress(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place);
    this.ControlsCountryCity.controls['country'].setValue(data_location.country);
    this.ControlsCountryCity.controls['city'].setValue(data_location.city);
    this.ControlsCountryCity.controls['latitude'].setValue(data_location.latitude);
    this.ControlsCountryCity.controls['longitude'].setValue(data_location.longitude);

    this.clearAll = true;
    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = data_location.country;
    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = data_location.city;
  }

// to call in location autocomlete
  getLocationAddressInfo(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place);
    this.currentCoords = {
      lat: data_location.latitude,
      lng: data_location.longitude
    };

    this.ControlsLocation.controls['country'].setValue(data_location.country);
    this.ControlsLocation.controls['city'].setValue(data_location.city);
    this.ControlsLocation.controls['location'].setValue(data_location.country + ', ' + data_location.city);
    this.ControlsLocation.controls['latitude'].setValue(data_location.latitude);
    this.ControlsLocation.controls['longitude'].setValue(data_location.longitude);
    
    this.setSelectedLocation();
  }

  // notifyCurrentMap(val) {
  //   this.currentSub.next(val);
  // }

  open_location_filter() {
    // if (this.currentCoords_from_user_top_Bar) {
    //   this.notifyCurrentMap(this.currentCoords_from_user_top_Bar)
    // }

    setTimeout(() => {
      this.getLocationPlaceAutocomplete();
    }, 1);

  }

  // initializePlaceData() {
  //   this.mapToFormService.personalMapData.skip(1).takeUntil(this.ngUnsubscribe).subscribe(
  //     (place: Place) => {
  //       this.currentLocation = place;
  //       this.setCurrentLocationControls(this.currentLocation);
  //     }
  //   );
  // }

  // setCurrentLocationControls(place: Place) {
  //   this.currentCoords = {
  //     lat: place.latitude,
  //     lng: place.longitude
  //   };
  //   if (place.full == 'Dragging') {
  //     this.ControlsLocation.controls['country'].setValue(place.country);
  //     this.ControlsLocation.controls['city'].setValue(place.city);
  //     this.ControlsLocation.controls['location'].setValue(place.country + ', ' + place.city);
  //     this.ControlsLocation.controls['latitude'].setValue(place.latitude);
  //     this.ControlsLocation.controls['longitude'].setValue(place.longitude);
  //   } else {
  //     this.ControlsLocation.controls['country'].setValue(place.country);
  //     this.ControlsLocation.controls['city'].setValue(place.city);
  //     this.ControlsLocation.controls['location'].setValue(place.country + ', ' + place.city);
  //     this.ControlsLocation.controls['latitude'].setValue(place.latitude);
  //     this.ControlsLocation.controls['longitude'].setValue(place.longitude);
  //   }
  //   this.setSelectedLocation();
  // }

  // if country city checked then disable location section and vice versa
  // the parameter passed to this function is the section we want to disable its controls
  disableOtherSectionOfLocation(section) {
    if (section === 'country-city') {
      this.ControlsCountryCity.controls['country'].setValue("");
      this.ControlsCountryCity.controls['city'].setValue("");

      this.ControlsCountryCity.controls['country'].disable();
      this.ControlsCountryCity.controls['city'].disable();

      this.ControlsLocation.controls['location'].enable();
      this.ControlsLocation.controls['distance'].enable();
      this.ControlsLocation.controls['unit_distance'].enable();
    }
    else {
      this.ControlsLocation.controls['location'].setValue("");
      this.ControlsLocation.controls['latitude'].setValue("");
      this.ControlsLocation.controls['longitude'].setValue("");
      this.ControlsLocation.controls['distance'].setValue("");
      this.ControlsLocation.controls['unit_distance'].setValue({ 'value': '', 'label': '' });

      this.ControlsLocation.controls['location'].disable();
      this.ControlsLocation.controls['distance'].disable();
      this.ControlsLocation.controls['unit_distance'].disable();

      this.ControlsCountryCity.controls['country'].enable();
      this.ControlsCountryCity.controls['city'].enable();
    }
  }

  get ControlsCountryCity() {
    return (this.filtersFormLocation.controls['country_city'] as FormGroup);
  }

  get ControlsLocation() {
    return (this.filtersFormLocation.controls['location'] as FormGroup);
  }

  //used for adv publish date label
  calculateDiff(sentDate) {
    var date1: any = new Date(sentDate);
    var current_Date: any = new Date();
    var diffDays: any = Math.floor((current_Date - date1) / (1000 * 60 * 60 * 24));
    var backgroundColor

    if (diffDays <= 4) {
      backgroundColor = 'new'
    } else if (diffDays >= 5 && diffDays <= 10) {
      backgroundColor = 'medium'
    } else if (diffDays >= 11) {
      backgroundColor = 'long'
    }
    return { diffDays, backgroundColor };
  }


  //apply for job on company's site
  apply_on_company_site(url){
    this.stopPropagation(event);
    if (this.role !== 'unauth') {
      window.open(url, '_blank');
    }
    else{
      this.actionAfterLogin = 'applyOnCompanySite';
      this.currentAdvrInfo = { 'url': url };
      $('#authModal').modal('toggle');
    }
  }

  //used to apply for a job on our site
  apply_for_job(advId, company_name, job_title) {
    this.applyWaiting = true;
    this.stopPropagation(event);
    let job_index;
    if (this.role !== 'unauth') {
      this.advrsView.applyForJob(advId).subscribe(
        (res) => {
          if (res['error']) {
            this.messageService.add({ severity: 'error', summary: 'Apply Job', detail: res['error'] });
          } else {
            let successMessage = '';
            if (company_name) {
              successMessage = 'Applied Successfully to ' + job_title + ' at ' + company_name;
            }
            else {
              successMessage = 'Applied Successfully to ' + job_title;
            }
            this.messageService.add({ severity: 'success', summary: 'Apply Job', detail: successMessage });
            job_index = this.data_Source.findIndex(x => x.job_adv_id === res['data']['adv_id'])
            this.data_Source[job_index].applied = true;
          }

          this.applyWaiting = false;
        }
      );
    } else {
      this.actionAfterLogin = 'apply';
      this.currentAdvrInfo = { 'advId': advId, 'company_name': company_name, 'job_title': job_title };
      $('#authModal').modal('toggle');
    }

  }

  displayColModal() {
    if(this.actionAfterLogin === 'changeCols'){
      setTimeout(() => {
        $('#ColsManagementModal').modal('toggle');
      }, 1000);
      this.actionAfterLogin = '';
    }
    else{
      $('#ColsManagementModal').modal('toggle');
    }
  }

  changeAdvId(Advid, AdvLang, source) {
    this.postJobService.changeAdvId_lang(Advid, AdvLang, source);
  }

  stopPropagation($event) {
    $event.stopPropagation();
  }
  displayAdvrModal(job_adv_id, slug) {
    this.AdvrId = job_adv_id
    this.changeAdvId(job_adv_id, 1, 'search-job');
    //// job_title + '-in-' + job_company + '-' + job_country = slug : ie now slug composite on front-end
    const url = this.router.serializeUrl(this.router.createUrlTree(['/jobs',
      job_adv_id, slug]));
    window.open(url, '_blank');
    //this.router.navigate(['/post', job_adv_id , job_title + '-in-' + job_company + '-' + job_country ]);
    //$('#AdvrsModal').modal('toggle');
  }

  navigateToCompanyProfile(companyUsername){
    const url = this.router.serializeUrl(this.router.createUrlTree(['/i/c', companyUsername]));
    window.open(url, '_blank');
  }
  handlePopup($event) {

    this.isMobileData = false;
    this.selectedColumns($event['new'])
  }

  display2($event) {
    if (this.apply_show) {
      this.apply_show = $event.target.hidden
    } else {
      this.apply_show = true;
    }
  }


  getSearchQueryResults() {
    if (this.job_Title_from_user_topBar.name !== '' && this.choosen_country) {
      this.FiltersFormData_getAdvrs(this.job_Title_from_user_topBar, this.choosen_country);
    } else if (this.job_Title_from_user_topBar.name !== '') {

      this.FiltersFormData_getAdvrs(this.job_Title_from_user_topBar, null);
    }
    // else if (this.choosen_country && this.appliedFilters['exp_field'].length > 0) {
    //   this.FiltersFormData_getAdvrs(null, this.choosen_country);
    // }
    // else if (this.choosen_country && this.appliedFilters['exp_field_name'] === '') {
    //   this.FiltersFormData_getAdvrs(null, this.choosen_country);
    // }
    else {
      this.FiltersFormData_getAdvrs(null, null, this.appliedFilters, 1);
    }
  }

  searchKey(keyword) {

  }

  FiltersFormData_getAdvrs(job_title?, country?, new_filters_values?, havedata?) {
    this.showLoader = true;
    let send_filter_data_job_coor_country: any = { 'job_title': '', 'country': '' }
    // let send_filter_data_new_filters_data;
    // let filter_Data_to_send;
    let haveData = 1;
    this.send_filter_data;
    send_filter_data_job_coor_country;

    if (job_title || country) {
      if (job_title) {
        send_filter_data_job_coor_country.job_title = job_title.name;
        //  this.job_title_search = this.job_Title_from_user_topBar;
      }
      if (country) {
        send_filter_data_job_coor_country.country = country;
      }
      //  filter_Data_to_send = send_filter_data_job_coor_country;
      this.send_filter_data = send_filter_data_job_coor_country;
    }
    else if (new_filters_values) {
      if (havedata) {
        haveData = havedata;
      }
      else {
        haveData = 0;
      }

      this.send_filter_data = new_filters_values;

      // if (this.send_filter_data.driving) {
      //   this.send_filter_data.driving = this.send_filter_data.driving.value;
      // }

    } else {
      this.send_filter_data = [];
      if (havedata) {
        haveData = havedata;
      }
      else {
        haveData = 1;
      }
    }

    //this.advrsView.getFilterData(this.rows, this.page_number, this.currentLangId, this.send_filter_data, haveData, this.isMobileData, []).subscribe
    this.advrsView.getFilterData(this.rows, 1, this.currentLangId, this.send_filter_data, haveData, this.isMobileData, []).subscribe(
      (res) => {

        this.showLoader = false;
        
        // Here we call want to get data with applied filters, so we need to change PageNumber to 1 to get the first page of result
        // if we don't change pageNumber to 1 it will call api with pageNumber = 2 or 3 or .. 
        // we need to display results from the first
        if(this.page_number !==1 ){
          this.page_number = 1;
          this.changingPaginationProgrammatically = true;
          this.paginator.changePage(0);
         //  setTimeout(() => this.paginator.changePage(0));
        }
          
        if(this.firstLoad === true)
          this.firstLoad = false;

        this.data_Source = [];
        this.published_dates = [];
        this.send_filter_data = [];

        if (haveData === 1) {
          this.editGlobalColSelected(res['columns']);
        }

        this._selectedColumns = this.standardCol;
        this._selectedColumns = [...this.standardCol,
        ...this.global_col.filter(col => res['columns'].includes(col.field))];

        this.totalRecords = res['jobAdvs']['total'];
        this.data_type[this.page_number] = [];

        if (res['jobAdvs']['data']) {
          for (let i = 0; i < res['jobAdvs']['data'].length; i++) {
            this.data_type[this.page_number][i] = res['jobAdvs']['data'][i];
          }

          for (let i = 0; i < this.data_type[this.page_number].length; i++) {
            this.data_Source[i] = this.data_type[this.page_number][i];          
          }
        }

        // haveData = 1 means : if it is the first load of this inerface so get all dropdown , multiselect options
        // from the server and bind those options to form controls multi select , else send haveData = 0 to backend
        // so backend don't send these data
        if (haveData === 1) {
          this.com_industries = res['filters_data']['industries'];
          this.ExpField = res['filters_data']['exp_field'];
          this.emp_type = res['filters_data']['emp_type'];
          this.nationality = res['filters_data']['nationalities'];
          this.langs = res['filters_data']['languages'];
          this.year_of_exp = res['filters_data']['years_of_exps'];
          this.jobTitles = res['filters_data']['job_titles'];
          this.degreeLevel = res['filters_data']['degree_level'];
          this.education_fields = res['filters_data']['educations'];
 
          if (this.appliedFilters['country'] !== '') {
            this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = this.ControlsCountryCity.value.country;
            this.searchJobContactsService.notify('countryChanged', 'advr-interface', 'jobSreach-form-navBar', { 'countryChanged': true, 'countryName': this.ControlsCountryCity.value.country });
          }
        }
        // haveData = 2  means send skills that corresponde to job title
        //previously it was send skills and experience field data that corresponde to job title
        else if (haveData === 2 ) {
          let jobTitleParam =  { paramName: 'job_title', paramValue: this.appliedFilters['job_title']};
          this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,this.currentLangId,jobTitleParam);
        //     this.skills = res['filters_data']['skill_types'];
        //  this.ExpField = res['filters_data']['exp_field'];
        }
        // haveData = 3  means send skills and job title that corresponde to exp field
        // else if (haveData === 3) {
       //   this.skills = res['filters_data']['skill_types'];
        //   this.jobTitles = res['filters_data']['job_titles'];
        // }
      });
  }

  editGlobalColSelected(resColumns) {
    for (let col of this.global_col) {
      if (resColumns.includes(col.field)) {
        col.selected = true;
      }
    }
    this.selectedColumnsWithoutStandardCols = this.global_col.filter(col => resColumns.includes(col.field));
    this.colsReadyPassToModal = true;
  }

  // checkDates() {
  //   this.image_code_url = []
  //   this.published_dates = []
  //   for (let i = 0; i < this.data_type[this.page_number].length; i++) {

  //     this.data_Source[i] = this.data_type[this.page_number][i];
  //     if (this.data_Source[i].company_logo) {
  //       this.image_uploaded_url[i] = this.get_img.getImage(this.data_Source[i].company_logo, 'storage/company/logo', 'opt_50/');
  //     } else {
  //       this.image_uploaded_url[i] = './assets/images/Confidential-icon.png';

  //     }
  //     this.image_code_url[i] = './assets/images/CountryCode/' + this.data_Source[i].country_code + '.gif';
  //     this.published_dates.push({
  //       'color': this.calculateDiff(this.data_Source[i].publish_data).backgroundColor,
  //       'date': this.calculateDiff(this.data_Source[i].publish_data).diffDays
  //     });
  //   }
  // }

  getImageLogo(logo,companyName, opportunityFor) {
    if(logo && companyName && opportunityFor === 'other_company')
      return this.imageProcessingService.getImagePath ('otherEmployerLogo','small_thumbnail',logo);
    else if(logo && companyName && opportunityFor === 'my_company')
      return this.imageProcessingService.getImagePath ('companyLogo','small_thumbnail',logo);
    else if(logo ===null && companyName===null)
      return './assets/images/Confidential-icon.png';
    else
      return '';
  }
  getFlagImage(code) {
    return './assets/images/CountryCode/' + code + '.gif';
  }

  Job_Title(event) {
    let query = event.query;
    this.filteredJobTitle = [];
    this.filteredJobTitle = this.filterJobTitle(query, this.jobTitles);
  }

  filterJobTitle(query, titles: any[]) {
    let filtered: any[] = [];
    for (let i = 0; i < titles.length; i++) {
      if (titles[i] !== undefined) {
        let filter_title = titles[i];

        if (filter_title.name.toLowerCase().indexOf(query.toLowerCase()) == 0) {
          filtered.push(filter_title);
        }
      }

    }

    return filtered;
  }

  selectedColumns(val: any[]) {
    //restore original order
    let Cols = this._selectedColumns;
    this._selectedColumns = [];
    this._selectedColumns = this.standardCol;
    this._selectedColumns = [...this.standardCol,
    ...this.global_col.filter(col => val.includes(col.field))]
    this.sendColumns = [];
    let not_found

    for (let i = 0; i < this._selectedColumns.length; i++) {
      this.sendColumns[i] = this._selectedColumns[i].field
    }


    if (this.sendColumns.length > 0) {
      for (let i = 0; i < this.sendColumns.length && not_found; i++) {
        not_found = Cols.some(ob => ob.field === this._selectedColumns[i].field);
      }

      if (not_found === undefined) {
        this.need_data = true;
      } else {
        this.need_data = false;
      }

      this.getData(this.rows, this.page_number, this.sendColumns);
    }


  }

  paginate(event) {
    
    //event.first = Index of the first record
    //event.rows = Number of rows to display in new page
    //event.page = Index of the new page
    //event.pageCount = Total number of pages
  
    //we put a condition here because when changing the page manually (in ts code without click event on paginator) --in applying filters--
    //that cause triggering "onPageChange" event on paginator then it calls this method and getData from backend again
    if(this.changingPaginationProgrammatically === false){
      this.data_Source = [];
      this.page_number = event.page + 1;
      this.getData(event.rows, event.page + 1, this.sendColumns);
    }
    else{
      this.changingPaginationProgrammatically = false;
    }

  }

  init_data_type(res, page_number) {
    for (let i = 0; i < res['jobAdvs']['data'].length; i++) {
      this.data_type[page_number][i] = res['jobAdvs']['data'][i];
    }

    for (let i = 0; i < this.data_type[page_number].length; i++) {
      this.data_Source[i] = this.data_type[page_number][i];
    }
  }

  getData(row_number, page_number, stat) {
    this.showLoader = true;

    this.advrsView.getFilterData(row_number, page_number, this.currentLangId, this.appliedFilters, 0, this.isMobileData, stat).subscribe(
      (res) => {
        this.showLoader = false;
        this.totalRecords = res['jobAdvs']['total']
        this.data_type[page_number] = [];

        this.init_data_type(res, page_number);
      });

    //this code is commented because it is causing issues
    //we can't save pages and display them lately because user may apply different filters
    // so result change and we should always get result from backend

    // if (this.data_type[page_number] === undefined && this.need_data) {
    //   this.data_type = [[]];
    //   this.need_data = false;

    //   this.advrsView.getFilterData(row_number, page_number, this.currentLangId, this.appliedFilters, 0, this.isMobileData, stat).subscribe(
    //     (res) => {
    //       this.showLoader = false;
    //       this.data_type[page_number] = [];
    //       this.totalRecords = res['jobAdvs']['total']
    //       this.init_data_type(res, page_number);
    //     });
    // } else if (this.data_type[page_number] === undefined && this.need_data === false) {
    //   this.advrsView.getFilterData(row_number, page_number, this.currentLangId, this.appliedFilters, 0, this.isMobileData, stat).subscribe(
    //     (res) => {
    //       this.showLoader = false;
    //       this.totalRecords = res['jobAdvs']['total']
    //       this.data_type[page_number] = [];

    //       this.init_data_type(res, page_number);
    //     });

    // } else if (this.data_type[page_number] != undefined && this.need_data === true) {
    //   this.data_type = [[]];
    //   this.need_data = false;
    //   this.advrsView.getFilterData(row_number, page_number, this.currentLangId, this.appliedFilters, 0, this.isMobileData, stat).subscribe(
    //     (res) => {
    //       this.showLoader = false;
    //       this.totalRecords = res['jobAdvs']['total']
    //       this.data_type[page_number] = [];
    //       this.init_data_type(res, page_number);
    //     });
    // } else if (this.data_type[page_number] != undefined && this.need_data === false) {
    //   for (let i = 0; i < this.data_type[page_number].length; i++) {
    //     this.data_Source[i] = this.data_type[page_number][i];
    //   }
    // }

  }

  col_settings() {
    if (this.role === 'unauth') {
      this.actionAfterLogin = 'changeCols';
      $('#authModal').modal('toggle');
      // window.location.href = "/m/user/login";
    }
    else {
      this.displayColModal();
    }
  }

  chosenFilterValues($event, ngModel_FormControl_name) {
    let filter_index
    this.clearAll = true;

    filter_index = this.selected_filters.findIndex(x => x.filter_name === ngModel_FormControl_name);
    this.selected_filters[filter_index]['filter_value'] = this[ngModel_FormControl_name];
    
    //Verified_Companies filter is commented temporarily
    if (ngModel_FormControl_name === 'Employment_Types' ||
      ngModel_FormControl_name === 'Skills' ||
      ngModel_FormControl_name === 'Year_of_experience'  ||
      ngModel_FormControl_name === 'remote' ||
      ngModel_FormControl_name === 'Verified_Companies'
      ) {

      if (ngModel_FormControl_name === 'Employment_Types') {
        this.appliedFilters['emp_type'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      else if (ngModel_FormControl_name === 'Skills') {
        this.appliedFilters['skills'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      else if (ngModel_FormControl_name === 'Year_of_experience') {
        this.appliedFilters['year_exp_id'] = this.selected_filters[filter_index]['filter_value'].id;
      }
      else if (ngModel_FormControl_name === 'remote' ) {
        this.appliedFilters['remote'] = this.selected_filters[filter_index]['filter_value'];
        this.removeNg5SliderFilterValue('country');
        this.removeNg5SliderFilterValue('location');
      }
      else if (ngModel_FormControl_name === 'Verified_Companies') {
        this.appliedFilters['verified_company'] = this.selected_filters[filter_index]['filter_value'];
      }
      this.check_Selected_filters_for_option_clearAll();
      this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
    }
    else {
      if (this.allFiltersVisibility === true) {
        this.currentChangedControlsAllFilter.push(ngModel_FormControl_name);
      }
    }

  }

  setSelectedLocation() {
    this.clearAll = true;
    if (this.ControlsLocation['controls'].location.value !== '' &&
      this.ControlsLocation['controls'].distance.value !== '' &&
      this.ControlsLocation['controls'].unit_distance.value.value !== '') {
      let locationTagContent = this.ControlsLocation['controls'].location.value + ',distance:' + this.ControlsLocation['controls'].distance.value + this.ControlsLocation['controls'].unit_distance.value.label;
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = locationTagContent;
    }
    else {
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = '';
    }
   
    this.check_Selected_filters_for_option_clearAll();
  }

  convertToIdsArr(arrayOfObj) {
    let idsArray = [];
    for (let obj of arrayOfObj) {
      idsArray.push(obj.id);
    }
    return idsArray;
  }

  removeFilterValue(ngModel_FormControl_name, index) {
    let filter_index;
  
    filter_index = this.selected_filters.findIndex(x => x.filter_name === ngModel_FormControl_name);

    this.selected_filters[filter_index]['filter_value'].splice(index, 1);
    
    if (this.selected_filters[filter_index]['filter_value'].length === 0) {
      this[ngModel_FormControl_name] = '';
    }
    else {
      this[ngModel_FormControl_name] = '';
      this[ngModel_FormControl_name] = this.selected_filters[filter_index]['filter_value'];
      if(ngModel_FormControl_name === 'Skills'){
        let remainingSkills = [];
        for (let skill of this.selected_filters[filter_index]['filter_value']){
          remainingSkills.push({"name":skill.name});
        }
        this[ngModel_FormControl_name] = remainingSkills;
      }
    }

    if (ngModel_FormControl_name === 'Employment_Types' ||
      ngModel_FormControl_name === 'Skills') {
      if (ngModel_FormControl_name === 'Employment_Types') {
        //  this.filtersForm.controls['emp_type'].setValue(this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']));
        this.appliedFilters['emp_type'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      else if (ngModel_FormControl_name === 'Skills') {
        //this.filtersForm.controls['skills'].setValue(this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']));
        this.appliedFilters['skills'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      //  this.FiltersFormData_getAdvrs(null, null, this.filtersForm.value);
      this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
      this.check_Selected_filters_for_option_clearAll();
    }
    else {
      if (ngModel_FormControl_name === 'Experience_Field') {
        this.resetPageTitleOnRemove('Experience_Field');
        this.appliedFilters['exp_field'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
        //i used this type of assignment for multiselect controls because when i used this type
        // this[ngModel_FormControl_name] = this.selected_filters[filter_index]['filter_value'];
        //it caused issue when clearing some values in multiselect it still displayed as selected in the placeholder
        //so this method solved the issue
        this[ngModel_FormControl_name] = [...this.selected_filters[filter_index]['filter_value']];
      }
      else if (ngModel_FormControl_name === 'Company_Industries') {
        this[ngModel_FormControl_name] = [...this.selected_filters[filter_index]['filter_value']];
        this.appliedFilters['com_industries'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      else if (ngModel_FormControl_name === 'Nationality') {
        this[ngModel_FormControl_name] = [...this.selected_filters[filter_index]['filter_value']];
        this.appliedFilters['nationality'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      else if (ngModel_FormControl_name === 'Languages') {
        this[ngModel_FormControl_name] = [...this.selected_filters[filter_index]['filter_value']];
        this.appliedFilters['langs'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
      else if (ngModel_FormControl_name === 'educations') {
        this[ngModel_FormControl_name] = [...this.selected_filters[filter_index]['filter_value']];
        this.appliedFilters['educations'] = this.convertToIdsArr(this.selected_filters[filter_index]['filter_value']);
      }
    
      this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
      this.check_Selected_filters_for_option_clearAll();
    }
  }

  removeDropdownFilterValue(ngModel_FormControl_name) {
    let filter_index
    filter_index = this.selected_filters.findIndex(x => x.filter_name === ngModel_FormControl_name)
    this.selected_filters[filter_index]['filter_value'] = ''
    this[ngModel_FormControl_name] = ''

    if (ngModel_FormControl_name === 'Degree_Level') {
      this.appliedFilters['degree_level_id'] = null;
    }
    else if (ngModel_FormControl_name === 'Year_of_experience') {
      this.appliedFilters['year_exp_id'] = null;
    }
    else if (ngModel_FormControl_name === 'Driving_License') {
      this.appliedFilters['driving'] = '';
    }

    this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
    this.check_Selected_filters_for_option_clearAll();
  }

  removeNg5SliderFilterValue(ngModel_FormControl_name) {
    let filter_index;
    // this.checkThreeFiltersValues();
    filter_index = this.selected_filters.findIndex(x => x.filter_name === ngModel_FormControl_name);
    if (ngModel_FormControl_name === 'Age') {
      this.selected_filters[filter_index]['filter_value'] = '';
      this.appliedFilters['age_from'] = null;
      this.appliedFilters['age_to'] = null;
      this[ngModel_FormControl_name] = [18, 80];
    } else if (ngModel_FormControl_name === 'Salary') {
      this.selected_filters[filter_index]['filter_value'] = '';
      this.appliedFilters['salary_from'] = null;
      this.appliedFilters['salary_to'] = null;
      this[ngModel_FormControl_name] = [1000, 80000];
    } else {
      this.selected_filters[filter_index]['filter_value'] = '';
      this[ngModel_FormControl_name] = '';
      if (ngModel_FormControl_name === 'company_name') {
        this.appliedFilters['company_name'] = '';
      }
      else if (ngModel_FormControl_name === 'certification') {
        this.appliedFilters['certification'] = '';
      }
      else if (ngModel_FormControl_name === 'Gender') {
        this.appliedFilters['gender'] = '';
      }
      else if (ngModel_FormControl_name === 'remote') {
        this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'remote')]['filter_value'] = false;
        this.appliedFilters['remote'] = false;
      }
      else if (ngModel_FormControl_name === 'Verified_Companies') {
        this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'Verified_Companies')]['filter_value'] = false;
        this.appliedFilters['verified_company'] = false;
      }
       
      else if (ngModel_FormControl_name === 'job_title') {
        this[ngModel_FormControl_name] = null;
        this.appliedFilters['job_title'] = '';
        this.searchJobContactsService.notify('jobTitleRemoved', 'advr-interface', 'jobSreach-form-navBar', { 'jobTitleRemoved': true });
        this.resetPageTitleOnRemove('job_title');
      }
      else if (ngModel_FormControl_name === 'country') {
        this.appliedFilters['country'] = '';
        this.appliedFilters['city'] = '';
        this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';
        this.ControlsCountryCity.controls['country'].setValue("");
        this.ControlsCountryCity.controls['city'].setValue("");
        this.searchJobContactsService.notify('countryRemoved', 'advr-interface', 'jobSreach-form-navBar', { 'countryRemoved': true });
        this.resetPageTitleOnRemove('country');
      }
      else if (ngModel_FormControl_name === 'city') {
        this.appliedFilters['city'] = '';
        this.ControlsCountryCity.controls['city'].setValue("");
      }
      else if (ngModel_FormControl_name === 'location') {
        this.appliedFilters['location'] = '';
        this.appliedFilters['longitude'] = '';
        this.appliedFilters['latitude'] = '';
        this.appliedFilters['distance'] = '';
        this.appliedFilters['unit_distance'] = '';

        this.ControlsLocation.controls['location'].setValue("");
        this.ControlsLocation.controls['latitude'].setValue("");
        this.ControlsLocation.controls['longitude'].setValue("");
        this.ControlsLocation.controls['distance'].setValue("");
        this.ControlsLocation.controls['unit_distance'].setValue({ 'value': '', 'label': '' });
      }

      //  this.filtersForm.controls[ngModel_FormControl_name].setValue('');
    }

   // currently they don't want relation between job title and skills,so send haveData=0
   //in old business we sent haveData=2
    // if (ngModel_FormControl_name === 'job_title') {
    //   this.FiltersFormData_getAdvrs(null, null, this.appliedFilters, 2);
    // }
    // else {
    //   this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
    // }
    this.FiltersFormData_getAdvrs(null, null, this.appliedFilters, 0);

    this.check_Selected_filters_for_option_clearAll();
  }

  check_Selected_filters_for_option_clearAll() {
    //clear_all_boolean  variable is not used
    this.clear_all_boolean = null;
    this.clear_all_boolean = this.selected_filters.some(data => data.filter_value != '');
    this.clearAll = this.selected_filters.some(data => data.filter_value != '');
  }


  // this function is called inside all filters p-dialog and location filter p-dialog
  submit(type) {
    this.allFiltersVisibility = false;

    if (type) {
      //  this.checkThreeFiltersValues();
      if (type === "formFilters") {

        for (let filter of this.selected_filters) {
          if (filter['filter_name'] === 'Experience_Field' && filter['filter_value'] !== '') {
            this.appliedFilters['exp_field'] = this.convertToIdsArr(filter['filter_value']);
          }
          else if (filter['filter_name'] === 'Company_Industries' && filter['filter_value'] !== '') {
            this.appliedFilters['com_industries'] = this.convertToIdsArr(filter['filter_value']);
          }
          else if (filter['filter_name'] === 'Salary' && filter['filter_value'] !== '') {
            this.appliedFilters['salary_from'] = filter['filter_value'][0];
            this.appliedFilters['salary_to'] = filter['filter_value'][1];
          }
          // && filter['filter_value'] !== ''
          // commented this condition so if user cleared certification or company name value inside the form
          // so we can clear the value also from appliedFilters
          else if (filter['filter_name'] === 'certification') {
            this.appliedFilters['certification'] = filter['filter_value'];
          }
          else if (filter['filter_name'] === 'company_name') {
            this.appliedFilters['company_name'] = filter['filter_value'];
          }
          else if (filter['filter_name'] === 'Driving_License' && filter['filter_value'] !== '') {
            this.appliedFilters['driving'] = filter['filter_value'].value;
          }
          else if (filter['filter_name'] === 'Degree_Level' && filter['filter_value'] !== '') {
            this.appliedFilters['degree_level_id'] = filter['filter_value'].id;
          }
          else if (filter['filter_name'] === 'Age' && filter['filter_value'] !== '') {
            this.appliedFilters['age_from'] = filter['filter_value'][0];
            this.appliedFilters['age_to'] = filter['filter_value'][1];
          }
          else if (filter['filter_name'] === 'Gender' && filter['filter_value'] !== '') {
            this.appliedFilters['gender'] = filter['filter_value'];
          }
          else if (filter['filter_name'] === 'Nationality' && filter['filter_value'] !== '') {
            this.appliedFilters['nationality'] = this.convertToIdsArr(filter['filter_value']);
          }
          else if (filter['filter_name'] === 'Languages' && filter['filter_value'] !== '') {
            this.appliedFilters['langs'] = this.convertToIdsArr(filter['filter_value']);
          }
          else if (filter['filter_name'] === 'educations' && filter['filter_value'] !== '') {
            this.appliedFilters['educations'] = this.convertToIdsArr(filter['filter_value']);        
          }
          
        }

        //  send_filter_data_all = JSON.parse(JSON.stringify(this.filtersForm.value));
        this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
        this.currentChangedControlsAllFilter = [];
      }

      else if (type === "formLocationFilters") {
        if (this.filtersFormLocation['controls'].location_radio.value === 'country-city') {
          if (this.ControlsCountryCity.value.country !== '') {
            this.appliedFilters['country'] = this.ControlsCountryCity.value.country;
            this.appliedFilters['city'] = this.ControlsCountryCity.value.city;
            this.searchJobContactsService.notify('countryChanged', 'advr-interface', 'jobSreach-form-navBar', { 'countryChanged': true, 'countryName': this.ControlsCountryCity.value.country });

            //in case the user didn't choose country from autocomplete values, it wont appear in filter tags
            // so it's better to fix it by assign the value to selected filters array
            if(this.ControlsCountryCity.value.country !== this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value']){
              this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = this.ControlsCountryCity.value.country;
            }

            if (this.ControlsCountryCity.value.city === '') {
              this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';
            }
            //in case the user didn't choose city from autocomplete values, it wont appear in filter tags
            // so it's better to fix it by assign the value to selected filters array
            else if(this.ControlsCountryCity.value.city !== this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value']){
              this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = this.ControlsCountryCity.value.city;
            }

            this.resetPageTitleOnCountryChange(this.ControlsCountryCity.value.country);

          }
          // in case the country-city radio checked but the user cleared the fields and click applied
          else {
            this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';
            this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';

            this.appliedFilters['country'] = "";
            this.appliedFilters['city'] = "";

            this.searchJobContactsService.notify('countryRemoved', 'advr-interface', 'jobSreach-form-navBar', { 'countryRemoved': true });
            this.resetPageTitleOnRemove('country');
          }

          //if country-city choosed , then clear location tags and applied location if exists
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = '';

          this.appliedFilters['location'] = "";
          this.appliedFilters['longitude'] = "";
          this.appliedFilters['latitude'] = "";
          this.appliedFilters['distance'] = "";
          this.appliedFilters['unit_distance'] = "";

          this.location_init = false;
          //  form.submitted = false;
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
          this.locationFilterVisibility = false;
        }
        else if (this.filtersFormLocation['controls'].location_radio.value === 'location') {
          if (this.ControlsLocation.value.location !== '') {
            this.appliedFilters['location'] = this.ControlsLocation.value.location;
            this.appliedFilters['longitude'] = this.ControlsLocation.value.longitude;
            this.appliedFilters['latitude'] = this.ControlsLocation.value.latitude;
            this.appliedFilters['distance'] = this.ControlsLocation.value.distance;
            this.appliedFilters['unit_distance'] = this.ControlsLocation.value.unit_distance.value;

            // console.log("this.ControlsLocation.value.country",this.ControlsLocation.value.country);
            // this.searchJobContactsService.notify('countryChanged', 'advr-interface', 'jobSreach-form-navBar', { 'countryChanged': true, 'countryName': this.ControlsLocation.value.country });
            // this.resetPageTitleOnCountryChange(this.ControlsLocation.value.country);

          }
          else {
            this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = '';
          }
          //if location choosed , then clear country city tags and applied country city if exists
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';
          this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';

          this.appliedFilters['country'] = "";
          this.appliedFilters['city'] = "";

          this.location_init = false;
          //  form.submitted = false;
          this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
          this.locationFilterVisibility = false;
        }

        if(this.appliedFilters['country'] !== '' ||  this.appliedFilters['city'] !== '' ||  this.appliedFilters['location'] !== ''){
          this.removeNg5SliderFilterValue('remote');
        }
      }
      
    }
  }

  initSelectedFilters() {
    this.selected_filters = [
      { filter_name: 'job_title', filter_value: '', filter_label: 'Job Title' },

      { filter_name: 'company_name', filter_value: '', filter_label: 'Name' },
      { filter_name: 'Company_Industries', filter_value: '', filter_label: 'Industry' },
      { filter_name: 'Experience_Field', filter_value: '', filter_label: 'Experience Field' },
      { filter_name: 'Employment_Types', filter_value: '', filter_label: 'Employment Types' },

      { filter_name: 'Salary', filter_value: '', filter_label: 'Salary' },
      //   { filter_name: 'Academic_Degree', filter_value: '', filter_label: 'Academic Degree' },
      { filter_name: 'educations', filter_value: '', filter_label: 'Educations' },
      // { filter_name: 'Minor', filter_value: '', filter_label: 'Minor' },
      { filter_name: 'certification', filter_value: '', filter_label: 'certification' },

      { filter_name: 'Driving_License', filter_value: '', filter_label: 'Driving License' },
      { filter_name: 'Degree_Level', filter_value: '', filter_label: 'Degree Level' },
      { filter_name: 'Age', filter_value: '', filter_label: 'Age' },
      { filter_name: 'Gender', filter_value: '', filter_label: 'Gender' },
      { filter_name: 'Nationality', filter_value: '', filter_label: 'Nationality' },
      { filter_name: 'Languages', filter_value: '', filter_label: 'Languages' },
      { filter_name: 'Skills', filter_value: '', filter_label: 'Skills' },
      { filter_name: 'Year_of_experience', filter_value: '', filter_label: 'Year of experience' },

      { filter_name: 'country', filter_value: '', filter_label: 'Country' },
      { filter_name: 'city', filter_value: '', filter_label: 'City' },
      { filter_name: 'location', filter_value: '', filter_label: 'Location' },
      { filter_name: 'remote', filter_value: false, filter_label: 'Remote' },
      { filter_name: 'Verified_Companies', filter_value: false, filter_label: 'Verified Companies' }
      // { filter_name: 'Verified_Companies', filter_value: true, filter_label: 'Verified Companies' }
    ];
  }
  //process data to be ready to send to backend
  initAppliedFilters() {
    this.appliedFilters = {
      'job_title': '',
      'exp_field_name': '',
      'emp_type': [],
      'skills': [],
      'year_exp_id': null,
      'exp_field': [],
      'com_industries': [],
      'salary_from': null,
      'salary_to': null,
      'certification': '',
      'company_name': '',
      'driving': '',
      'age_from': null,
      'age_to': null,
      'gender': '',
      'nationality': [],
      'langs': [],
      'degree_level_id': '',
      'educations': [],
      'country': '',
      'city': '',
      'longitude': '',
      'latitude': '',
      'distance': '',
      'unit_distance': '',
      'location': '',
      'remote':false,
      'verified_company' :false
      //'verified_company' :true
    }

  }

  // reset ngModel Variable
  clearNgModelVarFilters(typeOfFilters) {
    for (let filter of this.selected_filters) {
      if (typeOfFilters.indexOf(filter['filter_name']) > -1) {
        if (filter['filter_name'] === 'Age') {
          this.Age = [18, 80];
        }
        else if (filter['filter_name'] === 'Salary') {
          this.Salary = [1000, 80000];
        }
        else this[filter['filter_name']] = '';
      }
    }
  }

  // reset selected filters in All Filters interface only
  resetSelectedFiltersAllFilters(typeOfFilters) {
    for (let filter of this.selected_filters) {
      if (typeOfFilters.indexOf(filter['filter_name']) > -1) {
        filter['filter_value'] = '';
      }
    }

  }

  // reset Applied filters in All Filters interface only
  resetAppliedFiltersAllFilters() {
    this.appliedFilters['exp_field'] = [];
    this.appliedFilters['com_industries'] = [];
    this.appliedFilters['salary_from'] = null;
    this.appliedFilters['salary_to'] = null;
    this.appliedFilters['certification'] = '';
    this.appliedFilters['company_name'] = '';
    this.appliedFilters['driving'] = '';
    this.appliedFilters['age_from'] = null;
    this.appliedFilters['age_to'] = null;
    this.appliedFilters['gender'] = '';
    this.appliedFilters['nationality'] = [];
    this.appliedFilters['langs'] = [];
    this.appliedFilters['educations'] = [];
    this.appliedFilters['degree_level_id'] = null;
  }

  clear_all_filter() {
    //  this.displayBasic2 = false;
    this.clearAll = false;
    // this.filtersForm.reset({ salary: [1000, 80000], age: [18, 80] });
    //   this.selected_filters = [];
    this.initSelectedFilters();
    this.initAppliedFilters();
   
    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'remote')]['filter_value'] = false;
    this.appliedFilters['remote'] = false;
    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'Verified_Companies')]['filter_value'] = false;
    this.appliedFilters['verified_company'] = false;
    
    this.clearNgModelVarFilters(this.allAllFiltersControls);
    this.clearLocationFilter();
    this.searchJobContactsService.notify('jobTitleRemoved', 'advr-interface', 'jobSreach-form-navBar', { 'jobTitleRemoved': true });
    this.searchJobContactsService.notify('countryRemoved', 'advr-interface', 'jobSreach-form-navBar', { 'countryRemoved': true });
    // currently they don't want relation between job title and skills,so send haveData=0
    //in old business we sent haveData=2
    this.FiltersFormData_getAdvrs(null, null, null, 0);
    this.check_Selected_filters_for_option_clearAll();
    this.currentChangedControlsAllFilter = [];
    //to reset page main heading and page title when filters cleared
    this.resetPageTitleOnRemove('both');
  }

  // to call in all filters interface and clear the filters
  clear_all_filters_interface() {
    //  this.displayBasic2 = false;
    this.allFiltersVisibility = false;
    this.clearAll = false;
    //  this.filtersForm.reset({ salary: [1000, 80000], age: [18, 80] });

    //  this.initSelectedFilters();
    this.resetSelectedFiltersAllFilters(this.allFiltersControls);

    //should reset applied filters in the same way , array with names of all filter like : driving , company_name
    this.resetAppliedFiltersAllFilters();

    this.clearNgModelVarFilters(this.allFiltersControls);
    this.FiltersFormData_getAdvrs(null, null, this.appliedFilters);
    this.check_Selected_filters_for_option_clearAll();

    this.currentChangedControlsAllFilter = [];
    //to reset page main heading and page title when filters cleared
    this.resetPageTitleOnRemove('Experience_Field');
  }

  // function revert all changes made without click apply
  cancelAllFilters() {
    let unique = this.currentChangedControlsAllFilter.filter(function (elem, index, self) {
      return index === self.indexOf(elem);
    })
    this.currentChangedControlsAllFilter = unique;

    for (let filter of this.selected_filters) {
      if (this.currentChangedControlsAllFilter.indexOf(filter['filter_name']) > -1) {
        let backupValue = this.selected_filters_backup[this.selected_filters_backup.findIndex(x => x.filter_name === filter['filter_name'])]['filter_value'];

        // set backup value to selected filters array
        filter['filter_value'] = backupValue;

        // set backup value to the variable bound with ngModel
        if (filter['filter_name'] === 'Age') {
          if(filter['filter_value']=== "")
            this.Age = [18, 80];
          else   
            this.Age = [backupValue[0], backupValue[1]];
        }
        else if (filter['filter_name'] === 'Salary') {
          this.Salary = [backupValue[0], backupValue[1]];
        }
        else this[filter['filter_name']] = backupValue;
      }
    }

    // this.resetSelectedFiltersAllFilters(this.currentChangedControlsAllFilter);
    // this.clearNgModelVarFilters(this.currentChangedControlsAllFilter);
    this.allFiltersVisibility = false;
    this.selected_filters_backup = [];
  }

  cancelLocationFilter() {
    // if cancel the filter then reset all values to the state before opening the filter
    this.locationFilterVisibility = false;
    if (this.appliedFilters['country'] !== '') {
      this.filtersFormLocation['controls'].location_radio.setValue('country-city');
      this.ControlsCountryCity.controls['country'].setValue(this.appliedFilters['country']);
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = this.appliedFilters['country'];

      if (this.appliedFilters['city'] === '') {
        this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';
      }

      if (this.appliedFilters['city'] !== '') {
        this.ControlsCountryCity.controls['city'].setValue(this.appliedFilters['city']);
        this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = this.appliedFilters['city'];
      }

      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = '';
    }
    else {
      //  this.filtersFormLocation['controls'].location_radio.setValue('country-city');
      this.ControlsCountryCity.controls['country'].setValue('');
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';

      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';
      this.ControlsCountryCity.controls['city'].setValue('');
    }

    if (this.appliedFilters['latitude'] !== '') {
      this.filtersFormLocation['controls'].location_radio.setValue('location');
      this.ControlsLocation.controls['location'].setValue(this.appliedFilters['location']);
      this.ControlsLocation.controls['latitude'].setValue(this.appliedFilters['latitude']);
      this.ControlsLocation.controls['longitude'].setValue(this.appliedFilters['longitude']);
      this.ControlsLocation.controls['distance'].setValue(this.appliedFilters['distance']);
      if (this.appliedFilters['unit_distance'] === 'km') {
        this.ControlsLocation.controls['unit_distance'].setValue({ 'value': 'km', 'label': 'KM' });
      }
      else if (this.appliedFilters['unit_distance'] === 'mi') {
        this.ControlsLocation.controls['unit_distance'].setValue({ 'value': 'mi', 'label': 'Miles' });
      }
      let locationTagContent = this.ControlsLocation['controls'].location.value + ',distance:' + this.ControlsLocation['controls'].distance.value + this.ControlsLocation['controls'].unit_distance.value.label;
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = locationTagContent;

      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';
    }
    else {
      //  this.filtersFormLocation['controls'].location_radio.setValue('location');
      this.ControlsLocation.controls['location'].setValue('');
      this.ControlsLocation.controls['latitude'].setValue('');
      this.ControlsLocation.controls['longitude'].setValue('');
      this.ControlsLocation.controls['distance'].setValue('');
      this.ControlsLocation.controls['unit_distance'].setValue({ 'value': '', 'label': '' });
      this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = '';
    }

  }

  clearLocationFilter() {
    this.ControlsCountryCity.controls['country'].setValue("");
    this.ControlsCountryCity.controls['city'].setValue("");
    this.ControlsLocation.controls['location'].setValue("");
    this.ControlsLocation.controls['latitude'].setValue("");
    this.ControlsLocation.controls['longitude'].setValue("");
    this.ControlsLocation.controls['distance'].setValue("");
    this.ControlsLocation.controls['unit_distance'].setValue({ 'value': '', 'label': '' });

    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'country')]['filter_value'] = '';
    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'city')]['filter_value'] = '';
    this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'location')]['filter_value'] = '';

    this.appliedFilters['location'] = "";
    this.appliedFilters['longitude'] = "";
    this.appliedFilters['latitude'] = "";
    this.appliedFilters['distance'] = "";
    this.appliedFilters['unit_distance'] = "";
    this.appliedFilters['country'] = "";
    this.appliedFilters['city'] = "";
  }

  showAllFiltersDialog() {
    this.allFiltersVisibility = true;
    this.selected_filters_backup = JSON.parse(JSON.stringify(this.selected_filters));
    //
  }
  showlocationFilterDialog() {
    let locationRadioChecked = this.filtersFormLocation['controls'].location_radio.value;
    if (locationRadioChecked === 'country-city') {
      this.disableOtherSectionOfLocation('location');
    }
    else this.disableOtherSectionOfLocation('country-city');
    this.locationFilterVisibility = true;
  }

  resetPageTitleOnRemove(field){
    let searchQueryArray=[] , firstTerm = '' , secondTerm='' , jobTitle='';
    jobTitle = this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'];

    if (this.pageTitle.toLowerCase().includes('jobs in') ) {
      searchQueryArray = this.pageTitle.toLowerCase().split('jobs in');
      firstTerm = searchQueryArray[0];
      secondTerm = searchQueryArray[1];
    }
    else if(this.pageTitle.toLowerCase().includes('jobs')){
      searchQueryArray = this.pageTitle.toLowerCase().split('jobs');
      firstTerm = searchQueryArray[0];
    }

    firstTerm = firstTerm.charAt(0).toUpperCase() + firstTerm.slice(1);
    if(secondTerm !== ''){
      if(secondTerm.charAt(0) === ' ')
        secondTerm = secondTerm.charAt(1).toUpperCase() + secondTerm.slice(2);
      else 
        secondTerm = secondTerm.charAt(0).toUpperCase() + secondTerm.slice(1);
    }

    // field to remove is experience field or job title
    if( (field === 'Experience_Field' && jobTitle === '') || field === 'job_title' ){
      if(secondTerm !== '')
        this.pageTitle = 'Jobs in ' + secondTerm;
      else 
        this.pageTitle = 'Jobs';
    }
    // field to remove is country
    else if (field === 'country'){
      if(firstTerm !== '')
        this.pageTitle = firstTerm + 'jobs';
      else 
        this.pageTitle = 'Jobs';
    }
    else if(field === 'both'){
      this.pageTitle = 'Jobs';
    }
      
    this.metaTitle = this.pageTitle + ' | CVeek';
    this.title.setTitle(this.metaTitle);
    this.meta.updateTag({ property: 'og:title', content: this.metaTitle });
  }

  resetPageTitleOnCountryChange(country){
    let searchQueryArray=[] , firstTerm = '' , secondTerm='' , jobTitle='';
    jobTitle = this.selected_filters[this.selected_filters.findIndex(x => x.filter_name === 'job_title')]['filter_value'];

    if (this.pageTitle.toLowerCase().includes('jobs in') ) {
      searchQueryArray = this.pageTitle.toLowerCase().split('jobs in');
      firstTerm = searchQueryArray[0];
      secondTerm = searchQueryArray[1];
    }
    else if(this.pageTitle.toLowerCase().includes('jobs')){
      searchQueryArray = this.pageTitle.toLowerCase().split('jobs');
      firstTerm = searchQueryArray[0];
    }

    firstTerm = firstTerm.charAt(0).toUpperCase() + firstTerm.slice(1);
    if(secondTerm !== ''){
      if(secondTerm.charAt(0) === ' ')
        secondTerm = secondTerm.charAt(1).toUpperCase() + secondTerm.slice(2);
      else 
        secondTerm = secondTerm.charAt(0).toUpperCase() + secondTerm.slice(1);
    }

    if(firstTerm !== '')
      this.pageTitle = firstTerm + 'jobs in ' + country;
    else 
      this.pageTitle = 'Jobs in ' + country;

      
    this.metaTitle = this.pageTitle + ' | CVeek';
    this.title.setTitle(this.metaTitle);
    this.meta.updateTag({ property: 'og:title', content: this.metaTitle });
  }


  friendlyUrl(text){
    text = text.toLowerCase();
    text = text.replace(/[^0-9a-zء-ي\s-]+/g, '');
    text = text.replace(/[\s]+/g, "-");
    return text;
  }

  replaceDashWithSpace(text){
    //replaceDashWithSpace
    text = text.replace(/-/g, ' ');
    //replace multiple spaces with single space
    text = text.replace(/\s\s+/g, ' ');
    return text;
  }
  replaceDashWithEmptyString(text){
    text = text.replace(/-/g, '');
    return text;
  }

  processText(text){
    let processedValue:string;
    processedValue = this.friendlyUrl(text);
    processedValue = this.replaceDashWithEmptyString(processedValue);
    return processedValue;
  }


  //responsive layout
  isMobileAgent() {
    const agent = navigator.userAgent || navigator.vendor || (window as any).opera;
    // tslint:disable-next-line:max-line-length
    return (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(agent) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(agent.substr(0, 4)));
  }

  checkIfMobile() {
    this.isMobileLayout = this.isMobileAgent();
    this.isMobileLayout = window.innerWidth <= 767;
    this.isMobileData = this.isMobileLayout;
    window.onresize = () => {
      this.isMobileLayout = window.innerWidth <= 767;
    };
  }

  ngOnDestroy(): void {
    this.searchJobContactsService.notify('countryRemoved', 'advr-interface', 'jobSreach-form-navBar', { 'countryRemoved': true });
  }
}



