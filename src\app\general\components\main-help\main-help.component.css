a {
  cursor: pointer;
}
.container-padding{
  padding-top:36px;
}
/* h2 {
  padding-top: 100px;
  padding-left: 10px;
} */
/* .container{
  padding-top: 50px !important;
} */

h2.company-heading {
  margin-top: 0px !important;
}

div.content-col.company-content-col {
  padding-top: 130px;
}

.badge.category-badge {
  padding: 5px 10px;
  margin-top: 12px;
  margin-bottom: 12px;
  color:#4876BA;
  /* color: #005b9f; */
  background-color: transparent;
  cursor: pointer;
  /* display:left block inline ; */
  text-align: left !important;
  white-space: normal;
  line-height: 1.3;
}
.badge.category-badge h2{
  font-size: 20px;
  margin: 0;
  font-weight: bold;
}
/* .badge{
  text-align: left !important;

} */

div, table, td,th,p,h2,form ,ul, li, ol, .breadcrumb {
  font-family: 'Exo2-Regular', sans-serif;
}

.container {
  width: 100%;
  height: 100%;
  /* background-color: #03253f; */
}

span.input-group-btn button.btn.btn-default {
  cursor: default;
  background-color: whitesmoke;
}

span.input-group-btn button.btn.btn-default:active,
 span.input-group-btn button.btn.btn-default:click,
 span.input-group-btn button.btn.btn-default:focus,
 span.input-group-btn button.btn.btn-default:hover  {
  cursor: default;
  background-color: whitesmoke;

}

span.languages button.btn {
  float: right;
  margin-top: 30px;;
}

.list-group-item>.badge.badge-primary.category-badge {
  float: left;

}
.maincat-title{
  font-size: 23px;
  color: #3D7BCE;
  margin-bottom: 22px;
}
ul.list-group.sub-group-list {
  margin-left: 15px;
}
ul.list-group.sub-group-list  .list-group-item {
  border: none !important;
  font-size: 18px !important;
}
ul.list-group.sub-group-list  .list-group-item h3 , ul.list-group.sub-group-list  .list-group-item h2{
  margin:0;
  font-size: 18px !important;
}
a {
  display: block;
}

a:hover {
 text-decoration: none;
}
a.show {
  padding-bottom: 10px;
}
.header {
  /* background:#efefef; */
  height: fit-content;
  padding-bottom: 25px;
  /* margin-top: -22px; */
}

div.lang-col {
  margin-top: -50px;
  padding-right: 15px;
}

.card {
  background-color: white;
  transition: all 0.6s ease-in-out;
}
.cat-col .card{
  padding:20px;
}
.main-group-list .card{
  padding:13px 0;
}
.content-row{
  margin-top: 10px;
}

.cat-col{
  /* padding-right: 10px;
  padding-left: 10px; */
  border-bottom :#efefef 3px solid;
}

.breadcrumb {
  font-size: 20px;
  padding: 15px 15px;
  border-bottom: 1px solid #ddd;
}

.fa.fa-home {
  font-size: 1.3em;
}
:host ::ng-deep p-progressSpinner svg circle {
  fill:transparent;
}
:host ::ng-deep .aw-wizard-navigation-bar ul.steps-indicator {
  color:red !important
}
/* ---------------------------------------------------------------------- */
/* Start Responsive  */
@media screen and (max-width: 850px ) {
  .nav-row {
    padding-top: 0px;
  }
  /* .header {
      margin-top: -62px;
  } */
}

@media screen and (min-width: 851px) and (max-width: 1014px ) {
  .nav-row {
    padding-top: 15px;
  }
  /* .header {
    margin-top: -30px;
  } */
}

@media screen and (min-width: 1025px) and (max-width: 1399px ) {
  .nav-row {
    padding-top: 10px;
  }
  /* .header {
    margin-top: -20px;
  } */
}

@media screen and (min-width: 1400px)  {
  .content-col {
    padding-top: 20px;
  }
  /* .header {
    margin-top: 0px;
  } */
}
@media screen and (max-width: 767px ) {
  .container-padding{
    padding-top: 28px;
  }
  .header{
    background: #fff;
  }
  .breadcrumb{
    font-size: 14px;
  }
  ul.list-group.sub-group-list {
    margin-top: 0px;
    margin-left:8px;
  }
  .card{
    padding: 0;
  }
  /* .cat-col{
    padding-right: 12px;
    padding-left: 12px;
  } */
  /* h2{
    padding-left: 15px;
  } */
  .badge.category-badge{
    padding: 5px 0;
  }
  .list-group-item{
    padding: 10px 0;
  }
  .list-group {
    margin-bottom: 12px;
  }
}


