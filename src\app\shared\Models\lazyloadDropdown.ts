//import { Injectable } from "@angular/core";
import { LazyloadDropdownService } from "shared/shared-services/lazyload-dropdown.service";
import { AbstractControl } from "@angular/forms";

// @Injectable()
export class LazyloadDropdownClass {
    ddType:string;
    items=[];
    next_page_url: string;
    loading = false;
    pgsize = 10;
    current_language = 1;
    additional_param : {paramName:string , paramValue:any};

    constructor(
        private lazyloadDropdownService:LazyloadDropdownService,
        ddType:string,
        pgsize?: number,
        current_language?:number,
        additional_param?:{paramName:string , paramValue:string}
        ) {
        this.ddType = ddType;
        if(pgsize){
            this.pgsize = pgsize;
        }
        if(current_language){
            this.current_language = current_language;
        }
        if(additional_param){
            this.additional_param = additional_param;
        }

        if(this.additional_param === undefined || (this.additional_param && this.additional_param.paramName !== 'get_original') )
            this.initDropdown();
    }

    initDropdown(){
        this.lazyloadDropdownService.getData(this.ddType,'',this.pgsize,'',this.current_language,this.additional_param)
            .subscribe(res => {
                let data;
                data = res;
                this.items = data.data;
                this.next_page_url=data.next_page_url;
            },error => {
        });
    }

    search(term: any,evt?: any) {
        this.loading = true;
        this.lazyloadDropdownService.getData(this.ddType,term.term,this.pgsize,'',this.current_language,this.additional_param)
            .subscribe(res => {
                this.items = [];
                let data:any;
                data=res;
                this.items = data.data;
                this.next_page_url=data.next_page_url;
                this.loading = false;
            },
            error => {
            });
    }

    //using this function beacause the default filtering was not displaying some items returned from backend
    // ex: when typing in skills "backend" and the original value was back-end, it is returned right from backend
    // but not displayed by the control ng-select
    // so fixed it using this way
    customSearchFn(term: string, item: any) {
        return term;
    }

    onScrollToEnd() {
      if(this.next_page_url !== null){
        this.loading = true;
        this.lazyloadDropdownService.getNextPageData(this.next_page_url)
        .subscribe(res => {
          let data;
          data=res;
          this.items = this.items.concat(data.data);
          this.next_page_url=data.next_page_url;
          this.loading = false;
          },
          error => {

          });
      }
    }

    addNewItem(name) {
      return { name: name, id: -1 };
    }

    // custom function to clear autocomplete when clicking delete button
    deleteAutoCompleteItem(control:AbstractControl){
        control.setValue(null);
    }

    //pass item name and get orignal value, in case name string modified for some reason we can get the original string
    // ex: when passing job title in url, all special charachters are removed and spaces converted to -
    //so we use this function to handle this case
    getOriginalValue(name){
        return this.lazyloadDropdownService.getData(this.ddType,name,this.pgsize,'',this.current_language,{paramName:"get_original" , paramValue:1})
    }

    //pass item id and get specific item by id
    getById(id){
        return this.lazyloadDropdownService.getData(this.ddType,'',this.pgsize,'',this.current_language,{paramName:"id" , paramValue:id})
    }

}

