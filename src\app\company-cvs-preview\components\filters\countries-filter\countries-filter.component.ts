import { Component, OnInit } from '@angular/core';
import { GeneralService } from '../../../../general/services/general.service';
import { FiltersService } from '../../../services/filters.service';

@Component({
  selector: 'app-countries-filter',
  templateUrl: './countries-filter.component.html',
  styleUrls: ['./countries-filter.component.css']
})
export class CountriesFilterComponent implements OnInit {
  countriesList=[];
  countries = [];
  initFilters = {};
  initFiltersTags = [];
  tags = [];

  constructor(
    private generalService:GeneralService,
    private filtersService:FiltersService) { }

  ngOnInit(): void {
    this.sendInitStateToWarapper();

    this.generalService.internalMessage.subscribe( (data) => {
      if (data['message'] === 'removedFilters' && data['dist'] === 'countries_filter') {
        if(data['mData']['removedArrayTypeFilters'].length>0){
          console.log(data['mData']['removedArrayTypeFilters']);
         // this.countries = data['mData']['removedArrayTypeFilters'][0]['filterValue'];
        //   data['mData']['removedArrayTypeFilters'].forEach( (filter) => {
        //     this.filtersForm.controls[filter.filterName].setValue(filter.filterValue);
        //  });
        }

        this.setTags();
      }

      if (data['message'] === 'clearAllFilters' && data['src'] === 'filters-wrapper'){
        this.clearFilters();
      }
    });
  }

  ngAfterViewInit() {
    this.filtersService.getCountriesLists().subscribe(res => {
      this.countriesList = res['data'];
    });
  }

  setFilters(){
    let filters = {"countries":this.countries};
    return filters;
  }
  setTags(){
    this.tags = [
      {"name":"countries" ,"title":"Countries", "value":this.countries , "type":"array"}
     ]
    return this.tags;
  }

  sendInitStateToWarapper() {
    this.initFilters = {"countries":this.countries};
    this.initFiltersTags = this.setTags();
    this.generalService.notify('init-filters',
      'countries_filter', 'filters-wrapper', {'filters':this.initFilters , 'tags':this.initFiltersTags});
  }

  sendFilters() {
    this.generalService.notify('filters-changes', 'countries_filter', 'filters-wrapper', {"filters":this.setFilters(), "tags":this.setTags()});
  }

  sendFiltersOnChange($event){
    this.sendFilters();
  }
  
  // sendFiltersOnHide($event){
  //   this.sendFilters();
  // }

  clearFilters(){
    this.countries = [];
    this.tags = this.initFiltersTags;
   }

}
