import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { SocketioService } from 'shared/shared-services/socketio.service';

import {MenuItem} from 'primeng/api';
import { AuthService } from 'shared/shared-services/auth-service';
declare var $: any;
@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.css']
})

export class AdminComponent implements OnInit {
  name = '';
  displayModal       = false;
  displayCreatePopup = false;
  displayCreateTopic = false;
  displayCreateTip   = false;
  mode = 'create';
  items: MenuItem[];
  img = '';
  constructor(private title: Title, public socketService: SocketioService,
              private auth: AuthService) {
    title.setTitle('CVeek Administration');

   }

  ngOnInit() {
    this.name = localStorage.getItem('username');
    this.img =  localStorage.getItem('imgUrl');
    this.items =  [
      {
          icon: 'pi pi-bell',
          items : []
      },
  ];


  }

  logout() {
    this.auth.logout();
  }

  displayCreateModal() {
    this.displayModal       = true;
    this.displayCreatePopup = true;
    this.displayCreateTopic = false;
    this.displayCreateTip   = false;
  }


  closeModal() {
    console.log('inside close  modal in the admin');
    this.displayModal       = false;
    this.displayCreatePopup = false;
    this.displayCreateTopic = false;
    this.displayCreateTip   = false;
    $('#modal').hide();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
    $('div.modal-backdrop.fade.in').remove();
  }

  displayCreateTopicModal() {
    this.displayModal       = true;
    this.displayCreatePopup = false;
    this.displayCreateTopic = true;
    this.displayCreateTip   = false;

  }



  displayCreateTipModal() {
    this.displayModal       = true;
    this.displayCreatePopup = false;
    this.displayCreateTopic = false;
    this.displayCreateTip   = true;

  }


  toggleTable() {
    $('div.col-md-9.col-sm-9').toggleClass('collapsed');
    $('div.col-md-9.col-sm-9').toggleClass('expanded');
  }




}
