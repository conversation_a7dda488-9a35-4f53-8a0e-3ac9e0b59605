<app-pre-loader [show]="showLoader"></app-pre-loader>

<form #form="ngForm" [formGroup]="fileForm" (ngSubmit)="fromSteps ? nextPage() : submitStepUploadCv()"
  class="form-horizontal validate-form">
  <div class="cvPdfContainer" [ngClass]="{'imgError': fileUploadError}">
    <label class="cvPdfLabel" for="cvPdf">
      <div class="icons-div">
        <i *ngIf="uploadLabelDisplay" class="fa fa-upload" aria-hidden="true"></i>
        <i *ngIf="!uploadLabelDisplay" class="fa fa-file-pdf-o" aria-hidden="true"></i>
      </div>

      <input id="cvPdf" type="file" class="form-control" style="visibility:hidden;position:absolute;" accept=".pdf"
        (change)="onFileChanged($event);">


      <span *ngIf="uploadLabelDisplay">Upload Your Cv Pdf File</span>
      <!-- <span id="fileName" *ngIf="!uploadLabelDisplay" [innerHTML]="fileName"></span> -->
      <span id="fileName" *ngIf="!uploadLabelDisplay">
        {{fileName | slice:0:30}}
        <span *ngIf="fileName?.length>30">.....</span>
      </span>
      <span class="edit-btn" *ngIf="!uploadLabelDisplay">
        <i class="fa fa-edit" aria-hidden="true"></i>
      </span>
    </label>
    <span *ngIf="!uploadLabelDisplay" class="delete-photo" (click)="deleteUploadedFile()">
      <i class="fa fa-trash" aria-hidden="true"></i>
    </span>
    <div>
      <span *ngIf="fileUploadError" class="error-message" translate>{{fileUploadError}}</span>
    </div>
    <div class="form-group" *ngIf="fromSteps">
      <br>
      <span>

      </span>
      <p-checkbox formControlName="analyzeByAI" name="analyzeByAI" [(ngModel)]="analyzeByAI" binary="true"
        inputId="analyzeByAI" styleClass="mr-2"> </p-checkbox>
      <label for="analyzeByAI" style="margin-left: 8px;">
        Analyze file by AI <i class="fa fa-magic"></i></label>
    </div>
  </div>



  <div *ngIf="!fromSteps" class="form-group text-center submit-btns-div div-margin-top-40">
    <button type="submit" class="btn btn-success">
      <i class="fa fa-floppy-o"></i> <span translate>shared.save</span>
    </button>
    <!-- <button type="button" class="btn btn-primary" style="margin-left:20px;" (click)="nextPage()" *ngIf="fromSteps">
      <span translate>shared.next</span> <i class="fa fa-arrow-right"></i>
    </button> -->
  </div>
  <div *ngIf="fromSteps" class="row form-group text-right div-margin-top-40">
    <div class="col-sm-10 col-xs-12">
      <button type="submit" class="btn btn-primary btnNext">
        <i class="fa fa-arrow-right"></i> <span translate>shared.next</span>
      </button>
    </div>
  </div>
  <!-- <p> {{ form.value | json }} </p> -->
</form>
