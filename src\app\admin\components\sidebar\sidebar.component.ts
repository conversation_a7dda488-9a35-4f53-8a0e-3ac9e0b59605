import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { SideToFormSignalService } from 'app/user/cv-services/side-to-form-signal.service';
import { TopToSideSignalService } from 'app/user/cv-services/top-to-side-signal.service';
import { Subscription } from 'rxjs/Subscription';


@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent implements OnInit {
  display = [false, false, false, false, false, false, false];
  sideBarStatus = '' ;
  subscribe: Subscription;

  role = '';

  @Output('createFaqClicked') createFaqClicked = new EventEmitter();
  @Output('createHelpTopicClicked') createHelpTopicClicked = new EventEmitter();
  @Output('createHelpTipClicked') createHelpTipClicked = new EventEmitter();
  @Output('toggleSidebarClicked')toggleSidebarClicked = new EventEmitter();

  constructor(private sideToFormShared: SideToFormSignalService,private topShared: TopToSideSignalService) {

  }

  ngOnInit() {

    if(localStorage.getItem("role")){
      this.role = localStorage.getItem("role");
    }

    this.subscribe = this.topShared.cssValue.skip(1).subscribe(value => {
      if (this.sideBarStatus === 'collapsed') {
        this.sideBarStatus = '';
        this.changeFormStatus('expand');

      } else {
        this.sideBarStatus = 'collapsed';
        this.changeFormStatus('collapsed');

      }
    });
   }

   changeSideBarStatus() {
    if (this.sideBarStatus === 'collapsed') {
      this.sideBarStatus = '';
      this.changeFormStatus('expand');
     } else {
      this.sideBarStatus = 'collapsed';
      this.changeFormStatus('collapsed');

    }

    this.toggleSidebarClicked.emit();
  }

  changeFormStatus(x: String) {
    this.sideToFormShared.expandAndCollapseForm(x);
  }



  displayCreateModal() {
    this.createFaqClicked.emit();
  }

  displayCreateTopicModal() {
    this.createHelpTopicClicked.emit();
  }

  displayCreateTipModal() {
    this.createHelpTipClicked.emit();

  }






}
