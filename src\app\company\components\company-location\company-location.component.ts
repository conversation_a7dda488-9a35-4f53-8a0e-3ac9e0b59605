/// <reference types="@types/googlemaps" />
import { Component, OnInit, ViewChild, ElementRef, OnDestroy } from '@angular/core';
// import { MapToFormService } from '../../../user/cv-services/map-to-form.service';
import { Place } from 'shared/Models/place';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// import { ActivatedRoute, Router , NavigationEnd } from '@angular/router';
import { Subject } from 'rxjs';
import { CompanyLocationService } from './../../services/company-location.service';
import {LangChangeEvent, TranslateService} from '@ngx-translate/core';
import { DataMap } from "shared/Models/data_map";
import { MessageService } from 'primeng';
//  import { google } from '@agm/core/services/google-maps-types';

declare var $: any;

@Component({
  
  selector: 'app-company-location',
  templateUrl: './company-location.component.html',
  styleUrls: ['./company-location.component.css'],
  providers: [MessageService]
})
export class CompanyLocationComponent implements OnInit, OnDestroy, AfterViewInit {
  company_name: any;
  country_code: any;
  companyLocation: FormGroup;
  private ngUnsubscribe: Subject<any> = new Subject();
  @ViewChild('googleplace') googleplace: any;
  @ViewChild('googleplace') public googleplaceRef: ElementRef;
  @ViewChild('googlelocationplace') googlelocationplace: any;
  @ViewChild('googlelocationplace') public googlelocationplaceRef: ElementRef;
  //@ViewChild('currentSearch') public currentSearchRef: ElementRef;
  // tslint:disable-next-line:no-inferrable-types
  currentType: string = 'currentLocation';
  currentLocation: Place = new Place();
  currentSub: Subject<any> = new Subject();
  locationToBePassedToModal;
  city: string;
  country: string;
  check_name: boolean ;
  check_location: boolean ;
  companyId = Number (localStorage.getItem('company_id'));
  name: string;
  companyLocationData = [];
  companyLocationOrder = [];
  companyLocationAfterReorder = [];
  display_location_modal = false;
  mySubscription: any;
  currentLanguage = Number (localStorage.getItem('current_company_language'));
  data_map = new DataMap();
  streetAddr: any;

   //options for ngx-sortablejs(library for reorder table rows)
   options:any;
   tableLoader: boolean = true;
  constructor(
    // private mapToFormService: MapToFormService,
    private fb: FormBuilder,
    private messageService: MessageService,
    // private route: ActivatedRoute,
    // private router: Router,
    private companyLocationService: CompanyLocationService,
    private translate: TranslateService

  ) {

    

    this.companyLocation = this.fb.group({
        'name': ['',Validators.required],
        'company_id': [''],
        'location': ['',Validators.required],
        'latitude': [''],
        'longitude': [''],
        'country': [''],
        'country_code':['',Validators.required],
        'city': [''],
        'postal_code': [''],
        'street_address': [''],
        'verified_by_google': ['']
    });
   }

   setcompanyLanguage() {
      /* if (res['translated_languages_id'] == 1) {
        this.translate.setDefaultLang('en');
      } else if (res['translated_languages_id'] == 2) {
        this.translate.setDefaultLang('ar');
      } else {
        this.translate.setDefaultLang('en');
      } */

      if (this.currentLanguage && this.currentLanguage === 1) {
        this.translate.setDefaultLang('en');
      } else if (this.currentLanguage === 2) {
        this.translate.setDefaultLang('ar');
      } else {
        this.translate.setDefaultLang('en');
      }


  }


  /*--- i used this compnent's lifehock to initialize 
         google map ---*/
  ngAfterViewInit() {
    setTimeout(() => {
      this.getLocationPlaceAutocomplete();
      this.check_location = true;
    }, 1);
   
    
  }

  close_Location_Modal(){
      this.display_location_modal = false;
  }

  minimize() {
      // Slide certeficate bottom and top
      $(document).ready(function() {

        $('.minamize-certification').click(function() {
          $( '.add-certification .location' ).slideToggle( 'slow' );
          $('.minamize-certification .fa').toggleClass('rotate');
        });

      });
      /*     $('.rad-other-lang').click(function() {
        $(this).closest('.language').find('.other-lang').slideDown('slow');
      });
      $('.rad-mother-lang').click(function() {
        $(this).closest('.language').find('.other-lang').slideUp('slow');
      }); */
  }

  // initializePlaceData() {
  //   this.mapToFormService.personalMapData.skip(1).takeUntil(this.ngUnsubscribe).subscribe(
  //     (place: Place) => {
        
  //       this.currentLocation = place;
        
  //       this.setCurrentLocationControls(this.currentLocation);
  //     }
  //   );
  // }

  // setCurrentLocationControls(place: Place) {
   
  //   this.companyLocation.controls['country'].setValue(place.country);
  //   this.companyLocation.controls['city'].setValue(place.city);
  //   this.companyLocation.controls['postal_code'].setValue(place.postalCode);
  //   this.companyLocation.controls['street_address'].setValue(place.streetAddress);
  //   this.companyLocation.controls['latitude'].setValue(place.latitude);
  //   this.companyLocation.controls['longitude'].setValue(place.longitude);
  //   this.companyLocation.controls['country_code'].setValue(place.countryCode);
  //   if (place.full == 'Dragging') {
  //     this.companyLocation.controls['location'].setValue(this.companyLocation.get('country').value
  //       + ',' +this.companyLocation.get('city').value
  //       + ',' + this.companyLocation.get('street_address').value);
  //   } else {
  //     this.companyLocation.controls['location'].setValue(this.companyLocation.get('country').value + ',' +
  //     this.companyLocation.get('city').value
  //       + ',' + this.companyLocation.get('street_address').value);
  //   }
  // }

  // notifyCurrentMap(val) {
  //   this.currentSub.next(val);
  // }

  getLocationAddress(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place)
  
    this.city = data_location.city;
    this.country = data_location.country;
    this.country_code = data_location.country_code;
    this.streetAddr = data_location.street_address;
    this.companyLocation.controls['street_address'].setValue(this.streetAddr)
    this.companyLocation.controls['country'].setValue(this.country);
    this.companyLocation.controls['city'].setValue(this.city);
    this.companyLocation.controls['country_code'].setValue(this.country_code)
    this.companyLocation.controls['name'].setValue(this.companyLocation.controls['name'].value);
    this.companyLocation.controls['latitude'].setValue(data_location.latitude);
    this.companyLocation.controls['longitude'].setValue(data_location.longitude);
  }

  clearLocationData() {
    this.companyLocation.controls['country'].setValue('');
    this.companyLocation.controls['country_code'].setValue('');
    this.companyLocation.controls['city'].setValue('');
    this.companyLocation.controls['street_address'].setValue('');
    this.companyLocation.controls['latitude'].setValue('');
    this.companyLocation.controls['longitude'].setValue('');
  }


/*--- here we handle the state when the company put check if they will
      use google to locate their company's location or just use google map ---*/

  /* changAutoComplete() {
    if ($('#check-verify').prop('checked') === true) {
      this.getLocationPlaceAutocomplete();
      this.check_location = true;
    } else {
      
      google.maps.event.clearInstanceListeners(this.googlelocationplace.nativeElement);
      this.check_location = false;
    }
  } */

  private getLocationPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googlelocationplace.nativeElement,
      {
        types: ['geocode'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']   // 'name'
      });
    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      this.getLocationAddress(place);
    });
  }

  ngOnInit() {

    this.minimize();
    this.reorderTableRows();
    this.setcompanyLanguage();
    
    // this.initializePlaceData();
    this.companyLocation.controls['company_id'].setValue(this.companyId);
    this.companyLocationService.getCompanyName(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        this.company_name = res['company_name']
        this.companyLocation.controls['name'].setValue(res['company_name']);
      });

      this.companyLocationService.getCompanyLocationData(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.companyLocationData = [];
          this.companyLocationData = res['data'];
          this.initCompanyLocationOrder(this.companyLocationData);

          this.tableLoader = false;
        }
      );
  }

  submit(form) {
    let sendLocation;
    form.submitted = false;

    if (this.companyLocation.valid) {
      /* if ($('#check-verify').prop("checked") == true) {
        this.companyLocation.controls['verified_by_google'].setValue(true);
      } else {
        this.companyLocation.controls['verified_by_google'].setValue(false);
      } */
      sendLocation = this.companyLocation.value;
      if(sendLocation.street_Address === '') {
        sendLocation.street_address = null
      } else {
        sendLocation.street_address = this.companyLocation.controls['street_address'].value;
      }
      this.companyLocationService.addCompanyLocationData(sendLocation).toPromise().then(
        (result) => {
          if(result['error']) {
            window.alert(result['error'])
          } else {
            for (let i = 0; i < this.companyLocationData.length; i++){
              this.companyLocationData[i].order += 1;
            }
            this.companyLocationData.unshift(result['data']);
            this.initCompanyLocationOrder(this.companyLocationData);
            this.companyLocation.controls['name'].setValue('');
            this.companyLocation.controls['location'].setValue('');
          }
         
        }
      );
    }
  }

  initCompanyLocationOrder(companyLs: any) {
    this.companyLocationOrder = [];
    for (let location of companyLs) {
      let x = {'location_id' : location.id, 'orderId' : location.order };
      this.companyLocationOrder.push(x);
    }
  }

  handlePopup(event: any) {
    this.companyLocation.controls['name'].setValue(this.company_name);
    this.companyLocation.controls['location'].setValue('');
    let index = this.companyLocationData.indexOf(event['old']);
    this.companyLocationData[index] = event['new'];

    this.display_location_modal = false;
  }

  removeCompanyLocation(company_location) {
    if (confirm("Are you sure to delete this location?")) {
      let index  : number = this.companyLocationData.indexOf(company_location);
      if (index !== -1) {
        this.companyLocationService.deleteCompanyLocationData(company_location.id).takeUntil(this.ngUnsubscribe).subscribe((res) => {
          if(res['error']) {
            this.messageService.add({ severity:'error', detail:res['error'],life: 5000});
          } else {
            if(company_location.is_main_office){
              this.messageService.add({ severity:'warn', detail:'Please choose another  main branch for your company',life: 5000});
            }
            this.companyLocationData.splice(index, 1);
            let order = this.companyLocationAfterReorder.find(x => {return x.locationId === company_location.id;});
            for (let i=0 ; i<this.companyLocationData.length;i++) {
              this.companyLocationData[i].order = i+1;
            }
            this.initCompanyLocationOrder(this.companyLocationData);
          }
          
        });
      }
    }
  }

   //reorder table rows on drag and drop using ngx-sortablejs library
   reorderTableRows(){
    this.options = {
      onUpdate: (event: any) => {
        let orderedArray = [];
        for(let i=0; i < this.companyLocationData.length;i++){
          this.companyLocationData[i].order = i+1;
          orderedArray.push({'location_id':this.companyLocationData[i].id,'orderId':this.companyLocationData[i].order});
        }
        let orderedData ={"orderData":orderedArray};
        this.companyLocationService.orderCompanyLocations(this.companyId , orderedData).takeUntil(this.ngUnsubscribe)
        .subscribe( () => console.log('done'));
      }
    };
  }

  setMainOffice(companyMainOffice) {
    let index  : number = this.companyLocationData.indexOf(companyMainOffice);
    if (index !== -1) {
      this.companyLocationService.selectCompanyMain(companyMainOffice).takeUntil(this.ngUnsubscribe).subscribe(
      (res) =>  {
        
      $(document).on('click', 'input[name=\'radioBtn\']', function() {
        this.thisRadio = $(this);
        if (this.thisRadio.hasClass('imChecked')) {
          this.thisRadio.removeClass('imChecked');
          this.thisRadio.prop('checked', false);
        } else {
          this.thisRadio.prop('checked', true);
          this.thisRadio.addClass('imChecked');
         }
        });

        for(let i=0; i < this.companyLocationData.length;i++) {
          if(i === index)
            this.companyLocationData[i].is_main_office = 1;
          else
            this.companyLocationData[i].is_main_office = 0;
        }
      });

      
    }
  }

  display_Location_Modal(location) {
    this.display_location_modal = !this.display_location_modal;
    this.locationToBePassedToModal = location;
  }

  ngOnDestroy() {
       this.ngUnsubscribe.next();
       this.ngUnsubscribe.complete();
     }

  }

  interface AfterViewInit {
    ngAfterViewInit(): void
  }