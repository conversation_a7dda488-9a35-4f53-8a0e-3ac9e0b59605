
.form-section{
    background-color: #fff;
    padding:40px 40px 0;
    transition:box-shadow 0.3s ease;
  }
  .form-section.active{
    box-shadow: 0 0 5px #777;
    transform:translateZ(1px);
  }
  .form-section.header-form-section{
    padding:30px;
  }
   .form-control{
    border-bottom: 1px solid #ccc;
    border-top:none;
    border-left:none;
    border-right:none;
    box-shadow: none;
    border-radius: 0;
  }
   .form-control#question , select.form-control{
    font-size: 1.8rem;
  }
   .form-control#form-title{
    font-size:3rem;
  }
  .form-control:focus{
    box-shadow: none;
  }
   input[type=checkbox] ,  input[type=radio]{
    width: 18px;
    height: 18px;
  }
  .flex-vertical-end{
    display: flex;
    align-items:flex-end;
  }
  
  .del-fa-icon{
    color:#EA4335;
    font-size: 2.1rem;
  }
  
  .check-square-style{
    font-size:2rem;
    color:gray;
  }
  
  .form-footer{
    margin-top:20px;
    border-top:1px solid #ccc;
    padding-top:10px;
    display: flex;
    flex-wrap:wrap;
    justify-content: flex-end;
    align-items:center;
  }
  .footer-form-group{
    margin-bottom: 0;
    padding-bottom: 10px;
  }
  .form-footer button , .form-footer label{
    margin-right:10px;
  }
  
  
  /*  Start Custom toggle button  */
  .switch-toggle {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    border-radius: 25px;
    background-color:#c9d1d6;
  }
  
  .switch-toggle input {
    display: none;
  }
  
  .switch-toggle div {
    position: absolute;
    border-radius: 50%;
    background-color: #FAFAFA;
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.4);
    box-shadow: 0 1px 3px rgba(0,0,0,0.4);
  
    transition: .1s ease;
  }
  
  .switch-toggle input:checked + div {
    left: 50%;
    background-color: #314e63;
  }
  
  .switch-toggle.rect {
    border-radius: 0;
  }
  
  .switch-toggle.rect div {
    border-radius: 0;
  }
  
  .switch-toggle.inner div {
    width: 18px;
    height: 18px;
    top: 1px;
    left: 1px;
  
  }
  
  .switch-toggle.outer div {
    width: 23px;
    height: 23px;
    top: -2px;
    left: -3px;
  
  }
  
  
  /* End  Custom toggle button  */
  
  /* Start input animation  */
  
  .material-group {
    margin-bottom: 34px;
    position:relative;
  }
  
  .material-group input {
    display:block;
    width:100%;
    border:none;
    border-bottom:1px solid #ccc;
  }
  .material-group input:focus {
    outline:none;
  }
  
  
  
  /* BOTTOM BARS ================================= */
  .material-group .bar    { position:relative; display:block; width:100%; }
  .material-group .bar:before, .material-group .bar:after     {
    content:'';
    height:2px;
    width:0;
    top:32px;
    z-index: 2;
    position:absolute;
    background:#5264AE;
    transition:0.2s ease all;
    -moz-transition:0.2s ease all;
    -webkit-transition:0.2s ease all;
  }
  .material-group .bar:before {
    left:50%;
  }
  .material-group .bar:after {
    right:50%;
  }
  
  /* active state */
  .material-group input:focus ~ .bar:before,.material-group  input:focus ~ .bar:after {
    width:50%;
  }
  
  /* HIGHLIGHTER ================================== */
  .material-group .highlight {
    position:absolute;
    height:60%;
    width:100px;
    top:25%;
    left:0;
    pointer-events:none;
    opacity:0.5;
  }
  
  /* active state */
  .material-group input:focus ~ .highlight  {
    -webkit-animation:inputHighlighter 0.3s ease;
    -moz-animation:inputHighlighter 0.3s ease;
    animation:inputHighlighter 0.3s ease;
  }
  
  /* ANIMATIONS ================ */
  @-webkit-keyframes inputHighlighter {
    from { background:#5264AE; }
    to    { width:0; background:transparent; }
  }
  @-moz-keyframes inputHighlighter {
    from { background:#5264AE; }
    to    { width:0; background:transparent; }
  }
  @keyframes inputHighlighter {
    from { background:#5264AE; }
    to    { width:0; background:transparent; }
  }
  
  /* End input animation  */
  
   .dotted-border{
    border-bottom: 1px dotted rgba(0,0,0,0.38);
    padding:6px 12px;
  }
  
  
  /* Start custom buttons */
  
   .my-btn{
    border: none;
    font-family: inherit;
    font-size: inherit;
    color:#fff;
    cursor: pointer;
    padding: 10px 12px;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700;
    outline: none;
    position: relative;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
    overflow:hidden;
    border-radius:5px;
  }
   .icon-span{
    color:#fff;
    position: absolute;
    height: 100%;
    width: 100%;
    line-height: 2.4;
    font-size: 18px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
    bottom:0;
    left:0;
  }
   .my-btn:hover .icon-span{
    bottom:100%;
  }
   .word-span{
    display: inline-block;
    width: 100%;
    height: 100%;
    -webkit-transition: all 0.3s;
    -webkit-backface-visibility: hidden;
    -moz-transition: all 0.3s;
    -moz-backface-visibility: hidden;
    transition: all 0.3s;
    backface-visibility: hidden;
    -webkit-transform: translateY(300%);
    -moz-transform: translateY(300%);
    -ms-transform: translateY(300%);
    transform: translateY(300%);
  }
   .my-btn:hover .word-span{
    -webkit-transform: translateY(0%);
    -moz-transform: translateY(0%);
    -ms-transform: translateY(0%);
    transform: translateY(0%);
  }
  
  
  
   .save-btn{
    background-color: #5cb85c;
  }
   .cancel-btn{
    background-color: #999;
  }
   .delete-btn{
    background-color: #d43f3a;
  }
   .add-btn{
    background-color: #73b8ff;
    padding: 10px 5px;
  }
  
  .add-question{
    width: 50px;
    height: 50px;
    position: fixed;
    z-index: 300;
    left: 5%;
    top: 20%;
    border-radius:50%;
    color:#fff;
    border:0;
    background:#314e63;
    outline: none;
    box-shadow: 0 4px 4px #a1a1a1;
  }
  .form-footer{
  
  }
  
  .form-footer .flex-span{
    display:inline-flex;
  }
  
  /* End custom buttons */
  
  
  /************************** START PREVIEW ****************************/
  
  .form-section.inactive{
    padding-bottom:30px;
  }
  .form-section.inactive form select , .form-section.inactive form .add-btn ,.form-section.inactive form .footer-form-group , .form-section.inactive form .del-fa-icon{
    display:none;
  }
  .form-section.inactive input{
    cursor:text;
    background:transparent;
    border:0;
    color:black;
  }
  /************************** END PREVIEW ****************************/
  
  
  
  @media screen and (max-width:768px) {
     #question_global {
       padding: 19px;
     }
  }
  
  
  
  

  
  
  