.container-fluid{
  padding: 15px;
}

label[for="type"] {
  padding-left: 10px;
  margin-top: 5px;
}
div.form-group.type-form-group {
  margin-top: 10px;
  margin-bottom: 10px;
}
.alert.alert-danger {
  border: none;
  padding-left: 25px;
  background-color: transparent;
}

.form-group.type-form-group .row {
  padding-left: 45px;
  margin-top: 20px;
}

.input100 {
  position: relative;
  display: block;
  width: 100%;
  background: #fff;
  border-radius: 1px;
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 18px;
  color: #8f8fa1;
  line-height: 1.2;
}
.validate-input {
  position: relative;
}
.wrap-input100 {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.contact-form {
  width: 100%;
}
.wrap-input100 {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}
.input100 {
  display: block;
  width: 100%;
  background: #f2f2f2;
  border-radius: 1px;
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 18px;
  color: #8f8fa1;
  line-height: 1.2;
}
textarea.input100 {
  min-height: 169px;
  padding: 19px 35px 0 35px;
}

/* :host ::ng-deep p-dropdown .ui-dropdown.ui-widget.ui-state-default.ui-corner-all
     .ui-dropdown-label.ui-inputtext.ui-corner-all {
      font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
      color: #8f8fa1;
} */

.ui-state-disabled, .ui-widget:disabled {
  opacity: 1;
}

/* unify placeholer style for all different controls */
::placeholder,
 :host ::ng-deep .ui-dropdown label.ui-dropdown-label,
:host ::ng-deep .ui-autocomplete ::placeholder {
    /* ::placeholder Chrome, Firefox, Opera, Safari 10.1+ */
    color: #808080;
    opacity: 1;
    /* Firefox */
    font-weight: normal;
    font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
    font-size: 15px !important;
}

.has-error2{
  border-bottom:1px solid #a94442 !important;
}

.cat-form-group{
  margin-left: 45px;
}
:host ::ng-deep.ui-placeholder {
  font-size: 'Exo2-Regular', sans-serif !important  ;
  font-size: 16px !important;
}
:host ::ng-deep.ui-dropdown{
  padding-bottom: 38px !important;

}
.error-message2{
  color:#a94442;
}
.error-message{
  color: #a94442;
  position:relative;
  width:auto;
  position: relative;
  width: auto;
  left: unset;
  top: unset;
}

/* start upload file styles */
.input-file-container {
  border: 1px solid #e5e5e5;
}

.input-file-container input[type=file]::file-selector-button {
  background-color: #fff;
  color: #000;
  border: 0px;
  border-right: 1px solid #e5e5e5;
  padding: 10px 15px;
  margin-right: 20px;
  transition: .5s;
}

.input-file-container input[type=file]::file-selector-button:hover {
  background-color: #eee;
  border: 0px;
  border-right: 1px solid #e5e5e5;
}
/* End upload file styles */

@media screen and (max-width: 768px){
  .focus-no-padding {
      margin-bottom: 10px;
  }
}