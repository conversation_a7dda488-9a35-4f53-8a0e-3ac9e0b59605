import { Injectable } from '@angular/core';
import { HttpParams, HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class CvsTableService {

  url = '';
  searchUrl = '';
  fav = '';
  read = '';
  move_to = '';
  interviewSet = '';
  advFolders = '';
  constructor(private http: HttpClient ,
              private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url = data + 'cv_folder';
      this.searchUrl = this.url + '/search';
      this.fav = this.url + '/favourite';
      this.read = this.url + '/read';
      this.move_to = data + 'emp_app/move_to_folder';
      this.interviewSet = data + 'emp_app/interview';
      this.advFolders = data + 'emp_app/data';
    });
  }


  jsEncode(param: string){
    return encodeURIComponent(param);
  }

  jsDecode(param: string){
    return decodeURIComponent(this.jsEncode(param));
  }

  //commented have_data param
  getTableData(pgsize, pgnum, lang, filters, columns?) {
    let params = new HttpParams();
    let body_req = [];
    params = params.append('pgsize', pgsize);
    params = params.append('pgnum', pgnum);
    params = params.append('lang', lang);
    if(columns && columns.length > 0) {
      let ColList = columns
      body_req = filters;
      ColList.forEach((ColName:string) =>{
        params = params.append(`requirements[]`, ColName);
      });
    //  params = params.append('have_data',fitlerFormData)
      return this.http.post(this.searchUrl,body_req,{ params: params})
     }else{
      let encodedName = this.jsDecode('?requirements=[]');
    //  params = params.append('have_data',fitlerFormData)
      body_req = filters;
      return this.http.post(this.searchUrl+encodedName,body_req,{ params: params})
    }
  }

  moveCopyCVToFolder(data){
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/move_copy_to_folders' , JSON.stringify(data), {headers});
  }

  // here should edit line 70, it is not correct
  detachFolderFromResume(data){
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      body: data,
    };

    return this.http.delete(this.url+ '/detach_folder_from_resume', options);
  }

  getFoldersForResume(resume_id:number){
      let headers = new HttpHeaders().set('Content-Type', 'application/json');
      return this.http.get(this.url +'folders_for_resume/'+  resume_id , {headers});
  }

  deleteCV(data){
   // let data = {"resume_id":resume_id};
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      body: data,
    };
    return this.http.delete(this.url+ '/delete_resume', options);
  }

  getResumeAppFolders(resume_id){
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url +'/resume_app_folders/'+  resume_id , {headers});
  }
}
