import {Answer} from './answer';

export class Question {

  id: number;
  label: string;
  controlType: string;
  isRequired: boolean;
  order: number;
  form_id:number;
  choices:Answer[] = [];

  constructor (order: number ,formId :number) {
    this.label = "Question";
    this.controlType = 'dropDown';
    this.isRequired = false ;
    this.order = order;
    this.form_id=formId;
    this.choices.push(new Answer());
  }

}
