import { NewValueService } from './../../../services/new-value.service';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { Table } from 'primeng/table';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { LanguageService } from 'app/admin/services/language.service';
declare var $: any;

@Component({
  selector: 'app-manage-new-value',
  templateUrl: './manage-new-value.component.html',
  styleUrls: ['./manage-new-value.component.css']
})
export class ManageNewValueComponent implements OnInit, OnDestroy {
  cols = [];
  loading = true;
  items = [];
  filteredItems = [];
  @ViewChild('dt') table: Table;
  oldValue: any;
  notification;
  showNotif = false;
  indeX: number;
  field;
  type = '';
  opperationNum: number;
  private ngUnsubscribe: Subject<any> = new Subject();
  displayDeleteModal = false;
  displayItemModal = false;
  itemIdToDelete: any;
  mode;
  itemToPreview;
  displayActions: boolean[] = [];
  languagesArray = [];
  locations: any = [];
  expFields: any = [];
  majorJobTitles = [];
  majorEducationField = [];
  compInParents = [];
  compIndustries = [];
  dropdown = '';
  multiSelect = '';
  skillCats = [];
  jobTitles = [];
  showNotific = false;
  remainedIds = [];
  title: string;
  constructor(private route: ActivatedRoute,
    private newValueService: NewValueService,
    private messageService: MessageService,
    private languageService: LanguageService) { }

  ngOnInit(): void {
    this.items=[];
    console.log("in on init=============");
    this.getType();
    this.getLanguages();
  }

  getType() {
    this.loading = true;  // added line for test
    this.route.paramMap.subscribe(params => {
      this.type = params.get('type');
      console.log('in getType: type', this.type);
      this.initializeTable();
    });
  }

  initializeTable() {
    this.items = [];
    console.log("in init table",this.items);
    switch (this.type) {
      case 'Institution':
        this.cols = [
          { field: 'id', header: 'ID', filterMatchMode: 'startsWith', type: 'not-editable' },
          { field: 'university', header: 'University', filterMatchMode: 'contains', type: 'not-editable' },
          { field: 'url', header: 'URL', filterMatchMode: 'contains', type: 'string' },
          { field: 'location', header: 'Locations', filterMatchMode: 'contains', type: 'string' }
        ];
        this.title = 'Institution';
        break;
      case 'JobTitle':
        this.cols = [
          { field: 'id', header: 'ID', filterMatchMode: 'startsWith', type: 'not-editable' },
          { field: 'job_title', header: 'Job Title', filterMatchMode: 'contains', type: 'not-editable' },
          { field: 'major_job_title', header: 'Major Job Title', filterMatchMode: 'equals', type: 'dropdown', value: this.majorJobTitles },
          { field: 'experience_fields', header: 'Experience Fields', filterMatchMode: 'equals', type: 'multi-select', value: this.expFields },
          { field: 'job_title_synonyms_string', header: 'Job Title Synonyms', filterMatchMode: 'contains', type: 'not-editable' }
        ];
        this.title = 'Job Title';
        break;
      case 'EducationField':
        this.cols = [
          { field: 'id', header: 'ID', filterMatchMode: 'startsWith', type: 'not-editable' },
          { field: 'education_field', header: 'Education Field', filterMatchMode: 'contains', type: 'not-editable' },
          { field: 'majors_education_field', header: 'Major Education Field', filterMatchMode: 'equals', type: 'multi-select', value: this.majorEducationField },
        ];
        this.title = 'Education Field';
        break;

      case 'job_title_synonym':
        break;
      case 'Skill':
        this.cols = [
          { field: 'id', header: 'ID', filterMatchMode: 'startsWith', type: 'not-editable' },
          { field: 'skill_name', header: 'Name', filterMatchMode: 'contains', type: 'not-editable' },
          { field: 'skill_category_id', header: 'Skill Category', filterMatchMode: 'equals', type: 'dropdown', value: this.skillCats },
          { field: 'job_titles', header: 'Job Title', filterMatchMode: 'equals', type: 'multi-select', value: this.jobTitles }
        ];
        this.title = 'Skill';
        break;
      case 'ExperienceField':
        this.cols = [
          { field: 'id', header: 'ID', filterMatchMode: 'startsWith', type: 'not-editable' },
          { field: 'experience_field_name', header: 'Name', filterMatchMode: 'contains', type: 'not-editable' },
          { field: 'major_experience_field_id', header: 'Experience Field Major', filterMatchMode: 'equals', type: 'dropdown', value: this.expFields },
        ];
        this.title = 'Experience Field';
        break;
      // case 'CompanySpecialty':
      //   this.cols = [
      //     { field: 'id', header: 'ID', filterMatchMode: 'startsWith', type: 'not-editable' },
      //     { field: 'name', header: 'Name', filterMatchMode: 'contains', type: 'not-editable' },
      //     {
      //       field: 'company_industry_id', header: 'Company Industry', filterMatchMode: 'equals',
      //       type: 'dropdown', value: this.compIndustries
      //     },
      //   ];
      //   this.title = 'Company Specialty';
      //   break;
      default:
        break;
    }

    this.newValueService.getAllItems(this.type).takeUntil(this.ngUnsubscribe).subscribe(res => {
      
      this.items = res['data'];
      for (let item of this.items) {
        this.displayActions.push(false);
      }
      
      this.loading = false;
      console.log("loading in get items",this.loading, "print type",this.type);
      this.table.filter(null, 'id', 'startsWith');
    });

    this.getDDlData();
  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
    //  console.log(res);
      let temp = res['data'];
      for (let lang of temp) {
        this.languagesArray.push({
          'id': lang.id,
          'name': lang.name
        });
      }
      console.log('languages array', this.languagesArray);
    });
  }

  getDDlData() {
    // this.type === 'CompanyIndustry' ||  this.type === 'CompanySpecialty'
    if (this.type === 'JobTitle' || this.type === 'EducationField' || 
       this.type === 'Skill' || this.type === 'ExperienceField') {
      this.newValueService.getDDlData(this.type).subscribe(res => {
       
        if (this.type === 'JobTitle') {
          for (let cat of res['experience_fields']) {
            if (cat.experience_field_translation.length) {
              this.expFields.push({
                'value': cat.id,
                'label': cat.experience_field_translation[0].name
              });
            }

          }
          //  this.expFields.unshift({ 'value': null, 'label': ''});
          for (let cat of res['major_job_title']) {
            if (cat.job_title_translation.length) {
              this.majorJobTitles.push({
                'value': cat.id,
                'label': cat.job_title_translation[0].name
              });
            }
          }
          this.majorJobTitles.unshift({ 'value': null, 'label': '' });

        }
        
        else if (this.type === 'EducationField') {
          for (let cat of res as Array<any>) {
            this.majorEducationField.push({
              'value': cat.id,
              'label': cat.name
            });
          }
          this.majorEducationField.unshift({ 'value': '', 'label': '' });
        }
        
        else if (this.type === 'Skill') {
          for (let cat of res['skill_category'] as Array<any>) {

            this.skillCats.push({
              'value': cat.skill_category_id,
              'label': cat.name
            });

          }
          this.skillCats.unshift({ 'value': null, 'label': '' });
          for (let cat of res['job_titles'] as Array<any>) {

            this.jobTitles.push({
              'value': cat.id,
              'label': cat.name
            });

          }
          this.jobTitles.unshift({ 'value': null, 'label': '' });

        } else if (this.type === 'ExperienceField') {
          for (let cat of res as Array<any>) {
            if (cat.experience_field_translation.length) {
              this.expFields.push({
                'value': cat.id,
                'label': cat.experience_field_translation[0].name
              });
            }

          }
          this.expFields.unshift({ 'value': null, 'label': '' });
          console.log('exp', this.expFields);
        } 
        // else if (this.type === 'CompanySpecialty') {
        //   for (let cat of res as Array<any>) {
        //     if (cat.company_industry_translation.length) {
        //       this.compIndustries.push({
        //         'value': cat.id,
        //         'label': cat.company_industry_translation[0].name
        //       });
        //     }

        //   }
        //   this.compIndustries.unshift({ 'value': null, 'label': '' });
        //   console.log('exp', this.compIndustries);
        // }
      });
    }

  }


  editCellData(data, type, cellValue, field) {
    console.log(type);
    console.log(data, cellValue, field);
    if (cellValue === null || cellValue.length === 0) {
      this.items[this.indeX][field] = this.oldValue;
      this.notification = 'this field should not be empty!';
      this.showNotif = true;
      this.showError('this field should not be empty!');
      setTimeout(() => { this.showNotif = false; }, 5000);
      return;
    }
    if (field === 'id') {
      for (let i = 0; i < this.items.length; i++) {
        if (this.items[i].id === (+cellValue) && i !== this.indeX) {
          this.notification = 'id should be unique!';
          this.items[this.indeX].id = +this.oldValue;
          this.showError('id should be unique!');
          this.showNotif = true;
          setTimeout(() => { this.showNotif = false; }, 5000);
          return;
        }
      }
    }
    let value = data[this.field];
    let body: any = {};
    body[this.field] = value;
    console.log('body', body);
    this.newValueService.updateField(data.id, this.type, body).subscribe((res) => {
      console.log('res', res);
    },
      (error: Error) => {
        console.log('error', error);
        this.items[this.indeX][field] = this.oldValue;
        this.notification = 'failed to update this value!!';
        this.showNotif = true;
        this.showError('failed to update this value!');
        setTimeout(() => { this.showNotif = false; }, 5000);
      });
    console.log('items', this.items);


  }

  showError(msg) {
    this.messageService.add({ severity: 'error', summary: 'Error', detail: msg });
  }


  clear() {
    this.messageService.clear();
  }

  storeOldValue(cellValue, field, index) {
    console.log(cellValue, field, index);
    this.oldValue = cellValue;
    this.indeX = index;
    this.field = field;
  }

  displayCreateModal() {
    this.displayItemModal = true;
    this.mode = 'create';
    console.log('mode', this.mode);
  }

  displayEditModal(item) {
    this.displayItemModal = true;
    this.mode = 'edit';
    this.itemToPreview = {
      ...item
    };
    console.log('item', this.itemToPreview, this.mode);
  }

  viewLocations(item) {
    this.displayItemModal = true;
    this.mode = 'location';
    // this.locations = item.locations;
    this.itemToPreview = {
      ...item
    };
    console.log('item', item, this.itemToPreview, this.mode);

  }


  closeModal() {
    this.displayItemModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
  }

  parentAdded(event) {
    console.log(event);
    // if (event['type'] === 'major') {
    //   this.majorParents.push({ value: event['data'].id, label: event['data'].major });
    // } else 
    
    if (event['type'] === 'company_industry') {
      this.compInParents.push({ value: event['data'].id, label: event['data'].company_industry });
    }

  }

  closeItemModal(event) {
    console.log("on close",event);
    console.log("this.items",this.items);
    if (!event['add_one']) {
      for (let item of event['data']) {
        this.items.push(item);
      }
    } else {
      if (event['id'] === null) {
        this.items.push(event['data']);
      } else {
        let index = this.getItemIndex(event['id']);
        this.items.splice(index, 1, event['data']);
      }
    }

    this.table._totalRecords = this.items.length;
    this.closeModal();
  }

  closeLocationModal(event) {
    console.log(event);
    let index = this.getItemIndex(event['id']);
    this.items[index].locations = event['data'];
    this.closeModal();
  }


  displayDeleteAlert(num, msgId?) {
    this.opperationNum = num;
    if (msgId) {
      this.itemIdToDelete = msgId;
    }
    this.displayDeleteModal = true;
  }

  closeDeleteModal() {
    this.showNotific = false;
    this.displayDeleteModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
  }

  deleteMultiItems() {
    let itemsIds = [];
    for (let msg of this.filteredItems) {
      itemsIds.push(msg.id);
    }
    this.newValueService.deleteItems(this.type, { 'items_id': itemsIds, 'confirm': 0 }).subscribe(res => {
      console.log('res', res);

      if (!res['success']) {
        if (res['related']) {
          console.log('inside if');
          this.notification = res['massage'];
          this.remainedIds = res['items_id'];
          this.showNotific = true;
        }
        console.log('deleteMultiItems',itemsIds);
        this.refreshItems(itemsIds);
      } else {
        console.log('inside else');
        this.closeDeleteModal();
        this.remainedIds = itemsIds;
        console.log('inside else deleteMultiItems',itemsIds);
        // for (let i = 0; i < this.items.length; i++) {
        //   for (let id of itemsIds) {
        //     if (this.items[i].id === id) {
        //       this.items.splice(i, 1);
        //       this.table._totalRecords = this.items.length;
        //     }
        //   }
        // }
        console.log('else ids',itemsIds ,'inside remaiend ids',this.remainedIds);
        this.refreshItems(itemsIds);
        this.showNotific = false;
        //this.filteredItems = [];
      }
    });
  }

  confirmDelete() {
    this.newValueService.deleteItems(this.type, { 'items_id': this.remainedIds, 'confirm': 1 }).subscribe(res => {
      console.log('resconfirmDelete', res);
      console.log('remainedIds', this.remainedIds);
      this.closeDeleteModal();
      this.remainedIds = res['items_id'];
      let i = this.getItemIndex(this.itemIdToDelete);
      this.items.splice(i, 1);
      this.table._totalRecords = this.items.length;
      this.refreshItems(this.remainedIds);
      this.filteredItems = [];
      console.log('confirmDeleteItems', this.items);
    });
  }

  deleteItem() {
    this.newValueService.deleteItems(this.type, { 'items_id': [this.itemIdToDelete], 'confirm': 0 }).subscribe(res => {
      console.log('res', res);

      if (!res['success']) {
        if (res['related']) {
          console.log('inside if');
          this.notification = res['massage'];
          this.remainedIds = res['items_id'];
          this.showNotific = true;
          // alert(res['massage']);
        }
        this.refreshItems();
      } else {
        console.log('inside else',this.itemIdToDelete);
        this.closeDeleteModal();
        this.remainedIds = res['items_id'];
        let i = this.getItemIndex(this.itemIdToDelete);
        this.items.splice(i, 1);
        this.table._totalRecords = this.items.length;
        this.refreshItems();
       
      }
    });

  }

  refreshItems(ids=[]) {
    console.log('refreshItems',ids);
    if( ids.length>0){
      for (let i = 0; i < this.items.length; i++) {
        for (let id of ids) {
          if (this.items[i].id === id) {
            console.log('refreshItems',id);
            this.items.splice(i, 1);
            this.table._totalRecords = this.items.length;
          }
        }
      }
    
    }
    // else{
    //   const items2 = this.items;
    //   this.items = items2.slice(0);
    //   this.table._totalRecords = this.items.length;
    //   console.log('item',this.items);
    // }
    const items2 = this.items;
    this.items = items2.slice(0);
    this.filteredItems = [];
    console.log('items',this.items);
    console.log('refreshItemsLastItems',this.items);
  }
  getItemIndex(msgId) {
    for (let i = 0; i < this.items.length; i++) {
      if (this.items[i].id === msgId) {
        console.log('index', i);
        return i;
      }
    }
  }



  clearAll() {
    for (let col of this.cols) {
      this.table.filter(null, col.field, col.filterMatchMode);
    }

    this.table.filterGlobal(null, 'contains');
    $('.ui-table-globalfilter-container input').val(null);
    console.log($('.ui-column-filter').val());
    $('.ui-column-filter').val(null);
    this.dropdown = '';
    this.multiSelect = '';
  }

  getLabelByValue(ids: number[], array: { 'value': number, 'label': string }[]) {
    let labels = '';
    if (ids.length > 0) {
      for (let id of ids) {
        for (let item of array) {
          if (id === item.value) {
            if(item.label !== '')
              labels = labels + item.label + ', ';
            else
              labels = labels + item.label;
          }
        }
      }
    }
    return labels;
  }


  ngOnDestroy() {
    console.log("in on destroy");
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }


}
