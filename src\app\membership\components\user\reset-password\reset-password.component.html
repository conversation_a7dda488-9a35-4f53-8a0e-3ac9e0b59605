<app-pre-loader [show]="loader"></app-pre-loader>
<div class="home-page-content">
  <div class="container mobile-padding">
    <div class="row">
      <form  [formGroup]="resetPasswordForm"
             #form="ngForm"
             class="form-horizontal validate-form"
             (ngSubmit)="verify()">
        <br>
        <h1>Reset Password</h1>
        <br>
        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && isInvalid('password')) || errorMessage}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':resetPasswordForm.controls['password'].value}">
            <input  type="{{ type }}" formControlName="password" class="form-control" id="password"  (keydown)="errorMessage=null">
            <span type="button" 
              (click)="toggleShowPassword()"
              class="fa fa-fw password-eye-icon"  
              [ngClass]="{
                'fa-eye' : show,
                'fa-eye-slash' : !show
              }">
            </span>
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Enter New Password</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('password')">Required</span>
            <span class="error-message " *ngIf="errorMessage">{{errorMessage}}</span>
          </div>
        </div>
        <div class="text-center">
          <button type="submit" class="btn btn-success">Send</button>
        </div>
      </form>
    </div>
  </div>
</div>
