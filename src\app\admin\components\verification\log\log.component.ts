import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { VerificationService } from 'app/admin/services/verification.service';
import { Language } from 'app/admin/models/language';
import { Subject } from 'rxjs/Subject';
import { LanguageService } from 'app/admin/services/language.service';
import { Table } from 'primeng/table';
import { FilterUtils } from 'primeng/utils';
import { Calendar } from 'primeng/calendar';
declare var $: any;

@Component({
  selector: 'app-log',
  templateUrl: './log.component.html',
  styleUrls: ['./log.component.css']
})
export class LogComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  @ViewChild('cl') calendar: Calendar;
  rangeDates: Date[];
  private ngUnsubscribe: Subject<any> = new Subject();
  order = [false, false, false, false];
  displayModal  = false;
  transactionToPreview;
  transactionsArray = [];
  filteredTransactions = [] ;
  languagesArray: Language[] = [];
  transCount: number;
  currentLangId;
  adminEntry;
  loading = true;
  types = [
   {'value': null, 'label': ''},
   {'value': 'UNIVERSITY', 'label': 'University'},
   {'value': 'SKILL', 'label': 'Skill'},
   {'value': 'MAJOR', 'label': 'Major'},
   {'value': 'MINOR', 'label': 'Minor'},
   {'value': 'JOB_TITLE', 'label': 'Job Title'},
   {'value': 'JOB_TITLE_SYNONYMS', 'label': 'Job Title Synonyms'},
  ];
   type;
  logTableIsEmpty: boolean;
  message = '';

constructor(private translate: TranslateService,
            private verifictionService: VerificationService,
            private languageService: LanguageService) {
  translate.addLangs(['en', 'ar']);
  translate.setDefaultLang('en');
  const browserLang = translate.getBrowserLang();
  translate.use(browserLang.match(/en|ar/) ? browserLang : 'en');
  if (this.translate.currentLang === 'en') {this.currentLangId = 1;
  } else { this.currentLangId = 2; }

  FilterUtils['isBetween'] = (value: any, filter: any): boolean => {
    let newDate = new Date(value);
    console.log(newDate);
    filter = new Date(filter);
    if (this.rangeDates['1'] === null) {
        if (newDate === filter) {
          let startDate = new Date(this.rangeDates['0']);
          if (startDate <= newDate ) {
            return true;
           }
        }
    } else {
           let startDate =new Date(this.rangeDates['0']);
           let finishDate =new Date(this.rangeDates['1']);
           console.log(startDate, finishDate);
                if (startDate <= newDate && finishDate >= newDate) {                                                 {
                     return true;
                }
      }
    }
  };
}

ngOnInit() {
  this.getLanguages();
  this.getLogData();

}

getLogData() {
    // getting log data
    this.verifictionService.getLogData().takeUntil(this.ngUnsubscribe).subscribe((res) => {
      console.log('res', res, res['adminVerificationLogs']);
      let temp = res['adminVerificationLogs'];
      if (res['adminVerificationLogs'] !== undefined) {
        for (let t of temp) {
          let trans = [], minor_trans = [], major_trans = [], admin_minor_trans = [],
              admin_major_trans = [], skill_parents = [], skill_category_trans = [], experience_fields = [],
              admin_experience_fields = [], admin_job_title_trans = [], job_title_synonyms_trans = [],
              admin_job_title_synonyms_trans = [];
          if (t.user_operation.code === 'UNIVERSITY') {
            if (t.admin_operation.code !== 'END_TRANSACTION') {
              trans = t.verified_entry.institution_translation;
              this.transactionsArray.push({
                // 'trans_id'      : t.id,
                'trans_id'      : t.system_action_log_id,
                'id'                : t.cv_id?t.cv_id:t.adv_id,
                'cv_id'             : t.cv_id,
                'adv_id'            : t.adv_id,
                'type'          : t.user_operation.code,
                'time'          : t.user_entry_time,
                'date'          : t.user_entry_date,
                'admin'         : t.handled_by,
                'admin_time'    : t.admin_entry_time_stamp,
                'duration'      : t.execution_duration,
                'url'           : t.unverified_entry.url,
                'country'       : t.unverified_entry.country,
                'city'          : t.unverified_entry.city,
                'street_address': t.unverified_entry.street_address,
                'verified'      : t.unverified_entry.verified,
                'name'          : t.unverified_entry.institution_translation[0].name,
                'admin_entry': {
                  'trans'   : trans,
                  'url'     : t.verified_entry.url ,
                  'country' : t.verified_entry.country,
                  'city'    : t.verified_entry.city,
                  'street_address': t.verified_entry.street_address,
                  'operation'     : t.admin_operation.code
                }
              });
            } else {
              this.transactionsArray.push({
                // 'trans_id'  : t.id,
                'trans_id'      : t.system_action_log_id,
                'id'                : t.cv_id?t.cv_id:t.adv_id,
                'cv_id'             : t.cv_id,
                'adv_id'            : t.adv_id,
                'type'      : t.user_operation.code,
                'time'      : t.user_entry_time,
                'date'      : t.user_entry_date,
                'admin'     : t.handled_by,
                'admin_time': t.admin_entry_time_stamp,
                'duration'  : t.execution_duration,
                'url'       : t.unverified_entry.url,
                'country'   : t.unverified_entry.country,
                'city'      : t.unverified_entry.city,
                'verified'  : t.unverified_entry.verified,
                'name'      : t.unverified_entry.institution_translation[0].name,
                'admin_entry': {
                  'trans'   : [],
                  'url'     : ' ' ,
                  'country' : ' ',
                  'city': '',
                  'street_address': ' ',
                  'operation': t.admin_operation.code
                }
              });
            }

          } else if (t.user_operation.code === 'MAJOR') {
               if (t.admin_operation.code !== 'END_TRANSACTION') {
                trans = t.verified_entry.education_field_trans;
                this.transactionsArray.push({
                  // 'trans_id'  : t.id,
                  'trans_id'      : t.system_action_log_id,
                  'id'                : t.cv_id?t.cv_id:t.adv_id,
                  'cv_id'             : t.cv_id,
                  'adv_id'            : t.adv_id,
                  'type'      : t.user_operation.code,
                  'time'      : t.user_entry_time,
                  'date'      : t.user_entry_date,
                  'admin'     : t.handled_by,
                  'admin_time': t.admin_entry_time_stamp,
                  'duration'  : t.execution_duration,
                  'name'      : t.unverified_entry.name,
                //  'parent_id' : t.unverified_entry.major_parent_id,
                  // 'parent'    : t.unverified_entry.minor_name,
                  'verified'  : t.unverified_entry.verified,
                  'admin_entry': {
                    'trans'    : trans,
                  //  'parent-id': t.verified_entry.major_parent.id,
                  //  'parent'   : t.verified_entry.major_parent.major_parent_translation[0],
                    'operation': t.admin_operation.code
                  }
                });
               } else {
                this.transactionsArray.push({
                  // 'trans_id'  : t.id,
                  'trans_id'      : t.system_action_log_id,
                  'id'                : t.cv_id?t.cv_id:t.adv_id,
                  'cv_id'             : t.cv_id,
                  'adv_id'            : t.adv_id,
                  'type'      : t.user_operation.code,
                  'time'      : t.user_entry_time,
                  'date'      : t.user_entry_date,
                  'admin'     : t.handled_by,
                  'admin_time': t.admin_entry_time_stamp,
                  'duration'  : t.execution_duration,
                  'name'      : t.unverified_entry.name,
                //  'parent_id' : t.unverified_entry.major_parent_id,
                  // 'parent'    : t.unverified_entry.minor_name,
                  'verified'  : t.unverified_entry.verified,
                  'admin_entry': {
                    'trans'    : ' ',
                    // 'parent-id': null,
                    // 'parent'   : ' ',
                    'operation': t.admin_operation.code
                  }
                });
               }

                }  else if (t.user_operation.code === 'SKILL') {
                  if (t.admin_operation.code !== 'END_TRANSACTION') {
                    trans = t.verified_entry.skill_type_trans;
                    skill_category_trans = t.verified_entry.skill_category.skill_category_translation;
                    skill_parents =  t.verified_entry.job_title;
                    this.transactionsArray.push({
                    //  'trans_id'  : t.id,
                     'trans_id'      : t.system_action_log_id,
                     'id'                : t.cv_id?t.cv_id:t.adv_id,
                     'cv_id'             : t.cv_id,
                     'adv_id'            : t.adv_id,
                     'type'      : t.user_operation.code,
                     'time'      : t.user_entry_time,
                     'date'      : t.user_entry_date,
                     'admin'     : t.handled_by,
                     'admin_time': t.admin_entry_time_stamp,
                     'duration'  : t.execution_duration,
                     'name'      : t.unverified_entry.skill_type_trans[0].name,
                     'skill_parent': t.unverified_entry.skill_type_trans[0].name,
                     'skill_category_id'  : t.unverified_entry.skill_category_id,
                     'skill_category'     : t.unverified_entry.skill_category.skill_category_translation[0].name,
                     'verified'           : t.unverified_entry.verified,
                     'admin_entry': {
                       'trans'   : trans,
                       'skill_parents': skill_parents,
                       'skill_category_trans': skill_category_trans,
                       'operation': t.admin_operation.code
                     }
                    });
                  } else {
                    this.transactionsArray.push({
                      // 'trans_id'  : t.id,
                      'trans_id'      : t.system_action_log_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'      : t.user_operation.code,
                      'time'      : t.user_entry_time,
                      'date'      : t.user_entry_date,
                      'admin'     : t.handled_by,
                      'admin_time': t.admin_entry_time_stamp,
                      'duration'  : t.execution_duration,
                      'name'      : t.unverified_entry.skill_type_trans[0].name,
                      'skill_parent': t.unverified_entry.skill_type_trans[0].name,
                      'skill_category_id'  : t.unverified_entry.skill_category_id,
                      'skill_category'     : t.unverified_entry.skill_category.skill_category_translation[0].name,
                      'verified'           : t.unverified_entry.verified,
                      'admin_entry': {
                        'trans'   : [],
                        'skill_parents': [],
                        'skill_category_trans': [],
                        'operation': t.admin_operation.code
                      }
                    });
                  }

                }  else if (t.user_operation.code === 'MINOR') {
                  minor_trans = t.unverified_entry.education_field_trans;
                  major_trans = t.unverified_entry.major_education_field[0].education_field_trans;
                  if (t.admin_operation.code !== 'END_TRANSACTION') {
                    admin_major_trans = t.verified_entry.education_field_trans;
                    admin_minor_trans =  t.verified_entry.major_education_field[0].education_field_trans;
                //    if(t.verified_entry.major.major_parent==null)console.log('parent',t);
                    this.transactionsArray.push({
                      // 'trans_id'       : t.id,
                      'trans_id'      : t.system_action_log_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'           : t.user_operation.code,
                      'time'           : t.user_entry_time,
                      'date'           : t.user_entry_date,
                      'admin'          : t.handled_by,
                      'admin_time'     : t.admin_entry_time_stamp,
                      'duration'       : t.execution_duration,
                      'name'           : t.unverified_entry.name,
                      'minor_trans'    : minor_trans,
                   //   'major_parent_id': t.unverified_entry.major.major_parent_id,
                      'major_trans'    : major_trans,
                      'verified'       : t.unverified_entry.verified,
                      'admin_entry': {
                        // 'major_parent_id': t.verified_entry.major.major_parent_id,
                        // 'major_parent'   :t.verified_entry.major.major_parent_id? t.verified_entry.major.major_parent.major_parent_translation[0].name:null,
                        'major_trans'    : admin_major_trans,
                        'minor_trans'   : admin_minor_trans,
                        'operation'      : t.admin_operation.code
                      }
                    });
                  } else {
                    this.transactionsArray.push({
                      // 'trans_id'       : t.id,
                      'trans_id'      : t.system_action_log_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'           : t.user_operation.code,
                      'time'           : t.user_entry_time,
                      'date'           : t.user_entry_date,
                      'admin'          : t.handled_by,
                      'admin_time'     : t.admin_entry_time_stamp,
                      'duration'       : t.execution_duration,
                      'name'           : t.unverified_entry.name,
                      'minor_trans'    : minor_trans,
                    //  'major_parent_id': t.unverified_entry.major.major_parent_id,
                      'major_trans'    : major_trans,
                      'verified'       : t.unverified_entry.verified,
                      'admin_entry': {
                        // 'major_parent_id': null,
                        // 'major_parent'   : ' ',
                        'major_trans'    : [],
                        'minor_trans'    : [],
                        'operation'      : t.admin_operation.code
                      }
                    });
                  }

                } else if (t.user_operation.code === 'JOB_TITLE') {
                  experience_fields = t.unverified_entry.experience_fields;
                  if (t.admin_operation.code !== 'END_TRANSACTION') {
                    if(t.verified_entry==null)console.log('log',t);
                    admin_experience_fields = t.verified_entry.experience_fields;
                    admin_job_title_trans   = t.verified_entry.job_title_translation;
                    this.transactionsArray.push({
                      // 'trans_id'       : t.id,
                      'trans_id'      : t.system_action_log_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'           : t.user_operation.code,
                      'time'           : t.user_entry_time,
                      'date'           : t.user_entry_date,
                      'admin'          : t.handled_by,
                      'admin_time'     : t.admin_entry_time_stamp,
                      'duration'       : t.execution_duration,
                      'name'           : t.unverified_entry.job_title_translation[0].name,
                      'major_job_title_id': t.unverified_entry.major_job_title_id,
                      'experience_fields' : experience_fields,
                      'verified'       : t.unverified_entry.verified,
                      'admin_entry': {
                        'experience_fields': admin_experience_fields,
                        'job_title_trans'  : admin_job_title_trans,
                        'major_job_title_id':  t.verified_entry.major_job_title_id,
                        'operation'      : t.admin_operation.code
                      }
                    });
                  } else {
                    this.transactionsArray.push({
                      // 'trans_id'       : t.id,
                      'trans_id'      : t.system_action_log_id,
                      // 'cv_id'          : t.cv_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'           : t.user_operation.code,
                      'time'           : t.user_entry_time,
                      'date'           : t.user_entry_date,
                      'admin'          : t.handled_by,
                      'admin_time'     : t.admin_entry_time_stamp,
                      'duration'       : t.execution_duration,
                      'name'           : t.unverified_entry.job_title_translation[0].name,
                      'major_job_title_id': t.unverified_entry.major_job_title_id,
                      'experience_fields' : experience_fields,
                      'verified'       : t.unverified_entry.verified,
                      'admin_entry': {
                        'experience_fields': [],
                        'job_title_trans'  : [],
                        'major_job_title_id':  t.verified_entry? t.verified_entry.major_job_title_id:null,
                        'operation'      : t.admin_operation.code
                      }
                    });
                  }
                }  else if (t.user_operation.code === 'JOB_TITLE_SYNONYMS') {
                  job_title_synonyms_trans = t.unverified_entry.job_title_synonyms_trans;
                  if (t.admin_operation.code !== 'END_TRANSACTION') {
                    admin_job_title_synonyms_trans =  t.verified_entry.job_title_synonyms_trans;
                    admin_job_title_trans = t.verified_entry.job_title.job_title_translation,
                    this.transactionsArray.push({
                      // 'trans_id'       : t.id,
                      'trans_id'      : t.system_action_log_id,
                      // 'cv_id'          : t.cv_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'           : t.user_operation.code,
                      'time'           : t.user_entry_time,
                      'date'           : t.user_entry_date,
                      'admin'          : t.handled_by,
                      'admin_time'     : t.admin_entry_time_stamp,
                      'duration'       : t.execution_duration,
                      'name'           : t.unverified_entry.job_title_synonyms_trans[0].name,
                      'job_title_synonyms_trans': job_title_synonyms_trans,
                      'job_title_id'   : t.unverified_entry.job_title_id,
                      'job_title_name' : t.unverified_entry.job_title.job_title_translation[0].name,
                      'major_trans'    : major_trans,
                      'verified'       : t.unverified_entry.verified,
                      'admin_entry': {
                        'job_title_synonyms_trans': admin_job_title_synonyms_trans,
                        'job_title_id'   : t.verified_entry.job_title_id,
                        'job_title_trans': admin_job_title_trans,
                        'operation'      : t.admin_operation.code
                      }
                    });
                  } else {
                    this.transactionsArray.push({
                      // 'trans_id'       : t.id,
                      'trans_id'      : t.system_action_log_id,
                      // 'cv_id'          : t.cv_id,
                      'id'                : t.cv_id?t.cv_id:t.adv_id,
                      'cv_id'             : t.cv_id,
                      'adv_id'            : t.adv_id,
                      'type'           : t.user_operation.code,
                      'time'           : t.user_entry_time,
                      'date'           : t.user_entry_date,
                      'admin'          : t.handled_by,
                      'admin_time'     : t.admin_entry_time_stamp,
                      'duration'       : t.execution_duration,
                      'name'           : t.unverified_entry.job_title_synonyms_trans[0].name,
                      'job_title_synonyms_trans': job_title_synonyms_trans,
                      'job_title_id'   : t.unverified_entry.job_title_id,
                      'job_title_name' : t.unverified_entry.job_title.job_title_translation[0].name,
                      'major_trans'    : major_trans,
                      'verified'       : t.unverified_entry.verified,
                      'admin_entry': {
                        'job_title_synonyms_trans': [],
                        'job_title_id'   : null,
                        'job_title_trans': [],
                        'operation'      : t.admin_operation.code
                      }
                    });
                  }
                }


        }
        console.log('transactionsArray', this.transactionsArray);
      } else {
        this.message = 'There are no admin verification logs';
        alert('There are no admin verification logs');
      }
       this.table.filter(1, 'id', 'startsWith');
      this.table.filter(null, 'id', 'startsWith');
      // this.filteredTransactions = this.transactionsArray;
      this.loading = false;
    }, (error: Error) => {  this.loading = false; });

}




getLanguages() {
  this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
    console.log(res);
    let temp = res['data'];
    for ( let lang of temp) {
      this.languagesArray.push({
        'id'  : lang.id,
        'name': lang.name
      });
    }

    console.log('languages array', this.languagesArray);
    console.log('lang arr length', this.languagesArray.length);
});
}


closeModal() {
  this.displayModal  = false;
  $('#transModal').hide();
  $('div.modal-backdrop.fade.in').remove();

}



displayTModal(t) {
  if (t.type === 'UNIVERSITY') {
    this.transactionToPreview = {
      'id'            : t.trans_id,
      'cv_id'             : t.cv_id,
      'adv_id'            : t.adv_id,
      'type'          : t.type,
      'time'          : t.time,
      'date'          : t.date,
      'url'           : t.url,
      'country'       : t.country,
      'city'          : t.city,
      'street_address': t.street_address,
      'verified'      : t.verified,
      'name'          : t.name
      };
      this.adminEntry = {
        'trans'    : t.admin_entry.trans,
        'operation': t.admin_entry.operation,
        'date'     : t.admin_time,
        'admin'    : t.admin,
        'url'      : t.admin_entry.url,
        'country'  : t.admin_entry.country,
        'city'     : t.admin_entry.city,
        'street_address': t.admin_entry.street_address
      };
  } else if (t.type === 'SKILL') {
      this.transactionToPreview = {
      'id'                 : t.trans_id,
      'cv_id'             : t.cv_id,
      'adv_id'            : t.adv_id,
      'type'               : t.type,
      'time'               : t.time,
      'date'               : t.date,
      'name'               : t.name,
      'skill_parent'       : t.skill_parent,
      'skill_category_id'  : t.skill_category_id,
      'skill_category'     : t.skill_category,
      'verified'           : t.verified
    };
    this.adminEntry = {
      'trans'               : t.admin_entry.trans,
      'skill_parents'       : t.admin_entry.skill_parents,
      'skill_category_trans': t.admin_entry.skill_category_trans,
      'operation'           : t.admin_entry.operation,
      'date'                : t.admin_time,
      'admin'               : t.admin
    };
  } else if (t.type === 'MAJOR') {
    this.transactionToPreview = {
      'id'        : t.trans_id,
      'cv_id'             : t.cv_id,
      'adv_id'            : t.adv_id,
      'type'      : t.type,
      'time'      : t.time,
      'date'      : t.date,
      'name'      : t.name,
    //  'parent_id' : t.parent_id,
      'verified'  : t.verified
    };
    this.adminEntry = {
      'trans'    : t.admin_entry.trans,
      // 'parent_id': t.admin_entry.parent_id,
      // 'parent'   : t.admin_entry.parent,
      'operation': t.admin_entry.operation,
      'date'     : t.admin_time,
      'admin'    : t.admin
    };

  }  else if (t.type === 'MINOR') {
    this.transactionToPreview = {
      'id'        : t.trans_id,
      'cv_id'             : t.cv_id,
      'adv_id'            : t.adv_id,
      'type'      : t.type,
      'time'      : t.time,
      'date'      : t.date,
      'major_trans'      : t.major_trans,
    //  'major_parent_id'  : t.major_parent_id,
      'minor_trans'      : t.minor_trans,
      'verified'         : t.verified
    };
    this.adminEntry = {
      // 'major_parent_id': t.admin_entry.major_parent_id ,
      // 'major_parent'   : t.admin_entry.major_parent,
      'major_trans'    : t.admin_entry.major_trans,
      'minor_trans'    : t.admin_entry.minor_trans,
      'operation'      : t.admin_entry.operation,
      'date'           : t.admin_time,
      'admin'          : t.admin
    };

  }  else if (t.type === 'JOB_TITLE') {
    this.transactionToPreview = {
      'id'        : t.trans_id,
      // 'cv_id'     : t.cv_id,
      'cv_id'             : t.cv_id,
      'adv_id'            : t.adv_id,
      'type'      : t.type,
      'time'      : t.time,
      'date'      : t.date,
      'name'      : t.name,
      'major_job_title_id': t.major_job_title_id,
      'experience_fields' : t.experience_fields,
      'verified'         : t.verified
    };
    this.adminEntry = {
      'experience_fields' : t.admin_entry.experience_fields,
      'job_title_trans'   : t.admin_entry.job_title_trans,
      'major_job_title_id': t.admin_entry.major_job_title_id,
      'operation'         : t.admin_entry.operation,
      'date'              : t.admin_time,
      'admin'             : t.admin
    };
  }  else if (t.type === 'JOB_TITLE_SYNONYMS') {
    this.transactionToPreview = {
      'id'        : t.trans_id,
      // 'cv_id'     : t.cv_id,
      'cv_id'             : t.cv_id,
      'adv_id'            : t.adv_id,
      'type'      : t.type,
      'time'      : t.time,
      'date'      : t.date,
      'name'      : t.name,
      'job_title_synonyms_trans': t.job_title_synonyms_trans,
      'job_title_id'            : t.job_title_id,
      'job_title_name'          : t.job_title_name,
      'major_trans'             : t.major_trans,
      'verified'                : t.verified
    };
    this.adminEntry = {
      'job_title_synonyms_trans': t.admin_entry.job_title_synonyms_trans,
      'job_title_id'   : t.admin_entry.job_title_id,
      'job_title_trans': t.admin_entry.job_title_trans,
      'operation'      : t.admin_entry.operation,
      'date'           : t.admin_time,
      'admin'          : t.admin
    };
  }

  console.log('trans t prev', this.transactionToPreview);
  console.log('admin entry', this.adminEntry);
  this.displayModal  = true;

}



onDateSelect(value) {
  this.table.filter(this.formatDate(value), 'date', 'contains');
}

formatDate(date) {
  let month = date.getMonth() + 1;
  let day = date.getDate();

  if (month < 10) {
      month = '0' + month;
  }

  if (day < 10) {
      day = '0' + day;
  }

  return date.getFullYear() + '-' + month + '-' + day;
}


clearAll() {
  this.table.filter(null, 'id', 'startsWith');
  this.table.filter(null, 'trans_id', 'contains');
  this.table.filter('', 'date', 'contains');
  this.table.filter(null, 'type', 'equals');
  this.table.filter(null, 'time', 'contains');
  this.table.filter(null, 'admin', 'contains');
  this.table.filter(null, 'admin_time', 'contains');
  this.table.filter(null, 'duration', 'contains');
  this.table.filterGlobal(null, 'contains');
  $('span.ui-column-filter.ui-calendar input').val(null);
  $('.ui-table-globalfilter-container input').val(null);
  console.log($('.ui-column-filter').val());
  $('.ui-column-filter').val(null);
   this.type = '';
   this.rangeDates = [new Date(''), new Date('')];
   this.calendar.onClearButtonClick('');
 }

ngOnDestroy(): void {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();
}

}
