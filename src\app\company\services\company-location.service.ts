import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from 'rxjs';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class CompanyLocationService {

  url_name = '';
  url_add = '';
  url_edit = '';
  url_main_office = '';
  url_get = '';
  url_delete = '';
  url_order = '';
  constructor(private http: HttpClient , private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url_name = data + 'company/get_main_name';
      this.url_add = data + 'company/location';
      this.url_edit = data + 'company/location';
      this.url_main_office = data + 'company/set_location_main';
      this.url_get = data + 'company/get_all_location';
      this.url_delete = data + 'company/location';
      this.url_order = data + 'company/locations/order';
    });
   }

   getCompanyName(companyId) {
     return this.http.get(this.url_name + '/' + companyId);
   }

   getCompanyLocationData(companyId) {
     console.log('url_get', this.url_get + '/' + companyId);
     return this.http.get(this.url_get + '/' + companyId);
   }

   deleteCompanyLocationData(companyLocationId) {
     console.log('url_delete', this.url_delete + '/' + companyLocationId);
     return this.http.delete(this.url_delete + '/' + companyLocationId);
   }

   addCompanyLocationData(companyLocation) {
     console.log('url_add', this.url_add );
      return this.http.post(this.url_add, companyLocation);
   }

   editCompanyLocationData(companyLocation) {
     console.log('locationd_id', companyLocation.location_id);
     console.log('url_edit', this.url_edit + '/' + companyLocation.location_id);
     return this.http.put(this.url_edit + '/' + companyLocation.location_id, companyLocation);

   }

   selectCompanyMain(mainOffice) {
     console.log('url_main_office', this.url_main_office + '/' + mainOffice.id);
     return this.http.put(this.url_main_office + '/' + mainOffice.id, mainOffice);
   }

   orderCompanyLocations(companyId, orderdLocations) {
    console.log('url_order', this.url_order + '/' + companyId );
    console.log('orderdLocations', orderdLocations);
    return this.http.post(this.url_order + '/' + companyId, orderdLocations);
   }

}
