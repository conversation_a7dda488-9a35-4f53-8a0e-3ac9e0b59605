import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams, HttpParameterCodec, HttpUrlEncodingCodec } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from 'rxjs';
import { Observable } from 'rxjs/Observable';

@Injectable({
  providedIn: 'root'
})
export class AdvrsViewService {
  base_url='';
  url = '';
  fiters_data = '';
  apply_url = '';
  advData = '';
  job_titles_url = ''
  codec = new HttpUrlEncodingCodec;
  constructor(private http: HttpClient ,
              private privateSharedURL: ExportUrlService,
  
  ) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url = data + 'jobAdvs/publish';
      this.advData = data + 'jobAdvertisement';
      this.apply_url = data + 'emp_application';
      this.job_titles_url = data + 'search/jobs_key_words/'
      this.base_url=data;
    });

  }

  getAdvrs(pgsize, pgnum, stat, lang){
    let ColList = stat
    let params = new HttpParams();
    params = params.append('pgsize', pgsize);
    params = params.append('pgnum', pgnum);
    params = params.append('lang', lang);
    
    ColList.forEach((ColName:string) =>{
      params = params.append(`requirements[]`, ColName);
    })

    /* params = params.append('requirements[]', ColList.join(', ')); */
    if(ColList.length > 0) {
      return this.http.get(this.url,{ params: params})
    }
    
  }


  jsEncode(param: string){
    return encodeURIComponent(param);
  }

  jsDecode(param: string){
    return decodeURIComponent(this.jsEncode(param));
  }

  getFilterData(pgsize, pgnum, lang, filters, fitlerFormData, isMobile , columns?) {
   // this.fiters_data = 'http://3.20.142.149/search/jobs';
   this.privateSharedURL.publicUrl.take(1).subscribe(data => {
    this.fiters_data = data + 'search/jobs';
   });
  // this.fiters_data = ' http://cveek-env.eba-tkxc35yx.us-east-2.elasticbeanstalk.com/search/jobs';
    let params = new HttpParams();
    let body_req = [];
   
    params = params.append('pgsize', pgsize);
    params = params.append('pgnum', pgnum);
    params = params.append('lang', lang);
    params = params.append('isMobile', isMobile);
    
    
    if(columns && columns.length > 0) {

      let ColList = columns

      body_req = filters;
    
      ColList.forEach((ColName:string) =>{
        params = params.append(`requirements[]`, ColName);
      })
      
      params = params.append('have_data',fitlerFormData)

      return this.http.post(this.fiters_data,body_req,{ params: params})

    } else {
      let encodedName = this.jsDecode('?requirements=[]');
      params = params.append('have_data',fitlerFormData)
      body_req = filters;
      return this.http.post(this.fiters_data+encodedName,body_req,{ params: params})
    }
   
  }

  getPostData(job_advertisement_id, translated_languages_id) {
    return this.http.get(this.advData + '/' + job_advertisement_id + '/' + translated_languages_id);
  }

  //get job title and country
  // if we pass country=true , means that send just country array
  getjobTitles(LangId,countryOnly? , query?) {
    if(countryOnly === true){
      return this.http.get(this.job_titles_url + LangId+'?country=true');
    }

    if (query) {
    return this.http.get(this.job_titles_url + LangId + '/?job_title=' + query);
    } else {
          return this.http.get(this.job_titles_url + LangId );
    }

  }

  applyForJob(AdvId) {
    return this.http.post(this.apply_url + '?adv_id=' +AdvId, [])
  }
}

