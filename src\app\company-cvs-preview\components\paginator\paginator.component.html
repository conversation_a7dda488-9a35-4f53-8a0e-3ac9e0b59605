<div class="cust-paginator">
    <ul>
        <li class="pages-status">{{this.currentpage}} of {{this.totalPages}}</li>
        <li (click)="navigate('first')" class="page-block" [ngClass]="{'disabled': currentpage == 1 }">
            <span class="pi pi-step-backward"></span>
        </li>
        <li (click)="navigate('previous')" class="page-block"  [ngClass]="{'disabled': currentpage == 1 }">
            <span class="pi pi-caret-left"></span>
        </li>
        <li (click)="navigate('byId',b.id)" class="page-block {{b.status}} {{b.visibility}}" *ngFor="let b  of blocks">{{b.id}}</li>
        <li (click)="navigate('next')" class="page-block"  [ngClass]="{'disabled': currentpage == totalPages }">
            <span class="pi pi-caret-right"></span>
        </li>
        <li (click)="navigate('last')" class="page-block"  [ngClass]="{'disabled': currentpage == totalPages }">
            <span class="pi pi-step-forward"></span>
        </li>
    </ul>
</div>