

<div  class="modal-body-container" >
  <div class="modal-body " >

    <div *ngIf="mode === 'create' || mode === 'edit'">

      <form  *ngIf="languagesArray.length !== 0"  [formGroup]="faqForm" (ngSubmit)="saveQuestion(faqForm.value)" >
        <h3 class="heading" *ngIf="mode === 'create' && openedFromSidebar" translate>faqs.labels.AddNewFAQ</h3>
        <div class="row type-row" *ngIf="mode === 'edit'">
          <div class="col-sm-2 col-xs-6 type-label   text-right">
            <label for="type" class="control-label" translate>faqs.Type<span *ngIf="type.touched && type.invalid" class="required">**</span></label>
          </div>
          <div class="col-md-offset-2 col-sm-8 col-xs-6 type-value" translate>
            {{ question.type }}
          </div>
        </div>

        <div *ngIf="mode === 'create'" class="form-group type-form-group">
          <div class="row ">
            <div class=" col-sm-2 col-xs-12   text-right">
              <label for="type" class="control-label" translate>faqs.Type<span *ngIf="type.touched && type.invalid" class="required">**</span></label>
            </div>
            <div class=" col-md-offset-1 col-sm-4 col-xs-6">
              <label class="container radio-choose" for="user" translate>faqs.User
                <input type="radio" formControlName="type" id="user" value="User Faq" [disabled]="true">
                <span class="checkmark"></span>
              </label>
            </div>
            <div class="col-sm-4 col-xs-6">
              <label class="container radio-choose" for="company" translate>faqs.Company
                <input type="radio" formControlName="type" id="company" value="Company Faq" [disabled]="true">
                <span class="checkmark"></span>
              </label>
            </div>
          </div>
          <div *ngIf="type.touched && type.invalid" >
            <div *ngIf="type.errors.required" class="alert alert-danger"  translate>faqs.errorMessages.TypeRequired</div>
          </div>
        </div>

       <div class="row">
        <div class="form-group cat-form-group"  >
          <div class="col-md-2  text-right">
            <label for="category" translate>faqs.Category<span *ngIf="faq_category_id.touched && faq_category_id.invalid" class="required">**</span></label>
          </div>
          <div class="col-md-6" >
            <p-dropdown [options]="( type.value === 'User Faq') ? categories.user_categories[currentLangId - 1] : categories.employer_categories[currentLangId - 1]" formControlName="faq_category_id" [required]="true"[filter]="true">
              <ng-template let-category pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                  </div>
              </ng-template>
            </p-dropdown>
            <div *ngIf="faq_category_id.touched && faq_category_id.invalid" >
                <div *ngIf="faq_category_id.errors.required" class="alert alert-danger"  translate>faqs.errorMessages.CategoryRequired</div>
            </div>
          </div>
        </div>
       </div>


         <div class="row">
          <div class="form-group order-form-group"  title="Order should be the same for the question and all its translations">
            <div class="col-md-2  text-right">
              <label for="order"  translate>faqs.Order<span *ngIf="order.touched && order.invalid" class="required">**</span></label>
            </div>
            <div class="col-md-6">
              <p-dropdown type="number"  [options]="orders" formControlName="order"  [filter]="true">
                <ng-template let-order pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height:25px;">
                        <div style="font-size:14px;float:left;margin-top:4px" type="number">{{order.label}}</div>
                    </div>
                </ng-template>
              </p-dropdown>
              <div *ngIf="order.touched && order.invalid" >
                <div *ngIf="order.errors.required" class="alert alert-danger" translate>faqs.errorMessages.OrderRequired</div>
              </div>
            </div>
          </div>

          <div class="col-md-3 col-sml-3 col-xs-3" >
            <div class="form-check" >
              <input formControlName="active_temp"  name="activation" type="checkbox"
                      class="form-check-input" id="activation" style="margin-right: 10px;"  />
              <label class="form-check-label" for="activation" translate>faqs.Activation</label>
            </div>
          </div>


        </div>
        <br><br>



        <ul class="nav nav-tabs">
            <li *ngFor="let lang of languagesArray" role="presentation" class="btn" [class.active]="lang.id === currentLangId"
                (click)="changeLang(lang.id)" translate>{{ "faqs.languages."+lang.name}}</li>
        </ul>




        <div  formArrayName="faq_trans" *ngFor="let item of languagesArray; let i= index">
          <div [formGroupName]="i" *ngIf="item.id === currentLangId ">

              <div class="form-group row" style="margin-top: 25px;">
                 <div class="col-md-2  text-right">
                  <label for="question"  translate>faqs.Question
                    <span *ngIf="faq_trans.controls[i].controls.question.touched && faq_trans.controls[i].controls.question.invalid" class="required">**</span>
                  </label>
                 </div>
                 <div class="col-md-10">
                  <input formControlName="question" name="question" type="text" class="form-control"
                  id="question" >
                  <div *ngIf="faq_trans.controls[i].controls.question.touched && faq_trans.controls[i].controls.question.invalid" >
                    <div *ngIf="faq_trans.controls[i].controls.question.errors.required" class="alert alert-danger" translate>faqs.errorMessages.QuestionRequired</div>
                  </div>
                 </div>
              </div>

              <div class="form-group answer-form-group row">
                 <div class="col-md-2  text-right">
                  <label for="answer"  translate>faqs.Answer
                    <span *ngIf="faq_trans.controls[i].controls.answer.touched && faq_trans.controls[i].controls.answer.invalid" class="required">**</span>
                  </label>
                 </div>
                  <div class="col-md-10">
                    <p-editor formControlName="answer" [style]="{'height':'350px'}" id="answer"  onTextChange="render()" [required]="i === 0" >
                        <p-header>
                            <span class="ql-formats">
                                <select class="ql-header"> 
                                    <option value="4">Heading 4</option>     <!-- h4-->
                                    <option selected>Normal</option>
                                </select>
                                <button class="ql-bold" aria-label="Bold"></button>
                                <button class="ql-italic" aria-label="Italic"></button>
                                <button class="ql-underline" aria-label="Underline"></button>
                                <select title="Text Alignment" class="ql-align">
                                    <option selected>Gauche</option>
                                    <option value="center" label="Center"></option>
                                    <option value="right" label="Right"></option>
                                    <option value="justify" label="Justify"></option>
                                </select>
                                
                                <button aria-label="Ordered List" class="ql-list"
                                    value="ordered" type="button"></button>
                                <button aria-label="Bullet List" class="ql-list" value="bullet"
                                    type="button"></button>
                                <span class="ql-format-separator"></span>
                            </span>
                        </p-header>
                    </p-editor>
                    <div *ngIf="faq_trans.controls[i].controls.answer.touched && faq_trans.controls[i].controls.answer.invalid" >
                        <div *ngIf="faq_trans.controls[i].controls.answer.errors.required" class="alert alert-danger" translate>faqs.errorMessages.AnswerRequired</div>
                    </div>
                  </div>
              </div>

          </div>
      </div>

      <button *ngIf="languagesArray" type="submit" class="btn btn-success" [disabled]="faqForm.invalid" translate>faqs.Save</button>

      </form>
      <!-- <p> {{ faqForm.value | json }} </p> -->
    </div>





    <div *ngIf="mode === 'preview'">
      <div  *ngIf="questionTranslations.length !== 0" >

        <div class="card" style="width: 18rem;" *ngIf="question && languagesArray.length!==0 && questionCategories.length!==0 && questionTranslations.length!==0">
          <div class="card-body" *ngIf="question.id !== null">
            <div *ngIf="questionTranslations.length > 1  && questionCategories.length > 1">
              <ul class="nav nav-tabs">
                <li *ngFor="let lang of languagesArray" role="presentation" class="btn" [class.active]="lang.id === currentLangId"
                    (click)="changeLang(lang.id)" translate>{{ "faqs.languages."+lang.name}}</li>
              </ul>
            </div>
            <br><br>
            <div *ngFor="let lang of languagesArray">
              <div *ngIf="questionCategories.length > 1 && questionCategories[lang.id - 1].translated_languages_id === currentLangId">
                <div class="row">
                  <div class="col-md-4 col-sm-4 col-xs-4  text-right"><p class="card-text"><span translate>faqs.Category</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p class="card-text">{{ questionCategories[lang.id - 1].label }}</p></div>
                </div>
              </div>
              <div *ngIf="questionTranslations.length > 1 && questionTranslations[lang.id - 1].translated_language_id === currentLangId" >
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4  text-right"><p class="card-text"><span translate>faqs.Question</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"><p id="question" class=" content">{{ questionTranslations[lang.id - 1].question }}</p></div>
                </div>
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4  text-right"> <p class="card-text"><span translate>faqs.Answer</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> 
                      <p id="answer" class=" content user-input" [innerHTML]="questionTranslations[lang.id - 1].answer"></p>
                    <!-- <p id="answer" class=" content user-input" [innerHTML]="sanitizer.bypassSecurityTrustHtml(questionTranslations[lang.id - 1].answer)"></p> -->
                  </div>
                </div>
              </div>
            </div>


            <div class="row">
              <div class="col-md-4  col-sm-4 col-xs-4  text-right"><p class="card-text"><span translate>faqs.Order</span> </p></div>
              <div class="col-md-8 col-sm-8 col-xs-8"><p class="card-text"> {{ question.order }}</p></div>
            </div>

            <div class="row">
              <div class="col-md-4  col-sm-4 col-xs-4  text-right"><p class="card-text"><span translate>faqs.Type</span> </p></div>
              <div class="col-md-8 col-sm-8 col-xs-8"><p class="card-text"> <i class="fas fa" [class.fa-user]="question.type === 'User Faq'" [class.fa-suitcase]="question.type === 'Company Faq'"[title]="question.type"></i></p></div>
            </div>

            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-4  text-right activation"><span translate>faqs.Activation  </span></div>
              <div class="col-md-8 col-sm-8 col-xs-8"><i  id="inactive" *ngIf="!question.activation" class="fa fa-check-circle"></i>
                <i  id="active" *ngIf="question.activation" class="fa fa-check-circle"></i></div>
            </div>


          </div>
        </div>

      </div>
    </div>

    <div *ngIf="mode === 'delete'">
      <div class="delete" translate>
        faqs.errorMessages.DeleteConfirm
        <button type="button" class="btn btn-default" data-dismiss="modal"
            (click)="deleteFaq(question)" translate>faqs.labels.Yes</button>
      </div>
    </div>


  </div>

</div>



