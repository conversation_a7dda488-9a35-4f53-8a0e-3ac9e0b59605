<div class="container-fluid form-horizontal">
    <div class="top-tools">
        <div class="form-group">
                <input type="checkbox" class="form-check-input" (change)="checkAllMandatory($event)" [(ngModel)]="dataModel.allMandatory" />
                <label class="form-check-label" for="activation">All Mandatory</label>
        </div>
        <div class="form-group">
            <button (click)="addSkill()" class="btn btn-primary">Add Skill</button>
        </div>
        </div>
     

    <div *ngFor="let s of dataModel.skills;let i = index" class="row">
        <div class="col-sm-12 col-xs-8">
            <div class=" form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
                 
                </div>
                <div class="col-md-3 col-sm-4 col-xs-6 focus-no-padding">
                    <p-autoComplete field="name" inputId="id" styleClass="form-control" placeholder="Skill" [suggestions]="temp['skills']" (completeMethod)="filterArray($event , 'skills') " [(ngModel)]="s.type_id ">
                    </p-autoComplete>
                    <span class="custom-underline"></span>
                </div>
                <div class="col-md-3 col-sm-4 col-xs-6 focus-no-padding">
                    <p-dropdown [options]="filterData.skill_level" placeholder="Level" [(ngModel)]="s.level_id" optionLabel="name" optionValue="skill_level_id"></p-dropdown>
                </div>
                <div class="col-md-3" style="display: flex;
                justify-content: flex-start;
                align-content: center;
                align-items: center;">
                    <button (click)="removeSkill(i)" class="btn btn-danger">-</button>
                    <input type="checkbox" class="form-check-input" [(ngModel)]="s.mandatory" />
                    <p>Mandatory</p>
                </div>
            </div>
        </div>
    </div>
    <div class="filters-buttons">
        <button (click)="sendFilters()" class="btn btn-success">Apply</button>
        <button (click)="hideModal()" class="btn btn-default">Cancel</button>
    </div>

</div>