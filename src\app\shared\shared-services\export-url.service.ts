import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';

@Injectable()
export class ExportUrlService {
  //backend production
  // awsBasicUrl = 'https://www.cveek.com/backend/';

  //backend test
  // awsBasicUrl = 'https://dev.cveek.com/backend/';

  //backend development process
  // awsBasicUrl =  'https://api.cveek.com/'

  //backend localhost
  awsBasicUrl = 'http://localhost:8000/';


  //  filesUrl  = 'https://cevast-folder.s3.us-east-2.amazonaws.com/';

  public publicUrl = new BehaviorSubject<any>(this.awsBasicUrl);
  //public filesPublicUrl = new BehaviorSubject<any>(this.filesUrl) ;
  constructor() {
  }

  expandAndCollapseForm(active: any) {
    this.publicUrl.next(active);
    //this.filesPublicUrl.next(active);
  }

}
