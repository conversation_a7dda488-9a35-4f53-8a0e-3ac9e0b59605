/* .home-page-content .cus-navbar{
  background: transparent;
  border-bottom: none;
} */
@font-face {
  font-family: 'Bahnschrift';
  src: local('Bahnschrift'),
      url('assets/fonts/bahnschrift.woff2') format('woff2');
/*      font-weight: 600;
  font-style: normal; */
  font-display: swap;
}
/* body{
	font-family: 'Bahnschrift' !important;
} */
p , h1 , h2 , h3 , h4 , h5 , h6 , span , a{
	font-family: 'Bahnschrift' !important;
}
.section{
  padding:80px 30px;
}
.section-width{
  margin:auto;
}
.text-gray{
  color:#919191;
}
.text-blue{
  color:#3D7BCE;
}
.text-orange{
  color:#E2B233;
}
.bottom-spacing{
  margin-bottom:15px !important;
}

.blue-box, .smaller-blue-box{
  background:#3D7BCE;
  color:#fff;
  padding:15px;
  border-radius: 20px;
}
.smaller-blue-box{
  display:inline-block;
  padding:7px;
  border-radius: 10px;
}

.btn-orange {
  background: #e3b442;
  color: #fff;
  border: 0;
  outline: 0;
  font-size: 22px;
  font-weight: bold;
  margin: 0 6px;
  padding:12px 26px;
  border-radius: 20px;
}
.btn-orange:hover {
  background: #e6b541;
  border: 0;
  outline: 0;
  animation: rubberBand 1s;
}
.btn-blue {
  background: #3D7BCE;
  color: #fff;
  border: 0;
  outline: 0;
  font-size: 22px;
  font-weight: bold;
  margin: 0 6px;
  padding:12px 26px;
  border-radius: 20px;
}
.btn-blue:hover {
  background: #3D7BCE;
  border: 0;
  outline: 0;
  animation: rubberBand 1s;
}

.hero-section{
  padding-top:30px;
  background-color: #E2B233;
}
.inner-hero-section{
  background-image: url("/assets/images/home/<USER>");
  background-position:center 5%;
  background-size:cover;
  background-repeat: no-repeat; 
  color:#fff;
}
.hero-section .hero-text-and-search{
  display:flex;
  flex-direction: column;
  justify-content: center;
  padding:40px;
}
.hero-section .hero-text{
  margin-bottom: 20px;
}
.hero-section .hero-text h1{
  font-size:42px;
  margin: 20px 0;
}
.hero-section .hero-text p{
  font-size:16px;
}

.search-container{
  padding: 20px 30px;
  border-radius: 20px;
  background: #fff;
}
/* :host ::ng-deep .ui-carousel-container .ui-carousel-prev, :host ::ng-deep .ui-carousel-container .ui-carousel-next{
  display:none;
} */
:host ::ng-deep .ui-carousel-content{
  padding:0;
}


.jobs-by-location a{
  text-decoration:none;
}
.jobs-by-location a img{
  display:block;
  margin:0 auto 30px auto;
  border-radius: 20px;
} 




.build-cv-steps{
  background:#F1F1F1;
}
.build-cv-steps .row div .step{
  padding:20px;
  border-radius: 20px;
  background:#fff;
}
.build-cv-steps .row div .step h3{
  color:#676767;
}
.build-cv-steps .row div .step p{
  color:#A3A3A3;
}
.build-cv-steps .row div .step h3{
  margin-top: 5px;
  margin-bottom: 15px;
}
.build-cv-steps .row div .step:hover{
  background:#3D7BCE;
}
.build-cv-steps .row div .step:hover h3{
  color:#fff;
}
.build-cv-steps .row div .step:hover p{
  color:#D1D1D1;
}

.number{
  color:#E2B233 !important;
  font-size: 2.8rem;
  font-weight: bold;
  margin-bottom:0;
}

.masonry-section{
  display:flex;
}
.masonry-section .masonry-col{
  padding:0 8px;
}
.masonry-section .masonry-col:nth-child(1) {
  width:34%;
}
.masonry-section .masonry-col:nth-child(2) {
  width:37%;
}
.masonry-section .masonry-col:nth-child(3) {
  width:38%;
}
.masonry-section .blue-box ul{
  margin:0;
}
.masonry-section .blue-box ul a{
  color:#fff;
  text-decoration: none;
}
.masonry-section h3 {
  font-size: 20px;
}

.masonry-section .branding {
  color:#E2B233;
}
.masonry-section .headline {
  font-size: 3.8rem;
  font-weight: bold;
  line-height: 1;
  margin: 0;
}
.masonry-section .headline .global {
  display: block;
}
.masonry-section .headline .job-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.masonry-section .headline .job {
  font-size: 3.8rem;
  font-weight: bold;
}
.masonry-section .headline .subheadline {
  font-size: 1.3rem;
  font-weight: bold;
}


@media (min-width: 1300px){
	.limit-content-width{
		width:1250px  !important;
	}
	.hero-wrapper .vertical-center-absolute {
    	left: 6% !important;
	}
}

/* Start responsive styles */
@media (min-width: 1200px){
	.section-width{
		width:1100px  !important;
	}
}
@media (min-width: 1400px){
	.section-width{
		width:1250px  !important;
	}
}
@media (min-width: 1650px){
	.section-width{
		width:1500px  !important;
	}
}
@media (min-width: 1800px){
	.section-width{
		width:1700px !important;
	}
}
@media (max-width: 991px){
  .hero-section .hero-text h1{
    font-size:36px;
  }
  .hero-section .hero-text p{
    font-size:14px;
  }
  .bottom-spacing-med{
    margin-bottom:15px;
  }
  .jobs-by-location .image-grid {
    margin-bottom: 35px;
    display:block;
  }

  .masonry-section .blue-box ul{
    padding: 0 20px;
  }
}
@media (max-width: 767px){
  .hero-section .hero-text{
    margin-bottom: 10px;
  }
  .hero-section .hero-text h1{
    font-size:32px;
    margin-bottom: 10px;
  }
  .bottom-spacing-mob{
    margin-bottom:15px;
  }

  .masonry-section{
    flex-wrap: wrap;
  }
  .masonry-section .masonry-col{
    width:100% !important;
  }
  .order-mob-1{
    order:1;
    margin-bottom:15px;
  }
  .order-mob-2{
    order:2;
  }
  .inner-hero-section{
    background-position:center 4%;
    background-size: contain;
  }

  .build-cv-steps .row div .step h3 , .build-cv-steps .row div .step p{
    text-align: left;
  }
  .build-cv-steps .row div .step h3{
    margin:0;
  }
  .build-cv-steps .row div .step .step-name-div {
    margin:0;
    display:flex;
    align-items: center;
  }
  .build-cv-steps .row div .step .number{
    text-align: center;
  }
}

@media (max-width: 560px){
  .number{
    font-size: 2.2rem;
  }

  .masonry-section .headline {
    font-size: 4rem;
  }
  .masonry-section .headline .job {
    font-size: 4rem;
  }
  .masonry-section  .headline .subheadline {
    font-size: 1.5rem;
  }
}