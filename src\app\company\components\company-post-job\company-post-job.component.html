<!--<div class="page-navbar" style="position: fixed;">
    <ul id="page-navbar-list">
        <li>
            <a routerLink='language-setting'><i class="fa fa-language" style="font-size: 20px; margin-left:10px"></i></a>
        </li>
        <li>
            <a ><span>English</span></a>
        </li>
        <li>
            <a><span>Arabic</span></a>
        </li>
        <li>
            <a> <span>Sign in Info</span></a>
        </li>
    </ul>
</div>-->
<page-navbar [navType]="'empty'"></page-navbar>
<div class="container-fluid">
    <app-pre-loader [show]="employmentTypeOpt.length === 0"></app-pre-loader>
    <div id="page-content-wrapper" [hidden]="!(employmentTypeOpt.length != 0)">
        <!--  [style.margin-left]="marginLeft" -->
        <div class="page-content table-page-content">
            <!-- <p style="position: absolute;top: -110px;z-index: 100000;background: #fff;font-size:13px;"> {{ form.value | json }} </p> -->
            <form #form="ngForm" (keydown.enter)="$event.preventDefault()" [formGroup]="postForm" class="validate-form" (ngSubmit)="form.valid">
                <div class="row clearfix flex-row">
                    <aw-wizard [awNavigationMode] navigateBackward="allow" navigateForward="allow" [navBarLocation]="'left'" [navBarLayout]="'large-empty-symbols'">
                        <!-- <p-toast [style]="{marginTop: '100px'}"></p-toast> -->
                        <!-- *ngIf="!companyHaveProfile" -->

                        <!-- class="sticky-confirm-toast" -->
                        <p-toast key="companyDontHaveProfile" position="bottom-right" baseZIndex="1" >
                            <ng-template let-message pTemplate="message">
                                <span>{{message.detail}}</span> &nbsp;
                                <a [routerLink]="['/c', username, 'profile','new']" class="warning-msg-link">Add profile</a>
                            </ng-template>
                        </p-toast>

                        <!-- class="sticky-confirm-toast" -->
                        <p-toast key="companyDontHaveLocation" position="bottom-right" baseZIndex="1" >
                            <ng-template let-message pTemplate="message">
                                <span>{{message.detail}}</span> &nbsp;
                                <a [routerLink]="['/c', username, 'profile','location']" class="warning-msg-link">Add location</a>
                            </ng-template>
                        </p-toast>
                        <aw-wizard-step stepTitle="Basic Information "
                            [navigationSymbol]="{ symbol: '&#xf15c;', fontFamily: 'FontAwesome' }">

                            <div class="row">

                                <div>
                                    <ng-template awWizardStepTitle let-wizardStep="wizardStep">
                                            <!-- && this.jobTitleControl.controls['name'].value -->
                                        <span [ngClass]="{'red': valid_advr ,
                                         'green':this.postForm.controls['adv_title'].value  && this.postForm.controls['exp_field_id'].value!==''
                                         && !this.postForm.controls['job_title'].errors?.required && !this.postForm.controls['employment_type_temp'].errors?.required
                                          && this.salaryIsValid() }">
                                          {{ wizardStep.completed ? "Advertisement Information" : "Advertisement Information" }}
                                        </span>
                                        <p style="color:#ccc; font-size: 12px;">Enter Advertisement Information</p>
                                    </ng-template>
                                </div>
                                <!-- col-sm-9 col-xs-8 -->
                                <div class="add-certification details">
                                    <!--<p style="color: red;" class="add-certification-p" translate>post_advr.job_info</p>-->
                                    <div class="custom-row clearfix pad-top1" [ngClass]="{'has-error': !no_data_entered && !postForm.controls['adv_title'].value ||
                        submitted && !postForm.controls['adv_title'].value}">
                                        <div class="focus-no-padding-textarea validate-input">
                                            <div class="focus-container has-feedback">
                                                <!-- [ngClass]="{'has-error':form.submitted && !languageForm.controls['type'].valid}" -->
                                                <div class="col-sm-3 col-md-3 col-lg-3 alignment-right">
                                                    <label translate>post_advr.advr_title</label>
                                                </div>
                                                <div class="col-sm-8 col-md-8 col-lg-8 focus-no-padding  validate-input" [ngClass]="{'has-val':postForm.controls['adv_title'].value}">
                                                    <input formControlName="adv_title" placeholder=" " type="text" class="form-control">
                                                    <!-- (keyup.enter)="currentLocationKeyUp($event)" -->
                                                    <span class="custom-underline"></span>

                                                    <!-- <label class="control-label custom-control-label labelAdd2" translate>post_advr.advr_title</label> -->
                                                    <span *ngIf="!no_data_entered && !postForm.controls['adv_title'].value || submitted && !postForm.controls['adv_title'].value" class="error-message" translate>validationMessages.required</span>
                                                </div>
                                                <div class="col-sm-1 col-md-1 col-lg-1">
                                                    <button type="button" class="btn btn-primary btn-sm" (click)="openAiGenerator()" title="Generate job details with AI" data-toggle="tooltip" data-placement="right">
                                                        <i class="fa fa-magic"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-row clearfix" *ngIf="ExpField.length">
                                        <div class="col-sm-3 col-md-3 col-lg-3 alignment-right">
                                            <label translate>post_advr.exp_field</label>
                                        </div>

                                        <div class="col-sm-8 col-md-8 col-lg-8 focus-no-padding validate-input CScreen" [ngClass]="{'has-error': !no_data_entered && postForm.controls['exp_field_id'].value==='' ||
                            submitted && postForm.controls['exp_field_id'].value===''}" style="background: white !important; z-index: 101 !important;">

                                            <p-dropdown [options]="ExpField" optionLabel="name" formControlName="exp_field" [filter]="true" (onChange)="changeExpField()" [ngClass]="{'has-val':postForm.controls['exp_field'].value!=''}">

                                            </p-dropdown>
                                            <span class="custom-underline"></span>
                                            <span *ngIf="!no_data_entered && !postForm.controls['exp_field_id'].value ||  submitted && !postForm.controls['exp_field_id'].value" class="error-message" translate>validationMessages.required</span>
                                            <!-- <span *ngIf="!no_data_entered && !postForm.controls['exp_field'].value ||  submitted && !postForm.controls['exp_field'].value" class="error-message" translate>validationMessages.required</span> -->
                                        </div>
                                    </div>

                                    <!-- [ngClass]="{'has-error': !no_data_entered && !jobTitleControl.controls['name'].value ||
                                    submitted && !jobTitleControl.controls['name'].value  }" -->
                                    <div class="custom-row clearfix relative-row" [ngClass]="{'has-error': !no_data_entered && postForm.controls['job_title'].errors?.required ||
                        submitted && postForm.controls['job_title'].errors?.required  }">
                                        <div class="focus-no-padding-textarea validate-input">
                                            <div class="col-sm-3 col-md-3 col-lg-3 alignment-right">
                                                <label translate>post_advr.job_title</label>
                                            </div>
                                            <!-- [ngClass]="{'has-val':postForm.controls['job_title'].value}" -->
                                            <div class="col-sm-8 col-md-8 col-lg-8 focus-no-padding validate-input CScreen">
                                                <!-- [dropdownPosition]="'bottom'" -->
                                                <ng-select
                                                [items]="jobTitleDD.items"
                                                [virtualScroll]="true"
                                                formControlName="job_title"
                                                [loading]="jobTitleDD.loading"
                                                [multiple]="true"
                                                bindLabel="name"
                                                notFoundText="No items found"
                                                (scrollToEnd)="jobTitleDD.onScrollToEnd()"
                                                (search)="jobTitleDD.search($event)"
                                                [searchFn]="jobTitleDD.customSearchFn"
                                                class="form-control"
                                                >
                                                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                                        <span class="ng-value-label">{{item.name}}</span>
                                                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                                                            <i class="fa fa-times" aria-hidden="true"></i>
                                                        </span>
                                                    </ng-template>
                                                    <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                                                        {{item.name}}
                                                    </ng-template>
                                                </ng-select>
                                                <!-- <p-autoComplete [suggestions]="filteredJob" (completeMethod)="orderJobs($event)" field="name" inputId="id" styleClass="form-control" formControlName="name" [ngClass]="{'has-val':jobTitleControl.controls['name'].value}" (onSelect)="selectJobTitle($event)"
                                                    (onKeyUp)="onKeyUPJobTitle()">
                                                </p-autoComplete> -->
                                                <span class="custom-underline"></span>
                                                <!-- <label class="control-label custom-control-label labelAdd2" for="majorId" translate>post_advr.job_title</label> -->
                                                <span *ngIf="!no_data_entered && postForm.controls['job_title'].errors?.required || submitted && postForm.controls['job_title'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                <!-- <span *ngIf="!no_data_entered && !jobTitleControl.controls['name'].value || submitted && !jobTitleControl.controls['name'].value " class="error-message" translate>validationMessages.required</span> -->
                                            </div>
                                            <div class="col-sm-1">
                                                <button type="button" class="btn btn-primary btn-fa-info request-new-jobtitle" (click)="requestNewValue('jobTitle')" pTooltip="Request to add new value to the predefined job title values">
                                                    <i class="fa fa-info" aria-hidden="true"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="custom-row clearfix" [ngClass]="{'has-error': (!no_data_entered || submitted) && this.postForm.controls['employment_type_temp'].errors?.required}">
                                        <div class="col-sm-3 col-md-3 col-lg-3 alignment-right">
                                            <label translate>post_advr.emp_type</label>
                                        </div>
                                        <div class="col-sm-8 col-md-8 col-lg-8 focus-no-padding validate-input CScreen div-ejs-multiselect" translate>
                                                <!-- maximumSelectionLength="4" -->
                                            <ejs-multiselect #employment
                                                [dataSource]='employmentTypeOpt'
                                                [fields]='remoteFields'
                                                mode='Box'
                                                [allowFiltering]='true'
                                                [ignoreAccent]='true'
                                                formControlName="employment_type_temp"
                                                [value]="value_emp"
                                                [ngClass]="{'has-val':!this.postForm.controls['employment_type_temp'].errors?.required}"
                                                >
                                            </ejs-multiselect>
                                            <span class="custom-error-underline"></span>
                                            <span class="custom-underline"></span>
                                            <!-- <label class="control-label custom-control-label labelAdd2" for="majorId" translate>post_advr.emp_type</label> -->
                                            <span *ngIf="(!no_data_entered || submitted) && this.postForm.controls['employment_type_temp'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                            <!-- <span *ngIf="(!no_data_entered && !postForm.controls['employment_type_temp'].value) || (valid_advr && !postForm.controls['employment_type_temp'].value)" class="error-message" translate>validationMessages.required</span> -->
                                        </div>
                                    </div>

                                    <div class="row salary-div">
                                        <!-- <div class="col-sm-2 col-md-3 col-lg-2"></div> -->
                                        <div class="col-sm-3 col-md-3 col-lg-3 salary-label">
                                            <!-- <div class="form-group"> -->
                                                <div class="exInformation">
                                                    <div class="custom-checkbox checkbox" style="justify-content: right;">
                                                        <label class="control-label">
                                                            <input style="width: 18px; height: 18px;" id="extraSalary"
                                                                type="checkbox" [checked]="extraCheckedSalary"
                                                                (change)="minimize('salary')">
                                                            <span style="font-size:15px; font-weight: bold;color:#4f94df;"> &nbsp;
                                                                <span id="salary" translate>post_advr.salary</span>
                                                                <!-- <span style="font-size: 14px;"> (optional)</span> -->
                                                            </span>
                                                        </label>
                                                    </div>
                                                </div>
                                            <!-- </div> -->
                                        </div>
                                        <div class="col-sm-8 col-md-8 col-lg-8">
                                            <div class="custom-row clearfix extraInformationSalary">
                                                <!-- <div class="col-sm-12"> -->
                                                    <!-- <div class="focus-no-padding"> -->
                                                        <!--  [ngClass]="{'has-val':postForm.controls['job_title'].value}" -->
                                                        <!-- [ngClass]="{'has-error':postForm.controls['salary_to'].touched && salary_to_value && !salary_from_value ,'green': valid_salary}" -->
                                                        <div class="input-group col-sm-3 col-xs-3" [ngClass]="{'has-error':(!no_data_entered || submitted) && ( (!salary_from_value && salary_to_value) || postForm.controls['salary_from'].errors?.pattern || (salary_from_value >= salary_to_value) ) }">
                                                            <input placeholder="{{'post_advr.from' | translate}}" formControlName="salary_from" type="text" class="form-control" aria-label="Amount (to the nearest dollar)" [(ngModel)]="salary_from_value">
                                                            <span class="custom-underline"></span>
                                                            <span *ngIf="(!no_data_entered || submitted) && (!salary_from_value && salary_to_value)" class="salary-error-message" translate>validationMessages.required</span>
                                                            <span *ngIf="(!no_data_entered || submitted) && postForm.controls['salary_from'].errors?.pattern" class="salary-error-message" translate>Only numbers</span>
                                                            <span *ngIf="(!no_data_entered || submitted) && (!postForm.controls['salary_from'].errors && !postForm.controls['salary_to'].errors) && (salary_from_value >= salary_to_value)" class="salary-error-message" translate>Invalid salary range</span>
                                                            <!-- <span *ngIf="(!no_data_entered || submitted) && (!postForm.controls['salary_from'].errors?.pattern && !postForm.controls['salary_to'].errors?.pattern) && (salary_from_value >= salary_to_value)" class="salary-error-message" translate>Invalid salary range</span> -->
                                                        </div>
                                                        <!-- [ngClass]="{'has-error':postForm.controls['salary_from'].touched && !salary_to_value && salary_from_value}" -->
                                                        <!-- [ngClass]="{'has-error':(!no_data_entered && !salary_to_value && salary_from_value) || (submitted && !salary_to_value && salary_from_value)}" -->
                                                        <div class="input-group col-sm-3 col-xs-3" [ngClass]="{'has-error':(!no_data_entered || submitted) && ( (!salary_to_value && salary_from_value) || postForm.controls['salary_to'].errors?.pattern || (salary_from_value >= salary_to_value) ) }">
                                                            <input placeholder="{{'post_advr.to' | translate}}" formControlName="salary_to" type="text" class="form-control" aria-label="Amount (to the nearest dollar)" [(ngModel)]="salary_to_value">
                                                            <span class="custom-underline"></span>
                                                            <span *ngIf="(!no_data_entered || submitted) && (!salary_to_value && salary_from_value)" class="salary-error-message" translate>validationMessages.required</span>
                                                            <span *ngIf="(!no_data_entered || submitted) && postForm.controls['salary_to'].errors?.pattern" class="salary-error-message" translate>Only numbers</span>
                                                        </div>
                                                        <div class="input-group col-sm-6 col-xs-6" [ngClass]="{'has-error':(!no_data_entered || submitted) && ( (salary_to_value && salary_from_value) && !this.postForm.controls['currency_id'].value ) }">
                                                            <p-autoComplete [suggestions]="filteredCurrency" field="name" inputId="id" styleClass="form-control" (completeMethod)="Currency($event)" placeholder="Currency" [(ngModel)]="selectedCurrency" formControlName="currency_id">
                                                            </p-autoComplete>
                                                            <span *ngIf="(!no_data_entered || submitted) && ( (salary_to_value && salary_from_value) && !this.postForm.controls['currency_id'].value ) " class="salary-error-message" translate>validationMessages.required</span>
                                                            <span *ngIf="(!no_data_entered || submitted) && ( (salary_to_value && salary_from_value) && (this.postForm.controls['currency_id'].value && this.postForm.controls['currency_id'].value.id === undefined ) ) " class="salary-error-message" translate>Please choose currency from autocomplete suggestions</span>
                                                        </div>

                                                        <div class="btn-group btn-group-justified" style="padding-top:30px;" role="group" aria-label="...">
                                                            <!-- style="width: 50px;" -->
                                                            <p-selectButton styleClass="salary-type" [options]="payment_Date" formControlName="salary_type" [(ngModel)]="selectedType_salary_type"></p-selectButton>
                                                        </div>
                                                    <!-- </div> -->
                                                <!-- </div> -->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row wizardButton">
                                        <div class="col-sm-2 col-md-3 col-lg-2"></div>
                                        <div class="col-sm-9 col-md-8 col-lg-9 text-right">
                                            <button class="next custom-btn btn btn-info" type="button" awNextStep>Next
                                            </button>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!--<button class="btn btn-info" type="button" [awGoToStep]="{stepIndex: 2}">Go directly to third Step </button>-->
                        </aw-wizard-step>
                        <aw-wizard-step [navigationSymbol]="{ symbol: '&#xf040;', fontFamily: 'FontAwesome'  }">
                            <div class="row">
                                    <!-- col-sm-2 col-xs-0 -->
                                <div class="">
                                    <ng-template awWizardStepTitle let-wizardStep="wizardStep">
                                        <span [ngClass]="{'green': this.postForm.controls['job_description'].valid && this.postForm.controls['job_description'].value , 'red': !this.postForm.controls['job_description'].valid}">
                                            {{ wizardStep.completed ? "Description" : "Description" }} </span>
                                        <p style="color:#ccc; font-size: 12px;">Enter your Job Description</p>
                                    </ng-template>
                                </div>
                                <!-- col-sm-9 col-xs-8  -->
                                <div class="details editor">
                                    <div class="custom-row clearfix pad-top">
                                        <div class="col-sm-12 col-xs-12 focus-no-padding">
                                            <p-editor id="job_description" formControlName="job_description" [style]="{'height':'360px'}" onTextChange="render()">
                                                <p-header>
                                                    <span class="ql-formats">
                                                        <select class="ql-header">
                                                            <option value="3">Heading</option>     <!-- h3 -->
                                                            <option value="4">Subheading</option>  <!-- h4-->
                                                            <option selected>Normal</option>
                                                        </select>
                                                        <!-- <select class="ql-header">
                                                            <option value="1">Heading</option>
                                                            <option value="2">Subheading</option>
                                                            <option selected>Normal</option>
                                                        </select> -->
                                                        <button class="ql-bold" aria-label="Bold"></button>
                                                        <button class="ql-italic" aria-label="Italic"></button>
                                                        <button class="ql-underline" aria-label="Underline"></button>
                                                        <!--<button class="ql-order" aria-label="Underline"></button>-->
                                                        <select title="Text Alignment" class="ql-align">
                                                            <option selected>Gauche</option>
                                                            <option value="center" label="Center"></option>
                                                            <option value="right" label="Right"></option>
                                                            <option value="justify" label="Justify"></option>
                                                        </select>
                                                        <!--   <label style="cursor: pointer; font-weight: bold;
                                    color: #4d4da3;" translate>post_advr.title<button id="title" class="ql-header" aria-label="Title"></button></label>
                                    <label  style="cursor: pointer; color: #86c9c9;" translate>post_advr.subTitle
                                    <button id="subtitle" class="ql-header" value="2" aria-label=""></button></label> -->

                                                        <button aria-label="Ordered List" class="ql-list"
                                                            value="ordered" type="button"></button>
                                                        <button aria-label="Bullet List" class="ql-list" value="bullet"
                                                            type="button"></button>
                                                        <span class="ql-format-separator"></span>
                                                    </span>
                                                </p-header>
                                            </p-editor>
                                            <span class="error-message" *ngIf="form.submitted && postForm.controls['job_description'].errors?.maxlength" translate>validationMessages.tooLongText</span>
                                        </div>
                                    </div>

                                    <div style="margin-top: -10px;" class="row wizardButton">
                                        <div class="col-sm-12 text-right" style="padding-right: 26px;">
                                            <button class="back back1 custom-btn btn btn-info" type="button" awPreviousStep>Back </button>
                                            <button class="next custom-btn btn btn-info" type="button" awNextStep>Next
                                            </button>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-sm-1"></div>
                            </div>

                        </aw-wizard-step>
                        <aw-wizard-step [navigationSymbol]="{ symbol: '&#xf0b1;', fontFamily: 'FontAwesome'  }">
                            <div class="row">
                                    <!-- col-sm-2 col-xs-2 -->
                                <div class="">
                                    <ng-template awWizardStepTitle let-wizardStep="wizardStep">
                                        <span [ngClass]="{'red': (submitted && companyInfoStepValidity() === false) , 'green': companyInfoStepValidity() === true }">{{
                                            wizardStep.completed ? "Company Information " : "Company Information" }}
                                            <!--<i *ngIf="valid_company && this.page_name=== 'company_step' && !this.postForm.controls['company_locations'].value ||
                  this.submitted && !this.postForm.controls['company_locations'].value" class="fa fa-times" aria-hidden="true"></i>-->
                                        </span>
                                        <p style="color:#ccc; font-size: 12px;">Enter Company Information</p>
                                    </ng-template>
                                </div>

                                <div class="details">
                                    <div class="row clearfix pad-top1" *ngIf="company_permissions.job_publisher_other_companies === true">
                                        <div class="col-sm-3 col-md-3 col-lg-3 alignment-right">
                                            <label translate>This Opportunity is for</label>
                                        </div>
                                        <div class="col-sm-8 col-md-8 col-lg-8 focus-no-padding validate-input CScreen div-ejs-multiselect" translate>
                                            <div class="row">
                                                <div class="col-xs-6">
                                                    <label class="container radio-choose" translate>My Company
                                                        <input type="radio" formControlName="opportunity_for" value="my_company" (change)="minimize('opportunityFor','my_company')" checked>
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </div>
                                                <div class="col-xs-6">
                                                    <label class="container radio-choose" translate>Other Company
                                                        <input type="radio" formControlName="opportunity_for" value="other_company" (change)="minimize('opportunityFor','other_company')">
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="my-company">
                                        <div class="clearfix row pad-top1 flex-row-wrap">
                                            <div class="col-sm-9 col-xs-12 focus-no-padding validate-input CScreen flex-order-xs-2">
                                                <div class="row">
                                                    <div class="col-sm-4 alignment-right">
                                                            <!-- class="control-label custom-control-label companyName" -->
                                                        <label>Company Name</label>
                                                    </div>
                                                    <div class="col-sm-7">
                                                        <div class="row">
                                                            <div class="col-xs-6">
                                                                <label class="container radio-choose" translate>Show
                                                                    <input name='radioBtn2' (click)="hideShowCompIdentity('show')"
                                                                        [checked]="show" type="radio" value="show">
                                                                    <span class="checkmark"></span>
                                                                </label>
                                                            </div>
                                                            <div class="col-xs-6">
                                                                <label class="container radio-choose" translate>Hide
                                                                    <input name='radioBtn' [checked]="hide"
                                                                        (click)="hideShowCompIdentity('hide')" type="radio" value="hide">
                                                                    <span class="checkmark"></span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div *ngIf="company_locations.length" class="clearfix pad-top1 row" [ngClass]="{'has-error':(submitted && postForm.controls['company_locations'].errors?.required) }">
                                                    <div class="col-sm-4 alignment-right">
                                                        <label translate>Work Location</label>
                                                    </div>
                                                    <div class="col-sm-7 validate-input CScreen" [ngClass]="{'has-val':postForm.controls['company_locations'].value}">
                                                        <p-dropdown [options]="company_locations" optionLabel="company_name" formControlName="company_locations" [filter]="true" [ngClass]="{'has-val':postForm.controls['company_locations'].value}">
                                                        </p-dropdown>
                                                        <span class="custom-underline"></span>
                                                        <!-- <label class="control-label custom-control-label custom-control-labels labelAdd2" translate>post_advr.location_name</label> -->
                                                        <span *ngIf="submitted == true && postForm.controls['company_locations'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                    </div>

                                                </div>

                                                <div class="apply-on-container pad-top1" *ngIf="company_permissions.job_publisher_link === true">
                                                    <div class="row clearfix">
                                                        <div class="col-sm-4 alignment-right">
                                                            <label translate>Receive CVs on</label>
                                                        </div>
                                                        <div class="col-sm-7 validate-input CScreen div-ejs-multiselect" translate>
                                                            <div class="row">
                                                                <div class="col-xs-6">
                                                                    <label class="container radio-choose" translate>CVeek Inbox
                                                                        <input type="radio" formControlName="apply_on" value="cveek-inbox" (change)="minimize('applyOn','cveek-inbox')" checked>
                                                                        <span class="checkmark"></span>
                                                                    </label>
                                                                </div>
                                                                <div class="col-xs-6">
                                                                    <label class="container radio-choose" translate>Company Website
                                                                        <input type="radio" formControlName="apply_on" value="company-website" (change)="minimize('applyOn','company-website')">
                                                                        <span class="checkmark"></span>
                                                                    </label>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>

                                                    <div class="row company-apply-link pad-top1"
                                                    [ngClass]="{'has-error': submitted && !this.postForm.controls['external_link'].valid}"
                                                    [ngStyle]="hideMyCompanyExternalLink? {'display': 'none'} : {'display': 'block'}">
                                                        <div class="col-sm-offset-4 col-sm-7 fix-form-control-height">
                                                            <input type="text" class="form-control" formControlName="external_link" placeholder="Job opportunity link">
                                                            <span class="custom-underline"></span>
                                                            <span *ngIf="submitted && this.postForm.controls['external_link'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                            <span *ngIf="submitted && this.postForm.controls['external_link'].errors?.invalidUrlError" class="error-message" translate>{{postForm.controls['external_link'].errors?.invalidUrlError}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3 col-xs-12 text-center flex-order-xs-1">
                                                <div href="#" class="user-pro-pic">
                                                    <img *ngIf="show" [src]="image_uploaded_url" style="width:100px;max-width: 100%; top:-30px;margin-bottom:10px;">
                                                    <img *ngIf="hide" src="./assets/images/Confidential-icon.png" style="width : 100px; max-width: none;  top:-30px;border-radius:50%;margin-bottom:10px;">
                                                    <p *ngIf="show" class="company-name" style="text-align: center;" translate>{{ company_name || " " }}</p>
                                                    <p *ngIf="hide" class="company-name" style="text-align: center;" translate>Company Name</p>
                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <div class="other-company"
                                        *ngIf="company_permissions.job_publisher_other_companies === true"
                                        [ngStyle]="hideOtherCompanyDiv? {'display': 'none'} : {'display': 'block'}"
                                        >
                                        <label style="font-size: 16px;font-weight: bold;margin-top:20px;">Employer Details:</label>
                                        <div formGroupName="other_employer">
                                            <div class="row flex-row-wrap">
                                                <div class="col-sm-9 col-xs-12 flex-order-xs-2">
                                                    <div class="row clearfix pad-top1" [ngClass]="{'has-error':(submitted && !otherEmployerControl.controls['name'].valid) }">
                                                        <div class="col-sm-4  alignment-right">
                                                            <label translate>Employer Name</label>
                                                        </div>
                                                        <div class="col-sm-7  focus-no-padding  validate-input">
                                                            <input formControlName="name" type="text" class="form-control">
                                                            <span class="custom-underline"></span>
                                                            <span *ngIf="submitted == true && otherEmployerControl.controls['name'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                        </div>
                                                    </div>

                                                    <div class="row clearfix pad-top1">
                                                        <div class="col-sm-4  alignment-right">

                                                        </div>
                                                        <div class="col-sm-7  focus-no-padding">
                                                            <label class="checkbox-inline">
                                                                <input type="checkbox" formControlName="no_exist_logo_name" (change)="noExistLogoNameCheckboxChanged()">Don't have exist uploaded logo
                                                            </label>

                                                        </div>
                                                    </div>

                                                    <div class="row clearfix pad-top1" [ngClass]="{'has-error': otherEmployerControl.controls['logo_name'].errors?.logoNotFound }">
                                                        <div class="col-sm-4  alignment-right">
                                                            <label translate>Employer Logo Name</label>
                                                        </div>
                                                        <div class="col-sm-7  focus-no-padding  validate-input">
                                                            <input formControlName="logo_name" type="text" class="form-control" (change)="logoLinkChanged()" [attr.disabled]="otherEmployerControl.controls['no_exist_logo_name'].value? true : null">
                                                            <span class="custom-underline"></span>
                                                            <div *ngIf="otherEmployerControl.controls['logo_name'].pending" style="position:absolute;top:33px;">Checking if logo exist ...</div>
                                                            <span class="error-message" *ngIf="otherEmployerControl.controls['logo_name'].errors?.logoNotFound">Logo not exist</span>
                                                            <!-- <span *ngIf="submitted == true && otherEmployerControl.controls['logo_name'].errors?.logoImageNotFoundError" class="error-message" translate>{{otherEmployerControl.controls['logo_name'].errors?.logoImageNotFoundError}}</span>   -->
                                                            <!-- <span *ngIf="logoImageNotFoundError === true" class="error-message" translate>Logo not exist</span> -->
                                                        </div>
                                                    </div>
                                                    <div class="row clearfix pad-top1" [ngClass]="{'has-error':(submitted == true && otherEmployerControl.controls['company_industries'].errors?.required) }">
                                                        <div class="col-sm-4  alignment-right">
                                                            <label translate>Employer Company Industry</label>
                                                        </div>
                                                        <div class="col-sm-7  focus-no-padding  validate-input div-ejs-multiselect">
                                                                <!-- #language
                                                                [value]="value_lang"  -->
                                                            <ejs-multiselect
                                                                [dataSource]='ExpField2'
                                                                [fields]='remoteFields6'
                                                                mode='Box'
                                                                [allowFiltering]='true'
                                                                [ignoreAccent]='true'
                                                                style="color:rgb(91,211,55)"
                                                                formControlName="company_industries"
                                                                (change)="companyIndustryChanged($event)"
                                                                >
                                                            </ejs-multiselect>
                                                            <span class="custom-error-underline"></span>
                                                            <span class="custom-underline"></span>
                                                            <span *ngIf="submitted == true && otherEmployerControl.controls['company_industries'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <div class="row clearfix pad-top1">
                                                            <div class="col-sm-4  alignment-right">
                                                                <label translate>Work Location</label>
                                                            </div>
                                                            <div class="col-sm-7  focus-no-padding validate-input CScreen div-ejs-multiselect" translate>
                                                                <div class="row">
                                                                    <div class="col-xs-6">
                                                                        <label class="container radio-choose" translate>onsite
                                                                            <input type="radio" formControlName="location_type" value="onsite" (change)="minimize('locationType','onsite')" checked>
                                                                            <span class="checkmark"></span>
                                                                        </label>
                                                                    </div>
                                                                    <div class="col-xs-6">
                                                                        <label class="container radio-choose" translate>remote
                                                                            <input type="radio" formControlName="location_type" value="remote" (change)="minimize('locationType','remote')">
                                                                            <span class="checkmark"></span>
                                                                        </label>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="row clearfix pad-top1 other-company-location"
                                                            [ngClass]="{'has-error':(submitted && (!otherCompanyLocationControl.controls['country_code'].valid || !otherEmployerControl.controls['full_work_location'].valid ) ) }"
                                                            [ngStyle]="hideOtherCompanyWorkLocation? {'display': 'none'} : {'display': 'block'}">
                                                            <div class="col-sm-4  alignment-right">
                                                                <!-- <label translate>Location</label> -->
                                                            </div>
                                                            <div class="col-sm-7  focus-no-padding  validate-input">
                                                                <input formControlName="full_work_location"  placeholder="Location" type="text" class="form-control" id="other-Company-Location" (keyup)="clearLocationData(otherCompanyLocationControl)"  #otherCompanyLocation>
                                                                <span class="custom-underline"></span>
                                                                <span *ngIf="submitted == true && otherEmployerControl.controls['full_work_location'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                                <span *ngIf="submitted == true && ( otherEmployerControl.controls['full_work_location'].value && otherCompanyLocationControl.controls['country_code'].errors?.required)"
                                                                    class="error-message" translate>validationMessages.ChooseAutoCompleteSuggestionsError</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3 col-xs-12 flex-order-xs-1 text-center persPhoto-col">
                                                    <div *ngIf="otherEmployerControl.controls['no_exist_logo_name'].value === false" class="exist-logo">
                                                        <!-- (error)="enteredLogoNotFound()" -->
                                                        <img [src]="existLogoLink" class="img-responsive">
                                                    </div>

                                                    <div class="upload-image-container" *ngIf="otherEmployerControl.controls['no_exist_logo_name'].value === true">
                                                        <div class="inner-container">
                                                            <a data-toggle="modal" data-target="#imageEditorModal">
                                                                <img [src]="perPhotoSrc" class="img-responsive">
                                                                <span class="upload-label" *ngIf="uploadLabelDisplay">Upload Employer Logo</span>
                                                            </a>
                                                        </div>
                                                        <div class="upload-actions">
                                                            <a  *ngIf="!uploadLabelDisplay" class="edit-image" data-toggle="modal" data-target="#imageEditorModal">
                                                                <i class="fa fa-edit" aria-hidden="true"></i>
                                                            </a>
                                                            <a *ngIf="!uploadLabelDisplay" class="delete-image"  (click)="deleteProfilePicture()">
                                                                <i class="fa fa-trash" aria-hidden="true"></i>
                                                            </a>
                                                        </div>
                                                    </div>

                                                    <!-- <div class="persPhoto-div">
                                                        <div class="persPhotoContainer" [ngClass]="{'imgError': imgError}">
                                                            <label for="persPhoto" class="persPhotoLabel">
                                                                <img [src]=perPhotoSrc class="img-responsive persPhoto">
                                                                <input id="persPhoto" type="file" style="display:none;" accept="image/jpeg, image/png" (change)="onFileChanged($event);">
                                                                <span class="upload" *ngIf="uploadLabelDisplay" translate>Upload Employer Logo</span>
                                                                <span  *ngIf="!uploadLabelDisplay" class="edit-photo">
                                                                    <i class="fa fa-edit" aria-hidden="true"></i>
                                                                </span>
                                                            </label>

                                                            <span *ngIf="!uploadLabelDisplay" class="delete-photo"  (click)="deleteProfilePicture()">
                                                                <i class="fa fa-trash" aria-hidden="true"></i>
                                                            </span>
                                                        </div>

                                                        <div class="imgError-div">
                                                            <span style="margin-left: 18px;" *ngIf="imgError" class="error-message" translate>{{imgError}}</span>
                                                        </div>
                                                    </div> -->

                                                </div>
                                            </div>

                                        </div>

                                        <div class="row clearfix pad-top1" [ngClass]="{'has-error':(submitted && !postForm.controls['external_link'].valid) }">
                                            <div class="col-sm-9">
                                                <div class="row">
                                                    <div class="col-sm-4  alignment-right">
                                                        <label translate>Job Opportunity Link</label>
                                                    </div>
                                                    <div class="col-sm-7  focus-no-padding  validate-input">
                                                        <input formControlName="external_link" type="text" class="form-control">
                                                        <span class="custom-underline"></span>
                                                        <span *ngIf="submitted == true && postForm.controls['external_link'].errors?.required" class="error-message" translate>validationMessages.required</span>
                                                        <span *ngIf="submitted && postForm.controls['external_link'].errors?.invalidUrlError" class="error-message" translate>{{postForm.controls['external_link'].errors?.invalidUrlError}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="row wizardButton" style="margin-top:40px;padding-right:20px;">
                                        <div class="col-sm-12 text-right">
                                            <button  class="back back2 custom-btn btn btn-info" type="button" awPreviousStep>Back </button>
                                            <button class="next custom-btn btn btn-info" type="button" awNextStep>Next
                                            </button>
                                            <span id="add">
                                                <span id="save">
                                                    <button class="finish custom-btn btn btn-info"
                                                    [ngClass]="{'disabled':companyHaveProfile == false}" type="button" id="save"
                                                        (click)="submit(form)" (click)="onClickMe()"
                                                        (click)="step_exit('first_step')" [disabled]="!companyHaveProfile || submitWaiting===true" translate>Save
                                                    </button>
                                                </span>
                                            </span>
                                        </div>
                                        <div class="col-sm-12" *ngIf="formValid == false" style="height:60px;">
                                            <div class="alert alert-danger alert-dismissible global-error-msg" role="alert">
                                                Please check the error messages
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div class="row"></div> -->
                                </div>
                            </div>
                        </aw-wizard-step>
                        <aw-wizard-step stepTitle="More Details" [navigationSymbol]="{ symbol: '&#xf067;', fontFamily: 'FontAwesome'  }">
                            <div class="row" [ngClass]="this.postForm.controls['opportunity_for'].value === 'my_company' ? 'visible-checkbox' : 'hidden-checkbox'">
                                <div>
                                    <ng-template awWizardStepTitle let-wizardStep="wizardStep">
                                        <span [ngClass]="{ 'red': form.submitted && postForm.hasError('InvalidLocationError') }">
                                                {{ wizardStep.completed ? "More Details " : "More Details" }}
                                        </span>
                                        <p style="color:#ccc; font-size: 12px;">Enter Additional Information</p>
                                    </ng-template>
                                </div>

                                <div class="details pad-top-4">
                                    <div class="alert alert-info step4-warning" role="alert">
                                        <p>Note that this section is optional, and these are extra values to describe your vacancy.</p>
                                        <p>If you want to receive only CVs that exactly match one or more of these values, please click on the small square on the right of the corresponding value(s).</p>
                                    </div>

                                    <div class="row form-group" *ngIf="YearExpValues.length">
                                        <div class="col-sm-3 col-xs-3 labelAdd">
                                            <label translate>post_advr.year_exp </label>
                                        </div>
                                        <div class="col-sm-9 col-xs-9 validate-input CScreen">
                                            <div class="row">
                                                <p-dropdown class="col-sm-9 col-xs-10" [options]="YearExpValues" optionLabel="name" formControlName="year_of_experience" [filter]="true" [ngClass]="{'has-val':postForm.controls['year_of_experience'].value }">
                                                </p-dropdown>
                                                <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                    <input type="checkbox" [checked]="checklist[0].isSelected" (change)="CheckItemList($event)" name="CheckBox" class="custom-control-input check" id="0" value="is_mandatory_years_of_experience">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row form-group">
                                        <div class="col-xs-3 labelAdd">
                                            <label translate>post_advr.gender</label>
                                        </div>
                                        <div class="col-xs-9" role="group" aria-label="...">
                                            <div class="row">
                                                <div class="col-sm-9 col-xs-10">
                                                    <div class="row">
                                                        <p-radioButton class="col-xs-6"  formControlName="gender" name="gender" value="female" label="female" [(ngModel)]="selectedType_gender"></p-radioButton>
                                                        <p-radioButton class="col-xs-6" formControlName="gender" name="gender" value="male" label="male" [(ngModel)]="selectedType_gender"></p-radioButton>
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                    <input type="checkbox" [checked]="checklist[1].isSelected" name="CheckBox" (change)="CheckItemList($event)"  id="1" value="is_mandatory_gender">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row form-group">
                                        <div class="col-xs-3 labelAdd">
                                            <label translate>post_advr.languages </label>
                                        </div>
                                        <div class="col-xs-9">
                                            <div class="row">
                                                <div class="col-sm-9 col-xs-10  validate-input CScreen translate">
                                                    <ejs-multiselect
                                                        #language
                                                        [dataSource]='languages'
                                                        [value]="value_lang"
                                                        [fields]='remoteFields2'
                                                        mode='Box'
                                                        [allowFiltering]='true'
                                                        [ignoreAccent]='true'
                                                        style="color:rgb(91,211,55)"
                                                        formControlName="languages_temp"
                                                        >
                                                    </ejs-multiselect>
                                                </div>
                                                <!-- maximumSelectionLength="4"  -->
                                                <!-- (select)="selectLanguages($event)" -->
                                                <!-- (change)="OnChange($event)" -->
                                                <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                    <input (change)="CheckItemList($event)" [checked]="checklist[6].isSelected" type="checkbox" name="CheckBox"  id="8" value="is_mandatory_languages">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- <div class="col-xs-12">
                                            you can't add more items
                                        </div> -->

                                    </div>
                                    <div class="row form-group">
                                        <div class="col-xs-3 labelAdd">
                                            <label translate>post_advr.skills</label>
                                        </div>

                                        <div class="col-xs-9">
                                            <div class="row">
                                                <div class="col-sm-9 col-xs-10 validate-input CScreen translate">
                                                    <ng-select
                                                    [items]="skillsDD.items"
                                                    [virtualScroll]="true"
                                                    formControlName="skills"
                                                    [loading]="skillsDD.loading"
                                                    [multiple]="true"
                                                    bindLabel="name"
                                                    notFoundText="No items found"
                                                    [dropdownPosition]="'bottom'"
                                                    (scrollToEnd)="skillsDD.onScrollToEnd()"
                                                    (search)="skillsDD.search($event)"
                                                    [searchFn]="skillsDD.customSearchFn"
                                                    class="form-control"
                                                    >
                                                        <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                                            <span class="ng-value-label">{{item.name}}</span>
                                                            <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                                                                <i class="fa fa-times" aria-hidden="true"></i>
                                                            </span>
                                                        </ng-template>
                                                        <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                                                            {{item.name}}
                                                        </ng-template>
                                                    </ng-select>
                                                    <!-- <ejs-multiselect [value]="value_skill" [dataSource]='skillss' [fields]='remoteFields3' mode='Box' [allowFiltering]='true' [ignoreAccent]='true' maximumSelectionLength="8" style="color:rgb(91,211,55)" formControlName="skills">
                                                    </ejs-multiselect>                                -->
                                                </div>
                                                <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                    <input (change)="CheckItemList($event)" type="checkbox" [checked]="checklist[7].isSelected"  id="9" value="is_mandatory_skills">
                                                </div>
                                            </div>
                                        </div>

                                    </div>


                                    <div class="row form-group" [ngClass]="{'has-error': form.submitted && postForm.hasError('InvalidLocationError')}">
                                        <div class="col-xs-3 labelAdd">
                                            <label translate>post_advr.job_seeker_location</label>
                                        </div>
                                        <div class="col-xs-9">
                                            <div class="row">
                                                <div class="col-sm-9 col-xs-10 validate-input CScreen focus-no-padding" [ngClass]="{'has-val':postForm.controls['location'].value}">
                                                    <input formControlName="location" (focus)="sho($event)" (focusout)="hid($event)" placeholder=" " type="text" class="form-control" (keyup)="clearjobseekerLocationData()" id="location" #googlelocationplace>
                                                    <div style="color: #bbb; margin-top: -10px;" class="suggestion" *ngIf="canSee">
                                                        This field will not be visible to job seekers
                                                    </div>
                                                    <span *ngIf="form.submitted && postForm.hasError('InvalidLocationError')" class="error-message long-error-message" translate>{{ postForm.errors?.InvalidLocationError }}</span>
                                                </div>
                                                <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                    <input (change)="CheckItemList($event)" type="checkbox" [checked]="checklist[9].isSelected" id="10" value="is_mandatory_current_location">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="col-sm-12">
                                            <div class="row form-group focus-container exInformation">
                                                <div class="checkbox col-sm-12 text-center">
                                                    <label class="control-label">
                                                        <input class="extra-check" id="extra"  type="checkbox" [checked]="extraChecked"  (change)="minimize('more-values')">
                                                        <span>&nbsp;More Values</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="extraInformation" style="margin-bottom: 30px;">
                                            <div class="row form-group">
                                                <div class="col-xs-3 labelAdd">
                                                    <label class="labelAdd" translate>post_advr.age</label>
                                                </div>
                                                <div class="col-xs-9">
                                                    <div class="row">
                                                        <div class="col-sm-9 col-xs-10">
                                                            <ng5-slider [options]="options2" [(value)]="minValue2" [(highValue)]="maxValue2" formControlName="age"></ng5-slider>
                                                        </div>
                                                        <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                            <input type="checkbox" [checked]="checklist[2].isSelected" (change)="CheckItemList($event)" name="CheckBox" id="2" value="is_mandatory_age">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row form-group" *ngIf="degreeLevelValues.length">
                                                <div class="col-xs-3 labelAdd">
                                                    <label translate>post_advr.degree_level </label>
                                                </div>
                                                <div  class="col-xs-9">
                                                    <div class="row">
                                                            <!-- style="background: white !important; z-index: 150 !important;"                                                  -->
                                                        <div class="col-sm-9 col-xs-10">
                                                            <p-dropdown [options]="degreeLevelValues" (onChange)="changeDegreeLevel()" optionLabel="name" formControlName="degree_level" [filter]="true" [ngClass]="{'has-val':postForm.controls['degree_level'].value }">
                                                                <ng-template let-degree pTemplate="item">
                                                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                                                        <div>{{degree.label}}</div>
                                                                    </div>
                                                                </ng-template>
                                                            </p-dropdown>
                                                        </div>
                                                        <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                            <input (change)="CheckItemList($event)" [checked]="checklist[4].isSelected" type="checkbox" name="CheckBox" id="5" value="is_mandatory_degree_level">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row form-group">
                                                <div class="col-xs-3 labelAdd">
                                                    <label translate>education.educationField</label>
                                                </div>
                                                <div class="col-xs-9">
                                                    <div class="row">
                                                        <div class="col-sm-9 col-xs-10">
                                                                <!-- [value]="value_lang"  -->
                                                            <ejs-multiselect
                                                                #educationField
                                                                [dataSource]="educationFields"
                                                                formControlName="educations"
                                                                [fields]='remoteFields5'
                                                                mode='Box'
                                                                [allowFiltering]='true'
                                                                [ignoreAccent]='true'
                                                                >
                                                            </ejs-multiselect>
                                                            <span class="custom-underline"></span>

                                                            <!-- <span *ngIf="majorMinorValid == false" class="error-message er-msg-major" translate>You should add major</span> -->
                                                            <!-- <label class="majorLabel"  translate>
                                                                <input type="checkbox" [checked]="!displayMinor"
                                                                    name="checkMajor" class="custom-control-input"
                                                                    (change)="disableMinor($event)">
                                                                post_advr.equivalent_major
                                                            </label> -->
                                                        </div>
                                                        <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                            <input (change)="CheckItemList($event)" [checked]="checklist[5].isSelected" type="checkbox" name="CheckBox"  id="6" value="is_mandatory_education">
                                                            <button type="button" class="btn btn-primary btn-fa-info request-new-education" (click)="requestNewValue('educationField')" pTooltip="Request to add new value to the predefined education fields values" tooltipPosition="top">
                                                                <i class="fa fa-info" aria-hidden="true"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row form-group">
                                                <div class="col-xs-3 labelAdd">
                                                    <label translate>post_advr.nationality</label>
                                                </div>
                                                <div class="col-xs-9">
                                                    <div class="row">
                                                        <div class="col-sm-9 col-xs-10">
                                                            <ejs-multiselect [dataSource]='nationalites' [value]="value_nation"
                                                                [fields]='remoteFields4' mode='Box' [allowFiltering]='true'
                                                                [ignoreAccent]='true' style="color:rgb(91,211,55)"
                                                                formControlName="nationality_temp_id">
                                                            </ejs-multiselect>
                                                            <!-- maximumSelectionLength="6"   -->
                                                        </div>
                                                        <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                            <input (change)="CheckItemList($event)" [checked]="checklist[3].isSelected" type="checkbox" name="CheckBox" id="3" value="is_mandatory_nationality">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row form-group">
                                                <div class="col-xs-3 labelAdd">
                                                    <label translate>post_advr.certification</label>
                                                </div>
                                                <div class="col-xs-9">
                                                    <div class="row">
                                                        <div class="col-sm-9 col-xs-10" [ngClass]="{'has-val':postForm.controls['certification'].value}">
                                                            <input formControlName="certification" placeholder=" " type="text" class="form-control">
                                                            <span class="custom-underline"></span>
                                                        </div>
                                                        <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                            <input (change)="CheckItemList($event)" type="checkbox" [checked]="checklist[8].isSelected" id="0" value="is_mandatory_certification">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row form-group">
                                                <div class="col-xs-3 labelAdd">
                                                    <label translate>post_advr.driving_license</label>
                                                </div>
                                                <div class="col-xs-9">
                                                    <div class="row">
                                                        <div class="col-sm-9 col-xs-10" [ngClass]="{'has-val':postForm.controls['driving_license_temp'].value}">
                                                            <p-dropdown [options]="driving_license" optionLabel="label" formControlName="driving_license_temp"
                                                                [filter]="true" [ngClass]="{'has-val':postForm.controls['driving_license_temp'].value}">
                                                                <ng-template let-car pTemplate="item">
                                                                    <div class="ui-helper-clearfix" style="position: relative;height:25px;">
                                                                        <img *ngIf="car.label" src="./assets/images/driving/{{car.label}}.png" style="width:24px; top:1px; float:left;" />
                                                                        <div style="font-size:14px;margin-top:0px">
                                                                            &nbsp; {{car.label}}</div>

                                                                    </div>
                                                                </ng-template>
                                                                <ng-template let-item pTemplate="selectedItem">
                                                                    <span style="vertical-align:middle">{{item.label}}</span>
                                                                    <img *ngIf="item.label" src="./assets/images/driving/{{item.label}}.png" style="width:16px;vertical-align:middle" />

                                                                </ng-template>
                                                            </p-dropdown>
                                                            <span class="custom-underline"></span>
                                                        </div>
                                                        <div class="col-sm-2 col-xs-2 custom-checkbox condition-checkbox">
                                                            <input (change)="CheckItemList($event)" type="checkbox" [checked]="checklist[10].isSelected" id="11" value="is_mandatory_driving_license">
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>

                                        </div>
                                    </div>

                                    <div class="row wizardButton">
                                        <div class="col-xs-3"></div>
                                        <div class="col-sm-8 col-xs-9 text-right">
                                            <button class="back custom-btn btn btn-info" type="button" awPreviousStep>Back </button>
                                            <button class="finish custom-btn btn btn-info" [ngClass]="{'disabled':companyHaveProfile == false} " type="button" id="save" (click)="submit(form)" (click)="onClickMe()" (click)="step_exit('first_step')" [disabled]="!companyHaveProfile || submitWaiting===true" translate>Save</button>
                                        </div>
                                        <div class="col-sm-11 col-xs-12" *ngIf="formValid == false" style="height:60px;">
                                            <div class="alert alert-danger alert-dismissible global-error-msg" role="alert">
                                                Please check the error messages
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row tooltip">
                                <div class="col-sm-8 col-xs-6"></div>
                                <div class="col-sm-4 col-xs-6">
                                    <button style="margin-right: 2px;" class="custom-btn btn btn-info" type="button" awPreviousStep>Back </button>
                                    <button class="custom-btn btn btn-info" type="button" id="save" [disabled]="submitted" (click)="submit(form)">Finish </button>
                                    <span *ngIf="submitted" class="tooltiptext">The Request is being Processed...</span>
                                </div>
                            </div>
                        </aw-wizard-step>
                    </aw-wizard>
                </div>

            </form>
        </div>

    </div>
</div>


<div class="modal fade image-editor-modal" id="imageEditorModal"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Logo Editor</h4>
            </div>
            <div class="modal-body">
                <app-image-editor
                (closeModalPopup)="handleImageEditorPopup($event)">

                </app-image-editor>
            </div>
        </div>
    </div>
</div>

<app-ai-job-description #aiJobDescription (generatedJobData)="fillJobDataFromAi($event)"></app-ai-job-description>
