<app-pre-loader [show]="loader"></app-pre-loader>
<div class="home-page-content">
  <div class="container">
    <div>
      <form #form="ngForm"
            [formGroup]="signUpForm"
            class="form-horizontal validate-form"
            (ngSubmit)="signUp()">
        <br>
        <h1>Job Seeker Sign Up</h1>
        <br>
        <div class="row" *ngIf="alertResetMessage">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding">
            <div class="alert alert-danger alert-dismissible" role="alert" *ngIf="alertResetMessage">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              {{alertResetMessage}} 
              <a class="help-link" *ngIf="helpLink" routerLink={{helpLink}}>Sign in</a>
            </div>
          </div>
        </div>
        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && isInvalid('first_name')) || (form.submitted && isInvalidPattern('first_name')) }">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':signUpForm.controls['first_name'].value}">
            <input  type="text" formControlName="first_name" class="form-control" id="exampleInputFirstName" >
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">First Name</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('first_name')">Required</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidPattern('first_name') && signUpForm.controls['first_name'].value">Please enter only characters and numbers</span>
          </div>
        </div>
        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && isInvalid('last_name')) || (form.submitted && isInvalidPattern('last_name'))}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':signUpForm.controls['last_name'].value}">
            <input  type="text" formControlName="last_name" class="form-control" id="exampleInputLastName" >
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Last Name</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('last_name')">Required</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidPattern('last_name') && signUpForm.controls['last_name'].value">Please enter only characters and numbers</span>
          </div>
        </div>
        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': (form.submitted && isInvalid('email')) || (form.submitted && isInvalidSyn('email') && signUpForm.controls['email'].value) || resetMessage}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':signUpForm.controls['email'].value}">
            <input  type="email" formControlName="email" class="form-control" id="exampleInputEmail2" (keydown)="resetMessage=null" >
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Email address</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('email')">Required</span>
            <span class="error-message " *ngIf="resetMessage">{{resetMessage}}</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidSyn('email') && signUpForm.controls['email'].value">Invalid Email Syntax</span>
          </div>
        </div>
        <div class="form-group focus-container has-feedback" [ngClass]="{'has-error': form.submitted && (isInvalid('password') || isInvalidMin('password') || isInvalidMax('password'))}">
          <div class="col-sm-3 alignment-right">
          </div>
          <div class="col-sm-6 focus-no-padding"  [ngClass]="{'has-val':signUpForm.controls['password'].value}">
            <input  type="{{ type }}" formControlName="password" class="form-control" id="exampleInputPassword2" >
            <span type="button" 
              (click)="toggleShowPassword()"
              class="fa fa-fw password-eye-icon"  
              [ngClass]="{
                'fa-eye' : show,
                'fa-eye-slash' : !show
              }">
            </span>
            <span class="custom-underline"></span>
            <label class="control-label custom-control-label">Password</label>
          </div>
          <div class="col-sm-3">
            <span class="error-message " *ngIf="form.submitted && isInvalid('password')">Required</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidMin('password')">Too Short Password</span>
            <span class="error-message " *ngIf="form.submitted && isInvalidMax('password')">Too Long Password</span>
          </div>
        </div>
        <div class="form-group focus-container has-feedback">
          <div class="col-sm-3 alignment-right"></div>
          <div class="col-sm-6 focus-no-padding">
            <password-strength-meter [password]="password.value" enableFeedback="true"></password-strength-meter>
          </div>
          <div class="col-sm-3"></div>
        </div>

        <div class="form-group focus-container has-feedback"  [ngClass]="{'has-error': form.submitted && !signUpForm.controls['agree_terms'].valid }">
          <div class="col-sm-3 alignment-right"></div>
          <div class="col-sm-6 focus-no-padding">
            <input type="checkbox" formControlName="agree_terms" id="agree-terms">
            <label for="agree-terms" class="control-label">
              I agree to the 
              <a routerLink="/i/terms" target="_blank">terms and conditions</a> and
              <a routerLink="/i/privacy-policy" target="_blank"> privacy policy</a>
            </label>
          </div>
          <div class="col-sm-3">
            <span class="error-message" *ngIf="form.submitted && signUpForm.controls['agree_terms'].errors?.invalidCheckboxTrue" translate>{{signUpForm.controls['agree_terms'].errors?.invalidCheckboxTrue}}</span>
          </div>
        </div>

        <div class="text-center">
          <button type="submit" class="btn btn-success">Sign Up</button>

          <div class="or-container">
            <hr>
            <div class="or-text">
              <span>OR</span>
            </div>
          </div>
          
          <div class="social-login-btns">
            <button class="btn btn-default facebook-btn" (click)="$event.preventDefault();facebookLogin()"><i class="fa fa-facebook"></i> Sign up with Facebook</button> &nbsp;
            <!-- <button class="btn btn-default google-btn" (click)="$event.preventDefault();signinWithGoogle()">Sign up with Google <i class="fa fa-google"></i></button> -->
            <div id="googleLoginButtonDiv"></div> 
          </div>
        </div>
        <!-- <p>  {{ form.value | json }} </p> -->
      </form>
    </div>
  </div>
</div>
