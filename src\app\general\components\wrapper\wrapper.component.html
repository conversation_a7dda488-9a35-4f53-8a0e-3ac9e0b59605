<un-auth-top-navbar *ngIf="role === 'unauth'" [inHomePage]="false"></un-auth-top-navbar>
<user-topbar *ngIf="(role ==='ROLE_JOB_SEEKER') || (role === 'ROLE_ADMIN')|| (role === 'ROLE_CONTACT_ADMIN')" [inHomePage]="false"></user-topbar>
<company-topbar *ngIf="role === 'ROLE_EMPLOYER'" [inHomePage]="false"></company-topbar>

<confirm-message *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_EMPLOYER'"></confirm-message>

<!-- <page-navbar [navType]="'main'"></page-navbar> -->

<div class="general-wrapper">
    <router-outlet></router-outlet>
</div>

<div class="flex-space-fix"></div>
<app-footer></app-footer>

