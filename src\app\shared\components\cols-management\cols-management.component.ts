import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges, EventEmitter, Output, ViewChildren, QueryList, ElementRef } from '@angular/core';
declare var $: any;

@Component({
  selector: 'cols-management',
  templateUrl: './cols-management.component.html',
  styleUrls: ['./cols-management.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ColsManagementComponent implements OnInit, OnChanges{
  @Input() fromComponent;
  @Input() standardItems;
  @Input() allItems ;
  @Input() selectedItems ;
  @Input() maxColAllowed;
  @Output() closeModalPopup = new EventEmitter();

  arr1: any;
  temp1: any;
  arr2: any;
  temp2: any;
  // we hava a default value here so in the first time when ngOnChanges called it can pass the condition
  // note that in angular lifecycle hooks ngOnChanges called first then ngOnInit
  remainingColsAllowed = 6;
  // remaining number of columns that the user can add - this variable is used in preview
  remainingColsToAdd;

  initialStateBackup = {'arr1':[],'arr2':[],'remainingColsAllowed':0,'remainingColsToAdd':0}
  @ViewChildren("checkboxes") checkboxes: QueryList<ElementRef>;
  
  constructor() {
  }

  initializeItems(){
    this.arr1 = [...this.allItems];

    this.temp1 = [...this.selectedItems];
    this.arr2 = [];
    this.temp2 = [];
    this.exchangeItems('1', 'arr1', 'arr2');
    this.remainingColsAllowed = this.maxColAllowed - this.arr2.length ;
    this.remainingColsToAdd = this.remainingColsAllowed;
  }

// backup of initial state
  initialStateBackupFunc(){
    this.initialStateBackup.arr1 = this.arr1;
    this.initialStateBackup.arr2 = this.arr2;
    this.initialStateBackup.remainingColsAllowed = this.remainingColsAllowed;
    this.initialStateBackup.remainingColsToAdd = this.remainingColsToAdd;
  }

  ngOnInit(): void {
    this.initializeItems();
    this.initialStateBackupFunc();
  }


  // ngOnChanges is called just in recieve cvs' cols management!! don't know the reason
  ngOnChanges(changes: SimpleChanges) {
    if(this.arr1 !== undefined){
      if (changes['selectedItems'] && changes['selectedItems']['currentValue']) {
        this.arr2 = [];
        this.temp2 = [];
        this.temp1 = changes['selectedItems']['currentValue'];
        this.exchangeItems('1', 'arr1', 'arr2');
      }
    }
  }

  exchangeItems(temp,src,dist,action?) {
    if(this.remainingColsAllowed >= 0){
      if(action && action === 'add'){
        if(this.temp1.length === 0){
          alert("Please choose the columns you want to add");
        }
        this.remainingColsAllowed = this.maxColAllowed - (this.arr2.length + this.temp1.length) ;
        if(this.remainingColsAllowed >= 0){
          this.exchangeItemsBehaviour(temp,src,dist);
          this.remainingColsToAdd = this.remainingColsAllowed;
        }
        else {
          // reset to the previews state and don't add the checked
          this.remainingColsAllowed = this.remainingColsToAdd;
        }
      }
      else if(action && action === 'remove'){
        if(this.temp2.length === 0){
          alert("Please choose the columns you want to hide");
        }
        this.remainingColsAllowed = this.maxColAllowed - (this.arr2.length - this.temp2.length) ;
        if(this.remainingColsAllowed > this.maxColAllowed){
          this.remainingColsAllowed = this.maxColAllowed;
        }
        this.remainingColsToAdd = this.remainingColsAllowed;
        this.exchangeItemsBehaviour(temp,src,dist);
      }
      else {
        this.exchangeItemsBehaviour(temp,src,dist);
      }
    }
  }

  exchangeItemsBehaviour(temp,src,dist){
    this[dist] = this[dist].concat(this['temp' + temp]);
    for ( let i = 0 ; i < this['temp' + temp].length; i++) {
       this[src] =   this[src].filter((el) => el['field'] !== this['temp' + temp][i]['field']);
    }
    this['temp' + temp] = [];
  }

  onCheckboxChange(e, item , dist) {
     if (e.target.checked) {
      var El;
       if(this.fromComponent === 'advr-interface'){
        El = {
          'header' : item['header'],
           'field' : item['field'],
           'selected' : false,
        };
       }
       else if(this.fromComponent === 'cvs-table'){
        El = {
          'title' : item['title'],
           'field' : item['field'],
           'selected' : false,
           'showInList':item['showInList'],
        };
       }
      
     this[dist].push(El);
     } else {
      this[dist] =   this[dist].filter((el) => el['field'] !== item['field']);
     }
  }

  save() {
    /// because we don't  have more time  we didn't change any thing on backend
    //// so we will make the following processing
    //// because  requirements needs to be = ['field1' , 'field2', .... etc]
     // tslint:disable-next-line:prefer-const
     let requirements = [];
         this.arr2.forEach(element => {
            requirements.push(element['field']);
         });
    this.closeModalPopup.emit({'new': requirements});
    this.initialStateBackupFunc();
    
    //to reset all checkboxes to unchecked state, (if user check a checkbox and click save without add/hide actions)
    // because it causing issue in cvs table component
    if(this.fromComponent === 'cvs-table'){
      this.checkboxes.forEach((element) => {
        element.nativeElement.checked = false;
      });
    }
   
   $('#ColsManagementModal').modal('toggle');
  }

  //reset items to initial state
  resetAll(){
   this.arr1 = this.initialStateBackup.arr1;
   this.arr2 = this.initialStateBackup.arr2;
   this.remainingColsAllowed = this.initialStateBackup.remainingColsAllowed;
   this.remainingColsToAdd = this.initialStateBackup.remainingColsToAdd;

   for ( let i = 0 ; i < this.arr1.length; i++) {
    this.arr1[i].selected = false;
   }
  }

  cancel(){
    this.resetAll();
    $('#ColsManagementModal').modal('toggle');
  }

  // cancel($event) {
  //   this.resetAll();
  //   $event.preventDefault();
  //   $('#ColsManagementModal').modal('toggle');
  // }
}
