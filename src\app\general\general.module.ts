import { CompanyFormComponent } from 'app/company/components/company-form/company-form.component';
import { NgModule } from '@angular/core';
import { GeneralRoutingModule } from './general-routing.module';
import { SharedModule } from 'shared/shared.module';

import { HelpTopicComponent } from './components/help-topic/help-topic.component';
import { SubHelpComponent } from './components/sub-help/sub-help.component';
import { WrapperComponent } from './components/wrapper/wrapper.component';
import { FaqComponent } from './components/faq/faq.component';
import { HelpComponent } from './components/help/help.component';
import { MainHelpComponent } from './components/main-help/main-help.component';
import { ContactFormComponent } from './components/contact-form/contact-form.component';
import { HelpService } from './services/help.service';
import { FaqService } from './services/faq.service';
import { Title, BrowserModule } from '@angular/platform-browser';
import { ArticlesUserInterfaceComponent } from 'app/user/components/articles-user-interface/articles-user-interface.component';
import { ArticlesService } from 'app/user/cv-services/articles.service';
import { LanguageService } from 'app/admin/services/language.service';
import { ContactService } from './services/contact.service';
import { MessageService } from 'primeng/api';
import { CommonModule } from '@angular/common';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import { CompanyPreviewComponent } from './components/company-preview/company-preview.component';
import { ResumePreviewComponent } from './components/resume-preview/resume-preview.component';
import { HelpSearchComponent } from './components/help-search/help-search.component';
import { HelpHeaderComponent } from './components/help-header/help-header.component';
import { ErrorPageComponent } from './components/error-page/error-page.component';
import { ForbiddenErrorComponent } from './components/forbidden-error/forbidden-error.component';
import { OfflineErrorComponent } from './components/offline-error/offline-error.component';
import { TermsComponent } from './components/terms/terms.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { ResumeBuilderComponent } from './components/resume-builder/resume-builder.component';
import { CvTemplatesComponent } from './components/cv-templates/cv-templates.component';
import { AtsFriendlyCvComponent } from './components/ats-friendly-cv/ats-friendly-cv.component';
import { AdvancedJobSearchComponent } from './components/advanced-job-search/advanced-job-search.component';
import { InterviewTipsComponent } from './components/interview-tips/interview-tips.component';
import { AiJobPostingComponent } from './components/ai-job-posting/ai-job-posting.component';
import { AdvertisementDashboardComponent } from './components/advertisement-dashboard/advertisement-dashboard.component';
import { InboxDashboardComponent } from './components/inbox-dashboard/inbox-dashboard.component';
import { CveekPluginComponent } from './components/cveek-plugin/cveek-plugin.component';
import { AboutUsComponent } from './components/about-us/about-us.component';

@NgModule({
  imports: [
    GeneralRoutingModule,
    ProgressSpinnerModule,
    CommonModule,
    SharedModule
  ],
  declarations: [
    FaqComponent,
    WrapperComponent,
    HelpComponent,
    SubHelpComponent,
    HelpTopicComponent,
    MainHelpComponent,
    ArticlesUserInterfaceComponent,
    ContactFormComponent,
    CompanyPreviewComponent,
    ResumePreviewComponent,
    HelpSearchComponent,
    HelpHeaderComponent,
    ErrorPageComponent,
    ForbiddenErrorComponent,
    OfflineErrorComponent,
    TermsComponent,
    PrivacyPolicyComponent,
    ResumeBuilderComponent,
    CvTemplatesComponent,
    AtsFriendlyCvComponent,
    AdvancedJobSearchComponent,
    InterviewTipsComponent,
    AiJobPostingComponent,
    AdvertisementDashboardComponent,
    InboxDashboardComponent,
    CveekPluginComponent,
    AboutUsComponent,

  ],
  providers : [
    FaqService,
    HelpService,
    Title,
    ArticlesService,
    ContactService,
    Title,
    LanguageService,
    MessageService
  ]
})
export class GeneralModule { }
