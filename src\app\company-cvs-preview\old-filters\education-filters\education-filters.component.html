<div class="container-fluid form-horizontal">
    <div class="row">
        <div class="col-md-10 col-sm-11 col-xs-12">
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Min Degree Level</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div">
                    <ng5-slider class="form-control" [options]="dataModel.digreeOptions" [(value)]="dataModel.digreeValue"></ng5-slider>
                </div>
            </div>
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Degree Level</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-multiSelect [options]="filterData.degree_level" [(ngModel)]="dataModel.degreeLevel" optionLabel="name" [filter]="true" filterBy="label,value.name" [showTransitionOptions]="'1ms'" [hideTransitionOptions]="'2ms'" styleClass="cust-p-multiselect" defaultLabel="Degree Level">
                    </p-multiSelect>
                    <span class="custom-underline"></span>
                </div>
            </div>
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Major</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-autoComplete field="name" inputId="id" styleClass="form-control" [suggestions]="temp.majors" (completeMethod)="filterArray($event , 'majors')" [(ngModel)]="dataModel.major" placeholder="Major">
                    </p-autoComplete>
                    <span class="custom-underline"></span>
                </div>
            </div>
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Minor</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-autoComplete field="name" inputId="id" styleClass="form-control" [suggestions]="temp.minors" (completeMethod)="filterArray($event , 'minors')" [(ngModel)]="dataModel.minor" placeholder="Minor">
                    </p-autoComplete>
                    <span class="custom-underline"></span>
                </div>
            </div>
        </div>
    </div>
    <div class="filters-buttons">
        <button (click)="sendFilters()" class="btn btn-success">Apply</button>
        <button (click)="hideModal()" class="btn btn-default">Cancel</button>
    </div>

</div>