import {AbstractControl, FormGroup, ValidationErrors} from '@angular/forms';


export class CompanyInfoValidators {
    static companyLinkValidator(control: AbstractControl): ValidationErrors | null {
      let type = control.get('type').value;
      let url = control.get('url').value;
      let typeId = control.get('social_media_id').value;
  
      if ((typeId !== '' && url === '')) {
        return {'companyLinkError': 'validationMessages.pleaseChooseValidSMProviderValue'};
      }
      if ((typeId === '' && url !== '' )) {
        return {'companyLinkError': 'validationMessages.pleaseChooseProvider'};
      } else {
        return null;
      }
  
    }
  }