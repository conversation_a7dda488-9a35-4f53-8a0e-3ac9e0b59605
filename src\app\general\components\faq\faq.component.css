.custom-container{
  padding:35px 50px;
}
a:link {
  cursor: pointer;
  text-decoration:none;
}
h3.company-heading {
  margin-top: 0px !important;
}
div.content-col.company-content-col {
  padding-top: 130px;
}
.fa.fa-question {
  padding-right: 10px;
  transition: all 0.5s ease-in-out;
}
div, table, td,th,p,h3,form , p-accordion, .ui-accordion{
  font-family: 'Exo2-Regular', sans-serif;
}
.container {
  width: 100%;
  height: 100%;
}
.title{
  color:#4876BA;
  /* color:#005b9f; */
  margin-bottom: 25px;
  font-size:30px;
}
span.languages button.btn {
  float: right;
  margin-top: 30px;;
}
li.question {
  cursor: pointer;
  color: #7b7b7b;
  overflow-x: auto;
  transition: all 0.5s ease-in-out;
}
/*li.question:hover {
font-size: 1.1em;
background-color: #e3ecef;
}*/

.list-group-item {
  border: none;
}

li.list-group-item.answer {
  background-color: #f1fafd;
  border-left: 2px solid #bbb;
  border-bottom-left-radius: 0px;
  border-right: 2px solid #bbb;
  border-bottom-right-radius: 0px;
  color: #0f0f40;
  font-weight: 500;
  text-decoration: wavy;
  font-size: 1.1em;
  overflow-x: auto;
}

/* ----------------------------------------------------------------------------------------------------- */
/* ===================================================================================================== */

/* start of new  */

a {
  cursor: pointer;
}

.badge.category-badge {
  padding: 5px 10px;
  margin-top: 12px;
  margin-bottom: 12px;
  color:#4876BA;
  /* color:#005b9f; */
  background-color: transparent;
  cursor: pointer;
  display: block;
  text-align: left !important;
}
.badge.category-badge h2{
  font-size: 20px;
  font-weight: bold;
  margin:0;
}
div, table, td,th,p,h2,form ,ul, li, ol {
  font-family: 'Exo2-Regular', sans-serif;
}

.container {
  background-color: white;
  padding:50px;
  border-radius: 10px;
}
span.input-group-btn button.btn.btn-default {
  cursor: default;
  background-color: whitesmoke;
}
.input-group {
  /*padding-left: 10px;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 100%;*/
  padding:0 20px;
  margin:5px 0;
  float: right;
}

input.form-control {
  height: 34px;
  width: calc(50%);
  transition: all 0.5s ease-in-out;
}
span.languages button.btn {
  float: right;
  margin-top: 30px;;
}


.list-group-item>.badge.badge-primary.category-badge {
  float: left;

}
ul.list-group.sub-group-list {
  margin-top: 0px;
}
ul.list-group.sub-group-list  .list-group-item {
  margin-left: 10px;
  margin-right: 10px;
  border-color: #f9f9f9;
  transition: all 0.5s ease-in-out;
}

/*ul.list-group.sub-group-list  .list-group-item:hover{
  font-size: 1.1em;
  background-color: #e3ecef;
}*/

a {
  display: block;
}
/*a:hover {
 text-decoration: none;
}*/
a.show {
  padding-bottom: 10px;
}

div.header {

/*..*/
position: fixed;
  /* top: 67px; */
  font-size:15px;
  width: 100%;
  list-style: none;
  /* background: #f2f2f2; */
  background: #3D7BCE;
  z-index: 999;
  text-align: center;
  /* border-bottom: 1px solid #ddd; */
}

.search-input {
  width: 100%;
  font-size: 15px;
  color: #686F7A;
  border-radius: 1px;
  border: 1px solid #c5c5c5;
  box-sizing: border-box;
  margin: 8px 0 8px 36px;
  height: 32px;
  padding-left: 20px;
  padding-right: 20px;
  -webkit-appearance: none;
  font-weight: normal;
  transition: border .12s ease-in-out;
  background-color: white;
  display:inline-block;
}

/* .form-control.search-input {
    width: 100%;
    font-size: 15px;
    color: #686F7A;
    border-radius: 1px;
    border: 1px solid #c5c5c5;
    box-sizing: border-box;
    padding-left: 40px;
    padding-right: 20px;
    -webkit-appearance: none;
    font-weight: normal;
    transition: border .12s ease-in-out;
    background-color: white;
} */
div.lang-col {
    margin-top: 17px;
    padding-right: 0px;
}

.card {

}
/*.card:hover {
  box-shadow:  1px 2px 10px #686F7A;
}*/
.content-row{
  margin-top: 10px;
}

.cat-col{
  padding-right: 10px;
  padding-left: 10px;
}

:host ::ng-deep p-progressSpinner svg circle {
  fill:transparent;
}


:host ::ng-deep .ui-accordion .ui-accordion-header ,.ui-accordion-content	{
  background-color: white !important;
}
:host ::ng-deep .ui-accordion .ui-accordion-header a{
  background-color: white !important;
  color:#7b7b7b;
  border: none;
}
:host ::ng-deep .ui-accordion .ui-accordion-header a:hover{
  border: none;

}
:host ::ng-deep .ui-accordion-header .ui-state-default a{
  background-color: white!important;
  color: #7b7b7b;

}
:host ::ng-deep .ui-accordion .ui-accordion-header:not(.ui-state-disabled).ui-state-active a{
  /*border:1px solid #7b7b7b;*/
  color: #7b7b7b;
}
:host ::ng-deep .ui-accordion .ui-accordion-header:not(.ui-state-disabled).ui-state-active:hover a{
  color: #7b7b7b;
  border: none;

}
:host ::ng-deep .ui-accordion .ui-accordion-header:not(.ui-state-disabled).ui-state-active a{
  border: none;

}
:host ::ng-deep .ui-accordion .ui-accordion-header a:focus{
  box-shadow: none !important;
}

:host ::ng-deep .ui-state-active{
  border: none;

}
:host ::ng-deep .ui-state-default{
  border: none;

}
:host ::ng-deep .ui-accordion .ui-accordion-header:not(.ui-state-active):not(.ui-state-disabled):hover a{
  border:none;
  color:#4876BA;
  /* color:#005b9f; */
}
:host ::ng-deep .ui-accordion .ui-accordion-header:not(.ui-state-active):not(.ui-state-disabled):hover a .ui-accordion-toggle-icon{
  color:#4876BA;
  /* color:#005b9f; */
}
:host ::ng-deep .ui-accordion .ui-accordion-content{
  border:none;
}

:host ::ng-deep .ui-accordion .ui-accordion-header a{
  padding: 0.571em 0em;
}
:host ::ng-deep .ui-accordion .ui-accordion-header:not(.ui-state-disabled).ui-state-active a .ui-accordion-toggle-icon{
  color: #848484 !important;
}
:host ::ng-deep .ui-accordion .ui-accordion-header h3{
  font-size: 15px;
  font-weight: bold;
  display: inline;
}
.no-results{
  font-size: 16px;
}
/* Start Responsive  */
@media screen and (max-width: 767px ) {
  .custom-container{
    padding:10px 15px;
  }
  .title{
    margin-top: 32px;
    margin-bottom: 20px;
    font-size: 24px;
  }
  .search-input{
    margin: 8px 0 8px 0;
  }
}


