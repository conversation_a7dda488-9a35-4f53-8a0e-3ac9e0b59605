import { Subject } from 'rxjs';
import { Place } from './../../Models/place';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { DataMap } from 'shared/Models/data_map';
import { GeneralService } from 'app/general/services/general.service';
import * as $ from "jquery";
import { DataModel } from './DataModel';

@Component({
  selector: 'google-location',
  templateUrl: './google-location.component.html',
  styleUrls: ['./google-location.component.css']
})
export class GoogleLocationComponent implements OnInit {

 // private ngUnsubscribe: Subject<any> = new Subject();
  currentType: string = 'currentLocation';
  currentLocation: Place = new Place();

  ////////////map
  @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: any;

  @Input('location') location_input: any;
  @Input('displayDistanse') display_distanse: boolean;
  @Input('distanse') distanse_input: number;
  @Input('unitDistanse') unit_distanse_input: any;
  dataModel: any;
  dataMap = new DataMap();
  currentSub: Subject<any> = new Subject();


  constructor(private generalService: GeneralService) {
    
    this.dataModel = new DataModel();
  }

  ngOnInit(): void {
    this.dataModel.initializeDistance(this.unit_distanse_input, this.distanse_input);
    this.initializeLocation();
  }
  /////////////
  initializeLocation() {
    this.dataModel.initializeLocation(this.location_input);
    $('#locationSlected').focus();
   
  }
  ////// Location & map //////
  /////////////////////////  -------
  private getLocationPlacefromLocationAutoComplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googlelocationplaceLocationRef.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry'] // 'name
      });
    
    this.dataModel.readyCurrent = true;
    google.maps.event.addListener(autocomplete, 'place_changed', () => {
      const place = autocomplete.getPlace();
      this.invokeEvent(place);
    });
  }
  ngAfterViewInit() {
    this.getLocationPlacefromLocationAutoComplete();

  }
  ////////////////////////  ----------


  /////////// map
  // notifyBirthMap(val) {
  //   this.currentSub.next(val);
  // }

  invokeEvent(place: object) {
    let data_location = this.dataMap.getLocationDataFromGoogleMap(place);
    this.dataModel.cityLocation = data_location.city;
    this.dataModel.countryLocation = data_location.country;
    this.dataModel.countryCodeLocation = data_location.country_code;
    this.dataModel.latLocation = data_location.latitude;
    this.dataModel.lngLocation = data_location.longitude;
    this.dataModel.location = {
      'city': this.dataModel.cityLocation, 'country': this.dataModel.countryLocation,
      'latitude': this.dataModel.latLocation, 'longitude': this.dataModel.lngLocation, 'country_code': this.dataModel.countryCodeLocation
    };
    this.generalService.notify('placeChanged', 'location', 'location',
      {
        'location': this.dataModel.location,
        'unit_distance': this.dataModel.unit_distance ? this.dataModel.unit_distance.value : null, 'distance': this.dataModel.distance ? this.dataModel.distance : null
      });
    

  }
  //////////////



  // unitDistance
  // setSelectedUnitDistance() {
  //   this.generalService.notify('unitDistanceChanged', 'location', 'location',
  //     {
  //       'location': this.dataModel.location,
  //       'unit_distance': this.dataModel.unit_distance.value, 'distance': this.dataModel.distance
  //     });
   
    
  // }
  setDistanse($event) {
    if(this.dataModel.unit_distance && this.dataModel.distance){
      this.generalService.notify('unitDistanceChanged', 'location', 'location',
      {
        'location': this.dataModel.location,
        'unit_distance': this.dataModel.unit_distance.value, 'distance': this.dataModel.distance
      });
    }
  }
  // initializePlaceData() {
  //   this.mapToFormService.personalMapData.skip(1).takeUntil(this.ngUnsubscribe).subscribe(
  //     (place: Place) => {

  //       if (place.type === 'currentLocation') {
  //         this.currentLocation = place;
  //       }
  //       this.dataModel.setCurrentLocationControls(this.currentLocation);
  //     }
  //   );
  // }
}
