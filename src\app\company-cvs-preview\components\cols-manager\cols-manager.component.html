<!-- not using the component -->
<div class="modal fade" id="ColsModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModal2Label" translate>
                    Columns Management
                </h4>
            </div>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-4 col-xs-5">
                        <div class="cols-container">
                            <div class="cols-header">Hide</div>
                            <div class="cols-body">
                                <div *ngFor="let item  of arr1; let i = index" class="check-block">
                                    <input type="checkbox" (change)="onCheckboxChange($event, item, 'temp1')" [(ngModel)]="item.selected" name="unSelectedValue" value="{{item.field}}" id="{{item.field}}" class="checkbox-label" />
                                    <label for="{{item.field}}">{{item.title}}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4 col-xs-2 actions-col">
                        <p class="remainingColsMsg" [ngClass]="(remainingColsToAdd > 0)?'success':'error'">Remaining columns you can add : {{remainingColsToAdd}}</p>
                        <div class="form-group actions-block">
                            <button (click)="exchangeItems(1,'arr1','arr2','add')" type="button" class="btn btn-primary fixed-width-btn">
                                <span class="btn-txt btn-mar-right">Add</span>
                                <span class="glyphicon glyphicon-arrow-right btn-icon"></span>
                            </button>
                            <button (click)="exchangeItems(2,'arr2','arr1','remove')" type="button" class="btn btn-danger fixed-width-btn">
                                <span class="glyphicon glyphicon-arrow-left btn-icon"></span>
                                <span class="btn-txt btn-mar-left">Remove</span>
                            </button>
                        </div>
                        <div class="row div-margin-top-20">
                            <div class="col-xs-12 text-center">
                                <button (click)="save()" type="button" class="btn btn-success save-btn fixed-width-btn">
                                    <span class="fa fa-floppy-o btn-icon"></span>
                                    <span class="btn-txt btn-mar-left">Save</span>
                                </button>
                                <button (click)="cancel($event)" class="btn btn-default cancel-btn fixed-width-btn">
                                    <span class="fa fa-times btn-icon"></span>
                                    <span class="btn-txt btn-mar-left">Cancel</span>
                                </button>
                            </div>
                        </div>
                        <!-- <div class="form-group">
                            <button (click)="save()" type="button" class="btn btn-success">
                                Save
                            </button>
                        </div> -->
                    </div>
                    <div class="col-sm-4 col-xs-5">
                        <div class="cols-container">
                            <div class="cols-header">Show</div>
                            <div class="cols-body">
                                <div *ngFor="let item  of arr2; let i = index" class="check-block">
                                    <input *ngIf="!item.standard" type="checkbox" (change)="onCheckboxChange($event, item, 'temp2')" name="selectedValue" value="{{item.field}}" id="{{item.field}}" class="checkbox-label" />
                                    <label for="{{item.field}}">{{item.title}}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

        </div>
    </div>
</div>