<!-- <div class="dropdown-container">
    <p-dropdown 
        [options]="countryOpts"
        [(ngModel)]="country"
        placeholder=" "
        #selectedCountry
        (onChange)="onChangeCountry(selectedCountry.value)">
        <ng-template let-selected pTemplate="selectedItem">
            <span style="vertical-align:middle;margin-right:3px;color:#808080;">{{selected.label}}</span>
            <img src="./assets/images/CountryCode/{{selected.code}}.gif" class="country-img" style="width:18px" />
        </ng-template>
        <ng-template let-item pTemplate="it">
            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                <div style="font-size:14px;float:left;width:100px;">
                    <span style="display: inline-block;">
                        {{item.label}}
                    </span>
                    <img src="./assets/images/CountryCode/{{item.code}}.gif" style="width:16px;float:right;margin-top:4px;" />
                </div>
            </div>
        </ng-template>
    </p-dropdown>
</div> -->

