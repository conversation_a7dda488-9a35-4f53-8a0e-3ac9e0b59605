.error-wrapper{
    display:flex;
    align-items: center;
    justify-content: center;
    flex-direction:column;
    padding:15px 30px;
}
.title{
    color: #3D7BCE;
    margin-bottom:30px;
    font-size:30px;
}
.error-help{
    font-size: 15px;
    margin-bottom: 35px;
    text-align: center;
}
.contact{
    text-decoration:none;
    cursor: pointer;
    font-weight: bold;
}
.maintenance-icon{
    font-size:100px;
    text-align: center;
    color: #3D7BCE;
    -webkit-animation:spin 4s linear infinite;
    -moz-animation:spin 4s linear infinite;
    animation:spin 4s linear infinite;
}

@-moz-keyframes spin { 100% { -moz-transform: rotate(360deg); } }
@-webkit-keyframes spin { 100% { -webkit-transform: rotate(360deg); } }
@keyframes spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }

@media screen and (max-width:767px) {
    .error-wrapper{
        padding:15px 20px;
    }
    .title{
        font-size:22px;
    }
    .error-help{
        font-size: 14px;
    }
    .maintenance-icon{
        font-size:80px
    }
}

