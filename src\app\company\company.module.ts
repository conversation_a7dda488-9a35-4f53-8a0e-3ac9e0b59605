import { PostJobService } from './services/post-job.service';
import { CompanyWrapperComponent } from './components/company-wrapper/company-wrapper.component';
import { CompanyProfileInfoComponent } from './components/company-profile-info/company-profile-info.component';
import { NgModule, Provider, Type } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { SharedModule } from 'shared/shared.module';
import { CompanyFormComponent } from './components/company-form/company-form.component';
import { WrapperLanguageComponent } from './components/wrapper-language/wrapper-language.component';
import { CompanyFormService } from './services/company-form.service';
import { CompanyWrapperService } from './services/company-wrapper.service';
import { FormEditComponent } from './components/form-edit/form-edit.component';
import { FormLanguageComponent } from './components/form-language/form-language.component';
import { CompanyRoutingModule } from './company-routing.module';
import { CompanyInformationPreviewComponent } from './components/company-information-preview/company-information-preview.component';
import { CompanyLocationComponent } from './components/company-location/company-location.component';
import { CompanyLocationService } from './services/company-location.service';
import { LocationModalComponent } from './components/location-modal/location-modal.component';
import { CompanySettingsComponent } from './components/company-settings/company-settings.component';
import { CompanyVerificationComponent } from './components/company-verification/company-verification.component';
import { CompanyAdverComponent } from './components/company-adver/company-adver.component';
import { CompanyPostJobComponent } from './components/company-post-job/company-post-job.component';
import { AdvrLogComponent } from './components/advr-log/advr-log.component';
import 'hammerjs';
import { ManageAdvsComponent } from './components/manage-advs/manage-advs.component';
import { ManagePostService } from 'app/company/services/manage-post.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { CvSearchComponent } from './components/cv-search/cv-search.component';
/* import { DynamicClass } from "app/company/components/dynamically_filters"; */
import { AgmCoreModule } from '@agm/core';
// import { CvsPreviewComponent } from './components/cvs-preview/cvs-preview.component';
import { MenuModule } from 'primeng/menu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { FileUploadModule } from 'primeng/fileupload';
import { PersonalSettingsComponent } from './components/personal-settings/personal-settings.component';
import { LanguageSettingComponent } from './components/language-setting/language-setting.component';
import { TreeModule } from 'primeng/tree';
import { CompanyAccountSettingsComponent } from './components/company-account-settings/company-account-settings.component';
import { PasswordStrengthMeterModule } from 'angular-password-strength-meter';
import { PublicPreviewComponent } from './components/public-preview/public-preview.component';
import { PublicPreviewService } from './services/public-preview.service';
import { AdvsManageComponent } from './components/advs-manage/advs-manage.component';
import { SortablejsModule } from 'ngx-sortablejs';
import { AiJobDescriptionComponent } from './components/ai-job-description/ai-job-description.component';
import { AiJobGeneratorService } from './services/ai-job-generator.service';

@NgModule({
  imports: [
    SharedModule,
    CompanyRoutingModule,
    //  angular material
    NgxPaginationModule,
    AgmCoreModule,
    MenuModule,
    TableModule,
    RadioButtonModule,
    TreeModule,
    PasswordStrengthMeterModule,
    SortablejsModule
  ],

  declarations: [
    CompanyProfileInfoComponent,
    CompanyFormComponent,
    CompanyInformationPreviewComponent,
    CompanySettingsComponent,
    FormLanguageComponent,
    FormEditComponent,
    CompanyLocationComponent,
    LocationModalComponent,
    CompanyVerificationComponent,
    CompanyAdverComponent,
    CompanyPostJobComponent,
    ManageAdvsComponent,
    CvSearchComponent,
    PersonalSettingsComponent,
    LanguageSettingComponent,
    AdvrLogComponent,
    CompanyAccountSettingsComponent,
    PublicPreviewComponent,
    AdvsManageComponent,
    AiJobDescriptionComponent
  ],
  providers: [
    CompanyFormService,
    CompanyWrapperService,
    CompanyLocationService,
    PublicPreviewService,
    DatePipe,
    AiJobGeneratorService,
  ]
})
export class CompanyModule {
}

@NgModule({
})

export class A {
  static forRoot() {
    return { ngModule: A, exports: [CompanyFormComponent, CompanySettingsComponent] };
  }
}

