<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css"
integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">
<div class="top-banner">

</div>

<div style="height:30px;"></div>
<div class="container">
<div class="row">
<div class="col-md-8 offset-md-2">
<mat-card>
  <mat-card-content>

    <div style="display: block; width: 400px ; height: 300px"
         class="offset-md-2"
         *ngFor="let q of questions let i=index;">
        <div class="row">
        <div class="form-group col-md-8 left">
          {{i+1}}: {{q.questionLabel}}
        </div>
      </div>

      <canvas baseChart

              [data]=q.count
              [labels]=q.labels
              [chartType]="pieChartType"
              (chartHover)="chartHovered($event)"
              (chartClick)="chartClicked($event)"></canvas>



      <hr/>
    </div>


  </mat-card-content>
</mat-card>
</div>
</div>
<br>
</div>
