import {Component, OnInit} from '@angular/core';
import {QuestionService} from '../form-services/question.service';
import { ChartData } from "app/company/components/google-form/Models/ChartData";



@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.css']
})
export class ChartComponent implements OnInit {


  public questions: ChartData[] = [];

  public pieChartLabels: string[] = ['download'];
  public pieChartData: number[] = [100];
  // public color: any[] = [{backgroundColor: ['#b8436d', '#00d9f9', '#a4c73c', '#a4add3']}];
  public pieChartType: string = 'pie';


  constructor(private questionService: QuestionService) {
  }

  ngOnInit() {
    this.questionService.getAllQuestionsChart().subscribe(res => {
      this.calculateData(res);

    });
  }

//calculate
  public calculateData(res:any){
    for (let data of res['data']) {
      let q: ChartData = new ChartData();
      let values = Object.values(data)[0];
      q.questionLabel = Object.keys(data)[0];
      let sum=0;
      let limit = 2;
      // values.forEach((item, index) => {
      //   if(index <limit){
      //     q.labels.push(item.label);
      //     q.count.push(item.response_count);
      //   }else{
      //     sum+=item.response_count;
      //   }
      // });
      if(q.labels.length ==limit){
        q.labels.push('others');
        q.count.push(sum);
      }


      this.questions.push(q);
    }
  }
  // events
  public chartClicked(e: any): void {
    // console.log(e);
  }

  public chartHovered(e: any): void {
    // console.log(e);
  }
}
