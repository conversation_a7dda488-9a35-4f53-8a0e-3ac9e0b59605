<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">
<div class="top-banner">

</div>
<div style="height:100px;"> </div>
<div class="container">
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <mat-card>
        <mat-card-content>
          <form [formGroup]="myForm" (ngSubmit)="answer()">
            <div *ngFor="let q of questions; let i = index">
              <div class="row">
                <div class="form-group col-md-12 left">
                  {{ q.order }} - {{ q.label }}
                  <span *ngIf="q.isRequired" style="color: red">*</span>
                </div>
              </div>

              <div class="row" [ngSwitch]="q.controlType">

                <div class="form-group col-md-12 left" *ngSwitchCase="'Short Answer'">
                  <mat-form-field style="width: 100%">
                    <input matInput type="text" placeholder="Your answer" formControlName="{{q.id}}">
                  </mat-form-field>
                </div>

                <div class="form-group col-md-12 left" *ngSwitchCase="'Paragraph'">
                  <mat-form-field style="width: 100%">
                    <textarea matInput type="text" placeholder="Your answer" formControlName="{{q.id}}"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-group col-md-12 left" *ngSwitchCase="'dropDown'">
                  <mat-form-field style="width: 100%">
                    <mat-select formControlName="{{q.id}}" placeholder="Choose your answer">
                      <mat-option value="" *ngIf="!q.isRequired">Choose</mat-option>
                      <mat-option *ngFor="let a of q.choices"
                                  value="{{a.id}}">
                        {{ a.label }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-group col-md-12 left" *ngSwitchCase="'Checkboxes'">
                  <mat-radio-group formControlName="{{q.id}}">
                    <div  *ngFor="let a of q.choices">
                      <mat-radio-button color="primary"
                                        style="cursor: pointer"
                                        id="answer_{{a.id}}"
                                        value="{{a.id}}">
                        {{ a.label }}
                      </mat-radio-button>
                    </div>
                  </mat-radio-group>
                </div>

                <div class="form-group col-md-12 left" formGroupName="{{q.id}}" *ngSwitchCase="'Multiple Choice'">
                  <div *ngFor="let a of q.choices">
                    <mat-checkbox
                            color="primary"
                            type="checkbox"
                            style="cursor: pointer"
                            formControlName="{{a.id}}"
                            id="answer_{{a.id}}"
                            value="{{a.id}}">
                      {{ a.label }}
                    </mat-checkbox>
                  </div>
                </div>

                <div class="form-group col-md-4 left" *ngSwitchCase="'Date'">
                  <mat-form-field style="width: 100%">
                    <input matInput formControlName="{{q.id}}" [matDatepicker]="picker" placeholder="Choose a date">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                  </mat-form-field>
                </div>

                <!--<div formGroupName="{{q.id}}" class="form-group col-md-12 left" *ngSwitchCase="'Time'">-->
                  <!--<mat-form-field style="width: 50px; margin: 5px">-->
                    <!--<input matInput type="text" formControlName="H{{q.id}}">-->
                    <!--<mat-hint>Hours</mat-hint>-->
                  <!--</mat-form-field>-->
                  <!--<span>:</span>-->
                  <!--<mat-form-field style="width: 50px; margin: 5px">-->
                    <!--<input matInput type="text" formControlName="M{{q.id}}">-->
                    <!--<mat-hint>Minutes</mat-hint>-->
                  <!--</mat-form-field>-->
                  <!--<mat-form-field style="width: 50px; margin: 5px">-->
                    <!--<mat-select formControlName="T{{q.id}}">-->
                      <!--<mat-option value="AM">AM</mat-option>-->
                      <!--<mat-option value="PM">PM</mat-option>-->
                    <!--</mat-select>-->
                    <!--<mat-hint>AM/PM</mat-hint>-->
                  <!--</mat-form-field>-->
                <!--</div>-->
                <div formGroupName="{{q.id}}" class="form-group col-md-12 left" *ngSwitchCase="'Time'">
                  <mat-form-field style="width: 50px; margin: 5px">
                    <mat-select formControlName="H{{q.id}}">
                      <mat-option *ngFor="let h of hours" value="{{h}}">{{ h }}</mat-option>
                    </mat-select>
                    <!--<input matInput type="text" formControlName="H{{q.id}}">-->
                    <mat-hint>Hours</mat-hint>
                  </mat-form-field>
                  <span>:</span>
                  <mat-form-field style="width: 50px; margin: 5px">
                    <mat-select formControlName="M{{q.id}}">
                      <mat-option *ngFor="let m of minutes" value="{{m}}">{{ m }}</mat-option>
                    </mat-select>
                    <!--<input matInput type="text" formControlName="M{{q.id}}">-->
                    <!--..............................................-->
                    <mat-hint>Minutes</mat-hint>
                  </mat-form-field>
                  <mat-form-field style="width: 50px; margin: 5px">
                    <mat-select formControlName="T{{q.id}}">
                      <mat-option value="AM" selected="true">AM</mat-option>
                      <mat-option value="PM">PM</mat-option>
                    </mat-select>
                    <mat-hint>AM/PM</mat-hint>
                  </mat-form-field>
                </div>

              </div>
              <hr>
            </div>
            <mat-card-footer>
              <button style="margin: 20px" type="submit" mat-raised-button color="primary" [disabled]="!myForm.dirty || !myForm.valid || !validateCheckBoxes()">
                Submit
              </button>
            </mat-card-footer>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
  <br>
</div>

