.form-control {
  width: 100px;
}
.t-label {
  cursor: pointer;
}

input[type=number] {
  margin-left:  20px;;
}


tr.search-row td.select-td {
  width: 174px;
}
.modal-content{
  overflow-y: auto;
}

.modal-footer {
  padding: 25px;
}


/* start  of add value pop up */
#addValueModal .modal-content {
  width: 60%;
  left: 200px;
  top: 100px;
}

#addValueModal .modal-content .form-group {
  margin-top: 25px;
  width: 90%;
 }

 #addValueModal .modal-content .form-control {
  width: 90%;
 }



input.ng-dirty.ng-touched.ng-invalid {
  border-radius: 5px;
  border: 2px solid #ff000059;
  background-color: #ffeaea66 ;
}

.alert.alert-danger {
  padding: 0px;
  margin: 0px;
  background-color: transparent;
  border: none;
  margin-top: 5px;
  margin-left: 5px;
}

#addValueModal .modal-content .btn.btn-success {
  margin-top: 25px;
  margin-top: 25px;
  padding-left: 20px;
  padding-right: 20px;

}
/* end of add value pop up */
:host ::ng-deep .ui-table-questions tr.ui-selectable-row td{
  cursor: pointer;
}

.btn {
  cursor: pointer;
}

th {
  color: #276ea4;
  /* background-color: #f1f1f1; */
  background-color: #e5e5e5;
  font-size: 1.1em;
}

h3 {
  color: #276ea4;
  margin-top: 0px;
  margin-bottom: 0px;
}

h3.verf-heading {
  margin-top: 20px;
}

.btn-primary {
  color: #276ea4;
}


.badge-primary {
  background-color: #4f94df;
}



td select {
  width: 100px;
  height: 25px;
  padding: 0px;
  border-radius: 3px;
  border: 1px solid lightgrey;
  margin-left: 20px;
}

td input[type="text"] {
  width: 100px;
  border-radius: 3px;
  border: 1px solid lightgrey;
  margin-left: 25px
}

td input[type="number"] {
  width:auto;
  border-radius: 3px;
  border: 1px solid lightgrey;
  margin-left: 60px;
  transition: all 0.5s ease-in-out;
}

td input:focus {
 background-color: #f5f5f5;
 border-color: dodgerblue;
}


.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
  padding: 12px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd;
}


table.table.table-responsive {
  background-color: white;
  box-shadow: -1px 0px 3px 0px #ccc;
}

.table>tbody>tr {
  color: #777;
  transition: all 0.5s ease-in-out;
}

.table>tbody>tr:hover {
  background-color: #f9f9f9;
  color: dimgrey;
  font-weight: bold;

}

.table>tbody>tr.search-row:hover
{
  background-color: white!important;
}

table.table.table-responsive
{
  overflow-y: auto !important;
}

.counter {
  width: 20px;
  color: #aaa;
  background-color: #e5e5e575;
}

.counter-th {
  width: 20px;
}





div, table, td,th,p,h3,form, .modal-header, .modal-title, .modal-body, .modal-footer, .caption {
  font-family: 'Exo2-Regular', sans-serif;
}

input::-webkit-input-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}
input:-moz-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}
input::-moz-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}
input:-ms-input-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}




/*================================================================================================= */
/* start of primeng table styling */



.ui-table .ui-table-thead>tr>th,
 .ui-table .ui-table-tbody>tr>td,
  .ui-table .ui-table-tfoot>tr>td {
  padding: .5em .75em !important;
}


:host ::ng-deep .ui-table-questions {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);
  /* Responsive */
}
:host ::ng-deep .ui-table-questions .question-badge {
  border-radius: 2px;
  padding: 0.25em 0.5em;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 1em;
  letter-spacing: 0.3px;
}
:host ::ng-deep .ui-table-questions .question-badge.status-UNIVERSITY {
  background-color: #c8e6c9;
  color: #24922b;
}
:host ::ng-deep .ui-table-questions .question-badge.status-SKILL {
  background-color: #ffcdf4db;
  color: #c63737;
}

:host ::ng-deep .ui-table-questions .question-badge.status-MAJOR {
  background-color: #eaee0c4d;
  color: #828a0b;
}

:host ::ng-deep .ui-table-questions .question-badge.status-MINOR {
  background-color: #feedaf;
  color: #8a5340;
}
:host ::ng-deep .ui-table-questions .question-badge.status-JOB_TITLE {
  background-color: #b3e5fc;
  color: #23547b;
}
:host ::ng-deep .ui-table-questions .question-badge.status-JOB_TITLE_SYNONYMS {
  background-color: #eccfff;
  color: #694382;
}
:host ::ng-deep .ui-table-questions .question-badge.status-new {
  background-color: #ffd8b2;
  color: #805b36;
}
:host ::ng-deep .ui-table-questions .flag {
  vertical-align: middle;
  width: 30px;
  height: 20px;
}

:host ::ng-deep .ui-table-questions .ui-paginator .ui-dropdown {
  float: left;
  width: 60px;
}

:host ::ng-deep .ui-table-questions .ui-paginator .ui-dropdown.ui-widget.ui-state-default.ui-corner-all {
  width: 60px !important;
}

:host ::ng-deep .ui-table-questions .ui-paginator .ui-paginator-current {
  float: right;
}

:host ::ng-deep .ui-table-questions .ui-column-filter {
  display: block;
}
:host ::ng-deep .ui-table-questions  input.ui-column-filter  {
  width: auto;
  background: transparent;
  border: none;
  border-bottom: 1px solid #ccc;

}

:host ::ng-deep .ui-table-questions input.ui-column-filter:focus {
  width: auto;
  border:2px solid darkblue;
}

:host ::ng-deep .ui-table-questions .ui-table-globalfilter-container {
  float: right;
}
:host ::ng-deep .ui-table-questions .ui-table-globalfilter-container input {
  width: 200px;
}
:host ::ng-deep .ui-table-questions .ui-datepicker {
  min-width: 25em;
}
:host ::ng-deep .ui-table-questions .ui-datepicker td {
  font-weight: 400;
}
:host ::ng-deep .ui-table-questions .ui-table-caption {
  border: 0 none;
  padding: 12px;
  text-align: left;
  font-size: 20px;
  font-family: 'Exo2-Regular', sans-serif
}
:host ::ng-deep .ui-table-questions .ui-paginator {
  border: 0 none;
  padding: 1em;
  font-family: 'Exo2-Regular', sans-serif;
}
:host ::ng-deep .ui-table-questions .ui-table-thead > tr > th {
  border: 0 none;
  text-align: left;
  vertical-align: top;
}
:host ::ng-deep .ui-table-questions .ui-table-thead > tr > th.ui-filter-column {
  border-top: 1px solid #c8c8c8;
}
:host ::ng-deep .ui-table-questions .ui-table-thead > tr > th:first-child {
  width: 5em;
  /*text-align: center;*/
  
}
:host ::ng-deep .ui-table-questions .ui-table-thead > tr > th:last-child {
  width: 8em;
    /*text-align: center;*/

}
:host ::ng-deep .ui-table-questions .ui-table-tbody > tr > td {
  border: 0 none;
  cursor: auto;
  padding: 1.2em 1em;
  text-align: left;
}

:host ::ng-deep .ui-table-questions .ui-dropdown-label-container {
  /* margin-top: -6px; */
}


/* ++++++ */
:host ::ng-deep .ui-table-questions .ui-table-tbody > tr > td:first-child {
  width: 3em;
  text-align: center;
}
:host ::ng-deep .ui-table-questions .ui-table-tbody > tr > td:last-child {
  width: 8em;
  text-align: center;
}
:host ::ng-deep .ui-table-questions .ui-dropdown-label:not(.ui-placeholder) {
  text-transform: uppercase;
  background-color: transparent;
}
:host ::ng-deep .ui-table-questions .ui-dropdown-trigger.ui-state-default.ui-corner-right {
  /* left: 50px; */
  background: transparent;
}

:host ::ng-deep .ui-table-questions .ui-table-tbody > tr > td .ui-column-title {
  display: none;
}
@media screen and (max-width: 64em) {
  :host ::ng-deep .ui-table.ui-table-questions .ui-table-thead > tr > th, :host ::ng-deep .ui-table.ui-table-questions .ui-table-tfoot > tr > td {
    display: none !important;
  }
  :host ::ng-deep .ui-table.ui-table-questions .ui-table-tbody > tr > td {
    text-align: left;
    display: block;
    border: 0 none !important;
    width: 100% !important;
    float: left;
    clear: left;
    border: 0 none;
  }
  :host ::ng-deep .ui-table.ui-table-questions .ui-table-tbody > tr > td .ui-column-title {
    padding: 0.4em;
    min-width: 30%;
    display: inline-block;
    margin: -0.4em 1em -0.4em -0.4em;
    font-weight: bold;
    font-family: 'Exo2-Regular', sans-serif;
  }


}


:host ::ng-deep .ui-table .ui-sortable-column:not(.ui-state-highlight):hover {
  background-color: #e0e0e0;
  color: darkblue;
}

:host ::ng-deep .ui-table .ui-table-thead > tr > th {
  color: darkblue;
  background-color: #f4f4f4;
}

/* :host ::ng-deep .ui-table .ui-column-filter.type .ui-dropdown-trigger-icon.ui-clickable.pi.pi-chevron-down {
  left: -5px;
} */

:host ::ng-deep .ui-table  p-calendar  .ui-datepicker.ui-widget.ui-widget-content.ui-corner-all{
  display: block;
  transform: scale(0.75);
 }

 :host ::ng-deep .ui-table  p-calendar input {
    /* width: 80px; */
    height: 27px;
    /* border: 1px solid black; */
    border-radius: 0px;
    border: none;
    border-bottom: 1px solid #b9b7b7;
    background-color: transparent;
    margin-top: 7px;
}

