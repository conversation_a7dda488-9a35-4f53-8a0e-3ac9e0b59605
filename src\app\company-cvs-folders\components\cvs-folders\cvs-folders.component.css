.folders-heading{
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}
.my-folders-title{
    display: inline-block;
    width:calc(100% - 28px);
    color:#444;
    text-decoration: none;
    cursor: pointer;
    font-size: 17px;
    font-weight: bold;
    margin-left: 6px
}
.add-folder-btn{
    display: inline-block;
    width: 28px;
    height: 28px;
    font-size: 24px;
    line-height: 27px;
    text-decoration: none;
    color: #000;
    text-align: center;
    cursor: pointer;
    border-radius: 50%;
}
.add-folder-btn:hover{
    background: #ddd;
}
.folder-label{
    text-decoration: none;
    color: #333;
    cursor:pointer;
}
.folder-actions-dd .dropdown-menu .dropdown-item{
    display: block!important;
    text-decoration: none;
    color: #999;
    padding: 5px 10px;
    transition: all .2s ease;
    cursor: pointer;
}
.folder-actions-dd{
    position: absolute;
    right: -3px;
    top: 10px;
    background: #ddd;
    border-radius: 50%;
}
.folder-actions-dd .dropdown-toggle{
    display: none;
    color:#969895;
    width: 20px;
    text-align: center;
}
.folder-actions-dd .dropdown-toggle fa{
    font-size:17px;
}

/* :host ::ng-deep .ui-treenode:hover .dropdown-toggle{
    display:inline-block; 
} */
:host ::ng-deep .ui-tree {
    width: 100%;
    height:100%;
    background: transparent;
    border:none;
    padding:0;
    /* color:#969895; */
}
:host ::ng-deep  .ui-tree .ui-tree-wrapper{
    height:100%;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-label {
    width:80%;
    /* overflow:hidden; */
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-label.ui-state-highlight {
    /* background-color: transparent; */
    background:#fff;
    color: #333;
}

/* :host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content.ui-treenode-selectable .ui-treenode-label:not(.ui-state-highlight):hover{
    background:#fff;
} */

:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode:hover  .ui-treenode-label{
    background:#fff;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content:focus, :host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content:hover{
    /* background:#fff; */
    box-shadow:none;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-label {
    padding:0;
}

:host ::ng-deep .ui-tree .ui-tree-wrapper, :host ::ng-deep .ui-tree .ui-tree-container {
    overflow: unset;
}

.folder-label-div{
    width:100%;
}
.folder-label-div:hover .dropdown-toggle{
    display:inline-block;
    cursor: pointer;
}
.folder-label{
    width:100%;
    display:inline-block;
    padding: 0.286em;
    /* position:relative; */
}
.folder-label-div .dropdown-menu{
    left:unset;
    right:0;
}
.folder-label-div .dropdown-menu .dropdown-item:hover{
    color: #555;
    background: #f9f9f9;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode{
    position:relative;
}
.folder-label-div .name{
    margin-right:12px;
}
.folder-label-div .count{
    position:absolute;
    right:3px;
    color:#3bb34b;
}
.active-folder{
    background:#fff;
}

.additional-folders{
    margin-bottom:20px;
}
.additional-folders .folder-link{
    color:#848484;
    text-decoration: none;
    display: block;
    padding: 6px 5px;
    display: flex;
    align-items: center;
    position: relative;
    font-family: Exo2-Regular,sans-serif;
    border-radius: 5px;
}
.additional-folders .folder-link .folder-icon{
    font-size: 22px;
}
.additional-folders .folder-link .folder-label{
    font-size: 1.2em;
    color:#333;
    padding:0 0 0 0.35em;
}
.additional-folders .folder-link:hover{
    background: #fff;
    color: #3d7bce;
}
.additional-folders .folder-link:hover .folder-label{
    color: #3d7bce;
}


@media screen and (max-width:1180px){
    .folders-heading{
        margin-bottom:10px;
    }
}