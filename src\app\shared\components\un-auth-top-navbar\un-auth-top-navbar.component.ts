import { Component, OnInit, Input, HostListener } from '@angular/core';
import { GeneralService } from '../../../general/services/general.service';

@Component({
  selector: 'un-auth-top-navbar',
  templateUrl: './un-auth-top-navbar.component.html',
  styleUrls: ['./un-auth-top-navbar.component.css']
})
export class UnAuthTopNavbarComponent implements OnInit {
  searchActive = false;
  country='';
  @Input("inAdvrsInterface") inAdvrsInterface : boolean;
  @Input('inHomePage') inHomePage: boolean;
  isScrolled = false;
  
  constructor(private generalService: GeneralService, ) { }

  ngOnInit() {
    // get country for Homepage link
    if(localStorage.getItem('country'))
      this.country = localStorage.getItem('country');

    if(this.inAdvrsInterface){
      this.activateSearch();
    }
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Works for most browsers
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;

    this.isScrolled = scrollTop > 300;
  }
  
  displayContactModal() {
    this.generalService.notify('display Contact Modal' , 'topbar' , 'contact-us' , {'displayContactModal':true}) ;
  }
  activateSearch(){
    if(this.searchActive === false){
      this.searchActive = true;
    }
  }
  deactivateSearch(){
    if(this.searchActive === true){
      this.searchActive = false;
    }
  }
}
