<h3>Manage Help Tips</h3>


<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ helpTipsArray.length }}</span></p>
   <!-- <p>filtered: <span class="badge badge-primary badge-pill">{{ filteredHelpTips.length }}</span></p> -->



<p-table #dt [value]="helpTipsArray" [(selection)]="filteredHelpTips" dataKey="id" styleClass="ui-table-htips" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="helpTipsArray.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['field_id', 'field', 'section_id', 'section', 'description']">
    <ng-template pTemplate="caption">
         <!-- help tips -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th><i title="add new help tip" class="fas fa fa-plus-circle" data-toggle="modal" data-target="#tipModal" (click)="displayCreateModal()" ></i></th>
            <th pSortableColumn="section">Section Name / ID <p-sortIcon field="section" ></p-sortIcon></th>
            <th pSortableColumn="field" >Field Name / ID<p-sortIcon field="field" ></p-sortIcon></th>
            <th pSortableColumn="description" >Description <p-sortIcon field="description" ></p-sortIcon></th>
            <th pSortableColumn="active" style="width:100px;">Status <p-sortIcon field="active" ></p-sortIcon></th>
            <th  style="width:150px;">Actions</th>
        </tr>
        <tr>
            <th>
                <!-- <p-tableHeaderCheckbox></p-tableHeaderCheckbox> -->
                <!-- <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected htopics" (click)="displayDeleteAlert(4)"></i> -->
            </th>
            <th>
              <p-dropdown [options]="sections" (onChange)="dt.filter($event.value, 'section_id', 'equals')" styleClass="ui-column-filter" [(ngModel)]="section" [showClear]="false" filter="true">
                <ng-template let-option pTemplate="item">
                    <span >{{option.label}}</span>
                </ng-template>
              </p-dropdown>
              <!-- <input pInputText type="text" (input)="dt.filter($event.target.value, ['section', 'section_id'], 'contains')" placeholder="" class="ui-column-filter"> -->
            </th>
            <th>
              <p-dropdown [options]="doneFields" (onChange)="dt.filter($event.value, 'field_id', 'equals');print($event.value)" styleClass="ui-column-filter" [(ngModel)]="field" [showClear]="false"  filter="true">
                <ng-template let-option pTemplate="item">
                    <span>{{option.label  +'-' + option.value }}</span>
                </ng-template>
              </p-dropdown>
              <!-- <input pInputText type="text" (input)="dt.filter($event.target.value, ['field', 'field_id'], 'contains')" placeholder="" class="ui-column-filter"> -->
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'description', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'active', 'equals')" styleClass="ui-column-filter status" [(ngModel)]="status" [showClear]="false">
                <ng-template let-option pTemplate="item">
                    <span [class]="'htip-badge status-' + option.value">{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-htip>
        <tr class="ui-selectable-row" (mouseover)="htip.display = true" (mouseleave)="htip.display = false" >
            <td data-toggle="modal" data-target="#tipModal" (click)="displayEditFormModal(htip)">
                <!-- <p-tableCheckbox [value]="question" (click)="addToSelected(question)"></p-tableCheckbox> -->
            </td>
            <td data-toggle="modal" data-target="#tipModal" (click)="displayEditFormModal(htip)">
              {{ htip.section}} / {{ htip.section_id}}
            </td>
            <td data-toggle="modal" data-target="#tipModal" (click)="displayEditFormModal(htip)">
              {{ htip.field}} / {{ htip.field_id}}
            </td>
            <td  data-toggle="modal" data-target="#tipModal" (click)="displayEditFormModal(htip)">
              <span  [innerHTML]="sanitizer.bypassSecurityTrustHtml(htip.description)" ></span>
            </td>
            <!-- <td class="inactive " [class.active-text]="htip.active">
              {{  htip.active | activation:'Active': 'InActive'  }}
            </td> -->
            <td data-toggle="modal" data-target="#tipModal" (click)="displayEditFormModal(htip)">
              <span class="htip-badge" [class.status-0]="!htip.active" [class.status-1]="htip.active" >{{  htip.active | activation:'Active': 'InActive'  }}</span>
            </td>
            <td class="actions">
              <span *ngIf="htip.display">
                <i title="preview" data-toggle="modal" data-target="#tipModal" class="fa fa-eye" (click)="displayPreviewModal(htip)"  ></i>
                <i title="edit" data-toggle="modal" data-target="#tipModal"  class=" fa fa-edit" (click)="displayEditFormModal(htip)"></i>
                <i  title="delete" class="fa fa-trash"  data-toggle="modal" data-target="#tipModal" (click)="displayDeleteAlert(htip)"  ></i>
                <i  [class.activate]="htip.active" title="activation" (click)="activateHTip(htip)"  class="fa fa-power-off "></i>
              </span>
             </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptyhtip">
        <tr>
            <td colspan="6" style="text-align:center;padding:15px;">No help tips found.</td>
        </tr>
    </ng-template>
</p-table>








<!--  modal-->
<div class="modal fade" *ngIf="displayModal" id="tipModal"  tabindex="-1" role="dialog" aria-labelledby="tipModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button"   (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          <h3 class="modal-title" id="tipModalLabel" translate>
           <span *ngIf="mode === 'create'" translate>help.labels.AddNewHelpTip</span>
           <span *ngIf="mode === 'edit'" translate>help.labels.Edit</span>
           <span *ngIf="mode === 'preview'" translate>help.labels.Preview</span>
           <span *ngIf="mode === 'delete'" translate>help.labels.Delete</span>
          </h3>
      </div>

     <app-edit-tip-modal *ngIf="displayModal" [hTip]="hTip" [hTipId]="helpTipToDelete.id"  [openedFromSidebar]="false" (closeUpdateModal)="showUpdatedHelpTipInTable($event)"
      (closeCreateModal)="showNewHelpTipInTable($event)"(closeDeleteModal)="removeHelpTipFromTable($event)"[languagesArray]="languagesArray"
       [sections]="sections" [fields]="fields"[mode]="mode" ></app-edit-tip-modal>

     <div class="modal-footer">
      <button class="btn btn-default" *ngIf="mode === 'preview'"   (click)="closePreviewOpenEdit()" style="float:left" translate>help.labels.Edit</button>
      <button type="button"  *ngIf="mode === 'preview' " class="btn btn-danger" data-dismiss="modal" (click)="closeModal()" style="float:right;" translate>help.Cancel</button>

      </div>
    </div>
  </div>
</div>
<!-- end of   modal-->


