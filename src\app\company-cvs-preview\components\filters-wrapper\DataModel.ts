
export class DataModel {
  public filterSection: String;
  public filtersTitle: String;
  public filterData: any;
  ///////////////// Filters On Init
  public initFilters = {};
  public initFiltersTags = {};
  //// Filters We Send To Backend
  public filters = {};
  /////////// Current Filters For Tags
  public currentFilters = {};
  public currentFilterTags = [];
  ///////////
  public modalData = [
    { 'key': 'name_filter', 'title': 'Name' },
    { 'key': 'countries_filter', 'title': 'countries Filter' },
  //  { 'key': 'location_filter', 'title': 'Location Filter' },
    { 'key': 'all_filters', 'title': 'All Filters' },
    // { 'key': 'personal_info', 'title': 'Personal Information' },
    // { 'key': 'education', 'title': 'Education' },
    // { 'key': 'work_experience', 'title': 'Work Experience' },
    // { 'key': 'languages', 'title': 'Languages' },
    // { 'key': 'skills', 'title': 'Skills' },
    // { 'key': 'others', 'title': 'Others' },
  ];
  constructor() {
  }

  public setFiltersTitle() {
    this.filtersTitle = '';
    this.filtersTitle = this.modalData.find((el) => el.key === this.filterSection)['title'];
  }

  ////////////////////////////// Get Difference Between Two Objects
  public findDiffer(obj1, obj2) {
    let differs = [];
    this.modalData.forEach((el) => {
      if (obj1[el['key']] && obj2[el['key']]) {
        if (obj1[el['key']]) {
          for (const key in obj1[el['key']]) {
            /////////////////// convert object to string : Identication between two strings
            if (JSON.stringify(obj1[el['key']][key]) !== JSON.stringify(obj2[el['key']][key])) {
              // let temp = {};
              // temp[`${key}`] = obj2[el['key']][`${key}`];
              differs.push(obj2[el['key']][`${key}`]);
            }
          }
        }
      }

      ///
      ////////////////////////
    });

  //  this.currentFilters = differs;
    this.currentFilterTags = differs;
  }
  ////////////////////


}
