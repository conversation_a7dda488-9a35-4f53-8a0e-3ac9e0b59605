import { Component, OnInit, NgZone, Renderer2 } from '@angular/core';
import {AuthService} from 'shared/shared-services/auth-service';
import {HttpClient} from '@angular/common/http';
import {Router} from '@angular/router';
import {FormBuilder, Validators} from '@angular/forms';
import {AuthService as SocialAuthService, FacebookLoginProvider} from 'angular5-social-login';
import { Errors } from '../../../models/errors';
import 'rxjs/add/observable/empty' 
import { Observable } from 'rxjs';
import { EmptyObservable } from 'rxjs/observable/EmptyObservable';
import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from '../../../../general/services/general.service';
import { EmailValidator } from 'shared/validators/email.validators';
import { CredentialResponse} from 'google-one-tap';
import { environment } from 'environments/environment.prod';
import { ScriptService } from 'shared/shared-services/script.service';
import { booleanValidator } from '../../../validators/boolean.validators';

@Component({
  selector: 'app-sign-up',
  templateUrl: './sign-up-company.component.html',
  styleUrls: ['./sign-up-company.component.css']
})
export class SignUpCompanyComponent implements OnInit {

  signUpForm;
  resetMessage;
  alertResetMessage;
  helpLink;
  type= 'password';
  show = false;
  username ='';
  loader = false;
  constructor(private authService: AuthService,
              private http: HttpClient,
              private router: Router,
              private  fb: FormBuilder,
              private socialAuthService: SocialAuthService,
              private title: Title,
              private meta:Meta,
              private generalService: GeneralService,
              private ngZone: NgZone,
              private renderer: Renderer2,
              private scriptService: ScriptService
  ) {
    this.signUpForm = this.fb.group({
      name : ['', [Validators.required , Validators.pattern('[ a-zA-Z0-9أ-ي]+')]],
    //  email : ['', [Validators.required , Validators.email]],
      email : ['', [Validators.required , EmailValidator.isValidEmailFormat]],
      password : ['', [Validators.required , Validators.minLength(6), Validators.maxLength(20)]],
      agree_terms : [false,booleanValidator.isValidCheckboxTrue]
    });
  }

  ngOnInit() {
    this.title.setTitle('CVeek Website | Employer sign up');
    this.meta.updateTag({ name: 'description', content: 'Employer sign up | CVeek. Want to hire? Join CVeek! Create employer account, post jobs for free, and connect to talented job seekers. Join our companies'});

    this.authService.currentError.subscribe(errorMessage => this.alertResetMessage = errorMessage);

    this.initializeGoogleLogin();
  }

  isInvalid(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('required');
  }

  isInvalidSyn(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('invalidEmailError');
  //  return this.signUpForm.controls[controlName].hasError('email');
  }

  isInvalidMin(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('minlength');
  }

  isInvalidMax(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('maxlength');
  }
  isInvalidPattern(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('pattern');
  }

  signUp() {
    //remove white spaces from beggining and end of name if it exists
    this.signUpForm.controls['name'].setValue(this.signUpForm.controls['name'].value.trim());

    this.authService.logoutIfAlreadyLoggedin();

    const data = this.signUpForm.value;
    if ( this.signUpForm.valid) {
      this.loader = true;
    //  localStorage.setItem('newUser', JSON.stringify(data));
      localStorage.setItem('newUser', data.email);
      localStorage.setItem('futureRole', 'ROLE_EMPLOYER');
      this.authService.signUpCompany(data).subscribe(res => {
        this.loader = false;
        if(res['error']){
          this.resetHelpLink();
          this.alertResetMessage = res['error'];
          if(res['type']==='already_registered'){
            this.helpLink = res['help'];
          }
        }
        else
          this.router.navigate(['/m/company/verification']);
      }, (err) => {
        this.loader = false;
      //  this.alertResetMessage = err['error']['error']['email'][0];
      });
    }
  }

  public facebookLogin() {
    this.loader = true;

    this.authService.logoutIfAlreadyLoggedin();

    let socialPlatformProvider = FacebookLoginProvider.PROVIDER_ID;
    this.socialAuthService.signIn(socialPlatformProvider).then(
      (userData) => {
        // this will return user data from facebook. What you need is a user token which you will send it to the server
        // this.sendToRestApiMethod(userData.token);

        this.authService.loginWithFacebookCompany(userData).switchMap(
          data => {
            if(data['success']){
              localStorage.setItem('access_token',data['token'].access_token);
              return this.authService.getUserInfo();
            }
            else if(data['type']=== 'noEmailFacebookAccount'){
              this.loader = false;
              this.resetHelpLink();
              this.alertResetMessage = data['error'];
              return new EmptyObservable<Response>();
            }
            else{
              this.loader = false;
              if(data['error']){
                this.resetHelpLink();
                this.alertResetMessage = data['error'];
                if(data['help'])
                  this.helpLink = data['help'];
              // if(data['error']=== "There is no permission with Employer."){
              //   this.alertResetMessage = Errors.loginWithWrongRuleEmployer;
              // }
              // tslint:disable-next-line:no-unused-expression
              return new EmptyObservable<Response>();
            } 
          }
        })
        .subscribe(data => {
          localStorage.setItem("role",data['user_info'].roles[0].name);
          localStorage.setItem('company_id',data['company_id']);
          localStorage.setItem("username",data['user_info'].user_name);
          this.username = data['user_info'].user_name;
          localStorage.setItem('company_name',data['company_name']);
          localStorage.setItem("email",data['user_info'].email);
          //company logo
          if(data['user_info'].profile_picture){
            localStorage.setItem("pic",data['user_info'].profile_picture);
          }
          else {
            localStorage.setItem("pic","none");
          }

          this.generalService.notify(
            'roleChanged' , 'membership','contact' , 
            {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
          );

          localStorage.setItem("have_profile",data['have_profile']);

          this.loader = false;
          if(data['have_profile'] === 0){
            this.router.navigate(['c/',this.username,'profile','new']);
          }
          else{
            this.router.navigate(['c/',this.username,'manage_advs','published']);
          }
        //  this.router.navigate(['c/',this.username]);
        });

        // this.authService.loginWithFacebookCompany(userData).subscribe(data => {
        //   localStorage.setItem('access_token',data['token'].access_token);
        //   this.router.navigate(['']);
        // });
      }
    );
  }

  // old sign up with google
  // public signinWithGoogle () {
  //   this.loader = true;

  //   this.authService.logoutIfAlreadyLoggedin();
    
  //   let socialPlatformProvider = GoogleLoginProvider.PROVIDER_ID;

  //   this.socialAuthService.signIn(socialPlatformProvider)
  //     .then((userData) => {
  //       this.authService.loginWithGoogleCompany(userData).switchMap(
  //         data => {
  //           if(data['success']){
  //             localStorage.setItem('access_token',data['token'].access_token);
  //             return this.authService.getUserInfo();
  //           }
  //           else{
  //             this.loader = false;
  //             if(data['error']=== "There is no permission with Employer."){
  //               this.alertResetMessage = Errors.loginWithWrongRuleEmployer;
  //             }
  //           return new EmptyObservable<Response>();
  //           } 
  //       })
  //       .subscribe(data => {
  //         localStorage.setItem("role",data['user_info'].roles[0].name);
  //         localStorage.setItem('company_id',data['company_id']);
  //         localStorage.setItem("username",data['user_info'].user_name);
  //         this.username = data['user_info'].user_name;
  //         localStorage.setItem('company_name',data['company_name']);
  //         //company logo
  //         if(data['user_info'].profile_picture){
  //           localStorage.setItem("pic",data['user_info'].profile_picture);
  //         }
  //         else {
  //           localStorage.setItem("pic","none");
  //         }
          
  //         this.generalService.notify(
  //           'roleChanged' , 'membership','contact' , 
  //           {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
  //         );

  //         localStorage.setItem("have_profile",data['have_profile']);

  //         this.loader = false;
  //         if(data['have_profile'] === 0){
  //           this.router.navigate(['c/',this.username,'profile','new']);
  //         }
  //         else{
  //           this.router.navigate(['c/',this.username,'manage_advs','published']);
  //         }
  //       });
  //     });
  // }

  // new sign up with google
  initializeGoogleLogin(){
    const SCRIPT_PATH = 'https://accounts.google.com/gsi/client';
    const scriptElement = this.scriptService.loadJsScript(this.renderer, SCRIPT_PATH);
    scriptElement.onload = () => {
      // @ts-ignore
      google.accounts.id.initialize({
        client_id: environment.clientId,
        callback: this.handleGoogleLoginCredentialResponse.bind(this),
        auto_select: false,
        cancel_on_tap_outside: true
      });
      // @ts-ignore
      google.accounts.id.renderButton(
      // @ts-ignore
      document.getElementById("googleLoginButtonDiv"),
        {theme: "outline", size: "medium" , text:"signup_with", type:"standard",locale:"en-us"}
      );
    };
  }

  
  async handleGoogleLoginCredentialResponse(response: CredentialResponse) {
    this.ngZone.run( () => { 
      this.loader = true;
      let idToken = {
        "idToken":response.credential
      }
      this.authService.loginWithGoogleCompany(idToken).switchMap(
        data => {
          if(data['success']){
            localStorage.setItem('access_token',data['token'].access_token);
            return this.authService.getUserInfo();
          }
          else{
            this.loader = false;
            if(data['error']){
              this.resetHelpLink();
              this.alertResetMessage = data['error'];
              if(data['help'])
                this.helpLink = data['help'];
            // if(data['error']=== "There is no permission with Employer."){
            //   this.alertResetMessage = Errors.loginWithWrongRuleEmployer;
            // }
          return new EmptyObservable<Response>();
          } 
        }
      })
      .subscribe(data => {
        localStorage.setItem("role",data['user_info'].roles[0].name);
        localStorage.setItem('company_id',data['company_id']);
        localStorage.setItem("username",data['user_info'].user_name);
        this.username = data['user_info'].user_name;
        localStorage.setItem('company_name',data['company_name']);
        localStorage.setItem("email",data['user_info'].email);
        //company logo
        if(data['user_info'].profile_picture){
          localStorage.setItem("pic",data['user_info'].profile_picture);
        }
        else {
          localStorage.setItem("pic","none");
        }
        
        this.generalService.notify(
          'roleChanged' , 'membership','contact' , 
          {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
        );

        localStorage.setItem("have_profile",data['have_profile']);

        this.loader = false;
        if(data['have_profile'] === 0){
          this.router.navigate(['c/',this.username,'profile','new']);
        }
        else{
          this.router.navigate(['c/',this.username,'manage_advs','published']);
        }
      });
    });
  }

  public signout () {
    this.socialAuthService.signOut();
  }
  toggleShowPassword() {
    this.show = !this.show;
    if (this.show) {
        this.type = 'text';
    } else {
        this.type = 'password';
    }
  }
  get password() {
    return this.signUpForm.get('password');
  }

  resetHelpLink(){
    this.helpLink = '';
  }

}
