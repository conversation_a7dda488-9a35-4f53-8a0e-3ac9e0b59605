<un-auth-top-navbar *ngIf="role === 'unauth'" [inAdvrsInterface]="true" [inHomePage]="false"></un-auth-top-navbar>
<user-topbar *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_ADMIN'|| role === 'ROLE_SUB_ADMIN'" [inAdvrsInterface]="true" [inHomePage]="false"></user-topbar>
<company-topbar *ngIf="role === 'ROLE_EMPLOYER'" [inAdvrsInterface]="true" [inHomePage]="false"></company-topbar>
<confirm-message *ngIf="role ==='ROLE_JOB_SEEKER' || role === 'ROLE_EMPLOYER'"></confirm-message>


    <div class="filters-container page-navbar">
        <!-- comment verified company filter temporarily -->
        <!-- <div class="filter-btn verified-company-filter" pTooltip="Only show verified companies" tooltipPosition="top">
            <div class="checkbox">
                <label>
                    <img src="./assets/images/secondbar/search-job/verified-company.svg" class="filter-btn-icon">
                    <input type="checkbox" [(ngModel)]="Verified_Companies" (change)="chosenFilterValues($event,'Verified_Companies')">
                    <span>Verified Companies</span>
                </label>
            </div>
        </div> -->
        <div class="filter-btn">
            <div class="checkbox">
                <label>
                    <img src="./assets/images/secondbar/search-job/remote-work-white.svg" class="filter-btn-icon" alt="Remote work">
                    <input type="checkbox" [(ngModel)]="remote" (change)="chosenFilterValues($event,'remote')">
                    <span>Remote</span>
                </label>
            </div>
        </div>
        <div class="filter-btn filter-btn-multiselect">
                <!-- styleClass="multiselect-custom"  -->
            <img src="./assets/images/secondbar/search-job/Type of time.svg" class="filter-btn-icon">
            <p-multiSelect
                [options]="emp_type"
                [(ngModel)]="Employment_Types"
                optionLabel="name"
                [filter]="true"
                filterBy="label,value.name"
                styleClass="multiselect-filter"
                [showTransitionOptions]="'1ms'"
                [hideTransitionOptions]="'2ms'"
                (onChange)="chosenFilterValues($event,'Employment_Types')"
                defaultLabel="Type"
                [displaySelectedLabel]=false
                [showToggleAll]=false>
            </p-multiSelect>
        </div>
        <div class="filter-btn filter-btn-ng-select-multiselect">
            <img src="./assets/images/secondbar/search-job/Skills.svg" class="filter-btn-icon">
            <span class="filter-btn-label" style="color:#fff;">Skills</span>
            <ng-select 
                [items]="skillsDD.items"
                [virtualScroll]="true"
                [(ngModel)]="Skills"
                [loading]="skillsDD.loading"
                [multiple]="true"
                [closeOnSelect]="false"
                bindLabel="name"
                notFoundText="No items found"
                [dropdownPosition]="'bottom'"
                (scrollToEnd)="skillsDD.onScrollToEnd()"
                (search)="skillsDD.search($event)"
                [searchFn]="skillsDD.customSearchFn"
                (change)="chosenFilterValues($event,'Skills')"
            >
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <!-- <span class="ng-value-label">{{item.name}}</span>
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                        <i class="fa fa-times" aria-hidden="true"></i>
                    </span> -->
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                    {{item.name}}
                </ng-template>
            </ng-select> 
            <!-- <p-multiSelect
                [options]="skills"
                [(ngModel)]="Skills"
                optionLabel="name"
                [filter]="true"
                filterBy="label,value.name"
                [showTransitionOptions]="'1ms'"
                [hideTransitionOptions]="'2ms'"
                (onChange)="chosenFilterValues($event,'Skills')"
                styleClass="multiselect-filter"
                [displaySelectedLabel]=false
                defaultLabel="Skills"
                [showToggleAll]=false>
            </p-multiSelect> -->
        </div>
        <div class="filter-btn">
            <div class="dropdown">
                <span class="dropdown-toggle" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="border: none; font-weight: normal; cursor: pointer;">
                    <img src="./assets/images/secondbar/search-job/Experience.svg" class="filter-btn-icon">
                    <!-- <img src="./assets/images/secondbar/curriculum-vitae.svg" class="filter-btn-icon"> -->
                    <span class="filter-btn-label" style="color:#fff;">Experience</span>
                <!-- <i class="pi pi-chevron-down" style="top:2px; position: absolute;"></i> -->
                </span>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="width: 190px !important; padding: 0px !important;">
                    <p-listbox
                        class="dropdown-item"
                        [options]="year_of_exp"
                        [(ngModel)]="Year_of_experience"
                        [multiple]="false"
                        [checkbox]="false"
                        [filter]="true"
                        optionLabel="name"
                        filterBy="label,value.name"
                        styleClass="drop"
                        (onChange)="chosenFilterValues($event,'Year_of_experience')">
                    </p-listbox>
                </div>
            </div>
        </div>
        <div class="filter-btn">
            <span (click)="showlocationFilterDialog();open_location_filter()">
                <img src="./assets/images/secondbar/search-job/location-01.svg" class="filter-btn-icon">
                <span class="filter-btn-label">Location</span>
            </span>
            <!-- closable="true" dismissableMask="true" -->
            <p-dialog [(visible)]="locationFilterVisibility" styleClass="cust-p-dialog allFiltersDialog" [closable]="false" [draggable]="false" [focusOnShow]="false">
                <p-header>
                    <div class="dialog-close-btn" (click)="cancelLocationFilter()">
                        <i class="fa fa-times" aria-hidden="true"></i>
                    </div>
                </p-header>
                <div class="container-fluid">
                        <!-- (ngSubmit)="submit('formLocationFilters',op)" -->
                <form #formLocationFilters="ngForm" [formGroup]="filtersFormLocation" (keydown.enter)="$event.preventDefault()"
                    class="form-horizontal location-filter-interface validate-form">

                    <div class="row group-mar">
                        <div class="col-xs-2 alignment-right">
                            <input type="radio" formControlName="location_radio" value="country-city" (change)="disableOtherSectionOfLocation('location')"  class="custom-control-input check margin1">
                        </div>
                        <div class="col-xs-10" formGroupName="country_city">
                            <div class="form-group equal-height-row-cols">
                                <div class="col-sm-2 col-xs-12 label-fixed">
                                    <span>Country</span>
                                </div>
                                <div class="col-sm-8 col-xs-12 focus-no-padding">
                                    <input formControlName="country" placeholder=" " type="text" class="form-control" #googlelocationplaceCountry>
                                    <span class="custom-underline"></span>
                                </div>
                            </div>

                            <div class="form-group equal-height-row-cols">
                                <div class="col-sm-2 col-xs-12 label-fixed">
                                    <span>City</span>
                                </div>
                                <div class="col-sm-8 col-xs-12 focus-no-padding">
                                    <input formControlName="city" placeholder=" " id="test" type="text" class="form-control" #googlelocationplaceCity>
                                    <span class="custom-underline"></span>
                                </div>
                            </div>

                        </div>
                    </div>

                    <hr>

                    <div class="row group-mar">
                        <div class="col-xs-2 alignment-right">
                            <input type="radio" formControlName="location_radio" value="location"  (change)="disableOtherSectionOfLocation('country-city')"  class="custom-control-input check margin1" checked="checked">
                        </div>
                        <div class="col-xs-10" formGroupName="location">
                            <div class="form-group equal-height-row-cols">
                                <div class="col-sm-2 col-xs-12 label-fixed">
                                    <span>Location</span>
                                </div>
                                <div class="col-sm-8 col-xs-12 focus-no-padding">
                                    <input formControlName="location" placeholder=" " type="text" class="form-control" #googlelocationplaceLocation>
                                    <span class="custom-underline"></span>
                                </div>
                            </div>

                            <div class="form-group equal-height-row-cols">
                                <div class="col-sm-2 col-xs-12 label-fixed">
                                    <span>Distance Range</span>
                                </div>
                                <div class="col-sm-3 col-xs-12 focus-no-padding">
                                    <input formControlName="distance" type="number" class="form-control" (focusout)="setSelectedLocation()" style="height:100%;">
                                    <span class="custom-underline"></span>
                                </div>
                                <div class="col-sm-2 col-xs-12 label-fixed mar-top-mob">
                                    <span>Distance Unit</span>
                                </div>
                                <div class="col-sm-3 col-xs-12 focus-no-padding">
                                    <p-dropdown [options]="distanceTool" optionLabel="label"
                                        formControlName="unit_distance" placeholder="Distance Unit"
                                        (onChange)="setSelectedLocation()" styleClass="unit_distance">
                                    </p-dropdown>
                                    <!-- <span class="custom-underline"></span> -->
                                </div>
                            </div>

                            <div class="alert alert-info" role="alert">
                                    <p style="text-align:justify"><span style="font-weight:bold">Note:</span> If you want to filter based on location field, please choose both distance range and distance unit fields to get the required results</p>
                            </div>
                            
                        </div>
                    </div>

                    <!-- <div class="row group-mar">
                        <div class="col-sm-10 col-sm-offset-1" *ngIf="location_init">
                            <app-map [sub]="currentSub" birthOrCurrent="current" [type]="currentType" [search]="googlelocationplaceLocationRef">

                            </app-map>
                        </div>
                    </div> -->

                    <!-- <p>{{ formLocationFilters.value | json }}</p> -->
                    <div class="row">
                        <div class="col-sm-12 text-center">
                            <button pButton style="background-color: #3bb34b !important; color:white !important" icon="pi pi-check" type="submit" label="Apply" class="p-button-text" (click)="submit('formLocationFilters')"></button>
                        </div>
                    </div>

                    </form>
                </div>
            </p-dialog>
        </div>
        <div class="filter-btn all-filters-btn">
            <span (click)="showAllFiltersDialog()">
                <img src="./assets/images/secondbar/search-job/All-Filters.svg" class="filter-btn-icon">
                <span class="filter-btn-label">All Filters</span>
            </span>
            <p-dialog [(visible)]="allFiltersVisibility" styleClass="allFiltersDialog" [closable]="false" [draggable]="false" [focusOnShow]="false" >
                <p-header>
                    <div class="dialog-close-btn" (click)="cancelAllFilters()">
                        <i class="fa fa-times" aria-hidden="true"></i>
                    </div>
                </p-header>

                <form *ngIf="nationality.length" #formFilters="ngForm" class="form-horizontal validate-form">
                    <div class="container-fluid all-filters-form">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Experience</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                        <p-multiSelect
                                            styleClass="cust-p-multiselect"
                                            [options]="ExpField"
                                            [(ngModel)]="Experience_Field"
                                            name = "Experience_Field"
                                            optionLabel="name"
                                            [filter]="true"
                                            filterBy="label,value.name"
                                            [showTransitionOptions]="'1ms'"
                                            [hideTransitionOptions]="'2ms'"
                                            (onChange)="chosenFilterValues($event,'Experience_Field')"
                                            defaultLabel="Experience Field"
                                            [filterPlaceHolder]="'Search...'"
                                            appendTo="body">
                                        </p-multiSelect>
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>

                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Company</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="com_industries"    -->
                                        <p-multiSelect
                                            [options]="com_industries"
                                            [(ngModel)]="Company_Industries"
                                            name = "Company_Industries"
                                            optionLabel="name"
                                            [filter]="true"
                                            filterBy="label,value.name"
                                            styleClass="cust-p-multiselect"
                                            [showTransitionOptions]="'1ms'"
                                            [hideTransitionOptions]="'2ms'"
                                            (onChange)="chosenFilterValues($event,'Company_Industries')"
                                            defaultLabel="Company Industry"
                                            appendTo="body">
                                        </p-multiSelect>
                                        <!-- styleClass="multiselect-custom"  -->
                                        <!-- defaultLabel="ex:Acounting, Banking...." -->
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>

                                <!-- <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Salary</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div">
                                        <ng5-slider
                                            class="form-control"
                                            style="z-index: 1;"
                                            [options]="options2"
                                            [(value)]="minValue2"
                                            (userChange)="chosenFilterValues($event,'Salary')"
                                            [(ngModel)]="Salary"
                                            name = "Salary"
                                            [(highValue)]="maxValue2">
                                        </ng5-slider>
                                    </div>
                                </div> -->
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Certification</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="certification" -->
                                        <input type="text" [(ngModel)]="certification" name = "certification" (keyup)="chosenFilterValues($event,'certification')" class="form-control" placeholder="Certification">
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Company</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="company_name" -->
                                        <input type="text" class="form-control" [(ngModel)]="company_name" name="company_name" (keyup)="chosenFilterValues($event,'company_name')" placeholder="Company Name">
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
                                        <span>Driving</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="driving"  -->
                                        <p-dropdown
                                            [options]="driving_license"
                                            optionLabel="label"
                                            [filter]="true"
                                            (onChange)="chosenFilterValues($event,'Driving_License')"
                                            [(ngModel)]="Driving_License"
                                            name="Driving_License"
                                            placeholder="Driving License"
                                            appendTo="body">
                                            <ng-template let-car pTemplate="item">
                                                <div class="ui-helper-clearfix" style="position: relative;height:25px;">
                                                    <div style="font-size:14px;float:left;margin-top:4px">{{car.label}}</div>
                                                    <img src="./assets/images/driving/{{car.label}}.png" style="width:24px;position:absolute;top:1px;right:5px" />
                                                </div>
                                            </ng-template>
                                            <ng-template let-item pTemplate="selectedItem">
                                                <span style="vertical-align:middle;float:left">{{item.label}}</span>
                                                <img src="./assets/images/driving/{{item.label}}.png" style="width:24px;vertical-align:middle;float:right" />
                                            </ng-template>
                                        </p-dropdown>
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Age</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div">
                                            <!-- formControlName="age" -->
                                        <ng5-slider class="form-control" style="z-index: 1;" [options]="options" [(ngModel)]="Age" name="Age" (userChange)="chosenFilterValues($event,'Age')" [(value)]="minValue" [(highValue)]="maxValue"></ng5-slider>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="vl"></div> -->

                            <div class="col-lg-6">
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Gender</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="gender"  -->
                                        <p-selectButton [(ngModel)]='Gender' name="Gender" [options]="gender" (onChange)="chosenFilterValues($event,'Gender')" styleClass="cus-selectButton" ></p-selectButton>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Nationality</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="nationality"  -->
                                        <p-multiSelect
                                            [options]="nationality"
                                            [(ngModel)]="Nationality"
                                            name="Nationality"
                                            optionLabel="name"
                                            [filter]="true"
                                            filterBy="label,value.name"
                                            [showTransitionOptions]="'1ms'"
                                            [hideTransitionOptions]="'2ms'"
                                            (onChange)="chosenFilterValues($event,'Nationality')"
                                            styleClass="cust-p-multiselect"
                                            defaultLabel="Nationality"
                                            appendTo="body">
                                        </p-multiSelect>
                                        <!-- styleClass="multiselect-custom"  -->
                                        <!-- defaultLabel="ex: syrian, italian....." -->
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Languages</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="langs"  -->
                                        <p-multiSelect
                                            [options]="langs"
                                            [(ngModel)]="Languages"
                                            name="Languages"
                                            optionLabel="name"
                                            [filter]="true"
                                            filterBy="label,value.name"
                                            [showTransitionOptions]="'1ms'"
                                            [hideTransitionOptions]="'2ms'"
                                            (onChange)="chosenFilterValues($event,'Languages')"
                                            styleClass="cust-p-multiselect"
                                            defaultLabel="Languages"
                                            appendTo="body">
                                        </p-multiSelect>
                                        <!-- defaultLabel="ex: English, Arabic......" -->
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
                                        <span>Degree Level</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                                            <!-- formControlName="degree_level"  -->
                                        <p-dropdown
                                            [options]="degreeLevel"
                                            optionLabel="name"
                                            [filter]="true"
                                            (onChange)="chosenFilterValues($event,'Degree_Level')"
                                            [(ngModel)]="Degree_Level"
                                            name="Degree_Level"
                                            placeholder="Degree Level"
                                            appendTo="body">
                                            <ng-template let-degree pTemplate="item">
                                                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                                    <div style="font-size:14px;float:left;margin-top:4px">{{degree.label}}</div>
                                                </div>
                                            </ng-template>
                                        </p-dropdown>
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>
                                <div class="form-group equal-height-row-cols override-form-control-zindex">
                                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                                        <span>Educations</span>
                                    </div>
                                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding"> 
                                        <p-multiSelect
                                            [options]="education_fields"
                                            [(ngModel)]="educations"
                                            name="educations"
                                            optionLabel="name"
                                            [filter]="true"
                                            filterBy="label,value.name"
                                            [showTransitionOptions]="'1ms'"
                                            [hideTransitionOptions]="'2ms'"
                                            (onChange)="chosenFilterValues($event,'educations')"
                                            styleClass="cust-p-multiselect"
                                            defaultLabel="Education field"
                                            appendTo="body">
                                        </p-multiSelect>
                                        <span class="custom-underline"></span>
                                    </div>
                                </div>                             
                            </div>
                        </div>
                        <div class="row div-margin-bo-30">
                            <div class="col-xs-12 text-center">
                                <button pButton style="background-color: #3bb34b !important; color:white !important; outline: none !important; box-shadow: none !important;margin-right: 10px;" icon="pi pi-check" (click)="submit('formFilters')" label="Apply" class="p-button-text">
                                </button>
                                <!-- <p-button icon="pi pi-times" (click)="clear_all_filter()" label="Clear All Filter"></p-button> -->
                                <p-button icon="pi pi-times" (click)="clear_all_filters_interface()" label="Clear All Filter"></p-button>
                            </div>
                        </div>
                    </div>
                </form>
            </p-dialog>
        </div>
    </div>   <!-- End filters container  -->
    
    <app-pre-loader [show]="firstLoad"></app-pre-loader>
    
    <div class="general-wrapper custom-container" [hidden]="firstLoad">
        <h1 class="page-title">{{pageTitle}}</h1>
    <!-- start filter tags container -->
    <div class="tags-container">
        <div class="tags-col-1">
            <ng-container *ngFor="let c of selected_filters; let i = index;">
               
                <!-- <ng-container *ngIf= "(c.filter_name === 'Major' || c.filter_name === 'Minor') && c.filter_value.name">
                    <span class="selectLabel"> {{c.filter_label+': '}}</span>
                    <span class="tag-label">{{c.filter_value.name}}
                        <button (click)="removeNg5SliderFilterValue(c.filter_name)" class="close2"><span aria-hidden="true">&#10006;</span></button>
                    </span>
                    <span class="tag-separator">   </span>
                </ng-container> -->
                <ng-container *ngIf="c.filter_name ==='Driving_License'  && c.filter_value.label else elseYearExp">
                    <span class="selectLabel"> {{c.filter_label+': '}}</span>
                    <span class="tag-label"> {{c.filter_value.label}}
                        <button (click)="removeDropdownFilterValue(c.filter_name)" class="close2">
                            <span aria-hidden="true">&#10006;</span>
                        </button>
                    </span>
                    <span class="tag-separator">   </span>
                </ng-container>
                <ng-container *ngIf="c.filter_name ==='Degree_Level'  && c.filter_value.name">
                    <span class="selectLabel"> {{c.filter_label+': '}}</span>
                    <span class="tag-label"> {{c.filter_value.name}}
                        <button (click)="removeDropdownFilterValue(c.filter_name)" class="close2">
                            <span aria-hidden="true">&#10006;</span>
                        </button>
                    </span>
                    <span class="tag-separator">   </span>
                </ng-container>
                <ng-template #elseYearExp>
                    <ng-container *ngIf="c.filter_name ==='Year_of_experience'  && c.filter_value.name">
                        <span class="selectLabel"> {{c.filter_label+': '}}</span>
                        <span class="tag-label"> {{c.filter_value.name}}
                            <button (click)="removeDropdownFilterValue(c.filter_name)" class="close2">
                                <span aria-hidden="true">&#10006;</span>
                            </button>
                        </span>
                        <span class="tag-separator">   </span>
                    </ng-container>
                </ng-template>

                <ng-container *ngIf="c.filter_value.length">
                    <span class="selectLabel"> {{c.filter_label+': '}}</span>
                    <span *ngIf="c.filter_value[0].name else elseNg5">
                        <span *ngFor="let value of c.filter_value; let j = index" class="tag-label">
                            {{c.filter_value[j].name}}
                            <button (click)="removeFilterValue(c.filter_name,j)" class="close2"><span aria-hidden="true">&#10006;</span></button>
                        </span>
                        <span class="tag-separator">   </span>
                    </span>

                    <ng-template #elseNg5>
                        <span class="tag-label" *ngIf="c.filter_name ==='Age' || c.filter_name ==='Salary' "> {{c.filter_value[0] + '-' + c.filter_value[1]}} </span>
                        <span class="tag-label" *ngIf="c.filter_name ==='Gender'"> {{c.filter_value}} </span>
                        <span class="tag-label" *ngIf="c.filter_name ==='certification' || c.filter_name ==='company_name' || c.filter_name ==='job_title' || c.filter_name ==='country' || c.filter_name ==='city' || c.filter_name ==='location'"> {{c.filter_value}} </span>
                        <button (click)="removeNg5SliderFilterValue(c.filter_name)" class="close2"><span aria-hidden="true">&#10006;</span></button>
                        <span class="tag-separator">   </span>
                    </ng-template>
                </ng-container>

                <ng-container *ngIf="c.filter_name ==='remote' && c.filter_value === true">
                    <span class="selectLabel"> {{c.filter_label+': '}}</span>
                    <span class="tag-label" *ngIf="c.filter_value === true"> Remote </span>
                    <button (click)="removeNg5SliderFilterValue(c.filter_name)" class="close2"><span aria-hidden="true">&#10006;</span></button>
                    <span class="tag-separator">   </span>
                </ng-container>
                <!-- <ng-container *ngIf="c.filter_name ==='Verified_Companies' && c.filter_value === true">
                    <span class="selectLabel"> {{c.filter_label+': '}}</span>
                    <span class="tag-label" *ngIf="c.filter_value === true"> Only show verified companies </span>
                    <button (click)="removeNg5SliderFilterValue(c.filter_name)" class="close2"><span aria-hidden="true">&#10006;</span></button>
                    <span class="tag-separator">   </span>
                </ng-container> -->

            </ng-container>  <!-- end of first ng-container -->
        </div>
        <div class="tags-col-2">
            <button (click)="clear_all_filter()" *ngIf="this.clearAll == true" class="close2" style="font-size: 24px; background-color: white;"><span aria-hidden="true">&#10006;</span></button>
        </div>
    </div>

    <div style="clear:both"></div>
    <div>
        <!-- <p>{{ filtersForm.value | json }}</p> -->
    </div>
    <!-- end filter tags container -->

    <!-- start main advertisement table -->
    <!-- [style]="{marginTop: '100px'}" -->
    <p-toast position="bottom-right"></p-toast>

    <div *ngIf="role !=='unauth' && !isMobileLayout" class="config-btn-mobile" (click)="col_settings()">
        <i class="fa fa-cog" aria-hidden="true"></i>
    </div>
    <div *ngIf="role ==='unauth' && !isMobileLayout" class="config-btn-mobile" (click)="col_settings()" pTooltip="This is for Registered members only!">
        <i class="fa fa-cog" aria-hidden="true"></i>
    </div>

    <!-- <app-pre-loader [show]="showLoader === true"></app-pre-loader> -->
    <!-- *ngIf="showLoader === false" -->
    <p-table  #dt [value]="data_Source" dataKey="job_adv_id" styleClass="ui-table-customers" [reorderableColumns]="true" [rows]="100" [loading]="showLoader"  [columns]="_selectedColumns" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
        [filterDelay]="500" [globalFilterFields]="filterColumns" [rowHover]="true" [responsive]="true"  [autoLayout]="true">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th style="width:40px !important">
                    <div class="tooltip">
                        <span *ngIf="role ==='unauth'" id="cog" pTooltip="This is for Registered members only!" class="fa fa-cog" (click)="col_settings()"></span>
                        <span *ngIf="role !=='unauth'" id="cog" class="fa fa-cog" (click)="col_settings()"></span>
                    </div>
                </th>

                <th *ngFor="let col of columns" class="ui-column-title" [ngClass]="{'header':col.field === 'company_logo'}" [pSortableColumn]="col.field" translate>
                    {{col.header}}
                    <p-sortIcon [field]="col.field"></p-sortIcon>
                </th>
                <th style=" width: 140px;"></th>
            </tr>
        </ng-template>

        <ng-template  pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns" let-index="rowIndex">
            <tr *ngIf="isMobileLayout" class="ui-selectable-row tr-hover" id=" {{ data.job_adv_id }}" [pReorderableRow]="index" (click)="displayAdvrModal(data.job_adv_id, data.slug)">
                <td style="position:relative;cursor: pointer;">
                    <div class="publish-date-div">
                        <div *ngIf="data['opportunity_for']==='my_company'" class="verification-status">
                            <img *ngIf="data['company_verified'] === 'Golden Verified'" src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner">
                            <img *ngIf="data['company_verified'] === 'Silver Verified'" src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner">
                            <img *ngIf="data['company_verified'] === 'Verified'" src="./assets/images/verified-company.svg" alt="Verified company">
                        </div>
                        <div *ngIf="data['opportunity_for']==='other_company'" class="verification-status">
                            <img *ngIf="data['company_verified'] === 'Golden Verified'" src="./assets/images/icons/cveek-gold-partner-recruiting-company.svg" alt="CVeek gold partner recruiting company">
                            <img *ngIf="data['company_verified'] === 'Silver Verified'" src="./assets/images/icons/cveek-silver-partner-recruiting-company.svg" alt="CVeek silver partner recruiting company">
                            <img *ngIf="data['company_verified'] === 'Verified'" src="./assets/images/icons/verified-recruiting-company.svg" alt="Verified recruiting company">
                            <img *ngIf="data['company_verified'] === null" src="./assets/images/icons/recruiting-company.svg" alt="Recruiting company">
                        </div>
                        <span  class="customer-badge"
                        [ngClass]="{'status-new':data['publish_data'] <=4, 'status-medium': data['publish_data'] >= 5 && data['publish_data'] <=10 , 'status-long':data['publish_data'] >= 11}">
                            {{data['publish_data']}} days ago
                        </span>
                        <!-- <span  pTooltip="published  {{published_dates[index].date}} days ago" [class]="'customer-badge status-' + published_dates[index].color" style="text-transform: lowercase;font-size:13px;">
                            {{published_dates[index].date}} days ago </span>
                        <span style="background: #ffff00; color:black !important; border-radius: 0px; font-weight: normal !important;" *ngIf="published_dates[index].date < 5" class="badge badge-info">New</span> -->
                    </div>

                
                    <div class="adv-summary-mob">
                        <div class="adv-summary">
                            <div class="logo-div">
                                    <!-- (click)="stopPropagation($event);navigateToCompanyProfile(data['company_username'])" -->
                                <a *ngIf="data['company_name']!==null && data['company_logo']!==null">
                                    <img [src]="getImageLogo(data['company_logo'],data['company_name'],data['opportunity_for'])"  class="circle-img" alt="{{data['company_name']}}" />
                                </a>
                                <img *ngIf="data['company_name']===null" [src]="getImageLogo(data['company_logo'],data['company_name'],data['opportunity_for'])" class="circle-img" alt="Confidential Company" />
                                <div *ngIf="data['company_name']!==null && data['company_logo']===null"></div>
                            </div>

                            <div class="content-div">
                                <div class="comp-name">
                                    <span *ngIf="data['company_name']===null" style="color:#333333">Confidential Company &nbsp;</span>
                                    <span *ngIf="data['company_name']!==null">{{data['company_name']}} &nbsp;</span>
                                </div>
                                <h2 class="job-title">{{data['job_title']}}</h2>
                                <div class="location-mob" *ngIf="data['country']">
                                    <span *ngIf="data['country'] && data['is_remote'] === false">
                                        <img [src]="getFlagImage(data['country_code'])" width="17" style="margin-right: 5px;" />
                                        <span>{{data['country']}}</span>
                                    </span>
                                    <span *ngIf="data['city'] && data['is_remote'] === false">,
                                        {{data['city']}}
                                    </span>
                                    <span *ngIf="data['is_remote'] === true">
                                        <img  src="./assets/images/secondbar/search-job/remote-work-gray.svg" alt="Remote work" width="18">
                                        {{data['country']}}
                                    </span>
                                </div>
                                <div class="employment-type-mob"  *ngIf="data['emp_type']">
                                    <span>
                                        <img src="./assets/images/icons/emp-type.svg" alt="Job Type" style="width:18px;">
                                        {{data['emp_type']}}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="adv-apply" *ngIf="role === 'unauth' || role ==='ROLE_JOB_SEEKER'">
                            <button *ngIf="data.external_link !== null" id="apply" pButton type="button" 
                                  label="Apply" icon="fa fa-external-link" class="apply" (click)="apply_on_company_site(data.external_link)">
                            </button>
                            <div *ngIf="data.external_link === null">
                                <button *ngIf="!data.applied" id="apply" pButton type="button" 
                                    [disabled]="applyWaiting"  label="Apply" icon="pi pi-check" class="apply"
                                    (click)="apply_for_job(data.job_adv_id,data.company_name, data.job_title)">
                                </button>
                                <button *ngIf="data.applied" id="applied" pButton type="button" label="Applied" icon="pi pi-check" class="applied disabled">
                                </button>
                            </div>
                            
                        </div>
                    </div>
                </td>
            </tr>

            <tr *ngIf="!isMobileLayout" class="ui-selectable-row tr-hover" id=" {{ data.job_adv_id }}" [pReorderableRow]="index">
                <td class="not-visible-mobile">
                    <div *ngIf="data['opportunity_for']==='other_company'" class="verification-status-desktop">
                        <img *ngIf="data['company_verified'] === 'Golden Verified'" src="./assets/images/icons/cveek-gold-partner-recruiting-company.svg" alt="CVeek gold partner recruiting company" pTooltip="Posted by Golden partner recruiting company" tooltipStyleClass="wide-tooltip">
                        <img *ngIf="data['company_verified'] === 'Silver Verified'" src="./assets/images/icons/cveek-silver-partner-recruiting-company.svg" alt="CVeek silver partner recruiting company" pTooltip="Posted by Silver partner recruiting company" tooltipStyleClass="wide-tooltip">
                        <img *ngIf="data['company_verified'] === 'Verified'" src="./assets/images/icons/verified-recruiting-company.svg" alt="Verified recruiting company" pTooltip="Posted by verified recruiting company" tooltipStyleClass="wide-tooltip">
                        <img *ngIf="data['company_verified'] === null"  src="./assets/images/icons/recruiting-company.svg" alt="Recruiting company" pTooltip="Recruiting company">
                    </div>
                    <div *ngIf="data['opportunity_for']==='my_company'" class="verification-status-desktop">
                        <img *ngIf="data['company_verified'] === 'Golden Verified'" src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner" pTooltip="Gold Partner">
                        <img *ngIf="data['company_verified'] === 'Silver Verified'" src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner"  pTooltip="Silver Partner">
                        <img *ngIf="data['company_verified'] === 'Verified'" src="./assets/images/verified-company.svg" alt="Verified company"  pTooltip="Verified Company">
                    </div>
                </td>
                <td *ngFor="let col of columns; let i =index;" style=" cursor: pointer" (mouseover)="display2($event)" [ngClass]="{'header':col.field === 'company_logo',
                'name' :col.field=== 'company_name',
                'title' :col.field=== 'job_title',
                'industry' :col.field=== 'company_industry' ,
                'skills' :col.field=== 'skill' ,
                'nationality' :col.field=== 'nationality' ,
                'languages' :col.field=== 'languages'
                }" (click)="displayAdvrModal(data.job_adv_id , data.slug)">

               <!-- <span *ngIf="col.field !== 'company_logo' && col.field !== 'company_name' && data[col.data]" class="ui-column-title">{{col.header}}</span> -->

               <div *ngIf="col.field === 'company_logo'">
                    <!-- [routerLink]="['/i/c', data['company_username'] ]" -->
                    <!-- (click)="stopPropagation($event);navigateToCompanyProfile(data['company_username'])" -->
                    <a *ngIf="data['company_name']!==null && data['company_logo']!==null">
                        <img [src]="getImageLogo(data['company_logo'],data['company_name'],data['opportunity_for'])" width="60" class="circle-img" alt="{{data['company_name']}}" />
                    </a>
                    <img *ngIf="data['company_name']===null" [src]="getImageLogo(data['company_logo'],data['company_name'],data['opportunity_for'])" width="50" class="circle-img" alt="Confidential Company" />
                    <div *ngIf="data['company_name']!==null && data['company_logo']===null"></div>
               </div>
               
               <img *ngIf="col.field === 'country' && data[col.data] && data['is_remote']===false" [src]="getFlagImage(data['country_code'])" width="20" style="margin-right: 5px;" />
               <!-- <img *ngIf="(col.field === 'country' || col.field === 'city' || col.field === 'company_location') && data['is_remote'] === true" src="./assets/images/secondbar/search-job/remote-work-gray.svg" width="20" style="margin-right: 5px;" alt="Remote work"> -->
               <span title="{{ data[col.data] }}"  *ngIf="col.field !== 'company_logo' && col.field !== 'job_title' && col.field !== 'company_industry' && col.field !== 'skill' && col.field !== 'nationality' && col.field !== 'languages' && col.field !== 'educations' && col.field !== 'company_name' && col.field !== 'salary'  && col.field !== 'emp_type'">
                {{data[col.data] | slice:0:20}}
                <span *ngIf="data[col.data]?.length>20">...</span>
                </span>
                <h2 title="{{ data['job_title'] }}" *ngIf="col.field=== 'job_title'" class="title">
                    {{data['job_title'] | slice:0:60}}
                    <span *ngIf="data[col.data]?.length>60">...</span>
                </h2>

                <span *ngIf="col.field=== 'salary'">
                    {{data[col.data]}}
                </span>

               <span *ngIf="col.field=== 'company_name'">
                    <div *ngIf="data['company_name']===null">
                        <span style="color:#333333">Confidential Company</span>
                    </div>
                    <!-- <a *ngIf="data['company_name']!==null" (click)="stopPropagation($event);navigateToCompanyProfile(data['company_username'])"></a> -->
                    <div *ngIf="data['company_name']!==null">
                        <span title="{{data[col.data]}}">
                                <span>{{data[col.data] | slice:0:40}}</span>
                                <span *ngIf="data[col.data]?.length>=40">...</span>
                        </span>
                    </div>
               </span>
               <span *ngIf="col.field === 'skill'" title="{{data[col.data]}}">
               <span *ngFor="let value of data[col.data] | slice:0:2; let i = index">
                   {{value}}<span *ngIf="i < data[col.data].length - 1">,<span *ngIf="i == 1">...</span></span>
                   <!-- {{value}} <span *ngIf="i < data[col.data].length - 1"> , </span> -->
                   
               </span>
               </span>
               <span *ngIf="col.field === 'company_industry'" title="{{data[col.data]}}">
               <span *ngFor="let value of data[col.data] | slice:0:1; let i = index">
                   {{value}}<span *ngIf="i < data[col.data].length - 1">,<span *ngIf="i == 0">...</span></span>
               </span>
               </span>
               <span *ngIf="col.field === 'nationality'" title="{{data[col.data]}}">
                   <span *ngFor="let value of data[col.data] | slice:0:2; let i = index">
                       {{value}}<span *ngIf="i < data[col.data].length - 1">,<span *ngIf="i == 0">...</span></span>
                   </span>
                </span>
                <span *ngIf="col.field === 'languages'" title="{{data[col.data]}}">
                    <span *ngFor="let value of data[col.data] | slice:0:2; let i = index">
                        {{value}}<span *ngIf="i < data[col.data].length - 1">,<span *ngIf="i == 1">...</span></span>
                    </span>
                </span>

                <span *ngIf="col.field === 'educations'" title="{{data[col.data]}}">
                    <span *ngFor="let value of data[col.data] | slice:0:2; let i = index">
                        {{value}}<span *ngIf="i < data[col.data].length - 1">,<span *ngIf="i == 1">...</span></span>
                    </span>
                </span>

                <span *ngIf="col.field === 'emp_type'" title="{{data[col.data]}}">
                    <span *ngFor="let value of data[col.data] | slice:0:2; let i = index">
                        {{value}}<span *ngIf="i < data[col.data].length - 1">,<span *ngIf="i == 1">...</span></span>
                    </span>
                </span>

               <span *ngIf="col.field === 'publish_data'">
                    <span  pTooltip="published  {{data['publish_data']}} days ago" class="customer-badge"
                    [ngClass]="{'status-new':data['publish_data'] <=4, 'status-medium': data['publish_data'] >= 5 && data['publish_data'] <=10 , 'status-long':data['publish_data'] >= 11}">
                        {{data['publish_data']}}
                    </span>
                    <span style="background: #fdfd49; color:black !important; border-radius: 0px; font-weight: normal !important;font-size: 9px;padding: 3px;" *ngIf="data['status_new']" class="badge badge-info">New</span>
                    <!-- <span  pTooltip="published  {{published_dates[index].date}} days ago" [class]="'customer-badge status-' + published_dates[index].color">
                        {{published_dates[index].date}}
                    </span>
                    <span style="background: #ffff00; color:black !important; border-radius: 0px; font-weight: normal !important;" *ngIf="published_dates[index].date < 5" class="badge badge-info">New</span> -->
                </span>
                </td>

                <td  style="width: 140px;">
                    <div *ngIf="role === 'unauth' || role ==='ROLE_JOB_SEEKER'">
                        <button *ngIf="data.external_link !== null" id="apply" pButton type="button" 
                            label="Apply" icon="fa fa-external-link" class="apply" (click)="apply_on_company_site(data.external_link)">
                        </button>
                        <div *ngIf="data.external_link === null">
                            <button *ngIf="!data.applied" id="apply" pButton type="button" [disabled]="applyWaiting" 
                            (click)="apply_for_job(data.job_adv_id,data.company_name, data.job_title)" label="Apply" icon="pi pi-check" class="apply">
                            </button>
       
                            <button *ngIf="data.applied" id="applied" pButton type="button" label="Applied" icon="pi pi-check" class="applied disabled">
                            </button>
                        </div>
                    </div>
                    <!-- <p-toast [style]="{marginTop: '100px'}"></p-toast> -->
                </td>
            </tr>
        </ng-template>
    </p-table>

    <div class="clearfix"></div>

    <div *ngIf="data_Source.length === 0 && showLoader == false" style="color: #186ba0;font-weight: bold;text-align: center;margin-bottom: 15px;">
        <span>No results matched!</span>
    </div>
    <!-- end main advertisement table -->

    <p-paginator [rows]="rows" showCurrentPageReport="true" [totalRecords]="totalRecords" (onPageChange)="paginate($event)" #p>
    </p-paginator>

    <div class="clearfix"></div>

    <div class="modal fade" id="ColsModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModal2Label" translate>Columns Management</h4>
                </div>
                <app-table-cols-management [tableCols]="tableCols" (closeModalPopup)="handlePopup($event)">
                </app-table-cols-management>
            </div>
        </div>
    </div>

    <div *ngIf="colsReadyPassToModal == true">
        <cols-management
            [fromComponent]="'advr-interface'"
            [allItems]="global_col"
            [selectedItems]="selectedColumnsWithoutStandardCols"
            [standardItems]="standardCol"
            [maxColAllowed]=6
            (closeModalPopup)="handlePopup($event)">
        </cols-management>
    </div>


    <!-- <div class="modal fade" id="AdvrsModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModal3Label" translate>Advertisement Preview</h4>
                </div>
                <app-advr-preview (closeModalPopup)="handlePopup($event)">

                </app-advr-preview>
            </div>
        </div>
    </div> -->



    <div class="modal fade" id="authModal" tabindex="-1" role="dialog" aria-labelledby="authLabelModal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="authLabelModal" translate>Job Seeker Sign In</h4>
                </div>
                <login [fromPage]="'advr-interface'"></login>
            </div>
        </div>
    </div>

</div>

<div class="flex-space-fix"></div>
<app-footer></app-footer>