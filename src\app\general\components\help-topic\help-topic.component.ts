import { Activated<PERSON><PERSON><PERSON>, Router } from '@angular/router';
import { Component, OnInit, Input, Output, EventEmitter, OnD<PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from 'app/general/services/help.service';
import { <PERSON>, <PERSON>a, DomSanitizer } from '@angular/platform-browser';
import { Subject } from 'rxjs/Subject';
import { LanguageService } from 'app/admin/services/language.service';

@Component({
  selector: 'app-help-topic',
  templateUrl: './help-topic.component.html',
  styleUrls: ['./help-topic.component.css']
})
export class HelpTopicComponent implements OnInit, OnDestroy {
  id: number;
  @Input('helpTopic')helpTopic: { 'id': number, 'main_cat': {'id': number, 'name': string, 'langId': number}[],
  'sub_cat': {'id': number, 'name': string, 'main_cat_id': number}[]} = { 'id': null, 'main_cat': [], 'sub_cat': []} ;

  @Input('helpTopicTrans')helpTopicTrans: { 'title': string, 'description': string,
   'slug': string, 'page_title': string, 'meta_description': string, 'meta_keywords': string, 'langId': number }[] = [];
  @Input('currentLangId')currentLangId: number ;
  @Input('languagesArray')languagesArray = [];
  @Output('displayHomeClicked')displayHomeClicked = new EventEmitter();
  private ngUnsubscribe: Subject<any> = new Subject();
  username: string;

  constructor(private translate: TranslateService,
              private helpService: HelpService,
              private title: Title,
              private meta: Meta,
              private route: ActivatedRoute,
              private router: Router,
              public sanitizer: DomSanitizer,
              private languageService: LanguageService) {
    if (localStorage.getItem('defaultLang')) {
      this.currentLangId = +localStorage.getItem('defaultLang');
    } else {
      this.currentLangId = 1;
    }
    this.username = localStorage.getItem('username');
    
   }

  ngOnInit() {
     this.getId();
  }

  getId() {
    this.route.paramMap.subscribe(params => {
     
      this.id = +params.get('id');
      this.getLanguages();

    });
  }



  displayMainCats() {
    this.displayHomeClicked.emit();
  }



  setMetaData() {
    this.meta.updateTag({ name: 'meta_keywords', content: this.helpTopicTrans[this.currentLangId - 1].meta_keywords });
    this.meta.updateTag({ content: this.helpTopicTrans[this.currentLangId - 1].meta_description} , 'name="description"' );
  }


  changeLang( langId: number) {
    this.translate.use(this.languageService.getLangAbbrev(langId));
    this.currentLangId = langId;
    this.setMetaData();
    this.title.setTitle(this.helpTopicTrans[this.currentLangId - 1].page_title);
  }



  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
     
      let temp = res['data'];
      for ( let lang of temp) {
        this.languagesArray.push({
          'id'  : lang.id,
          'name': lang.name
        });

      }

      this.getHelpTopic(this.id);
    });

  }


  getHelpTopic(id) {
 
    this.helpService.getHelpTopic(id).subscribe(res => {
    
      let temp = res['help_center'];
      let main = [], sub = [];
      let temp2 = temp.help_center_main_cat.help_center_main_cat_trans;
      let temp3 =temp.help_center_sub_cat? temp.help_center_sub_cat.help_center_sub_cat__trans:null;
      for (let item of temp2) {
        main.push({
          'id'    : temp.help_center_main_cat_id,
          'name'  : item.name,
          'langId': item.langId
        });
      }
      if(temp3!=null)
      for (let item of temp3) {
        sub.push({
          'id'         : temp.help_center_sub_cat_id,
          'name'       : item.name,
          'main_cat_id': temp.help_center_main_cat_id
        });
      }
      
      this.helpTopic = {
        'id'      : temp.id,
        'main_cat': main,
        'sub_cat' : sub,

      };
      this.helpTopicTrans = [];
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.helpTopicTrans.push({
          'langId'                 : temp.help_center_trans[i].translated_languages_id,
          'title'                  : temp.help_center_trans[i].title,
          'description'            : temp.help_center_trans[i].description,
          'slug'                   : temp.help_center_trans[i].slug,
          'page_title'             : temp.help_center_trans[i].page_title ,
          'meta_description'       : temp.help_center_trans[i].meta_description,
          'meta_keywords'          : temp.help_center_trans[i].meta_keywords
         });
      }
     
      if ( this.helpTopicTrans.length !== 0) {
        
        this.title.setTitle(this.helpTopicTrans[this.currentLangId - 1].page_title);
        this.setMetaData();
      }
    });
  }

  navigate(type,mainCatName,mainCatId,subCatName?,subCatId?){
    if(type==='main')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) , mainCatId ]);
    else if(type==='sub')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName), mainCatId , 's' ,  this.friendlyUrl(subCatName) , subCatId]);
  }

  friendlyUrl(text){
    text = text.toLowerCase();
    text = text.replace(/ /g,'-');
    return text;
  }

  ngOnDestroy() {
    this.helpTopic = {id: null ,  main_cat: [],
      sub_cat: [] };
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
