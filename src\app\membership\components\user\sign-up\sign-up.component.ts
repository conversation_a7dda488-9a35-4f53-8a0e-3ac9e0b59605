import { Compo<PERSON>, OnInit, NgZone, Renderer2 } from '@angular/core';
import {AuthService} from 'shared/shared-services/auth-service';
import {Router} from '@angular/router';
import {AuthService as SocialAuthService, FacebookLoginProvider} from 'angular5-social-login';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import { Errors } from '../../../models/errors';
import { Observable } from 'rxjs';

import {EmptyObservable} from 'rxjs/observable/EmptyObservable';
import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from '../../../../general/services/general.service';
import { EmailValidator } from 'shared/validators/email.validators';

import { CredentialResponse} from 'google-one-tap';
import { environment } from 'environments/environment.prod';
import { ScriptService } from 'shared/shared-services/script.service';
import { booleanValidator } from '../../../validators/boolean.validators';

@Component({
  selector: 'sign-up',
  templateUrl: './sign-up.component.html',
  styleUrls: ['./sign-up.component.css']
})
export class SignUpComponent implements OnInit {

  signUpForm;
  resetMessage;
  alertResetMessage;
  helpLink;
  type= 'password';
  show = false;
  username = '';
  loader = false;
  constructor(private authService: AuthService,
              private router: Router,
              private  fb: FormBuilder,
              private socialAuthService: SocialAuthService,
              private title: Title,
              private meta:Meta,
              private generalService: GeneralService,
              private ngZone: NgZone,
              private renderer: Renderer2,
              private scriptService: ScriptService
  ) {
    this.signUpForm = this.fb.group({
      first_name : ['', [Validators.required , Validators.pattern('[ a-zA-Z0-9أ-ي]+')] ],
    //  first_name : ['', [Validators.required, Validators.pattern('[ أ-يa-zA-Z]+[a-zA-Z0-9أ-ي]*')] ],
      last_name : ['', [Validators.required, Validators.pattern('[ a-zA-Z0-9أ-ي]+')] ],
      //email : ['', [Validators.required , Validators.email]],
      email : ['', [Validators.required , EmailValidator.isValidEmailFormat]],
      password : ['', [Validators.required , Validators.minLength(6), Validators.maxLength(20)]],
      // add validation that value should be true
      agree_terms : [false,booleanValidator.isValidCheckboxTrue]
    });
  }

  ngOnInit() {
    this.title.setTitle('CVeek Website  سيفيك | Job seeker sign up');
    this.meta.updateTag({ name: 'description', content: 'Register now as job seeker, create your CV and start applying for new jobs for free.'});

    this.authService.currentError.subscribe(errorMessage => this.alertResetMessage = errorMessage);

    this.initializeGoogleLogin();
  }

  isInvalid(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('required');
  }

  isInvalidSyn(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('invalidEmailError');
  //  return this.signUpForm.controls[controlName].hasError('email');
  }

  isInvalidMin(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('minlength');
  }

  isInvalidMax(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('maxlength');
  }

  isInvalidPattern(controlName: string) {
    return this.signUpForm.controls[controlName].hasError('pattern');
  }

  signUp() {
    //remove white spaces from beggining and end of name if it exists
    this.signUpForm.controls['first_name'].setValue(this.signUpForm.controls['first_name'].value.trim());
    this.signUpForm.controls['last_name'].setValue(this.signUpForm.controls['last_name'].value.trim());

    this.authService.logoutIfAlreadyLoggedin();

    const data = this.signUpForm.value;
    if ( this.signUpForm.valid) {
      this.loader = true;
    //  localStorage.setItem('newUser', JSON.stringify(data));
      localStorage.setItem('newUser', data.email);
      localStorage.setItem('futureRole', 'ROLE_JOB_SEEKER');
      this.authService.signUp(data).subscribe(res => {
        this.loader = false;
        if(res['error']){
          this.resetHelpLink();
          this.alertResetMessage = res['error'];
          if(res['type']==='already_registered'){
            this.helpLink = res['help'];
          }
        }
          
        else
          this.router.navigate(['/m/user/verification']);
      }, (err) => {
        this.loader = false;
      //  this.alertResetMessage = err['error']['error']['email'][0];
      });
    }
  }

  public facebookLogin() {
    this.loader = true;

    this.authService.logoutIfAlreadyLoggedin();

    let socialPlatformProvider = FacebookLoginProvider.PROVIDER_ID;
    this.socialAuthService.signIn(socialPlatformProvider).then(
      (userData) => {
        // this will return user data from facebook. What you need is a user token which you will send it to the server
        // this.sendToRestApiMethod(userData.token);
        this.authService.loginWithFacebook(userData).switchMap(
          data => {
            if(data['success']) {
              localStorage.setItem('access_token', data['token'].access_token);
              return this.authService.getUserInfo();
            }
            else if(data['type']=== 'noEmailFacebookAccount'){
              this.loader = false;
              this.resetHelpLink();
              this.alertResetMessage = data['error'];
              return new EmptyObservable<Response>();
            }
            else{
              this.loader = false;
              if(data['error']){
                this.resetHelpLink();
                this.alertResetMessage = data['error'];
                if(data['help'])
                  this.helpLink = data['help'];
              // if(data['error']=== "There is no permission with Job Seeker."){
              //   this.alertResetMessage = Errors.loginWithWrongRule;
              // }
              return new EmptyObservable<Response>();
            } 
          }
        })
        .subscribe(data => {
          localStorage.setItem("role",data['user_info'].roles[0].name);
          localStorage.setItem("fname",data['user_info'].first_name);
          localStorage.setItem("lname",data['user_info'].last_name);
          localStorage.setItem("username",data['user_info'].user_name);
          localStorage.setItem("userId",data['user_info'].id);
          localStorage.setItem("email",data['user_info'].email);
          if(data['user_info'].profile_picture){
            localStorage.setItem("pic",data['user_info'].profile_picture);
          }
          else {
            localStorage.setItem("pic","none");
          }
          this.username = data['user_info'].user_name;
          this.generalService.notify(
            'roleChanged' , 'membership','contact' , 
            {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
          );
          this.loader = false;
          this.router.navigate(['u/',this.username,'resumes']);    
        });

        // this.authService.loginWithFacebook(userData).subscribe(data => {
        //   localStorage.setItem('access_token',data['token'].access_token);
        //   this.router.navigate(['user/resumes']);
        // });
      }
    );
  }

  // old sign up with google
  // public signinWithGoogle () {
  //   this.loader = true;

  //   this.authService.logoutIfAlreadyLoggedin();
    
  //   let socialPlatformProvider = GoogleLoginProvider.PROVIDER_ID;

  //   this.socialAuthService.signIn(socialPlatformProvider)
  //     .then((userData) => {
  //       this.authService.loginWithGoogle(userData).switchMap(
  //         data => {
  //           if(data['success']){
  //             localStorage.setItem('access_token',data['token'].access_token);
  //             return this.authService.getUserInfo();
  //           }
  //           else{
  //             this.loader = false;
  //             if(data['error']=== "There is no permission with Job Seeker."){
  //               this.alertResetMessage = Errors.loginWithWrongRule;
  //             }
  //             return new EmptyObservable<Response>();
  //           } 
  //       })
  //         .subscribe(data => {
  //           localStorage.setItem("role",data['user_info'].roles[0].name);
  //           localStorage.setItem("fname",data['user_info'].first_name);
  //           localStorage.setItem("username",data['user_info'].user_name);
  //           localStorage.setItem("userId",data['user_info'].id);
  //           if(data['user_info'].profile_picture){
  //             localStorage.setItem("pic",data['user_info'].profile_picture);
  //           }
  //           else {
  //             localStorage.setItem("pic","none");
  //           }
  //           this.username = data['user_info'].user_name;
  //           this.generalService.notify(
  //             'roleChanged' , 'membership','contact' , 
  //             {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
  //           );
  //           this.loader = false;
  //           this.router.navigate(['u/',this.username,'resumes']);
  //         });
  //     });
  // }


  // new sign up with google
  initializeGoogleLogin(){
    const SCRIPT_PATH = 'https://accounts.google.com/gsi/client';
    const scriptElement = this.scriptService.loadJsScript(this.renderer, SCRIPT_PATH);
    scriptElement.onload = () => {
      // @ts-ignore
      google.accounts.id.initialize({
        client_id: environment.clientId,
        callback: this.handleGoogleLoginCredentialResponse.bind(this),
        auto_select: false,
        cancel_on_tap_outside: true
      });
      // @ts-ignore
      google.accounts.id.renderButton(
      // @ts-ignore
      document.getElementById("googleLoginButtonDiv"),
        {theme: "outline", size: "medium" , text:"signup_with", type:"standard",locale:"en-us"}
      );
      // @ts-ignore
      // google.accounts.id.prompt((notification: PromptMomentNotification) => {});
    };
  }

  async handleGoogleLoginCredentialResponse(response: CredentialResponse) {
    this.ngZone.run( () => {
      this.loader = true;
        let idToken = {
          "idToken":response.credential
        }

        this.authService.loginWithGoogle(idToken).switchMap(
          data => {
            if(data['success']){
              localStorage.setItem('access_token',data['token'].access_token);
              return this.authService.getUserInfo();
            }
            else{
              this.loader = false;
              if(data['error']){
                this.resetHelpLink();
                this.alertResetMessage = data['error'];
                if(data['help'])
                  this.helpLink = data['help'];
              // if(data['error']=== "There is no permission with Job Seeker."){
              //   this.alertResetMessage = Errors.loginWithWrongRule;
              // }
              return new EmptyObservable<Response>();
            } 
          }
        })
          .subscribe(data => {
            localStorage.setItem("role",data['user_info'].roles[0].name);
            localStorage.setItem("fname",data['user_info'].first_name);
            localStorage.setItem("lname",data['user_info'].last_name);
            localStorage.setItem("username",data['user_info'].user_name);
            localStorage.setItem("userId",data['user_info'].id);
            localStorage.setItem("email",data['user_info'].email);
            if(data['user_info'].profile_picture){
              localStorage.setItem("pic",data['user_info'].profile_picture);
            }
            else {
              localStorage.setItem("pic","none");
            }
            this.username = data['user_info'].user_name;
            this.generalService.notify(
              'roleChanged' , 'membership','contact' , 
              {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
            );
            this.loader = false;
            this.router.navigate(['u/',this.username,'resumes']);
          });

    });
  }

  public signout () {
    this.socialAuthService.signOut();
  }

  get password() {
    return this.signUpForm.get('password');
  }

  toggleShowPassword() {
    this.show = !this.show;
    if (this.show) {
        this.type = 'text';
    } else {
        this.type = 'password';
    }
  }

  resetHelpLink(){
    this.helpLink = '';
  }
}
