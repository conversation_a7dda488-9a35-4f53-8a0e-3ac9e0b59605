import { Component, OnInit } from '@angular/core';
import { HelpService } from 'app/general/services/help.service';
import { Language } from 'app/admin/models/language';
import { LanguageService } from 'app/admin/services/language.service';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { GeneralService } from '../../services/general.service';
import { Title, Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-help-search',
  templateUrl: './help-search.component.html',
  styleUrls: ['./help-search.component.css']
})
export class HelpSearchComponent implements OnInit {
  searchQuery = '';
  currentLangId: number;
  languagesArray: Language[] = [];

  mainCategories: {
    'id': number, 'name': string, 'langId': number,
    'sub_cats': { 'id': number, 'name': string, 'main_cat_id': number, }[],}[][] = [];
  subCategories: { 'id': number, 'name': string, 'main_cat_id': number, 'main_name': '' }[][][] = [];
  helpTopics: { 'id': number, 'title': string, 'slug': string, 'main_cat_id': number, 'sub_cat_id': number }[][] = [];
  filteredMainCategories: {
    'id': number, 'name': string, 'langId': number,
    'sub_cats': { 'id': number, 'name': string, 'main_cat_id': number }[]
  }[][] = [];
  filteredSubCategories: { 'id': number, 'name': string, 'main_cat_id': number }[][][] = [];
  filteredHelpTopics: { 'id': number, 'title': string, 'slug': string, 'main_cat_id': number, 'sub_cat_id': number }[][] = [];
  fSCIsEmpty: boolean;
  private ngUnsubscribe: Subject<any> = new Subject();
  
  constructor(private helpService: HelpService,
              private languageService: LanguageService,
              private route: ActivatedRoute,
              private generalService: GeneralService,
              private router: Router,
              private title:Title,
              private meta:Meta
    ) { }

  ngOnInit(): void {
    this.title.setTitle('CVeek Website  سيفيك | Help Center | Search Results');
    this.meta.updateTag({ name: 'description', content: 'CVeek | Help Center | Search Results'});
    this.route.params.subscribe(res => {
      this.searchQuery = res['query'];
    });

    if (localStorage.getItem('defaultLang')) {
      this.currentLangId = +localStorage.getItem('defaultLang');
    } else {
      this.currentLangId = 1;
    }

    this.getLanguages();

    this.generalService.internalMessage.subscribe((data) => {
      if (data['src'] === 'help-header' && data['dist'] === 'help-search' && data['message'] === 'inHelpSearchInterface') {
        this.searchQuery = data['mData'].searchQuery;
        this.search(this.searchQuery);
      }
    });


  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
      let temp = res['data'];
      for (let lang of temp) {
        if (lang.id == 1)
          this.languagesArray.push({
            'id': lang.id,
            'name': lang.name
          });

      }
      this.getHelpData();
    });

  }

  getHelpData() {
    for (let i = 0; i < this.languagesArray.length; i++) {
      this.helpService.getData(i + 1).subscribe(res => {

        let temp = res['categories'];
        this.mainCategories[i] = [];
        this.filteredMainCategories[i] = [];
        this.subCategories[i] = [];
        this.filteredSubCategories[i] = [];
        this.helpTopics[i] = [];
        this.filteredHelpTopics[i] = [];
        for (let j = 0; j < temp.length; j++) {
          let temp2 = temp[j].help_center_sub_cat;
          let main_name = temp[j].help_center_main_cat__trans[0].name;
          this.subCategories[i][j] = [];
          this.filteredSubCategories[i][j] = [];
          // this.helpTopics[i][j]     = [];
          for (let k = 0; k < temp2.length; k++) {
            this.subCategories[i][j].push({
              'id': temp2[k].id,
              'name': temp2[k].help_center_sub_cat__trans[0].name,
              'main_cat_id': temp2[k].help_center_main_cat_id,
              'main_name': main_name
            });


          }

          let temp3 = temp[j].help_center;
          // this.helpTopics[i][j] = [];
          for (let m = 0; m < temp3.length; m++) {
            this.helpTopics[i].push({
              'id': temp3[m].id,
              'main_cat_id': temp3[m].help_center_main_cat_id,
              'sub_cat_id': temp3[m].help_center_sub_cat_id,
              'title': temp3[m].help_center_trans[0].title,
              'slug': this.validSlug(temp3[m].help_center_trans[0].slug),
            });
          }

          this.mainCategories[i].push({
            'id': temp[j].id,
            'name': temp[j].help_center_main_cat__trans[0].name,
            'langId': temp[j].help_center_main_cat__trans[0].translated_languages_id,
            'sub_cats': this.subCategories[i][j],
          });
          this.filteredMainCategories[i].push({
            'id': temp[j].id,
            'name': temp[j].help_center_main_cat__trans[0].name,
            'langId': temp[j].help_center_main_cat__trans[0].translated_languages_id,
            'sub_cats': this.subCategories[i][j]
          });

        }
        this.search(this.searchQuery);
      });
    } 
    
  }

  search(query) {
    this.fSCIsEmpty = false;
    this.searchQuery = query;

    this.filteredMainCategories[this.currentLangId - 1] = (query) ? this.mainCategories[this.currentLangId - 1]
      .filter(mainCat => mainCat.name.toLowerCase().includes(query.toLowerCase())) : this.mainCategories[this.currentLangId - 1];
 

    for (let i = 0; i < this.mainCategories[this.currentLangId - 1].length; i++) {
      this.filteredSubCategories[this.currentLangId - 1][i] = (query) ? this.subCategories[this.currentLangId - 1][i]
        .filter(subCat => subCat.name.toLowerCase().includes(query.toLowerCase())) : this.subCategories[this.currentLangId - 1][i];
    }


    for (let i = 0; i < this.helpTopics[this.currentLangId - 1].length; i++) {
      this.filteredHelpTopics[this.currentLangId - 1] = (query) ? this.helpTopics[this.currentLangId - 1]
        .filter(hTopic => hTopic.title.toLowerCase().includes(query.toLowerCase())) : this.helpTopics[this.currentLangId - 1];
    }

    // calculate sub re length
    let i = 0;
    for (let k of this.filteredSubCategories[this.currentLangId - 1]) {
      if (k.length === 0) {
        i++;
      }
    }
    if (i === this.filteredSubCategories[this.currentLangId - 1].length) {
      this.fSCIsEmpty = true;
    }
    //////
  }

  validSlug(slug: string) {
    let validSlug: string = '';
    for (let i = 0; i < slug.length; i++) {
      if (slug[i] === ' ') {
        validSlug = validSlug.concat('-');
      } else {
        validSlug = validSlug.concat(slug[i]);
      }

    }

    return validSlug;
  }

  getMainCatName(id) {
    for (let main of this.mainCategories[this.currentLangId - 1]) {
      if (main.id === id) {
        return main.name;
      }
    }
  }

  getSubCatName(id, main_id) {
    for (let main of this.mainCategories[this.currentLangId - 1]) {
      for (let sub of main.sub_cats) {
        if (sub.id === id && sub.main_cat_id === main_id) {
          return sub.name;
        }
      }

    }
  }

  friendlyUrl(text){
    text = text.toLowerCase();
    text = text.replace(/ /g,'-');
    return text;
  }

  navigate(type,mainCatName,mainCatId?,subCatName?,subCatId?,helpId?,helpSlug?){
    if(type==='main')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) , mainCatId ]);
    else if(type==='sub')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) , mainCatId , 's' ,  this.friendlyUrl(subCatName) , subCatId]);
    else if(type==='slug')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) ,  this.friendlyUrl(subCatName) , helpId, this.friendlyUrl(helpSlug) ]);
  }

}
