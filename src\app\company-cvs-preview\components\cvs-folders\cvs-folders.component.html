<p-toast position="bottom-right" [style]="{marginTop: '100px'}"></p-toast>

<a class="folders-heading" [routerLink]="['/c', 'cvs-folders', username]">
    <span class="my-folders-title">My Folders</span>
    <span class="add-folder-btn">+</span>
</a>
    
<p-tree 
    [value]="folders" 
    selectionMode="single" 
    [(selection)]="selectedFolder" 
    [filter]="true"
    [emptyMessage]="'No folders found'"
    [loading]="loading"
    (onNodeSelect)="navigateToCvsFolders($event)"
    >
    <ng-template let-node  pTemplate="default">
        <div 
            class="folder-label-div"
            cdkDropList
            (cdkDropListDropped)="dropCV($event)"
            id="cdkDropList-{{node.key}}"
        >
            <span class="name">
                {{node.label}}
            </span> 
            <span class="count">
                {{node.data.count}}
            </span>
            
        </div>
    </ng-template>
</p-tree>