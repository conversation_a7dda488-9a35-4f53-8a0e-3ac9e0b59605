import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {Form<PERSON>rray, FormBuilder, FormGroup, Validators,} from '@angular/forms';
import 'rxjs/add/operator/debounceTime';
import {Subscription} from 'rxjs/Subscription';
import 'rxjs/add/operator/distinctUntilChanged';
import { Question } from "app/company/components/google-form/Models/question";
import { SharedService } from "app/company/components/google-form/form-services/shared.service";
import { QuestionService } from "app/company/components/google-form/form-services/question.service";
import { Answer } from "app/company/components/google-form/Models/answer";
import { FormJobService } from "app/company/components/google-form/form-services/form-job.service";

@Component({
  selector: 'form-customer',

  templateUrl: './form-customer.component.html',
  styleUrls: ['./form-customer.component.css']
})
export class FormCustomerComponent implements  OnInit,On<PERSON><PERSON>roy {

  public myForm: FormGroup;
  _objRef: any;
  order: number;
  quest: Question;
  quest_id: number;
  form_id:number;
  quest_status: boolean ;
  list: string[] = ['dropDown', 'Multiple Choice', 'Checkboxes', 'Short Answer', 'Paragraph', 'Date', 'Time'];
  isFocused: boolean = false;
  isSaved:string='';
  Active:boolean=false;
  subscription:Subscription;


  constructor(private _fb: FormBuilder,
              private data: SharedService,
              private questionService: QuestionService,
              private formJobService : FormJobService
              ) {}

  ngOnInit(){
    this.subscription=this.myForm.valueChanges
      .debounceTime(1100)
      .subscribe((data)=>{
        if(this.myForm.get('label').value == ''){
          this.myForm.get('label').setValue('Question');
        }
        this.updateQuestion(this.myForm.value);
      });

  }


  buildForm(answerSetter: boolean){
    this.myForm = this._fb.group({
      label: [this.quest.label, [Validators.required]],
      answers: this._fb.array(
        []
      ),
      controlType: this.quest.controlType,
      isRequired: this.quest.isRequired,
      order: this.order,
    });
    if(!answerSetter) {

      this.answers.push(this.AddAnswerOption());
    }else {

      for (let answer of this.quest.choices){
        this.answers.push( this._fb.control(answer.label, Validators.required));
      }
    }
  }

  addQuestion(question?: Question ) {
    if ((question === undefined) || (question === null)) {
      this.quest = new Question(this.order,this.form_id);
     
      this.buildForm(false);
      this.formJobService.addQuestion(this.quest).subscribe(res => {
       
        let lengthQues = res['data']['question'].length;
       
        this.quest_id = res['data']['question'][lengthQues - 1].id;
       
      });
    }
    else {
      this.quest = question;
      this.quest_id = question.id;
      this.form_id=question.form_id;
      this.quest_status= true;
      this.buildForm(this.quest_status);
    }
  }

  updateQuestion(value: any) {
    let newAnswers: Answer[] = [];
    for (let answer of value.answers) {
      newAnswers.push(new Answer(answer));
    }

    const body: { [k: string]: any } = {
        ['label']: value.label,
        ['controlType']: value.controlType,
        ['isRequired']: value.isRequired,
        ['order']: this.order,
        ['form_id']:this.form_id,
        ['choices']:null
      };
    if (newAnswers.length !== 0) {
      body.choices = newAnswers;
    }
    this.isSaved='Saving ......';
    this.formJobService.updateQuestion(body, this.quest_id).subscribe(val => this.isSaved= 'Data is Saved');
  }


  removeQuestion() {
    if (confirm('Are You sure to delete ?')) {
      this.formJobService.deleteQuestion(this.quest_id).subscribe(data => {
         console.log('data removed', data);
         this.data.changeOrder(this._objRef);
         this._objRef.destroy();
      });
    }
  }


  AddAnswerOption() {
    return this._fb.control('Option', Validators.required);
  }

  addAnswer() {
    this.isFocused = true;
    this.answers.push(this.AddAnswerOption());
    }

  removeAnswer(i) {
    this.myForm.markAsDirty();
    this.answers.removeAt(i);

  }

  clearAnswers(event) {
    const controlType = event.target.value;
    if (controlType === ('Short Answer') || (controlType === 'Paragraph') || (controlType ===  'Date') || (controlType === 'Time'))
    {
      while (this.answers.length !== 0)
        this.answers.removeAt(0);
    }
    else {
      if (this.answers.length === 0) this.addAnswer();
    }

  }


  makeActive(){
    if(this.Active!==true) {
      this.Active = !this.Active;
      this.data.changeActiveClass(this._objRef);
    }
  }


  get answers() {
    return this.myForm.get('answers') as FormArray;
  }

  get controlType(): string {
    return this.myForm ? this.myForm.get('controlType').value : 'no value selected ';
  }

  handleEnterKeyPress(event) {
    const tagName = event.target.tagName.toLowerCase();
    if (tagName === 'input') {
      return false;
    }
  }

  ngOnDestroy(){
    this.subscription.unsubscribe();

  }


}
