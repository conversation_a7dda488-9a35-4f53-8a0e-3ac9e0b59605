import { Component, OnInit, NgModuleFactory } from '@angular/core';
/* import {DynamicClass, DynamicArrayClass} from "app/company/components/dynamically_filters"; */
import { Observable } from "rxjs/Rx";
import { HttpClient } from "@angular/common/http";
import { ActivatedRoute } from '@angular/router';
/* import * as company from "app/company/company.module"; */
/* import('app/company/company.module').then(
  module => {
console.log(module);
    return module.CompanyModule;
  }
).then(mod => {
console.log(mod);
}); */
@Component({
  selector: 'app-company-settings',
  templateUrl: './company-settings.component.html',
  styleUrls: ['./company-settings.component.css']
})
export class CompanySettingsComponent implements OnInit {
t ;
e;
arr;
test;

username = '';
private baseUrl = 'http://cre-s.com/backend-test/api/post';
  constructor(
    private route: ActivatedRoute,
    private http?: HttpClient
  ) {

    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });

   
    /* this.t = new DynamicClass('personal_Info', 'personal');
    console.log(`Type of object \'personal_Info\': ${this.t.constructor['name']}`);
    this.e = new DynamicClass('education_filter', 'education');
    console.log(`Type of object \'education_filter\': ${this.e.constructor['name']}`);
    this.arr = new DynamicClass('Blog','Blog');
    console.log(`Type of object \'Blog\': ${this.t.constructor['name']}`); */
   /*  this.test = new DynamicClass('Global'); */
   }

   print() {
    //  console.log('dooonnnneee!')
   }

   getData(type){
    let data ;
       return this.http.get(this.baseUrl)
  }

  /* data_type(type,data){
    let res;
    res = data.map((response) =>{
      return new DynamicClass(type,response);
     
    })

    return res;
  } */
 
  
  

  ngOnInit() {
 
    /* console.log(this.test = new DynamicClass('Global','CompanySettingsComponent'))
    console.log(this.test.print())
    let data;
    this.getData('Blog').subscribe(
      (res) => {
        data = this.data_type('Blog',res)

        console.log('data',data)
      }) */
      
    
  }

}
