import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, Predicate } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validator, Validators, AbstractControl } from '@angular/forms';
import { MultiSelectComponent } from '@syncfusion/ej2-angular-dropdowns';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import 'rxjs/add/operator/takeUntil';
@Component({
  selector: 'app-company-verification',
  templateUrl: './company-verification.component.html',
  styleUrls: ['./company-verification.component.css']
})
export class CompanyVerificationComponent implements OnInit {
  companyVerfication;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    this.companyVerfication = this.fb.group({
      id: [''],
      company_name: [''],
      company_industry: [''],
      company_type: [''],
      status: [''],
      country: ['']
      });
    }

  ngOnInit() {
  }

}
