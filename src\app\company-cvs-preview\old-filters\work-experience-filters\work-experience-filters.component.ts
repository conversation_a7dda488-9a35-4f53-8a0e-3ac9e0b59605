import { Component, DoCheck, Input, OnInit } from '@angular/core';
import { WorkExperienceFiltersDataModel } from './WorkExperienceFiltersDataModel';
import { KeyValue<PERSON><PERSON><PERSON>, KeyValueDiffer, KeyValueDiffers } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
declare var $: any;
@Component({
  selector: 'app-work-experience-filters',
  templateUrl: './work-experience-filters.component.html',
  styleUrls: ['./work-experience-filters.component.scss']
})
export class WorkExperienceFiltersComponent implements OnInit {
  dataModel: WorkExperienceFiltersDataModel;
  @Input() filterData ;
  private dataModelDiffer: KeyValueDiffer<string, any>;

  constructor(private differs: KeyValueDiffers , private  generalService: GeneralService) {
    this.dataModel  = new  WorkExperienceFiltersDataModel() ;
   }

  ngOnInit(): void {
    // this.dataModelDiffer = this.differs.find(this.dataModel).create();
    this.sendInitStateToWarapper();
  }


  filterArray(event , arr) {
    this.dataModel[arr] = this.filterData[arr].filter(e => e.name.toLowerCase() === event.query.toLowerCase() );
  }



// ngDoCheck(): void {
//   const changes = this.dataModelDiffer.diff(this.dataModel);
//   if (changes) {
//     this.dataModel.setFilters();
//     //  *  here  we set src of   this.generalService.notify equals to 'education' which it's not
//     // the same of component name  "education-filters" please see filters-wrapper component
//     this.generalService.notify('filters-changes',
//       'language', 'filters-wrapper', this.dataModel.filters);
//   }
// }

sendFilters() {
  this.dataModel.setFilters();
  this.generalService.notify('filters-changes',
   'work_experience', 'filters-wrapper', this.dataModel.filters);
   $('#filtersModal').modal('hide');
}

sendInitStateToWarapper() {
  this.dataModel.setFilters();
  this.generalService.notify('init-filters',
    'work_experience', 'filters-wrapper', this.dataModel.filters);
}

hideModal() {
  $('#filtersModal').modal('hide');
}

}
