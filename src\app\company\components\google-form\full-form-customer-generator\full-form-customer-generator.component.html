<head>
  <meta charset="UTF-8">
  <title>company form</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
</head>

<div class="modal-body-container">
    <div class="modal-body">
      <form #form="ngForm" (keydown.enter)="$event.preventDefault()" [formGroup]="postForm" class="form-horizontal validate-form">
        <div class="material-form-wrapper">
          <div class="top-banner">
          </div>

          <button class="btn-success add-form" (click)="addForm(form)">

              <span>Add From</span>
          </button>

          <button [disabled] = "formAdded" class="btn-success add-question" (click)="addComponent()">
            <!-- <i class="fa fa-plus" aria-hidden="true"></i> -->
            <span>Add Question</span>
          </button>
        
            <div class="material-form-body">
                
              <div class="container-fluid">
                <div class="form-section header-form-section" [class.active]="Active" [class.inactive]="!Active" (click)="makeActive()">

                    <div class="form-group row">
                      <div class="col-sm-12">
                        <div class="material-group form-title-material-group" [ngClass]="{'has-error': form.submitted && !postForm.controls['header'].value }">
                          <input formControlName ="header" type="text" class="form-control" id="form-title" placeholder="Form Title" value="Untitled Form">
                          <span class="highlight"></span>
                          <span class="bar"></span>
                          <span *ngIf="form.submitted && !postForm.controls['header'].value" class="glyphicon form-control-feedback glyphicon-remove" aria-hidden="true"></span>
                          <span *ngIf="form.submitted && !postForm.controls['header'].value" class="error-message" translate>validationMessages.required</span>
                        </div>
                      </div>
                      <div class="col-sm-12">
                        <div class="material-group" [ngClass]="{'has-error': form.submitted && !postForm.controls['description'].value }">
                          <input formControlName ="description" type="text" class="form-control" id="form-desc" placeholder="Form Description">
                          <span class="highlight"></span>
                          <span class="bar"></span>
                          <span *ngIf="form.submitted && !postForm.controls['description'].value" class="glyphicon form-control-feedback glyphicon-remove" aria-hidden="true"></span>
                          <span *ngIf="form.submitted && !postForm.controls['description'].value" class="error-message" translate>validationMessages.required</span>
                        </div>
                      </div>
                    </div>
                </div>
                <ng-template #dynamicInsert></ng-template>
                </div>
            </div>
       
        </div>
      </form>
    </div>
</div>
<!-- <p> {{ form.value | json }} </p> -->






































<!--<ng-template #dynamicInsert></ng-template>-->

<!--<button class="plus-q  btn btn-primary fixed-top"-->

        <!--title="Add Question"-->
        <!--(click)="addComponent()">-->
  <!--<i class="fa fa-plus"></i>-->
<!--</button>-->




