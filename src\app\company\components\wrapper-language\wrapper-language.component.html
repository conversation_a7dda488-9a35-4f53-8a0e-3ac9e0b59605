<div class="modal-body-container">
    <div class="modal-body">
        <form *ngIf="translated_language_temp.length" class="form-horizontal" #form="ngForm" [formGroup]="languageForm" (keydown.enter)="$event.preventDefault()" (ngSubmit)="form.valid && submit()">
            <div class="row">
                <div class="col-sm-12">
                    <div class="custom-row flex-row focus-container  has-feedback">
                        <!-- [ngClass]="{'has-error':form.submitted && !isDDValid(languageControl)}" -->
                        <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                        </div>
                        <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding" style="
                        margin-bottom: 15px;">
                            <!-- [ngClass]="{'has-val':resumeForm.controls['translated_languages_id'].value}" -->
                            <p-dropdown [options]="translated_language_temp" optionLabel="name" formControlName="translated_languages_id" [ngClass]="{'has-val':languageForm.controls['translated_languages_id'].value}">
                                <ng-template let-lang pTemplate="item">
                                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                        <div style="font-size:14px;float:left;margin-top:4px">{{lang.label}}</div>
                                    </div>
                                </ng-template>
                            </p-dropdown>
                            <span class="custom-underline"></span>
                            <!-- <span class="glyphicon  form-control-feedback" aria-hidden="true"></span> -->
                            <label class="control-label custom-control-label">Language</label>
                        </div>
                    </div>
                    <div class="form-group div-margin-top-70">
                        <div class="col-sm-12">
                            <div class="row">
                                <div class="col-sm-12 text-center">
                                    <button class="btn btn-success cust-btn">
                                        <i class="fa fa-floppy-o"></i> Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>