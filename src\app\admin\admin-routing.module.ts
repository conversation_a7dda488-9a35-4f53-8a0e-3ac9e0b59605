import { ManageNewValueComponent } from './components/new-values/manage-new-value/manage-new-value.component';
import { NewValueModalComponent } from './components/new-values/new-value-modal/new-value-modal.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AdminFaqsComponent } from './components/faqs/admin-faqs/admin-faqs.component';
import { AdminComponent } from './admin.component';
import { EditModalComponent } from './components/faqs/edit-modal/edit-modal.component';
import { AdminHelpTipsComponent } from './components/help/help-tips/admin-help-tips/admin-help-tips.component';
import { AdminHelpTopicsComponent } from './components/help/help-topics/admin-help-topics/admin-help-topics.component';
import { EditTopicModalComponent } from './components/help/help-topics/edit-topic-modal/edit-topic-modal.component';
import { EditTipModalComponent } from './components/help/help-tips/edit-tip-modal/edit-tip-modal.component';
import { ManageComponent } from './components/verification/manage/manage.component';
import { LogComponent } from './components/verification/log/log.component';
import { MessagesComponent } from './components/contact/messages/messages.component';
import { MessageArchiveComponent } from './components/contact/message-archive/message-archive.component';
import { MessageTemplatesComponent } from './components/contact/message-templates/message-templates.component';
import { ManageCompanyComponent } from './components/company-verification/manage-company/manage-company.component';
import { ManageArtComponent } from './components/articles/manage-art/manage-art.component';
import { AdminGuardService } from 'shared/shared-services/admin-guard.service';
import { ManageAdvsComponent } from './components/manage-advs/manage-advs.component';
import { RoleGuardService } from 'shared/shared-services/role-guard.service';
import { Roles } from 'app/Enum/Roles';

const routes: Routes = [
    {
        path: '', component: AdminComponent, children: [
            {
                path: 'faq/new', component: EditModalComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'faqs', component: AdminFaqsComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'help-topic/new', component: EditTopicModalComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'help-topics', component: AdminHelpTopicsComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'help-tip/new', component: EditTipModalComponent,
                canActivate: [AdminGuardService]
            },
            { path: 'help-tips', component: AdminHelpTipsComponent },

            {
                path: 'manage-values-verification', component: ManageComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'actions-log', component: LogComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'messages', component: MessagesComponent,
                canActivate: [RoleGuardService],
                data: { expectedRoles: [Roles.ROLE_ADMIN, Roles.ROLE_CONTACT_ADMIN] }

            },
            {
                path: 'message-archive', component: MessageArchiveComponent,
                canActivate: [RoleGuardService],
                data: { expectedRoles: [Roles.ROLE_ADMIN, Roles.ROLE_CONTACT_ADMIN] }

            },
            {
                path: 'message-templates', component: MessageTemplatesComponent,
                canActivate: [RoleGuardService],
                data: { expectedRoles: [Roles.ROLE_ADMIN, Roles.ROLE_CONTACT_ADMIN] }

            },
            {
                path: 'company-verification', component: ManageCompanyComponent,
                canActivate: [AdminGuardService]

            },
            {
                path: 'articles', component: ManageArtComponent,
                canActivate: [AdminGuardService]

            },
            {
                path: 'new-value/:type', component: NewValueModalComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'manage-values/:type', component: ManageNewValueComponent,
                canActivate: [AdminGuardService]
            },
            {
                path: 'manage-active-advs', component: ManageAdvsComponent, data: { status: 'active-advs' },

                canActivate: [AdminGuardService]
            },
            {
                path: 'manage-ended-advs', component: ManageAdvsComponent, data: { status: 'ended-advs' },

                canActivate: [AdminGuardService]
            },

        ]
        // , canActivateChild: [AdminGuardService] 
    },
];


@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AdminRoutingModule {

}
