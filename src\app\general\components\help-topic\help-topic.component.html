<app-help-header></app-help-header>
<app-pre-loader [show]="helpTopicTrans.length === 0"></app-pre-loader>
<div style="min-height:400px;">

<div *ngIf="helpTopicTrans.length !== 0" class="header">
  
  <!-- row content-row -->
  <div class="container-padding" *ngIf="helpTopicTrans.length !== 0">
      <!-- class="col-md-10 col-sm-10 col-md-offset-1 col-sm-offset-1 col-xs-12 cat-col" -->
    <div>
      <div class="card">
        <div>
          <ol class="breadcrumb"
            *ngIf="helpTopicTrans.length !== 0 &&  helpTopic.main_cat.length !== 0 && helpTopic.sub_cat.length !== 0">
            <li class="badge badge-primary category-badge"><a [routerLink]="['/i/' + '/help']"><i class="fa fa-home"
                  title="home page of help"></i></a></li>
            <li class="badge badge-primary category-badge">
                <!-- [routerLink]="['/i/' + '/help/' + helpTopic.main_cat[currentLangId -1].name + '/' + helpTopic.main_cat[currentLangId -1].id]" -->
              <a
                (click)="navigate('main',helpTopic.main_cat[currentLangId -1].name ,helpTopic.main_cat[currentLangId -1].id)"
                >
                {{helpTopic.main_cat[currentLangId -1].name }}
              </a>
            </li>
            <li *ngIf="helpTopic.sub_cat.length !== 0" class="badge badge-primary category-badge">
                <!-- [routerLink]="['/i/' + '/help/' + helpTopic.main_cat[currentLangId -1].name + '/' + helpTopic.main_cat[currentLangId -1].id + '/s/' +  helpTopic.sub_cat[currentLangId - 1].name + '/' + helpTopic.sub_cat[currentLangId - 1].id]" -->
              <a
                (click)="navigate('sub',helpTopic.main_cat[currentLangId -1].name,helpTopic.main_cat[currentLangId -1].id, helpTopic.sub_cat[currentLangId - 1].name, helpTopic.sub_cat[currentLangId - 1].id)"
              >
                {{ helpTopic.sub_cat[currentLangId - 1].name }}
              </a></li>
            <li class="badge badge-primary category-badge" *ngIf="helpTopicTrans.length !== 0">
              {{helpTopicTrans[currentLangId - 1].title}}
            </li>
          </ol>
        </div>
        <h1 class="helptopic-title">{{helpTopicTrans[currentLangId - 1].title}}</h1>
        <!-- <h2 class="badge badge-primary category-badge">{{ helpTopicTrans[currentLangId - 1].title }}</h2> -->
        <div class="description">
            <div [innerHTML]="helpTopicTrans[currentLangId -1].description" class="editor-description"></div>
          <!-- <div [innerHTML]="sanitizer.bypassSecurityTrustHtml(helpTopicTrans[currentLangId -1].description)" class="editor-description"></div> -->
        </div>
      </div>
    </div>
  </div>
</div>

</div>


