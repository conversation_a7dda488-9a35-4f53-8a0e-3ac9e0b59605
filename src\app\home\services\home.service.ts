import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { PublicPreviewService } from '../../company/services/public-preview.service';

@Injectable({
  providedIn: 'root'
})
export class HomeService {

  url = '';
  constructor(
    private http:HttpClient, 
    private privateSharedURL: ExportUrlService,
    private publicPreviewService:PublicPreviewService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data=>{
      this.url = data ;
    });
  }

  getCompanies() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url +'companies', {headers});
  }

  applyToCompany(companyId:number){
    return this.publicPreviewService.applyToCompany(companyId);
  }
}
