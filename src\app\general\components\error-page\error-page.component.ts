import { Component, OnInit } from '@angular/core';
import { GeneralService } from '../../services/general.service';

@Component({
  selector: 'app-error-page',
  templateUrl: './error-page.component.html',
  styleUrls: ['./error-page.component.css']
})
export class ErrorPageComponent implements OnInit {

  constructor(private generalService:GeneralService) { }

  ngOnInit(): void {
  }

  displayContactModal() {
    this.generalService.notify('display Contact Modal' , 'error-page' , 'contact-us' , {'displayContactModal':true}) ;
  }

}
