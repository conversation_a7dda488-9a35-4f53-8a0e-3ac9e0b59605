import { Component, EventEmitter, Input, NgZone, OnDestroy, OnInit, Output, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import {Subject} from 'rxjs/Subject';
import {AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {WorkExperiencesService} from '../../cv-services/work-experiences.service';
import {EducationValidators} from '../education/education.validators';
import 'rxjs/add/operator/takeUntil';
import { DataMap } from 'shared/Models/data_map';
import { ExperienceField } from '../../Models/ExperienceField';
import { WorkExperienceValidators } from '../work-experience/work-experience.validators';
import { HelpTipsService } from '../../cv-services/help-tips.service';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";
import { UrlValidator } from 'shared/validators/url.validators';
// import { } from 'googlemaps';
//  //  import { google } from '@agm/core/services/google-maps-types';
declare var $: any;

@Component({
  selector: 'app-work-experience-modal',
  templateUrl: './work-experience-modal.component.html',
  styleUrls: ['./work-experience-modal.component.css']
})
export class WorkExperienceModalComponent implements OnInit  , OnDestroy, AfterViewInit {
  locationService = new DataMap();
  workExperienceForm ;
  toYearOpts = [];
  fromYearOpts = [];
  fromDate;
  toDate;
  currentYear = (new Date()).getFullYear();
  monthOpts = [
      {'value': '0', 'label': 'MM'},
      {'value': '1', 'label': 'January'},
      {'value': '2', 'label': 'February'},
      {'value': '3', 'label': 'March'},
      {'value': '4', 'label': 'April'},
      {'value': '5', 'label': 'May'},
      {'value': '6', 'label': 'June'},
      {'value': '7', 'label': 'July'},
      {'value': '8', 'label': 'August'},
      {'value': '9', 'label': 'September'},
      {'value': '10', 'label': 'October'},
      {'value': '11', 'label': 'November'},
      {'value': '12', 'label': 'December'}
    ];

  companySizeValues = [
    {'value': '', 'label': ''},
    {'value': '1to9employees', 'label': 'workExperience.1to9employees'},
    {'value': '10to49employees', 'label': 'workExperience.10to49employees'},
    {'value': '50to99employees', 'label': 'workExperience.50to99employees'},
    {'value': '100to499employees', 'label': 'workExperience.100to499employees'},
    {'value': '500employeesOrMore', 'label': 'workExperience.500employeesOrMore'},
  ];
  googleVerify = true;
  extraChecked = false;
  externalInfoVisible = false;
  companyIndustries = [];
  
  employmentTypes = [];
//  expFieldsValues: ExperienceField[]=[];
  // @Input('companyIndustriesData') companyIndustriesData ;
  @Input('employmentTypesData') employmentTypesData ;
  @Input('companySizeData') companySizeData ;
  @Input('expFields') expFields;
  @Input('resumeLang') resumeLang: number;
  // @Input('jobTitles') jobTitles;
  @Input("helpTips") helpTips ;
  private ngUnsubscribe: Subject<any> = new Subject();
  resumeIdField: number;
  workExperienceFormData;
  @Input('workExperience_item') workExperience_item: any;
  @Output() closeModalPopup = new EventEmitter();
  @ViewChild('googlePlace') googlePlace: any;
  @ViewChild('googlePlace') public googlePlaceRef: ElementRef;
  @ViewChild('googleLocation') googleLocation: any;
  @ViewChild('googleLocation') public googleLocationRef: ElementRef;
  city: string;
  country: string;
  countryCode: string;
  website: string;
  name: string;
  inputType: string;
  // expFields: ExperienceField[]=[];
  // jobTitles = [];
 // filteredJobTitles: any[] = [];
  employmentTypesForm = {
    1: [''],
    2: [''],
    3: [''],
    4: [''],
    5: [''],
    6: [''],
    7: [''],
    8: [''],
    9: [''],
    10: [''],
    11: [''],
    12: [''],
    13: [''],
    14: [''],
    15: [''],
    16: [''],
    17: [''],
    18: [''],
    19: [''],
    20: ['']
  };
  displayError = false;
  errorMsg = '';
  checkVerify = true;
  // checkVerify = null;
  jobTitleDD:any;

  constructor(private  fb: FormBuilder,
              private workExperiencesService: WorkExperiencesService,
              private helpTipService:HelpTipsService,
              private lazyloadDropdownService:LazyloadDropdownService) {
    this.fromYearOpts.push({'value' : '' , 'label' : 'YYYY'});
    this.toYearOpts.push({'value' : '' , 'label' : 'YYYY'});
    this.toYearOpts.push({'value' : 'Present' , 'label' : 'shared.present'});
    for (let year = this.currentYear; year >= 1918; year--) {
      this.toYearOpts.push({'value': year.toString(), 'label': year});
      this.fromYearOpts.push({'value': year.toString(), 'label': year});
    }

    this.workExperienceForm = this.fb.group({
      resume_id : ['', Validators.required],
   //   company_name: ['', Validators.required],
      company: this.fb.group({
        name: ['', Validators.required],
        city: [''],
        country: [''],
        country_code: [''],
        company_industry_id: [''],
        company_size: [''],
        company_size_id: [''],
        company_website: ['', UrlValidator.isValidUrlFormat],
        company_description: ['',Validators.maxLength(3000)],
        verified_by_google: [1]
      },{validator: WorkExperienceValidators.googleVerifiedCompanyValidator}),
      location: ['',],
      from: this.fb.group({
        year: ['', Validators.required],
        month: ['']
      } ),
      to: this.fb.group({
        year: ['', Validators.required],
        month: ['']
      }),
      exp_field_id:['', Validators.required],
      exp_field:['', Validators.required],
      job_title_s:['', Validators.required],
      // job_title_s:this.fb.group({
      //   id:[-1, Validators.required],
      //   name:['', Validators.required]
      // }),
    //  job_title : ['', Validators.required],
      isPresent: [false, Validators.required],
      isFromMonthPresent : [''],
      isToMonthPresent : [''],
    //  company_industries: this.fb.array([]),
      // company_industry: this.fb.group({
      //   company_industry_id: [''],
      //   name: ['']
      // } ),
      
      employment_types: this.fb.array([]),
      description: ['',Validators.maxLength(4000)],
      employment_types_form: this.fb.group(this.employmentTypesForm)
    }, {validator : [EducationValidators.compareDatesValidator, WorkExperienceValidators.validLocationValidator]});
    this.fromDateControl.setValidators( EducationValidators.fromMonthValidator) ;
    this.toDateControl.setValidators( EducationValidators.toMonthValidator) ;
  }

  ngOnInit() {
    this.helpTipService.nextHelpTip("");
    this.jobTitleDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'job_titles',10,this.resumeLang);
    this.workExperienceFormData = this.workExperience_item;
    // this.companyIndustries = this.companyIndustriesData;
    this.employmentTypes = this.employmentTypesData;
    this.buildFilledForm();

  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.workExperienceFormData.company.verified_by_google == 1 || this.workExperienceFormData.company.verified_by_google == true) {
        this.googleVerify = true;
        this.getPlaceAutocomplete();
        google.maps.event.clearInstanceListeners(this.googleLocation.nativeElement);
      } else {
        this.googleVerify = false;
        this.getLocationPlaceAutocomplete();
        google.maps.event.clearInstanceListeners(this.googlePlace.nativeElement);
      }
    }, 1);
  }

  clearCompanyInfo(){
    this.companyControl.controls['name'].setValue('');
    this.companyControl.controls['country'].setValue('');
    this.companyControl.controls['country_code'].setValue('');
    this.companyControl.controls['city'].setValue('');
    this.companyControl.controls['company_website'].setValue('');
    this.workExperienceForm.controls['location'].setValue('');
  }
  changAutoComplete() {
    this.clearCompanyInfo();
    this.googleVerify = !this.googleVerify;
   
    if (this.googleVerify === true) {
      this.checkVerify = true;
      this.getPlaceAutocomplete();
      google.maps.event.clearInstanceListeners(this.googleLocation.nativeElement);
    } else {
      this.checkVerify = false;
      this.getLocationPlaceAutocomplete();
      google.maps.event.clearInstanceListeners(this.googlePlace.nativeElement);
    }
  }

  private getPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googlePlace.nativeElement,
      {
        types: ['establishment'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry','name','website']
      });
    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      this.getAddress(place);
    });
  }

  private getLocationPlaceAutocomplete() {
    //to stop bot traffic to google maps
    if(navigator.userAgent.match(/Googlebot/i)){
      return;
    }
    const autocomplete = new google.maps.places.Autocomplete(this.googleLocation.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']  // 'name'
      });
    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      this.getLocationAddress(place);
    });
  }
  getLocationAddress(place: object) {
    let newPlace = this.locationService.getLocationDataFromGoogleMap(place);
    this.city = newPlace.city;
    this.country = newPlace.country;
    this.countryCode = newPlace.country_code;
    this.companyControl.controls['country'].setValue(this.country);
    this.companyControl.controls['country_code'].setValue(this.countryCode);
    this.companyControl.controls['city'].setValue(this.city);
    this.workExperienceForm.controls['location'].setValue((this.city || '') + ' ' + (this.country || ''));
  //  this.companyControl.controls['name'].setValue(this.workExperienceForm.controls['company_name'].value);

  }

  getAddress(place: object) {
    let newPlace = this.locationService.getLocationDataFromGoogleMap(place);
    this.city = newPlace.city;
    this.country = newPlace.country;
    this.countryCode = newPlace.country_code;
    if (place['website']) {
      this.website = place['website'];
    } else {
      this.website = '';
    }
    this.name = place['name'];
    this.companyControl.controls['country'].setValue(this.country);
    this.companyControl.controls['country_code'].setValue(this.countryCode);
    this.companyControl.controls['city'].setValue(this.city);
    this.companyControl.controls['company_website'].setValue(this.website);
    this.companyControl.controls['name'].setValue(this.name);

    this.workExperienceForm.controls['location'].setValue((this.city || '') + ' ' + (this.country || ''));
    $('#location').focus();
  }


  currentLocationKeyUp($event) {
    this.companyControl.controls['country'].setValue('');
    this.companyControl.controls['country_code'].setValue('');
    this.companyControl.controls['city'].setValue('');
    this.companyControl.controls['company_website'].setValue('');
    // this.companyControl.controls['name'].setValue('');

  }


  //  setLocation() {
  //   let country = this.companyControl.controls['country'].value;
  //   let city = this.companyControl.controls['city'].value;
  //   if (country !== '' && city !== ''){
  //     return city + ', ' + country;
  //   } else if (country !== '' ) {
  //     return country;
  //   } else if (city !== '') {
  //     return city;
  //   } else {
  //     return '';
  //   }
  // }

  buildFilledForm() {
    this.fromDate = this.workExperienceFormData.from == null ? null : new Date(this.workExperienceFormData.from);
    this.toDate = new Date(this.workExperienceFormData.to);
    this.resumeIdField =   this.workExperienceFormData.resume_id;
    this.workExperienceForm = this.fb.group({
      resume_id : [this.resumeIdField ? this.resumeIdField : '', Validators.required],
   //   company_name: [this.workExperienceFormData.company.name, Validators.required],
      company: this.fb.group({
        name: [this.workExperienceFormData.company.name, Validators.required],
        city: [this.workExperienceFormData.company.city],
        country: [this.workExperienceFormData.company.country],
        country_code: [this.workExperienceFormData.company.country_code],
        company_industry_id: [ this.workExperienceFormData.company.company_industry_id  ? { "id": this.workExperienceFormData.company.company_industry_id , "name" :this.workExperienceFormData.company.company_industry_name, "translated_languages_id": this.workExperienceFormData.translated_languages_id } : ''],
        company_size: [this.workExperienceFormData.company.company_size_id ? {'company_size_id': this.workExperienceFormData.company.company_size.company_size_translation[0].company_size_id , 'name': this.workExperienceFormData.company.company_size.company_size_translation[0].name} : ''],
        company_size_id: [this.workExperienceFormData.company.company_size_id],
        company_website: [this.workExperienceFormData.company.company_website,UrlValidator.isValidUrlFormat],
        company_description: [this.workExperienceFormData.company.company_description,Validators.maxLength(3000)],
        verified_by_google: [this.workExperienceFormData.company.verified_by_google]
      },{validator: WorkExperienceValidators.googleVerifiedCompanyValidator}),
      location: [this.setFullLocation()],
      from : this.setFromMonth(),
      to : this.setIsPresent() ,
    //  job_title : [this.workExperienceFormData.job_title, Validators.required],
      isPresent : [this.workExperienceFormData.isPresent],
      isFromMonthPresent : [this.workExperienceFormData.isFromMonthPresent],
      isToMonthPresent : [this.workExperienceFormData.isToMonthPresent],
      exp_field_id:[this.workExperienceFormData.experience_field_id , Validators.required],
      exp_field:[{"id":this.workExperienceFormData.experience_field_id , "name":this.workExperienceFormData.exp_field , "translated_languages_id": this.workExperienceFormData.translated_languages_id } , Validators.required],
      job_title_s:[{"id":this.workExperienceFormData.job_title_id,"name":this.workExperienceFormData.job_title_synonym}, Validators.required],
      employment_types: this.fb.array([]),
      description: [this.workExperienceFormData.description,Validators.maxLength(4000)],
      employment_types_form: this.fb.group(this.employmentTypesForm)
    }, {validator : [EducationValidators.compareDatesValidator, , WorkExperienceValidators.validLocationValidator]});
    this.fromDateControl.setValidators( EducationValidators.fromMonthValidator) ;
    this.toDateControl.setValidators( EducationValidators.toMonthValidator) ;

    this.setEmpTypesNew();
    this.onToYearSelect(this.toDateControl.controls['year'].value);
    if (this.workExperienceFormData.description !== null || this.workExperienceFormData.company.company_description !== null || this.workExperienceFormData.company.company_website !== null || this.workExperienceFormData.company.company_size !== null) {
      this.extraChecked = true;
      this.externalInfoVisible = true;
      $('.extraInformation').slideDown('slow');
    }
  }

  selectCompanySizeChange(companySize) {
    this.companyControl.controls['company_size_id'].setValue(companySize.value.company_size_id);
  }

  setFullLocation() {
    if (this.workExperienceFormData.company.country !== null && this.workExperienceFormData.company.city !== null) {
      return this.workExperienceFormData.company.city + ', ' + this.workExperienceFormData.company.country;
    } else if (this.workExperienceFormData.company.country !== null ) {
      return   this.workExperienceFormData.company.country;
    } else if (this.workExperienceFormData.company.city !== null) {
      return this.workExperienceFormData.company.city;
    } else {
      return '';
    }
  }

  setFromMonth(){
    if( this.fromDate !==null){
      if(this.workExperienceFormData.isFromMonthPresent == true ){
        let month = this.fromDate.getMonth() + 1;
        return this.fb.group({
          year: [this.fromDate.getFullYear().toString(), Validators.required],
          month: [month.toString()],
        });
      }

      else
        return this.fb.group({
          year: [this.fromDate.getFullYear().toString(), Validators.required],
          month: [''],
        });
    }else
      return this.fb.group({
        year: ['', Validators.required],
        month: [''],
      });
  }

  setIsPresent(){

    if (this.workExperienceFormData.isPresent==0 || this.workExperienceFormData.isPresent==false){
      if(this.workExperienceFormData.isToMonthPresent == true){
        let month = this.toDate.getMonth() + 1;
        return this.fb.group({
          year: [this.toDate.getFullYear().toString(), Validators.required],
          month: [month.toString()],
        });
      }

      else return this.fb.group({
        year: [this.toDate.getFullYear().toString(), Validators.required],
        month: [''],
      });
    }else if (this.workExperienceFormData.isPresent==1 || this.workExperienceFormData.isPresent==true) {
      return this.fb.group({
        year : ['Present' , Validators.required],
        month:[{ value:'', disabled: true}]
      })
    }
  }

  get descriptionControl() {
    return (this.workExperienceForm.get('description'));
  }

  changeExperiencefield($event){
    this.workExperienceForm.get('exp_field_id').setValue(this.workExperienceForm.controls['exp_field'].value.id);
  }

  submit() {
    if(this.workExperienceForm.valid) {
      if (this.companyIndustryControl.value){
        this.companyIndustryControl.setValue(this.companyIndustryControl.value.id);
      }

      if (this.fromDateControl.controls['month'].value !== '' && this.fromDateControl.controls['month'].value !== '0' && this.fromDateControl.controls['month'].value !== null)
        this.workExperienceForm.controls['isFromMonthPresent'].setValue(true);
      else this.workExperienceForm.controls['isFromMonthPresent'].setValue(false);

      if (this.toDateControl.controls['month'].value !== '' && this.toDateControl.controls['month'].value !== '0' && this.toDateControl.controls['month'].value !== null)
        this.workExperienceForm.controls['isToMonthPresent'].setValue(true);
      else this.workExperienceForm.controls['isToMonthPresent'].setValue(false);

      if (this.workExperienceForm.controls['isPresent'].value == true)
        this.workExperienceForm.controls['isToMonthPresent'].setValue(false);

    }
    this.updateEmpTypes();
    // if(this.jobTitleGroup.controls['id'].value !== -1){
    //   this.jobTitleGroup.controls['name'].setValue(this.jobTitleGroup.controls['name'].value.name);
    // }

    const sendData = this.workExperienceForm.value;
    this.workExperiencesService.updateWorkExperience(sendData , this.workExperience_item.id).subscribe(res => {
      
      this.closeModalPopup.emit({'new': res['data'], 'old': this.workExperience_item});
    });
    $('#myModal').modal('hide');

  }

  get fromDateControl() {
    return (this.workExperienceForm.controls['from'] as FormGroup);
  }

  get toDateControl() {
    return (this.workExperienceForm.controls['to'] as FormGroup);
  }
  getValueToDD(item: AbstractControl) {
    let x  = item.value;

    if (x === '00' || x === '') {
      return false;
    }
    return true;
  }
  onToYearSelect(year: any) {

    if(year === 'Present') {
      this.toDateControl.controls['month'].disable();
      this.workExperienceForm.controls['isPresent'].setValue(true);
    }else{
      this.toDateControl.controls['month'].enable();
      this.workExperienceForm.controls['isPresent'].setValue(false);
    }
  }

  get companyControl() {
    return (this.workExperienceForm.controls['company'] as FormGroup);
  }

  get countryCodeControl() {
    return this.workExperienceForm.controls['company'].controls['country_code'];
  }

  get locationControl() {
    return this.workExperienceForm.controls['location'];
  }

  get companyIndustryControl() {
    return (this.companyControl.controls['company_industry_id'] as FormControl);
    //return (this.workExperienceForm.controls['company_industry'] as FormGroup);
  }
  get employmentTypeFormControl() {
    return (this.workExperienceForm.controls['employment_types_form'] as FormGroup);
  }
  get empTypeControl() {
    return (this.workExperienceForm.controls['employment_types'] as FormArray);
  }

  minimize() {
    // Slide certeficate bottom and top
    if( !this.externalInfoVisible )
    {
      this.externalInfoVisible = true;
      $(".extraInformation").slideDown("slow");
    }else {
      this.externalInfoVisible = false;
      $(".extraInformation").slideUp("slow");
    }
  }

  setEmpTypesNew() {
    if (this.workExperienceFormData.employment_types.length > 0) {
      Object.keys(this.employmentTypeFormControl.controls).forEach((key: string) => {
        this.employmentTypeFormControl.controls[key].setValue(false);
      });
      for (let employment_type of this.workExperienceFormData.employment_types) {
        this.empTypeControl.push(this.createEmpType(employment_type.employment_type_parent_id));
        this.employmentTypeFormControl.controls[employment_type.employment_type_parent_id].setValue(true);
      }
      for (let emploType of this.employmentTypes){
        for ( let empChild of emploType.child_types)
          if (this.employmentTypeFormControl.controls[empChild.id].value === true) {
            this.employmentTypeFormControl.controls[empChild.parent_id].setValue(true);
          }
      }
    }
    else{
      Object.keys(this.employmentTypeFormControl.controls).forEach((key: string) => {
        this.employmentTypeFormControl.controls[key].setValue(false);
      });
    }
  }
  updateEmpTypes() {
    let wasHere = false;
    let isHere = false;
    let keyId : number;
    Object.keys(this.employmentTypeFormControl.controls).forEach((key2: string) => {
      if (this.employmentTypeFormControl.controls[key2].value === false) {
        Object.keys(this.empTypeControl.controls).forEach((key3: string) => {
          if (this.empTypeControl.controls[key3].value.employment_type_parent_id == parseInt(key2)) {
            isHere = true;
            keyId = this.empTypeControl.controls[key3].id;
          }
        });
        this.empTypeControl.removeAt(keyId);
      }
    });
    Object.keys(this.employmentTypeFormControl.controls).forEach((key: string) => {
      if ( key !== '1' && key !== '2' && key !== '3') {
        if (this.employmentTypeFormControl.controls[key].value === true) {
          Object.keys(this.empTypeControl.controls).forEach((key2: string) => {
            if (this.empTypeControl.controls[key2].value.employment_type_parent_id == parseInt(key)) {
              wasHere = true;
            }
          });
          if (!wasHere) {
            this.empTypeControl.push(new FormControl({'employment_type_parent_id': parseInt(key)}));
          }
        }
      }
    });
  }
  private createEmpType(id) {
    return new FormGroup({
      'employment_type_parent_id': new FormControl(id),
    });
  }
  changeRadio(id , childId) {
    if (id == 1 || id == 2) {
      if (this.employmentTypeFormControl.controls[childId].value === false) {
        for (let emploType of this.employmentTypes) {
          for ( let empChild of emploType.child_types)
            if (empChild.parent_id == id){
              this.employmentTypeFormControl.controls[empChild.id].setValue(false);
            }
              
        }
        this.employmentTypeFormControl.controls[childId].setValue(true);
      } else {
        this.employmentTypeFormControl.controls[childId].setValue(false);
      }
    }
  }
  // changeRadio2(id) {
  //   if (this.employmentTypeFormControl.controls[id].value === false) {
  //     for (let emploType of this.employmentTypes) {
  //       for ( let empChild of emploType.child_types)
  //         if (empChild.parent_id == id)
  //           this.employmentTypeFormControl.controls[empChild.id].setValue(false);
  //     }
  //   }
  // }

  setHelpTip(fieldId){
    if(this.helpTips){
      let helpTipsIndex = this.helpTips.findIndex(x => x.field_id === fieldId);
      this.helpTipService.nextHelpTip(this.helpTips[helpTipsIndex].description);
    }
  }
  clearHelpMessage(){
    this.helpTipService.nextHelpTip("");
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
