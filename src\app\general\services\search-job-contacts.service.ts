import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SearchJobContactsService {
  
  private internalMessage = new BehaviorSubject({});
  sharedMessage = this.internalMessage.asObservable();
  
  // constructor() { }

  // sendData(state: string) {
  //   this.message.next(state)
  // }
  notify(message , src , dist , mData) {
    const  data  = {
       'message' :  message ,
        'src' :  src ,
        'dist' :  dist,
         'mData' : mData
     }
      this.internalMessage.next(data) ;
  }
}
