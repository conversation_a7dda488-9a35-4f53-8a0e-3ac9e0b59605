import { Component, OnInit, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Cv } from '../cvs-table/DataModel';
import { CvsTableService } from '../../../company-cvs-folders/services/cvs-table.service';
// import { GeneralService } from '../../../general/services/general.service';
import { CvsFoldersService } from '../../../company-cvs-folders/services/cvs-folders.service';
declare var $: any;

@Component({
  selector: 'app-move-cv-modal',
  templateUrl: './move-cv-modal.component.html',
  styleUrls: ['./move-cv-modal.component.css']
})
export class MoveCvModalComponent implements OnInit {

  moveCVForm:FormGroup;
  submitted:boolean=false;
  modalLoader=false;
  @Input('currentCv') currentCv: Cv;
  @Input('resumes_ids') resumes_ids = [];
  @Input() foldersddData = [];
  @Input() sourceInterface: string;
  @Output() closeModalPopup = new EventEmitter();

  constructor(
    private fb: FormBuilder,
    private cvsTableService:CvsTableService,
    private cvsFoldersService:CvsFoldersService
  //  private generalService:GeneralService
    ) { }

  ngOnInit(): void {
    this.moveCVForm = this.fb.group({
      resume_id: [''],
      resumes_ids: [[]],
      folders_ids: ['',Validators.required]
    });

    if(this.sourceInterface === 'receive-cvs'){
      this.cvsFoldersService.getFoldersData().subscribe(data => {
        this.foldersddData = data['data'];
      //  this.foldersddData.unshift("");
        if(this.currentCv !== null){
          this.moveCVForm.controls['resume_id'].setValidators([Validators.required]);
          this.moveCVForm.controls['resume_id'].updateValueAndValidity();
          this.buildFilledForm();
        }
        else if(this.resumes_ids.length>0){
          this.moveCVForm.controls['resumes_ids'].setValidators([Validators.required]);
          this.moveCVForm.controls['resumes_ids'].updateValueAndValidity();
          this.moveCVForm.controls['resumes_ids'].patchValue(this.resumes_ids);
        }
      });
    }
    else if(this.sourceInterface === 'cvs-folders'){
      this.foldersddData = this.foldersddData.slice(1);
      if(this.currentCv !== null){
        this.moveCVForm.controls['resume_id'].setValidators([Validators.required]);
        this.moveCVForm.controls['resume_id'].updateValueAndValidity();
        this.buildFilledForm();
      }
      else if(this.resumes_ids.length>0){
        this.moveCVForm.controls['resumes_ids'].setValidators([Validators.required]);
        this.moveCVForm.controls['resumes_ids'].updateValueAndValidity();
        this.moveCVForm.controls['resumes_ids'].patchValue(this.resumes_ids);
      }
    }
      
    // this.generalService.internalMessage.subscribe( (data) => {
    //   // to set folders again if user deleted any folder from tags in cv previewer toolbar
    //   if (data['message'] === 'cvFoldersChanged' && data['src'] === 'cv-previewer-toolbar') {
    //     this.moveCVForm.controls['folders_ids'].setValue(data['mData'].cv_folders);
    //   }
    // });
    
  }

  buildFilledForm(){
    this.moveCVForm.controls['resume_id'].setValue(this.currentCv.resume_id);
  //  if(this.currentCv.cv_folders && this.currentCv.cv_folders.length)
    if(this.currentCv.cv_folders)
      this.moveCVForm.controls['folders_ids'].setValue(this.currentCv.cv_folders);
  }

  ngOnChanges(changes: SimpleChanges) {
 //   console.log(changes);
    if(changes['foldersddData'] && this.moveCVForm !==undefined && this.currentCv !==null){
   //   this.foldersddData = changes['foldersddData'].currentValue;
      this.buildFilledForm();
    //  console.log("inside if changes['foldersddData']",this.foldersddData);
    }
    
    if(changes['currentCv']){
      this.currentCv = changes['currentCv'].currentValue;
    //  console.log("inside changes['currentCv']",this.currentCv);
      if(this.moveCVForm !==undefined){
        this.buildFilledForm();
      }
        
    }
        
  }

  resetForm(){
    this.moveCVForm.controls['folders_ids'].setValue("");
  }

  submit(){
    this.submitted = true;
    this.modalLoader=true;
    if(this.moveCVForm.valid){
      this.submitted=false;
    //  form.submitted=false;
      let sendData, folders_ids = [] , folders = [];
      sendData = this.moveCVForm.value;
      folders = this.moveCVForm.controls['folders_ids'].value;

      for ( let i = 0 ; i < folders.length; i++ ) {
        if (folders[i].key !== "") {
          folders_ids.push(+folders[i].key);
        }
      }
      sendData.folders_ids = folders_ids; 
      
      this.cvsTableService.moveCopyCVToFolder(sendData).subscribe( res => {
        console.log("should emit cv_folders",res['data']);
        this.closeModalPopup.emit({'cv_folders': res['data']});
        this.modalLoader=false;
        $('#moveCVToFolderModal').modal('hide');
      });
    }
  }

  cancel(){
    this.submitted = false;
    $('#moveCVToFolderModal').modal('hide');
    this.resetForm();
    this.moveCVForm.markAsPristine();
    this.moveCVForm.markAsUntouched();
    this.buildFilledForm();
  }

}
