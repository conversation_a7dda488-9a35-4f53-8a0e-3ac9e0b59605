<div class="modal fade" id="aiJobDescriptionModal" tabindex="-1" role="dialog"
  aria-labelledby="aiJobDescriptionModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="aiJobDescriptionModalLabel" translate>AI Job Description Generator</h4>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="jobDescriptionText" class="control-label alignment-right" translate>Describe the job
            position</label>
          <!-- <p class="help-text alignment-left" translate>Please include specific details about:</p>
          <ul class="help-text">
            <li translate>Job title (e.g., Systems Engineer, IT Engineer)</li>
            <li translate>Experience field (e.g., IT Consultancy, Software Development)</li>
            <li translate>Required skills (e.g., Problem Solving, Programming Languages)</li>
            <li translate>Required languages (e.g., English, Arabic)</li>
            <li translate>Preferred nationalities (if any)</li>
            <li translate>Employment type (e.g., Full Time, Remote)</li>
            <li translate>Years of experience required</li>
            <li translate>and any additional requirements</li>
          </ul> -->
          <textarea class="form-control" id="jobDescriptionText" rows="5" [(ngModel)]="jobText"
            placeholder="Example: Looking for a Senior Systems Engineer with 3-5 years experience in IT Consultancy. Must have skills in problem solving and ability to work independently. Requires English proficiency. Full-time position at our technology firm..."></textarea>
        </div>
        <div *ngIf="isLoading" class="text-center">
          <i class="fa fa-spinner fa-spin fa-2x"></i>
          <p class="text-center alignment-left" translate>Generating job description...</p>
        </div>
      </div>
      <div class="modal-footer">
        <p class="help-text mt-2 alignment-left desc-justify" style="color: darkgrey;" translate>The AI will generate a
          complete job posting
          including title, description, required skills, job types, and other relevant details based on your
          input.</p>
        <button type="button" class="btn btn-default" data-dismiss="modal" translate>Cancel</button>
        <button type="button" class="btn btn-primary" [disabled]="!jobText || isLoading"
          (click)="generateDescription()">
          <i class="fa fa-magic"></i> <span translate>Generate</span>
        </button>
      </div>
    </div>
  </div>
</div>