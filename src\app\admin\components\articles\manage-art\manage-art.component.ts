import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { FormBuilder, Validators } from '@angular/forms';
import { ContactService } from 'app/admin/services/contact.service';
import { Table } from 'primeng/table';
declare var $: any;

@Component({
  selector: 'app-manage-art',
  templateUrl: './manage-art.component.html',
  styleUrls: ['./manage-art.component.css']
})
export class ManageArtComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  rangeDates: Date[];
  filteredArticles = [];
  admins: {'value': number, 'label': string}[] = [];
  msgToPreview;
  operationType;
  displayMsgModal = false;
  displayAddModal = false;
  private ngUnsubscribe: Subject<any> = new Subject();
  articles = [];
  types = [
        {'label': '', 'value': '' },
        {'label': 'Visitor', 'value': 'ROLE_VISITOR' },
        {'label': 'Admin', 'value': 'ROLE_ADMIN' },
        {'label': 'Job Seeker', 'value': 'ROLE_JOB_SEEKER' },
        {'label': 'Employer', 'value': 'ROLE_EMPLOYER' }
        ];
  statuses = [
    {'label': '', 'value': '' },
    {'label': 'new', 'value': 'new' },
    {'label': 'opened', 'value': 'opened' },
    {'label': 'commented', 'value': 'commented' },
    {'label': 'done', 'value': 'done' },
    {'label': 'replied', 'value': 'replied' },
    {'label': 'assigned', 'value': 'assigned' },
    {'label': 'restored', 'value': 'restore from delete' }
  ];
  opperationNum: number;
  commentForm: any;
  assignForm: any;
  msgIdToDelete: number;
  newCommentDetails: { comment: any; created_at: any; };
  msgId: number;
  mode: string;
  msgLog = [];
  templates = [];
  templateTitles = [];
  loading = true;

  constructor(private contact: ContactService, private fb: FormBuilder) { }

  ngOnInit(): void {
   this.getMessages();
   this.getAdmins();
   this.getTemplates();
  }

  getAdmins() {
    this.contact.getAdmins().takeUntil(this.ngUnsubscribe).subscribe(res => {
     console.log('adm res', res);
     for (let admin of res['data']) {
       this.admins.push({
         'value': admin.id,
         'label': admin.display_name
       });
     }
     this.admins.unshift({'value': null, 'label': '' });
     console.log('adms', this.admins);
    });
  }

  getMessages() {
   this.contact.getMessages().takeUntil(this.ngUnsubscribe).subscribe(res => {
     console.log('res', res);
     let temp = res['data'];
     let comments = [], replies = [], assign_log = [];
     for (let msg of temp) {
       comments = msg.admin_comment;
       replies = msg.admin_replied;
       assign_log = msg.admin_email_assign_log;
          this.articles.push({
            'id'         : msg.id,
            'name'       : (msg.user === null) ? '.....' : msg.user.display_name,
            'type'       : (msg.user === null) ? 'ROLE_VISITOR' : msg.user.role,
            'message'    : msg.message,
            'main_cat_id': msg.contact_main_cat_id,
            'main_cat'   : msg.contact_main_cat,
            'sub_cat_id' : msg.contact_sub_cat_id,
            'sub_cat'    : (msg.contact_sub_cat === null) ? '....' : msg.contact_sub_cat,
            'email'      : msg.email,
            'langId'     : msg.translated_languages_id,
            'language'   : msg.translated_languages,
            'date'       : msg.created_at,
            'assign_log' : assign_log,
            'comments'   : comments,
            'replies'    : replies,
            'last_reply' : msg.last_reply,
            'handled_by' : msg.handled_by,
            'status'     : (msg.last_action_name === null ) ? 'new' : msg.last_action_name.toLowerCase()
          });

     }
     // this.filteredArticles = this.articles;
     this.table.filter(1, 'id', 'startsWith');
     this.table.filter(null, 'id', 'startsWith');
     this.loading = false;
     console.log('messages', this.articles);
   });


  }


  getTemplates() {
    this.contact.getMsgTemplates().takeUntil(this.ngUnsubscribe).subscribe(res => {
       console.log('res', res);
       let temp = res['template_emails'];
       let temp2 = res['template_titles'];
       for (let t of temp) {
           this.templates.push({
             'id'         : t.id,
             'title'      : (t.user === null) ? '.....' : t.template_title,
             'email_title': t.email_title,
             'main_cat_id': t.contact_main_cat_id,
             'main_cat'   : t.contact_main_cat,
             'sub_cat_id' : t.contact_sub_cat_id,
             'sub_cat'    : (t.contact_sub_cat === null) ? '....' : t.contact_sub_cat,
             'short_reply'   : t.short_reply,
             'detailed_reply': t.detailed_reply,
           });

       }
       console.log('template msg', this.templates);
       for (let title of temp2) {
         this.templateTitles.push({
           'label': title.template_title,
           'value': title.id,
          //  'id'   : title.id
         });
       }
       this.templateTitles.unshift({ 'label': '', 'value': null});

     });
   }

  activateArticle(article) {

  }

  addToSelected(msg) {
    console.log('selected', this.filteredArticles);
  }


  displayModal(msg) {
    this.displayMsgModal = true;
    this.mode = 'reply_form';
    this.msgToPreview = {
      'id'         : msg.id,
      'name'       : msg.name,
      'type'       : msg.type,
      'message'    : msg.message,
      'main_cat_id': msg.main_cat_id,
      'main_cat'   : msg.main_cat,
      'sub_cat_id' : msg.sub_cat_id,
      'sub_cat'    : msg.sub_cat,
      'email'      : msg.email,
      'langId'     : msg.langId,
      'language'   : msg.language,
      'date'       : msg.date,
      'assign_log' : msg.assign_log,
      'comments'   : msg.comments,
      'replies'    : msg.replies,
      'last_reply' : msg.last_reply,
      'status'     : msg.status

    };
    console.log('msg', this.msgToPreview);
    if (msg.status === 'new') {
      this.contact.setAsOpen(msg.id).subscribe(res => {
        console.log('res', res);
        let i = this.getMsgIndex(msg.id);
        this.articles[i].status = 'opened';
      });
    }


  }

  closeModal() {
    this.displayMsgModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
    this.msgLog = [];
  }


  buildEmptyForm() {
    if (this.opperationNum === 1) {
      this.commentForm = this.fb.group({
        'received_email_id': [ this.msgToPreview.id, Validators.required],
        'comment'      : ['', Validators.required]
      });
      console.log('form', this.commentForm);
    } else if (this.opperationNum === 2) {
      this.assignForm = this.fb.group({
          'received_email_id': [this.msgToPreview.id, Validators.required],
          'to_assigned_admin_user_id': [null, Validators.required]
      });
      console.log('form', this.assignForm);
    }
  }


  displayCommentForm(event) {
    this.opperationNum = 1;
    this.displayAddModal = true;
    this.buildEmptyForm();
    console.log('event', event);
    console.log('msg to preview', this.msgToPreview);
    // this.msgId = event['msg'].id;
    // console.log('msgId'i:, msgId);
  }

  displayAssignForm(i: any) {
    this.opperationNum = 2;
    this.displayAddModal = true;
    this.buildEmptyForm();
    console.log('event', event);
    console.log('msg to preview', this.msgToPreview);

  }

  closeAddModal() {
    this.displayAddModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
  }

  addNewComment() {
    let dataToSend = this.commentForm.value;
    if (this.commentForm.valid) {
      this.contact.addComment(dataToSend).subscribe((res: { 'admin_comment': any[] }) => {
        console.log('res', res);
        this.closeAddModal();
        // update msg status
          console.log('msg to prev', this.msgToPreview);
          let i = this.getMsgIndex(this.msgToPreview.id);
          let newComment = res.admin_comment[res.admin_comment.length - 1];
          this.articles[i].comments.push(newComment);
          this.articles[i].status = 'commented';

      });
    }
  }

  assignTo() {
    let dataToSend = this.assignForm.value;
    if (this.assignForm.valid) {
      this.contact.assignTo(dataToSend).subscribe((res: { 'admin_email_assign_log': any[]}) => {
        console.log('res', res);
        this.closeAddModal();
          let i = this.getMsgIndex(this.msgToPreview.id);
          this.articles[i].assign_log = res.admin_email_assign_log;
          this.articles[i].status = 'assigned';

      });
    }
  }

  displayMsgLog(msg) {
    this.contact.getMsgLog(msg.id).subscribe(res => {
     console.log('res', res);
     this.msgLog = res as Array<Object>;
     console.log('msg log', this.msgLog);
    });
    this.displayMsgModal = true;
    this.mode = 'log_mode';


  }

  updateMsgStatus(event) {
    console.log('event', event);
    let status = event['status'];
    let msgId = event['msgId'];
    let i = this.getMsgIndex(msgId);
    this.articles[i].status = status;
    console.log('new status', this.articles[i].status) ;
  }

  addReplyResult(event) {
    let msg = event['msg'];
    let index = this.getMsgIndex(msg.id);
    this.articles[index].replies = msg.admin_replied;
  }


  displayDeleteAlert(num, msgId?) {
    this.opperationNum = num;
    if (msgId) {
      this.msgIdToDelete = msgId;
    }
    this.displayAddModal = true;
  }

  deleteMultiMsgs() {
   let msgsIds = [];
   for (let msg of this.filteredArticles) {
      msgsIds.push(msg.id);
   }
   this.contact.deleteMultiMsgs({ 'received_messages_ids': msgsIds}).subscribe(res => {
      console.log('res', res);
      this.closeAddModal();
      if ((res['success'] as string) === 'false' ) {
        alert(res['messages']);
      } else {
        for (let i = 0; i < this.articles.length; i++) {
          if (this.articles[i].id in msgsIds) {
            this.articles.splice(i, 1);
          }
        }
      }
      this.table._totalRecords = this.articles.length;
    });

  }

  deleteMsg() {
    this.contact.deleteMultiMsgs({ 'received_messages_ids': [this.msgIdToDelete] }).subscribe(res => {
      console.log('res', res);
      this.closeAddModal();
      if ((res['success'] as string) === 'false') {
        alert(res['messages']);
      } else {
        let i = this.getMsgIndex(this.msgIdToDelete);
        this.articles.splice(i, 1);
        this.table._totalRecords = this.articles.length;
      }
    });

  }

  getMsgIndex(msgId) {
    for (let i = 0; i < this.articles.length; i++) {
      if (this.articles[i].id === msgId) {
        console.log('index', i);
        return i;
      }
    }
  }


  onDateSelect(value) {
    this.table.filter(this.formatDate(value), 'date', 'contains');
  }

  formatDate(date) {
    let month = date.getMonth() + 1;
    let day = date.getDate();

    if (month < 10) {
        month = '0' + month;
    }

    if (day < 10) {
        day = '0' + day;
    }

    return date.getFullYear() + '-' + month + '-' + day;
  }





  get comment() {
    return this.commentForm.get('comment');
  }

  get admin_user_id() {
    return this.assignForm.get('to_assigned_admin_user_id');
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }


}
