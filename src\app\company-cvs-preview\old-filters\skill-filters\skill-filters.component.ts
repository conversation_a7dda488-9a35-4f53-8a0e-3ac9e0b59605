import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ue<PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
import { SkillFiltersDataModel } from './SkillFiltersDataModel';
declare var $: any;
@Component({
  selector: 'app-skill-filters',
  templateUrl: './skill-filters.component.html',
  styleUrls: ['./skill-filters.component.css']
})
export class SkillFiltersComponent implements OnInit {
  dataModel: SkillFiltersDataModel;
  @Input() filterData;
  public temp: any;
  ////  private dataModelDiffer: KeyValueDiffer<string, any>;
  constructor(private generalService: GeneralService, private differs: KeyValueDiffers) {
    this.dataModel = new SkillFiltersDataModel();
  }

  ngOnInit(): void {
    ///// this.dataModelDiffer = this.differs.find(this.dataModel).create();
    this.dataModel.skills.push(this.dataModel.firstSkill);
    ///
    this.temp = { 'skilles': [] };
    this.temp['skills'] = this.filterData.skilles;
  }

  filterArray(event, arr) {
    this.temp[arr] = this.filterData[arr].filter(e => e.name.toLowerCase().includes(event.query.toLowerCase()));
  }

  addSkill() {
    this.dataModel.skills.push({
      type_id: '',
      level_id: '',
      mandatory: ''
    });
  }

  removeSkill(index) {
    this.dataModel.skills.splice(index , 1);
 }

  // ngDoCheck(): void {
  //   const changes = this.dataModelDiffer.diff(this.dataModel);
  //   if (changes) {
  //     this.dataModel.setFilters();
  //     //  *  here  we set src of   this.generalService.notify equals to 'education' which it's not
  //     // the same of component name  "education-filters" please see filters-wrapper component
  //     this.generalService.notify('filters-changes',
  //       'language', 'filters-wrapper', this.dataModel.filters);
  //   }
  // }

  sendFilters() {
    this.dataModel.setFilters();
    this.generalService.notify('filters-changes',
      'skills', 'filters-wrapper', this.dataModel.filters);
    $('#filtersModal').modal('hide');
  }


  sendInitStateToWarapper() {
    this.dataModel.setFilters();
    this.generalService.notify('init-filters',
      'skills', 'filters-wrapper', this.dataModel.filters);
  }

  hideModal() {
    $('#filtersModal').modal('hide');
  }

  checkAllMandatory(event) {

     this.dataModel.skills.forEach( el => {
         el.mandatory = event.target.checked  ;
     });
  }

}
