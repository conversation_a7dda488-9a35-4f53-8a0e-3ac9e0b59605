import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GeneralService } from '../../../general/services/general.service';

@Component({
  selector: 'app-country-dropdown-topbar',
  templateUrl: './country-dropdown-topbar.component.html',
  styleUrls: ['./country-dropdown-topbar.component.css']
})
export class CountryDropdownTopbarComponent implements OnInit {

  country='';
  countryOpts = [
    {'value': '', 'label': 'Country', 'code':'all'},
    {'value': 'qatar', 'label': 'Qatar', 'code':'+974'},
    {'value': 'syria', 'label': 'Syria', 'code':'+963'},
    {'value': 'egypt', 'label': 'Egypt', 'code':'+20'},
    {'value': 'india', 'label': 'India', 'code':'+91'},
  ]

  constructor(
    private router: Router,
    private generalService:GeneralService) { }

  ngOnInit(): void {
    if(localStorage.getItem('country')){
      this.country = localStorage.getItem('country');
      this.notify();
    //  this.generalService.notify('countryChanged', 'country-dropdown-topbar', 'jobSreach-form-navBar', { 'countryChanged': true, 'countryName': this.country });
    }     
  }

  onChangeCountry(country){
    localStorage.setItem("country",country);
    this.notify();
  //  this.generalService.notify('countryChanged', 'country-dropdown-topbar', 'jobSreach-form-navBar', { 'countryChanged': true, 'countryName': this.country });
    this.router.navigate([country]);
  }

  notify(){
    this.generalService.notify('countryChanged', 'country-dropdown-topbar', 'jobSreach-form-navBar', { 'countryChanged': true, 'countryName': this.country });
  //  this.generalService.notify('countryChanged', 'country-dropdown-topbar', 'topbar', { 'countryChanged': true, 'countryName': this.country });
  }

}
