

<div class="modal-body-container" >

  <div class="modal-body ">

    <div *ngIf="mode === 'create' || mode === 'edit'">
      <form  *ngIf="languagesArray.length !== 0" [formGroup]="helpTopicForm" (ngSubmit)="saveQuestion(helpTopicForm.value)"  >
        <h3 class="heading" *ngIf="mode === 'create' && openedFromSidebar" translate>help.labels.AddNewHelpTopic</h3>

        <!-- <div class="col-md-5 col-sm-5 col-xs-5" > -->
          <div class="form-group type-form-group">
            <div class="row ">
              <div class="col-md-2 col-sm-2 col-xs-2 text-right ">
                <label for="type" class="control-label" translate>help.Type<span *ngIf="type.touched && type.invalid" class="required">**</span></label>
              </div>
              <div class="col-sm-5 col-xs-5">
                <label class="container radio-choose" for="user" translate>help.UserHelp
                  <input type="radio" formControlName="help_center_type" id="user" value="user help">
                  <span class="checkmark"></span>
                </label>
              </div>
              <div class="col-sm-5 col-xs-5">
                <label class="container radio-choose" for="company" translate>help.CompanyHelp
                  <input type="radio" formControlName="help_center_type" id="company" value="company help">
                  <span class="checkmark"></span>
                </label>
              </div>
            </div>
            <div *ngIf="type.touched && type.invalid" >
              <div *ngIf="type.errors.required" class="alert alert-danger"  translate>faqs.errorMessages.TypeRequired</div>
           </div>
          </div>
      <!-- </div> -->


      <div class="form-group cat-form-group row"  >
        <div class="col-md-2 text-right">
          <label for="main-category" translate>help.MainCategory<span *ngIf="main_category_id.touched && main_category_id.invalid" class="required">**</span></label>
        </div>
        <div class="col-md-10">
          <p-dropdown [options]="( type.value.toLowerCase() === 'user help') ? main_categories.user_main_categories[currentLangId - 1] : main_categories.company_main_categories[currentLangId - 1] " formControlName="help_center_main_cat_id" (onChange)="filter()"  [required]="true" [filter]="true">
            <ng-template let-category pTemplate="item">
                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                    <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                </div>
            </ng-template>
          </p-dropdown>
          <div *ngIf="main_category_id.touched && main_category_id.invalid" >
             <div *ngIf="main_category_id.errors.required" class="alert alert-danger"  translate>help.errorMessages.MainCategoryRequired</div>
          </div>
        </div>
      </div>

      <div class="form-group cat-form-group row"  >
        <div class="col-md-2 text-right">
          <label for="sub-category" translate>help.SubCategory
            <!-- <span *ngIf="sub_category_id.touched && sub_category_id.invalid" class="required">**</span> -->
          </label>
        </div>
         <div class="col-md-10">
          <p-dropdown [options]="filteredSubCategories[currentLangId - 1]" formControlName="help_center_sub_cat_id" [filter]="true">
            <ng-template let-category pTemplate="item">
                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                    <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                </div>
            </ng-template>
          </p-dropdown>
          <!-- <div *ngIf="sub_category_id.touched && sub_category_id.invalid" >
             <div *ngIf="sub_category_id.errors.required" class="alert alert-danger"  translate>help.errorMessages.SubCategoryRequired</div>
          </div> -->
         </div>
      </div>

      <div class="row">
        <!-- <div class="col-md-9 col-sm-9 col-xs-12" > -->
          <div class="form-group order-form-group" >
            <div class="col-md-2 col-xs-6 text-right">
              <label for="order"   translate>help.Order<span *ngIf="order.touched && order.invalid" class="required">**</span></label>
            </div>
            <div class="col-md-6 col-xs-6">
              <p-dropdown type="number"  [options]="orders" formControlName="order" [filter]="true" >
                <ng-template let-order pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height:25px;">
                        <div style="font-size:14px;float:left;margin-top:4px" type="number">{{order.label}}</div>
                    </div>
                </ng-template>
              </p-dropdown>
              <div *ngIf="order.touched && order.invalid" >
                <div *ngIf="order.errors.required" class="alert alert-danger" translate>help.errorMessages.OrderRequired</div>
              </div>
            </div>
          </div>
        <!-- </div> -->

        <div class="col-md-4 col-sm-4 col-xs-12" >
          <div class="form-check" title="you shouldn't check this until you enter all help topic translations">
            <input formControlName="active"  name="activation" type="checkbox"
                  class="form-check-input" id="activation" style="margin-right: 10px;" />
            <label class="form-check-label" for="activation" translate> help.Activation</label>
          </div>
        </div>

    </div>





      <br><br>



    <ul class="nav nav-tabs">
        <li *ngFor="let lang of languagesArray" role="presentation" class="btn" [class.active]="lang.id === currentLangId"
            (click)="changeLang(lang.id)" translate>{{ "help.languages."+lang.name}}</li>
    </ul>




      <div  formArrayName="help_center_trans" *ngFor="let item of languagesArray; let i= index">
        <div [formGroupName]="i" *ngIf="item.id === currentLangId ">


            <div class="form-group row" style="margin-top: 30px;">
              <div class="col-md-2 text-right">
                <label for="title"  translate>help.Title
                  <span *ngIf="help_center_trans.controls[i].controls.title.touched && help_center_trans.controls[i].controls.title.invalid" class="required">**</span>
                </label>
              </div>
              <div class="col-md-10">
                <input formControlName="title" name="title" type="text" class="form-control"
                    id="title" />
                <div *ngIf="help_center_trans.controls[i].controls.title.touched && help_center_trans.controls[i].controls.title.invalid" >
                  <div *ngIf="help_center_trans.controls[i].controls.title.errors.required" class="alert alert-danger" translate>help.errorMessages.TitleRequired</div>
                </div>
              </div>
            </div>

            <div class="form-group answer-form-group row">
              <div class="col-md-2 text-right">
                <label for="description"  translate>help.Description
                  <span *ngIf="help_center_trans.controls[i].controls.description.touched && help_center_trans.controls[i].controls.description.invalid" class="required">**</span>
                </label>
              </div>
              <div class="col-md-10">
                <p-editor formControlName="description" [style]="{'height':'350px'}" id="description"  onTextChange="render()" [required]="i === 0">
                    <p-header>
                        <span class="ql-formats">
                            <select class="ql-header">
                                <option value="2">Heading 2</option>     <!-- h2 -->
                                <option value="3">Heading 3</option>     <!-- h3 -->
                                <option value="4">Heading 4</option>     <!-- h4-->
                                <option selected>Normal</option>
                            </select>
                            <button class="ql-bold" aria-label="Bold"></button>
                            <button class="ql-italic" aria-label="Italic"></button>
                            <button class="ql-underline" aria-label="Underline"></button>
                            <select title="Text Alignment" class="ql-align">
                                <option selected>Gauche</option>
                                <option value="center" label="Center"></option>
                                <option value="right" label="Right"></option>
                                <option value="justify" label="Justify"></option>
                            </select>
                            
                            <button aria-label="Ordered List" class="ql-list"
                                value="ordered" type="button"></button>
                            <button aria-label="Bullet List" class="ql-list" value="bullet"
                                type="button"></button>
                            <span class="ql-format-separator"></span>
                        </span>
                    </p-header>
                </p-editor>
                <div *ngIf="help_center_trans.controls[i].controls.description.touched && help_center_trans.controls[i].controls.description.invalid" >
                    <div *ngIf="help_center_trans.controls[i].controls.description.errors.required" class="alert alert-danger" translate>help.errorMessages.DescriptionRequired</div>
                </div>
              </div>
            </div>


            <div class="form-group row" style="margin-top: 60px;">
              <div class="col-md-2 text-right">
                <label for="slug"  translate>help.Slug
                  <span *ngIf="help_center_trans.controls[i].controls.slug.touched && help_center_trans.controls[i].controls.slug.invalid" class="required">**</span>
                </label>
              </div>
              <div class="col-md-10">
                <input  formControlName="slug"  name="slug" type="text" class="form-control"
                id="slug" />
                <div *ngIf="help_center_trans.controls[i].controls.slug.touched && help_center_trans.controls[i].controls.slug.invalid" >
                  <div *ngIf="help_center_trans.controls[i].controls.slug.errors.required" class="alert alert-danger" translate>help.errorMessages.SlugRequired</div>
                </div>
              </div>
            </div>

            <div class="form-group row">
              <div class="col-md-2 text-right">
                <label for="page-title"  translate>help.PageTitle
                  <span *ngIf="help_center_trans.controls[i].controls.page_title.touched && help_center_trans.controls[i].controls.page_title.invalid" class="required">**</span>
                </label>
              </div>
              <div class="col-md-10">
                <input formControlName="page_title" name="page-title" type="text" class="form-control"
                id="page-title" />
                <div *ngIf="help_center_trans.controls[i].controls.page_title.touched && help_center_trans.controls[i].controls.page_title.invalid" >
                  <div *ngIf="help_center_trans.controls[i].controls.page_title.errors.required" class="alert alert-danger" translate>help.errorMessages.PageTitleRequired</div>
                </div>
              </div>
            </div>

            <div class="form-group row">
              <div class="col-md-2 text-right">
                <label for="meta-description"  translate>help.MetaDescription
                </label>
              </div>
              <div class="col-md-10" >
                <textarea formControlName="meta_description" name="meta-description" type="text" class="form-control"
                id="meta-description" ></textarea>
              </div>
            </div>

            <div class="form-group row">
              <div class="col-md-2 text-right">
                <label for="meta-keywords"  translate>help.MetaKeywords
                </label>
              </div>
              <div class="col-md-10" style="margin-top: 25px;" >
                <textarea formControlName="meta_keywords" name="meta-keywords" type="text" class="form-control"
                id="meta-keywords" ></textarea>
              </div>
            </div>

    </div>
  </div>


<button *ngIf="languagesArray " type="submit"  class="btn btn-success" [disabled]="helpTopicForm.invalid" translate>help.Save</button>



      </form>
      <!-- <p> {{ helpTopicForm.value | json }} </p> -->

    </div>

    <div *ngIf="mode === 'preview' && hTopicTranslations.length !== 0">
        <div class="card" style="width: 18rem;" *ngIf="htopicEn && languagesArray.length!==0 && hTopicMainCategories.length!==0 &&  hTopicSubCategories.length!==0 && hTopicTranslations.length!==0">
          <div class="card-body" *ngIf="htopicEn.id !== null">
            <div *ngIf="hTopicTranslations.length > 1  && hTopicMainCategories.length > 1">
              <ul class="nav nav-tabs">
                <li *ngFor="let lang of languagesArray" role="presentation" class="btn" [class.active]="lang.id === currentLangId"
                    (click)="changeLang(lang.id)" translate>{{ "faqs.languages."+lang.name}}</li>
              </ul>
            </div>
            <br><br>

            <div class="preview-content"></div>
            <div *ngFor="let lang of languagesArray">
              <div *ngIf="hTopicMainCategories.length > 1 && hTopicMainCategories[lang.id - 1].translated_languages_id === currentLangId">
                <div class="row">
                  <div class="col-md-4 col-sm-4 col-xs-4 text-right"><p class="card-text"><span translate>help.MainCategory</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p class="card-text">{{ hTopicMainCategories[lang.id - 1].label }}</p></div>
                </div>
              </div>
              <div *ngIf="hTopicSubCategories.length > 1 && hTopicSubCategories[lang.id - 1].translated_languages_id === currentLangId">
                <div class="row">
                  <div class="col-md-4 col-sm-4 col-xs-4 text-right"><p class="card-text"><span translate>help.SubCategory</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p class="card-text">{{ hTopicSubCategories[lang.id - 1].label }}</p></div>
                </div>
              </div>
              <div *ngIf="hTopicTranslations.length > 1 && hTopicTranslations[lang.id - 1].translated_languages_id === currentLangId" >
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4 text-right"><p class="card-text"><span translate>help.Title</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"><p id="title" class=" content">{{ hTopicTranslations[lang.id - 1].title }}</p></div>
                </div>
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4 text-right"> <p class="card-text"><span translate>help.Description</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p id="description" class="content user-input" [innerHTML]="sanitizer.bypassSecurityTrustHtml(hTopicTranslations[lang.id - 1].description)"></p></div>
                </div>
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4 text-right"> <p class="card-text"><span translate>help.Slug</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p id="slug" class="content user-input" [innerHTML]="hTopicTranslations[lang.id - 1].slug"></p></div>
                </div>
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4 text-right"> <p class="card-text"><span translate>help.PageTitle</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p id="page_title" class="content user-input" [innerHTML]="hTopicTranslations[lang.id - 1].page_title"></p></div>
                </div>
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4 text-right"> <p class="card-text"><span translate>help.MetaDescription</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p id="meta_description" class="content user-input" [innerHTML]="hTopicTranslations[lang.id - 1].meta_description"></p></div>
                </div>
                <div class="row">
                  <div class="col-md-4  col-sm-4 col-xs-4 text-right"> <p class="card-text"><span translate>help.MetaKeywords</span></p></div>
                  <div class="col-md-8 col-sm-8 col-xs-8"> <p id="meta_keywordsclass" class="content user-input" [innerHTML]="hTopicTranslations[lang.id - 1].meta_keywords"></p></div>
                </div>
              </div>
            </div>


            <div class="row">
              <div class="col-md-4  col-sm-4 col-xs-4 text-right"><p class="card-text"><span translate>help.Order</span> </p></div>
              <div class="col-md-8 col-sm-8 col-xs-8"><p class="card-text"> {{ htopicEn.order }}</p></div>
            </div>

            <div class="row">
              <div class="col-md-4  col-sm-4 col-xs-4 text-right"><p class="card-text"><span translate>help.Type</span> </p></div>
              <div class="col-md-8 col-sm-8 col-xs-8"><p class="card-text"> <i class="fas fa" [title]="htopicEn.type" [class.fa-user]="htopicEn.type.toLowerCase() === 'user help'" [class.fa-suitcase]="htopicEn.type.toLowerCase() === 'company help'"></i></p></div>
            </div>

            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-4  text-right activation"><span translate>help.Activation  </span></div>
              <div class="col-md-8 col-sm-8 col-xs-8"><i  id="inactive" *ngIf="!htopicEn.active" class="fa fa-check-circle"></i>
                <i  id="active" *ngIf="htopicEn.active" class="fa fa-check-circle"></i></div>
            </div>


          </div>
        </div>
    </div>

    <div *ngIf="mode === 'delete'">
      <div class="delete"  translate>
        help.errorMessages.DeleteConfirm
        <button type="button" class="btn btn-default" data-dismiss="modal"
            (click)="deleteHTopic(htopicId)" translate>help.labels.Yes</button>
      </div>
    </div>


  </div>

</div>




