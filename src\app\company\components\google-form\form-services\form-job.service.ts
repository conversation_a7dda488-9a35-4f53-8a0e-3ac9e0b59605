import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from 'rxjs';
import { Observable } from 'rxjs/Observable';
import { Question } from "app/company/components/google-form/Models/question";

@Injectable()
export class FormJobService {
  url = '';
  getQusUrl = '';
  addQuesUrl = '';
  editQuesUrl = '';
  deleQuesUrl = '';
  reoQuesUrl = '';
  constructor(private http: HttpClient , private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url = data + 'job_form/forms';
      this.getQusUrl = data + 'job_form/question/getall';
      this.addQuesUrl = data + 'job_form/questions';
      this.editQuesUrl = data + 'job_form/questions';
      this.deleQuesUrl = data + 'job_form/questions';
      this.reoQuesUrl = data + '/job_form/questions/reorder'
     });
    }

  AddForm(data){
    console.log('url_form',this.url);
    return this.http.post(this.url, data);
    
  }

  getAllQuestions(form_id?: number): Observable<Question[]> {
    if(!form_id) form_id=19;
    return this.http.get <Question[]>(this.getQusUrl + '/' + form_id);
  }

  addQuestion(question: Question) {
    const body = JSON.stringify({
        'form_id':question.form_id,
        'label': question.label,
        'controlType': question.controlType,
        'isRequired': question.isRequired,
        'order': question.order,
        'choices': question.choices,
      }
    );
    console.log(body);
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.addQuesUrl , body, {headers});
  }

  updateQuestion(question: any, id: any,formId?:number) {
    /* if(!formId) formId=1; */
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.editQuesUrl + '/' + id, JSON.stringify(question), {headers});
  }

  deleteQuestion(id: any,formId?:number) {
    /* if(!formId) formId=1; */
    return this.http.delete(this.deleQuesUrl + '/'+ id);
  }

  reOrderQuestion(data: any) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    const body = JSON.stringify( {'questions': data} );
    return this.http.post(this.reoQuesUrl, body, {headers});
  }

  

}
