<div class="slider-container">
  <!-- Idle state: only current image -->
  <img *ngIf="!animating"
       [src]="images[currentIndex]"
       class="slide-image" />

  <!-- Animating state: both images -->
  <img *ngIf="animating"
       [src]="images[currentIndex]"
       class="slide-image"
       [ngClass]="getOutClass()" />

  <img *ngIf="animating"
       [src]="images[nextIndex]"
       class="slide-image"
       [ngClass]="getInClass()" />

</div>
