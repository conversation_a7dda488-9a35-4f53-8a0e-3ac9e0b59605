import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class UploadCvPdfService {
  baseUrl = '';
  url = '';

  constructor(private http: HttpClient ,
              private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data;
      this.url = data + 'upload_cv';
    });

  }

  getDropdownData(resumeId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + resumeId, {headers});
  }

  addStepData(step, stepData) {
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    let params = new HttpParams();
    params = params.append('step', step);

    return this.http.post(this.url + '?' + params, JSON.stringify(stepData), {headers});
  }

  getFileName(resumeId){
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + 'upload_file_by_resume/' + resumeId, {headers});
  }

}
