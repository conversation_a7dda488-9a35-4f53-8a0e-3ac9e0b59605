.container-padding{
  padding-top:75px;
}
a:link , a:hover {
  cursor: pointer;
  text-decoration: none
}
.helptopic-title{
  font-size: 23px;
  color: #3D7BCE;
  margin-bottom: 22px;
}
.badge.category-badge {
  padding: 5px 10px;
  font-size: 19px;
  white-space: normal;
  line-height: 1.3;
  /* color: #465798; */
  background-color: transparent;
  cursor: pointer;
  display:left block inline ;
  text-align: left !important;
}
.badge{
  color:#777;
}
.breadcrumb {
  font-size: 16px;
  padding: 15px 15px;
  /* margin-top: 20px; */
  border-bottom: 1px solid #ddd;
}
a:link{
  text-decoration: none;
}
:host ::ng-deep p-progressSpinner svg circle {
  fill:transparent;
}
.fa.fa-home {
  font-size: 1.3em;
}
.content-row{
  margin-top: 10px;
}
.cat-col{
  padding-right: 10px;
  padding-left: 10px;
}
.htopic-container {
  padding: 15px;
  margin: 15px;
}

h4, h5, h2 {
  padding-left: 15px;
  font-weight: bold;
}

h5 {
  padding-left: 20px;
  background-color: white;
}

.card {
background-color: white;
  /* margin: 20px; */
  padding:20px 30px;
  transition: all 0.6s ease-in-out;
}


div.description {
  padding: 6px 0;
  margin-bottom: 45px;
  background-color: white;
  min-height: 300px;
  overflow-x: auto;
  /* font-size: 18px; */
  text-align: justify;
}

span.languages {
  float: right;
  margin-bottom: 5px;
  margin-top: -10px;
}

div, table, td,th,p,h2,form ,ul, li, ol, .breadcrumb, span, div, button  {
  font-family: 'Exo2-Regular', sans-serif;
}
div.header {
  /* background: -webkit-linear-gradient(top, #f3f70205 0%, #276ea4 100%); */
  min-height: 100px;
  height: fit-content;
  padding-bottom: 25px;
  /* margin-top: -30px; */

}
.header {
  /* background:#efefef; */
  height: fit-content;
  padding-bottom: 25px;
  /*margin-top: -30px;*/
}



/* ---------------------------------------------------------------------- */
/* Start Responsive  */
@media screen and (max-width: 850px ) {

  .nav-row {
    padding-top: 0px;

  }

  .header {
      margin-top: -40px;
  }
}

@media screen and (min-width: 851px) and (max-width: 1014px ) {

  .nav-row {
    padding-top: 15px;

  }

  .header {
    margin-top: -65px;
  }
}

@media screen and (min-width: 1025px) and (max-width: 1399px ) {

  .nav-row {
    padding-top: 10px;

  }

  .header {
    margin-top: -50px;
  }
}

@media screen and (min-width: 1400px)  {

  .content-col {
    padding-top: 20px;

  }

  .header {
    margin-top: -30px;
  }
}
@media screen and (max-width: 767px ) {
  .header{
    margin-top: 0;
    background:#fff;
    padding-bottom: 0;
  }
  .container-padding{
    padding-top: 25px;
  }
  .badge.category-badge{
    font-size: 14px;
    padding: 5px 4px
  }
  .fa.fa-home{
    font-size: 1.1em;
  }
  .card{
    margin:0;
  }
  div.description{
    padding:0;
  }
  p.note{
    padding:0;
  }
  div.description{
    font-size:15px;
  }
  .card{
    padding: 20px;
  }
}
/* End Responsive */
/* ----------------------------------------------------------------------------- */

