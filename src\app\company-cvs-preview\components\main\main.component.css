.filters-wrapper {
    margin-bottom: 25px;
}
.folders-section{
    position: sticky;
    top: 100px;
    float: left;
    height: 80vh;
    /* height: 100vh; */
    /* position:fixed; */
    width:250px;
    /* height: 100%; */
    overflow: auto;
    z-index: 80;
    padding: 12px 6px 5px 6px;
    background: #efefed;
    transition: width .2s;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 20%), 0 1px 1px 0 rgb(0 0 0 / 14%), 0 2px 1px -1px rgb(0 0 0 / 12%);
    /* padding-bottom: 140px; */
}
/* .fix-position .folders-section {
    z-index: -1;
}  */

.folders-toggle{
    display:none;
}
.table-section{
    margin-left:270px;
    padding: 0 15px 15px 0;
}

.folders-toggle{
    display:block;
    position: fixed;
    top: 175px;
    background: #3D7BCE;
    z-index: 80;
    padding: 10px;
    color: #fff;
    padding: 7px 0px 0px 0px;
    transition: left .2s;
    cursor: pointer;
    width: 30px;
    height: 40px;
    font-size: 22px;
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
}
.collapse-folders .folders-toggle{
    left:50px;
}
.expand-folders .folders-toggle{
    left:250px;
}
.collapse-folders .table-section{
    margin-left:76px;
}
.collapse-folders .folders-section{
    width:50px;
    padding:6px 0 0 0;
}
.expand-folders .folders-section{
    width:250px;
}

.mobile-pagenavbar{
    display:none;
}

:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-icon{
    font-size: 1.5em;
}

:host ::ng-deep .collapse-folders .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-tree-toggler{
    display:none !important;
}
:host ::ng-deep .collapse-folders .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content{
    padding-left:0 !important;
}
:host ::ng-deep .collapse-folders .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-icon{
    font-size: 1.8em;
    margin-left: 10px;
    margin-right: 5px;
}
:host ::ng-deep .collapse-folders .ui-tree .ui-tree-filter-container{
    display:none;
}

:host ::ng-deep .collapse-folders .folders-heading span{
    display:none !important;
}
:host ::ng-deep .collapse-folders .add-folder-btn{
    margin-left:11px;
}
:host ::ng-deep .collapse-folders .folder-label-div .count{
    display:none;
}

@media screen and (max-width:1180px){
    .filters-wrapper {
        margin-bottom: 10px;
    }
    .collapse-folders .table-section , .expand-folders .table-section{
        margin-left:76px;
    }
    .expand-folders .folders-section{
        position:fixed;
    }
}
@media screen and (max-width: 991px){
    .mobile-pagenavbar{
        display:block;
    }
    .empty-pagenavbar , .folders-toggle{
        display:none;
    }
}

@media screen and (max-width: 767px){
    :host ::ng-deep .collapse-folders .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-icon{
        margin-left: 5px;
        margin-right: 3px;
    }
    :host ::ng-deep .collapse-folders .add-folder-btn {
        margin-left: 8px;
    }
}

@media screen and (min-width: 768px) and (max-width: 850px) and (orientation: landscape) {
    .folders-section{
        height:74vh;
    }
}

@media screen and (max-width: 767px) and (orientation: landscape) {
    .folders-section{
        height:232px;
    }
}

@media screen and (max-width:500px){
    .collapse-folders .table-section, .expand-folders .table-section {
        margin-left: 60px;
    }
    /* .folders-toggle { 
        width: 23px;
        height: 37px;
        font-size: 18px;
    } */
}
