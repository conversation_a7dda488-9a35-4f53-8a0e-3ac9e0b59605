import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from 'rxjs';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class PostJobService {
  private edit = new BehaviorSubject(false);
  currentedit = this.edit.asObservable();
  private adv_lang_Id = new BehaviorSubject([]);
  private Temp_Id = new BehaviorSubject([]);
  currenttempId = this.Temp_Id.asObservable();
  currentadvId = this.adv_lang_Id.asObservable();
  private ok = new BehaviorSubject([]);
  Data = this.ok.asObservable();
  baseUrl = '';
  url = '';
  post = '';
  advData = '';
  advLang = '' ;
  advDraft = '';
  advTemplate = '';
  advTempData = '';
  advPublish = '';
  constructor(private http: HttpClient , private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data;
      this.url = data + 'jobAdvertisementData';
      this.post = data + 'jobAdvertisement';
      this.advData = data + 'jobAdvertisement';
      this.advLang = data + 'jobAdvertisement/other_languages';
      this.advTempData = data + 'job_advertisement_template'
      this.advDraft = data + 'save_as_draft';
      this.advTemplate = data + 'save_as_template';
      this.advPublish = data + 'save_as_publish';
     });
    }

    changeAdvId_lang(Advid,Langid,source) {
      let value = [];
      value.push({
        'translated_languages_id':Langid,
        'source': source,
        'Advid': Advid,
      });
      this.adv_lang_Id.next(value);
    }

    send_Data(value) {
      this.ok.next(value);
    }

    changeTempId(tempId) {
      let value = [];
      value.push({
        'Temp_id': tempId,
      });
      this.Temp_Id.next(value);
    }

    getPostFormData(companyLang) {
      return this.http.get(this.url + '/' + companyLang);
    }

    getPostData(job_advertisement_id, translated_languages_id,publicAdv?) {
      if(publicAdv){
        let params = new HttpParams();
        params = params.append('public', publicAdv);
        return this.http.get(this.advData + '/' + job_advertisement_id + '/' + translated_languages_id,{ params: params});
      }
      else{
        return this.http.get(this.advData + '/' + job_advertisement_id + '/' + translated_languages_id);
      }
    }

    getTempData(job_advertisement_id, translated_languages_id,publicAdv?) {
      return this.http.get(this.advTempData + '/' + job_advertisement_id + '/' + translated_languages_id);
    }

    getOtherLanguages(companyAdvId) {
      return this.http.get(this.advLang + '/' + companyAdvId );
    }

    addAdv(AdverData) {
      /* const headers = new HttpHeaders().set('Content-Type', 'application/json'); */
      return this.http.post(this.post, AdverData);
    }

    saveAsDraft(job_advertisement_id) {
      return this.http.post(this.advDraft + '/' + job_advertisement_id,'');
    }

    saveAsTemplate(job_advertisement_id) {
      return this.http.post(this.advTemplate + '/' + job_advertisement_id,'');
    }

    saveAsPublish(job_advertisement_id) {
      return this.http.post(this.advPublish + '/' + job_advertisement_id,'');
    }

    getGlobalAdvId(companyAdvId,advType){
      if(advType === 'template')
        return this.http.get(this.baseUrl + 'adv_temp/' + companyAdvId);
      else
        return this.http.get(this.baseUrl + 'advertisement/' + companyAdvId);
    }

    changeStatus(edit_status: boolean) {
      this.edit.next(edit_status);
    }

    checkOtherEmployerLogoExist(logo_name){
      let params = new HttpParams();
      params = params.append('logo_name', logo_name.logo_name);
      return this.http.get(this.baseUrl + 'other_employer_logo_exist', { params: params} );
    }
}
