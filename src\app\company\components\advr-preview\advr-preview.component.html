<page-navbar *ngIf="this.source==='post_job' || this.source === 'reload'"  [navType]="'advrPreview'" (outData)="changeAction($event)"></page-navbar>

<button 
    *ngIf="source==='post_job' || source === 'reload'"
    (click)="saveAsPublish()" data-target="#advLang" class="btn btn-success float-publish-btn">
    <i class="fa fa-check" aria-hidden="true"></i>Publish 
</button>

<p-toast key="expired-adv" position="bottom-right" baseZIndex="1" >
    <ng-template let-message pTemplate="message">
        <span>{{message.detail}}</span> &nbsp;
        <a routerLink="{{message.data}}" class="warning-msg-link">Browse similar jobs</a>
    </ng-template>
</p-toast>

<div class="container-fluid" style="font-family: 'Exo2-Regular', sans-serif !important;">
    <app-pre-loader [show]="showLoader"></app-pre-loader>

    <div class="CForm" *ngIf="!this.showLoader">
        <form class="form-horizontal validate-form">
            <!-- Adv header  -->
            <div id="header" class="row adv-header-desktop">
                <div class="col-sm-8 col-xs-9 page-title">
                    <h1>{{job_adv_title}}</h1>
                </div>
                <div *ngIf="otherEmployer === null">
                    <div class="col-sm-8 col-xs-9 left-col-preview-alignleft-1">
                        <p *ngIf="show_company_name">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" pTooltip="View company profile" class="company-name">
                                {{ company_name || " " }}
                            </a>
                            <img *ngIf="company_verified === 'Golden Verified'" src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner" class="verified-status" pTooltip="Gold Partner">
                            <img *ngIf="company_verified === 'Silver Verified'" src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner" class="verified-status" pTooltip="Silver Partner">
                            <img *ngIf="company_verified === 'Verified'" src="./assets/images/verified-company.svg" alt="Verified company" class="verified-status" pTooltip="Verified Company">
                        </p>
                        <p class="left-col-preview-alignleft-2"> {{ company_industry || " " }}</p>
                    </div>
                    <div class="col-sm-4 col-xs-3 left-col-preview-alignleft-2" style="margin-top: -60px;">
                        <div class="user-pro-pic">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" *ngIf="show_company_name">
                                <img *ngIf="image_uploaded_url" [src]="image_uploaded_url" alt="{{ company_name }} logo" class="img-responsive">
                            </a>
                            <img *ngIf="!show_company_name" src="./assets/images/Confidential-icon.png" class="img-responsive">
                            <p *ngIf="AdvType !== 'template' && AdvType !== 'draft'" style="font-size: 14px; color: #ccc;margin-top: 10px;" tooltipPosition="right">Published  {{ published_days_ago }} days ago</p>
                            <p *ngIf="(source==='post_job' || source==='manage_advs' )&& published_renewed_days_ago != null" style="font-size: 14px; color: #ccc;margin-top: 10px;" tooltipPosition="right">published/renewed {{ published_renewed_days_ago }} days ago</p>
                        </div>
                    </div>
                </div>
                <div *ngIf="otherEmployer !== null">
                    <div class="col-sm-8 col-xs-9 left-col-preview-alignleft-1">
                        <p *ngIf="otherEmployer.name" style="color:#007ad9">
                            {{ otherEmployer.name || " " }}
                        </p>
                        <p class="left-col-preview-alignleft-2" *ngIf="otherEmployer.industries"> {{ otherEmployer.industries || " " }}</p>
                    </div>
                    <div class="col-sm-4 col-xs-3 left-col-preview-alignleft-2" style="margin-top: -60px;">
                        <div class="user-pro-pic">
                            <img *ngIf="otherEmployer.logo !==null" [src]="otherEmployerLogo" class="img-responsive">
                            <p *ngIf="AdvType !== 'template' && AdvType !== 'draft'" style="font-size: 14px; color: #ccc;margin-top: 10px;" tooltipPosition="right">Published  {{ published_days_ago }} days ago</p>
                            <p *ngIf="(source==='post_job' || source==='manage_advs' )&& published_renewed_days_ago != null" style="font-size: 14px; color: #ccc;margin-top: 10px;" tooltipPosition="right">published/renewed {{ published_renewed_days_ago }} days ago</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="adv-header-mobile">
                <div class="company-div" *ngIf="otherEmployer === null">
                    <div class="logo">
                        <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" *ngIf="show_company_name">
                            <img *ngIf="image_uploaded_url" [src]="image_uploaded_url" alt="{{ company_name }} logo" class="img-responsive">
                        </a>
                        <img *ngIf="!show_company_name" src="./assets/images/Confidential-icon.png" class="img-responsive">
                    </div>
                    <div class="company-info">
                        <p *ngIf="show_company_name">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()"  class="company-name">
                                {{ company_name || " " }}
                            </a>
                            <img *ngIf="company_verified === 'Golden Verified'" src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner" class="verified-status">
                            <img *ngIf="company_verified === 'Silver Verified'" src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner" class="verified-status">
                            <img *ngIf="company_verified === 'Verified'" src="./assets/images/verified-company.svg" alt="Verified company" class="verified-status">
                        </p>
                        <p class="left-col-preview-alignleft-2"> {{ company_industry || " " }}</p>
                    </div>
                </div>
                <div class="company-div" *ngIf="otherEmployer !== null">
                    <div class="logo">   
                        <img *ngIf="otherEmployer.logo !==null" [src]="otherEmployerLogo" class="img-responsive">
                    </div>
                    <div class="company-info">
                        <p *ngIf="otherEmployer.name"  style="color:#007ad9;font-size:18px;">
                            {{otherEmployer.name || " " }}
                        </p>
                        <p class="left-col-preview-alignleft-2" *ngIf="otherEmployer.industries"> {{ otherEmployer.industries || " " }}</p>
                    </div>
                </div>
                <div class="page-title">
                    <h1>{{job_adv_title}}</h1>
                </div>
            </div>

            <!-- adv info -->
            <div class="job-details">

            <div style="position:relative;">
                <div class="published-mobile">
                    <p *ngIf="AdvType !== 'template' && AdvType !== 'draft'" style="font-size: 14px; color: #ccc;margin-top: 10px;" tooltipPosition="right">Published  {{ published_days_ago }} days ago</p>
                    <p *ngIf="(source==='post_job' || source==='manage_advs' )&& published_renewed_days_ago != null" style="font-size: 14px; color: #ccc;margin-top: 10px;" tooltipPosition="right">published/renewed {{ published_renewed_days_ago }} days ago</p>
                </div>
                <h2 class="title">Job Details</h2>
            </div>
            
            <div class="row con con1" pTooltip="Advertisement Info" tooltipPosition="left">
                <div class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-briefcase"></span>
                    <span> {{ compnay_exp_field || " " }}</span>
                </div>
                <div *ngIf="job_titles" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-briefcase"></span>
                    <span *ngFor="let jobTitle of job_titles; let i = index"> 
                        {{ jobTitle.name}}
                        <span *ngIf="i < job_titles.length - 1">, </span>
                    </span>
                </div>
                <div [hidden]="!location_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-map-marker"></span>
                    <span> {{ location_job || " " }}</span>
                </div>
                <div [hidden]="!emp_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-clock-o"></span>
                    <span> {{ employment_types || " " }}</span>
                </div>

                <div [hidden]="!money_job_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-money"></span>
                    <span> {{ money_job || " " }}</span>
                </div>
            </div>

            <div id="middle" class="row con con2" style="" pTooltip="Job Candidate Info" tooltipPosition="left">
                <div [hidden]="!year_exps_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-briefcase"></span>
                    <span> {{ year_exps || " " }}</span>
                </div>
                <div [hidden]="!lang_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="font-size: 1.1rem; color: #464a4c;" class="fa fa-language"></span>
                    <span> {{ languages || " " }}</span>
                </div>
                <div [hidden]="!age_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-birthday-cake"></span>
                    <span> {{ age || " " }}</span>
                </div>

                <div [hidden]="!nation_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-globe"></span>
                    <span> {{ nationality || " " }}</span>
                </div>

                <div [hidden]="!gender_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span *ngIf="gender === 'female'" style="color: #464a4c;font-size: 19px" class="fa fa-female"></span>
                    <span *ngIf="gender === 'male'" style="color: #464a4c;font-size: 20px" class="fa fa-male"></span>
                    <span style="text-transform: capitalize;"> {{ gender || " " }}</span>
                </div>
                <div [hidden]="!degree_level_major_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-graduation-cap"></span>
                    <span> {{ degree_level_major || " " }}</span>
                </div>

                <!-- <div [hidden]="!minor_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-graduation-cap"></span>
                    <span> {{ minor || " " }}</span>
                </div> -->
                <div [hidden]="!skill_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-cogs"></span>
                    <span> {{ skill_type || " " }}</span>
                </div>
                <div [hidden]="!certification_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-address-card-o"></span>
                    <span> {{ certification || " " }}</span>
                </div>

                <div [hidden]="!driving_exist" class="col-sm-4 col-xs-4 left-col-preview-alignleft-3">
                    <span style="color: #464a4c;" class="fa fa-car"></span>
                    <span> {{ driving || " " }}</span>
                </div>

            </div>
               
            </div>

            <!-- Job description section -->
            <div *ngIf="job_advertisement_translation">
                <h2 class="title" translate style="text-align: justify !important">
                    advr_preview.job_description
                </h2>
                <p 
                    [innerHTML]="job_advertisement_translation" class="editor-description">
                </p>
            </div>

     
            <div *ngIf="source === 'search-job' &&  appliedForJob !== null" class="apply-div text-center">
                <div *ngIf="post_Data['external_link']===null">
                    <button (click)="apply_for_job(this.AdvrIdSearch)" *ngIf="appliedForJob == false" style="border-bottom-right-radius: 5px;border-top-right-radius: 5px;"
                    class="publish btn apply"><i class="pi pi-check"></i> &nbsp;Apply</button>

                    <button *ngIf="appliedForJob == true"
                    class="publish btn applied" style="border-bottom-right-radius: 5px;border-top-right-radius: 5px;" disabled><i class="pi pi-check"></i> &nbsp;Applied</button>

                    <p-toast position="bottom-right"></p-toast>
                </div>
                <div *ngIf="post_Data['external_link']!==null">
                    <button (click)="apply_on_company_site(post_Data['external_link'])"  style="border-bottom-right-radius: 5px;border-top-right-radius: 5px;"
                    class="publish btn apply"><i class="fa fa-external-link" aria-hidden="true"></i> &nbsp;Apply</button>
                </div>
            </div>
     
            <div class="adv-separator">
                <span>This job advertisement posted by the following company</span>               
            </div>
            <br>

            <!-- About company section -->        
            <div *ngIf="otherEmployer === null" class="about-company">
                <div id="header" class="row adv-header-desktop">   
                    <div class="col-sm-8 col-xs-9 left-col-preview-alignleft-1">
                        <p *ngIf="show_company_name">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" pTooltip="View company profile" class="company-name">
                                {{ company_name || " " }}
                            </a>
                            <img *ngIf="company_verified === 'Golden Verified'" src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner" class="verified-status" pTooltip="Gold Partner">
                            <img *ngIf="company_verified === 'Silver Verified'" src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner" class="verified-status" pTooltip="Silver Partner">
                            <img *ngIf="company_verified === 'Verified'" src="./assets/images/verified-company.svg" alt="Verified company" class="verified-status" pTooltip="Verified Company">
                        </p>
                        <p class="left-col-preview-alignleft-2"> {{ company_industry || " " }}</p>
                    </div>
                    <div class="col-sm-4 col-xs-3 left-col-preview-alignleft-2">
                        <div class="user-pro-pic">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" *ngIf="show_company_name">
                                <img *ngIf="image_uploaded_url" [src]="image_uploaded_url" alt="{{ company_name }} logo" class="img-responsive">
                            </a>
                            <img *ngIf="!show_company_name" src="./assets/images/Confidential-icon.png" class="img-responsive">
                        </div>
                    </div>
                </div>
            
                <div class="adv-header-mobile">
                    <div class="company-div">
                        <div class="logo">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" *ngIf="show_company_name">
                                <img *ngIf="image_uploaded_url" [src]="image_uploaded_url" alt="{{ company_name }} logo" class="img-responsive">
                            </a>
                            <img *ngIf="!show_company_name" src="./assets/images/Confidential-icon.png" class="img-responsive">
                        </div>
                        <div class="company-info">
                            <p *ngIf="show_company_name">
                                <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()"  class="company-name">
                                    {{ company_name || " " }}
                                </a>
                                <img *ngIf="company_verified === 'Golden Verified'" src="./assets/images/cveek-gold-partner.svg" alt="CVeek Gold Partner" class="verified-status">
                                <img *ngIf="company_verified === 'Silver Verified'" src="./assets/images/cveek-silver-partner.svg" alt="CVeek Silver Partner" class="verified-status">
                                <img *ngIf="company_verified === 'Verified'" src="./assets/images/verified-company.svg" alt="Verified company" class="verified-status">
                            </p>
                            <p class="left-col-preview-alignleft-2"> {{ company_industry || " " }}</p>
                        </div>
                    </div>
                </div>

                <div *ngIf="company_description !== null" class="noParagraphMarginB" style="padding-bottom: 8px;">
                    <p style="font-size: 16px; text-align: justify !important; " *ngIf="company_description !== null && company_description.length < 400" [innerHTML]="company_description"></p>
    
                    <div style="font-size: 16px; text-align: justify !important;vertical-align: top" *ngIf="company_description !== null && company_description.length >= 400">
                        <span class="firstDescPart" *ngIf="company_description !== null && company_description.length >= 400 && showShortDesciption == true" innerHTML="{{company_description  | slice:0:400}}  ..."></span>
                        <span *ngIf="showShortDesciption == false" [innerHTML]="company_description" class="editor-description"></span>
                        <span *ngIf="company_description !== null && company_description.length >= 400" class="blauen" type="button" (click)="alterDescriptionText()">
                            {{ showShortDesciption ? 'read more': 'read less' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- About recruitment company  -->        
            <div *ngIf="otherEmployer !== null" class="about-company"> 
                <div id="header" class="row adv-header-desktop">                   
                    <div class="col-sm-8 col-xs-9 left-col-preview-alignleft-1">
                        <p>
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()" pTooltip="View company profile" class="company-name">
                                {{ company_name || " " }}
                            </a>
                            <span class="verification-status">
                                <img *ngIf="company_verified === 'Golden Verified'" src="./assets/images/icons/cveek-gold-partner-recruiting-company.svg" alt="CVeek gold partner recruiting company" pTooltip="Verified recruiting company" tooltipStyleClass="wide-tooltip">
                                <img *ngIf="company_verified === 'Silver Verified'" src="./assets/images/icons/cveek-silver-partner-recruiting-company.svg" alt="CVeek silver partner recruiting company" pTooltip="Verified recruiting company" tooltipStyleClass="wide-tooltip">
                                <img *ngIf="company_verified === 'Verified'" src="./assets/images/icons/verified-recruiting-company.svg" alt="Verified recruiting company" pTooltip="Verified recruiting company" tooltipStyleClass="wide-tooltip">
                                <img *ngIf="company_verified === null"  src="./assets/images/icons/recruiting-company.svg" alt="Recruiting company" pTooltip="Recruiting company">
                            </span>
                        </p>
                        <p class="left-col-preview-alignleft-2"> {{ company_industry || " " }}</p> 
                    </div>
                    <div class="col-sm-4 col-xs-3 left-col-preview-alignleft-2">
                        <div class="user-pro-pic">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()">
                                <img *ngIf="image_uploaded_url" [src]="image_uploaded_url" alt="{{ company_name }} logo" class="img-responsive">
                            </a>
                        </div>
                    </div>
                </div>

                <div class="adv-header-mobile">
                    <div class="company-div">
                        <div class="logo">
                            <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()">
                                <img *ngIf="image_uploaded_url" [src]="image_uploaded_url" alt="{{ company_name }} logo" class="img-responsive">
                            </a>
                        </div>
                        <div class="company-info">
                            <p>
                                <a [routerLink]="['/i/c', companyUsername ]" (click)="closeModal()"  class="company-name">
                                    {{ company_name || " " }}
                                </a>
                                <span class="verification-status">
                                    <img *ngIf="company_verified === 'Golden Verified'" src="./assets/images/icons/cveek-gold-partner-recruiting-company.svg" alt="CVeek gold partner recruiting company">
                                    <img *ngIf="company_verified === 'Silver Verified'" src="./assets/images/icons/cveek-silver-partner-recruiting-company.svg" alt="CVeek silver partner recruiting company">
                                    <img *ngIf="company_verified === 'Verified'" src="./assets/images/icons/verified-recruiting-company.svg" alt="Verified recruiting company">
                                    <img *ngIf="company_verified === null"  src="./assets/images/icons/recruiting-company.svg" alt="Recruiting company">
                                </span>
                            </p>
                            <p class="left-col-preview-alignleft-2"> {{ company_industry || " " }}</p>
                        </div>

                    </div>                     
                </div>

                <div class="noParagraphMarginB" style="padding-bottom: 8px;">
                    <p style="font-size: 16px; text-align: justify !important; " *ngIf="company_description !== null && company_description.length < 400" [innerHTML]="company_description"></p>
    
                    <div style="font-size: 16px; text-align: justify !important;vertical-align: top" *ngIf="company_description !== null && company_description.length >= 400">
                        <span class="firstDescPart" *ngIf="company_description !== null && company_description.length >= 400 && showShortDesciption == true" innerHTML="{{company_description  | slice:0:400}}  ..."></span>
                        <span *ngIf="showShortDesciption == false" [innerHTML]="company_description" class="editor-description"></span>
                        <span *ngIf="company_description !== null && company_description.length >= 400" class="blauen" type="button" (click)="alterDescriptionText()">
                            {{ showShortDesciption ? 'read more': 'read less' }}
                        </span>
                    </div>
                </div>

            </div>


            <!-- Adv Statistics and apply to job section -->
            <div class="row" style="margin-bottom: 20px;">
                <div class="col-sm-12 col-xs-8 focus-no-padding-button" id="Add" style="margin-top:20px">
                    <div class="row">
                        <!-- <div class="col-sm-12"> -->
                            <div class="col-sm-8" *ngIf="(source==='post_job' || source==='manage_advs' ) && cv_read_count != undefined && AdvType !== 'draft'">
                                <div class="row" style="background-color: white !important; ">
                                    <span class="col-sm-6 title" translate>About Adv</span>
                                </div>
                                <div class="row col-sm-6 adv-statistics" style="background-color: white !important; margin-top:3px; margin-left:5px">
                                    <div class="row" style="margin-top:20px">
                                        <span>Received:</span>
                                        <span style="color:blue"> {{ cv_received_count }}</span>
                                    </div>
                                    <div class="row" style="margin-top:20px">
                                        <span>Seen:</span>
                                        <span style="color:green"> {{ cv_read_count }}</span>
                                    </div>
                                    <div class="row" style="margin-top:20px">
                                        <span>Auto Rejected:</span>
                                        <span style="color:red"> {{ cv_rejected_count }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <!-- <div *ngIf="source==='post_job' || source === 'reload'">
                                    <button (click)="saveAsPublish()" data-target="#advLang" class="publish btn finish"><i class="pi pi-check"></i> &nbsp;Publish </button>
                                </div>
                                <span *ngIf="source==='post_job' || source === 'reload'" class="dropup">
                                    <button type="button"
                                    class="btn finish1 btn-fa-edit dropdown-toggle"
                                    type="button" id="dropdownMenuButton"
                                        data-toggle="dropdown" aria-haspopup="true
                                        " aria-expanded="false">

                                        <span class="pi pi-angle-down" ></span>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                    <li><a style="cursor:pointer" class="dropdown-item" (click)="saveAsTemplate()" translate>advr_preview.save_as_template</a></li>
                                    <li><a style="cursor:pointer" class="dropdown-item" id="draft" [ngClass]="{ 'disabled': template}" (click)="saveAsDraft()" translate>advr_preview.save_as_draft</a></li>
                                    <li><a style="cursor:pointer" class="dropdown-item" data-toggle="modal" data-target="#advForm" (click)="Display_google_form()" translate>post_advr.add_job_application</a></li>
                                    <li><a style="cursor:pointer" class="dropdown-item" (click)="edit()" translate>Edit</a></li>
                                </ul>
                                </span> -->

                                <!-- commented code for share save report -->

                                <!-- <span *ngIf="source === 'search-job' && appliedForJob != null" class="dropup">
                                    <button class="btn finish1 btn-fa-edit dropdown-toggle" type="button" id="dropdownMenuButton"
                                    data-toggle="dropdown" aria-haspopup="true
                                    " aria-expanded="false">
                                        <i class="pi pi-angle-down
                                        " aria-hidden="true"></i>
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                        <li> <a style="cursor:pointer" class="dropdown-item" translate>Save</a></li>
                                        <li> <a style="cursor:pointer" class="dropdown-item" (click)="visibleSidebar4 = true"
                                        translate>Share</a></li>
                                        <li><a style="cursor:pointer" class="dropdown-item" translate>Report</a></li>

                                    </ul>
                                </span> -->
                                <div *ngIf="source==='manage_advs'">
                                        <button (click)="edit(AdvrIdSearch)" class="publish btn finish"><i class="pi pi-check"></i>Edit</button>
                                    <!-- <button (click)="fullActionAdvrsPreview('edit', AdvrIdSearch)" class="publish btn finish"><i class="pi pi-check"></i>Edit</button> -->
                                </div>
                        
                                <span *ngIf="AdvType==='published'" class="dropup">
                                    <button type="button" class="btn finish1 btn-fa-edit dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="pi pi-angle-down
                                        " aria-hidden="true"></i>
                                    </button>
                                    <ul class="dropdown-menu adv-actions" aria-labelledby="dropdownMenu2" >
                                        <li> <a style="cursor:pointer" class="dropdown-item"
                                            (click)="fullActionAdvrsPreview('renew', AdvrIdSearch)">
                                            <i class="fa fa-refresh" style="cursor: pointer; color: gold;">
                                            <!--  *ngIf="renew_count <= 5 && renew_count < 3 && data.active_status"  -->
                                            </i><span translate>&nbsp;Renew</span>
                                </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('refreshListing', AdvrIdSearch)">
                                        <i style="color: teal;" class="fa fa-undo" aria-hidden="true"></i><span translate>&nbsp;Refresh Listing</span>
                                    </a>
                                </li>


                                <li>
                                    <a (click)="fullActionAdvrsPreview('activate',AdvrIdSearch)" class="dropdown-item" style="cursor:pointer">
                                        <i class="fa fa-power-off" style="color: #4CAF50;" aria-hidden="true">
                                            </i>
                                        <span  translate>&nbsp;manage_advs.activate_deactivate</span>
                                    </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('endWorkFlow', AdvrIdSearch)">
                                        <i class="fa fa-times-circle" style="color: #C63720; " aria-hidden="true">
                                            </i><span translate>&nbsp;manage_advs.end_work_flow</span>
                                    </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('saveAsTemplate', AdvrIdSearch)">
                                        <i class="fa fa-save" style="color: black;" aria-hidden="true">
                                            </i><span translate>&nbsp;Save as Template</span>
                                    </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item"  (click)="fullActionAdvrsPreview('displayAdvrLogModal', AdvrIdSearch)">
                                        <!-- (click)="fullActionAdvrsPreview('save_template_for_adv', AdvrIdSearch)" -->
                                        <i class="fa fa-info-circle" pTooltip="Log" style="color:#30457c;"></i>
                                        <span translate>&nbsp;Adv Log</span>
                                    </a>
                                </li>



                                </ul>
                                </span>

                                <span *ngIf="AdvType==='expired'" class="dropup">
                                        <button type="button" class="btn finish1 btn-fa-editropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" >
                                            <i class="pi pi-angle-down
                                            " aria-hidden="true"></i>
                                        </button>

                                        <ul class="dropdown-menu adv-actions" >

                                            <li> <a style="cursor:pointer" class="dropdown-item"
                                                (click)="fullActionAdvrsPreview('renew', AdvrIdSearch)">
                                                <i class="fa fa-refresh" style="cursor: pointer; color: gold; font-weight: bold;">
                                                <!--  *ngIf="renew_count <= 5 && renew_count < 3 && data.active_status"  -->
                                                </i><span translate>&nbsp;Renew</span>
                                </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('saveAsTemplate', AdvrIdSearch)">
                                        <i class="fa fa-save" style="color: black" aria-hidden="true">
                                                </i><span translate>&nbsp;Save as Template</span>
                                    </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('displayAdvrLogModal', AdvrIdSearch)">
                                        <!-- (click)="fullActionAdvrsPreview('save_template_for_adv', AdvrIdSearch)" -->
                                        <i class="fa fa-info-circle" pTooltip="Log" style="color:#30457c;"></i>
                                        <span translate>&nbsp;Adv Log</span>
                                    </a>
                                </li>

                                </ul>
                                </span>

                                <span *ngIf="AdvType==='draft'" class="dropup">
                                    <button type="button" class="btn finish1 btn-fa-edit dropdown-toggle" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" >
                                        <i class="pi pi-angle-down
                                        " aria-hidden="true"></i>
                                    </button>

                                    <ul class="dropdown-menu adv-actions">
                                        <li><a (click)="fullActionAdvrsPreview('saveAsPublish', AdvrIdSearch)" style="cursor:pointer;">
                                            <i class="fa fa-save" aria-hidden="true" style="color: black" ></i>
                                            <span translate>&nbsp;Publish</span>
                                </a>
                                </li>

                                <li>
                                    <a (click)="fullActionAdvrsPreview('deleteAdvr', AdvrIdSearch)" style="cursor:pointer" class="dropdown-item">
                                        <i class="fa fa-trash" aria-hidden="true" style="color: red;">
                                            </i><span translate>&nbsp;Delete</span>
                                    </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('saveAsTemplate', AdvrIdSearch)">
                                        <i class="fa fa-save" style="color: black" aria-hidden="true">
                                            </i><span translate>&nbsp;Save as Template</span>
                                    </a>
                                </li>

                                <li>
                                    <a style="cursor:pointer" class="dropdown-item" (click)="fullActionAdvrsPreview('displayAdvrLogModal', AdvrIdSearch)">
                                        <!-- (click)="fullActionAdvrsPreview('save_template_for_adv', AdvrIdSearch)" -->
                                        <i class="fa fa-info-circle" pTooltip="Log" style="color:#30457c;"></i>
                                        <span translate>&nbsp;Adv Log</span>
                                    </a>
                                </li>

                                </ul>
                                </span>

                                <span *ngIf="AdvType==='template'" class="dropup">
                                    <button type="button" class="btn finish1 btn-fa-editdropdown-toggle" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" >
                                        <i class="pi pi-angle-down" aria-hidden="true"></i>
                                    </button>

                                    <ul class="dropdown-menu adv-actions">
                                        <li><a dropdown-item (click)="fullActionAdvrsPreview('saveTemplateAsPublish', AdvrIdSearch)" style="cursor:pointer;">
                                            <i class="fa fa-save" aria-hidden="true" style="color: black" ></i>
                                            <span translate>&nbsp;Publish</span>
                                </a>
                                </li>

                                <li>
                                    <a dropdown-item (click)="fullActionAdvrsPreview('delete_temp', AdvrIdSearch)" style="cursor:pointer">
                                        <i class="fa fa-trash" aria-hidden="true" style="color: red;">
                                            </i><span translate>&nbsp;Delete</span>
                                    </a>
                                </li>

                                <li>
                                    <a dropdown-item style="cursor:pointer" (click)="fullActionAdvrsPreview('displayAdvrLogModal', AdvrIdSearch)">
                                        <!-- (click)="fullActionAdvrsPreview('save_template_for_adv', AdvrIdSearch)" -->
                                        <i class="fa fa-info-circle" pTooltip="Log" style="color:#30457c;"></i>
                                        <span translate>&nbsp;Adv Log</span>
                                    </a>
                                </li>

                                </ul>
                                </span>
                            </div>

                        <!-- </div> -->
                    </div>
                    <!-- <script async defer crossorigin="anonymous"
                                    src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v8.0" nonce="X2dLfwRx"></script>

                                    <iframe src="https://www.facebook.com/plugins/share_button.php?href=https%3A%2F%2Fdevelopers.facebook.com%2Fdocs%2Fplugins%2F&layout=button_count&size=large&width=110&height=28&appId"
                                    width="110" height="28"
                                    style="border:none ; overflow:hidden" scrolling="no"
                                    frameborder="0" allowTransparency="true" allow="encrypted-media">
                                    <img src="./assets/images/Social Media/LinkedIn.png" alt=""/></iframe> -->
                                    
                    <!-- <p-sidebar [(visible)]="visibleSidebar4" [dismissible]="false" position="bottom">
                        <h1 style="font-weight:normal;margin-bottom:15px">Share on Social Media</h1>
                        <div class="row custom-row">
                            <div class="col-sm-12">
                                <div class="col-sm-2 col-sm-offset-4">
                                    <a href="https://www.facebook.com/sharer/sharer.php?app_id=1460941267346567&sdk=joey&u=https://chillyfacts.com/create-facebook-share-button-for-website-webpages/&display=popup&ref=plugin&src=share_button" onclick="return !window.open(this.href, 'Facebook', 'left=20,top=20,width=640,height=580')">
                                        <img src="./assets/images/Social Media/Facebook.png"></a>
                                </div>
                                <div class="col-sm-2">
                                    <a href="https://www.linkedin.com/shareArticle?mini=true&url=https://chillyfacts.com/create-linkedin-share-button-on-website-webpages&title=Create LinkedIn Share button on Website Webpages&summary=chillyfacts.com&source=Chillyfacts" onclick="window.open(this.href, 'mywin',
                                    'left=20,top=20,width=640,height=580,toolbar=1,resizable=0'); return false;">
                                        <img src="./assets/images/Social Media/LinkedIn.png" alt="" /></a>
                                </div>
                                <div class="col-sm-2">
                                    <a href="whatsapp://send?text=https://chillyfacts.com/run-command-prompt-cmd-commands-from-java/" data-action="share/whatsapp/share">
                                        <img src="./assets/images/Internet_communication/Whatsapp.png" alt="" /></a>
                                </div>
                            </div>
                        </div>
                    </p-sidebar> -->
                </div>
               
            </div> 

            <p class="adv-footer">
                Cveek does Not have information about the validity of the advertisements.
                If you have any suspicions, feel free and 
                <a class="contact" (click)="displayContactModal()">contact us</a>. 
            </p>
        </form>
    </div>


    <!-- <div class="modal fade" id="advForm" *ngIf="display_google_form" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" (click)="closeModal_google_form()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModal2Label">Add Application From</h4>
                </div>
                <full-form-customer-generator>
                </full-form-customer-generator>
            </div>
        </div>
    </div> -->

    <div class="modal fade" id="advLang" *ngIf="display_advr_lang" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document" *ngIf="passed_Data_to_translated">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" (click)="closeModal_advr_lang()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModal2Label">Choose another language for company advertisement info</h4>
                </div>
                <advr-lang [companyAdvId]="companyAdvId" [passed_Data_to_translated]="passed_Data_to_translated" (closeModalPopup)="handlePopup($event)">
                </advr-lang>
            </div>
        </div>
        <!--  <div class="modal-dialog" role="document" *ngIf="!passed_Data_to_translated">
            <div class="modal-content">
                <span>
                    Your advertisement is template can't be publish at the moment!...
                    Please check manage advertisement
                </span>
            </div>
        </div> -->
    </div>


    <div class="modal fade" id="authModal" tabindex="-1" role="dialog" aria-labelledby="authLabelModal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="authLabelModal" translate>Sign In</h4>
                </div>
                <login [fromPage]="'advr-preview'"></login>
            </div>
        </div>
    </div>

</div>
