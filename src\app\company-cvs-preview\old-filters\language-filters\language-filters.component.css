/* unify placeholer style for all different controls */
::placeholder,
 :host ::ng-deep .ui-dropdown label.ui-dropdown-label,
:host ::ng-deep .ui-autocomplete ::placeholder {
    /* ::placeholder Chrome, Firefox, Opera, Safari 10.1+ */
    color: #808080;
    opacity: 1;
    /* Firefox */
    font-weight: normal;
    font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
    font-size: 15px !important;
}

.tag-label {
    color: #808080;
    background-color: #f2f2f2;
    padding: 3px;
    margin-left: 5px !important;
}
.equal-height-row-cols{
    display: flex;
    flex-flow: row wrap;
}
.label-fixed{
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    color:#4f94df;
    font-weight: normal;
}
.label-fixed-dd{
    align-items: center;
}
@media screen and (max-width:767px){
    .focus-no-padding{
        padding-left: 15px !important;
    }
    .label-fixed{
        justify-content: flex-start;
    }
    :host ::ng-deep .ui-autocomplete .ui-inputtext {
        height: 32px;
        margin-top: 0px;
    }
    .add-item{
        margin-top: 20px;
    }
}

.top-tools{
    display: flex;
    justify-content: center;
    background: #a9f1ac;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 5px;
    align-content: center;
    align-items: center;
}

.top-tools .form-group{
    margin: 0px 1.1rem;
}


.filters-buttons{
    display: flex;
    flex-wrap: nowrap;
    align-content: space-between;
    justify-content: center;
    align-items: center;
}
