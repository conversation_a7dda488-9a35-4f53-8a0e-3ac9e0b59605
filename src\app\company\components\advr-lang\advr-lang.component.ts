import { Component, OnInit, Input } from '@angular/core';
import { PostJobService } from "app/company/services/post-job.service";
import { FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
declare var $: any;
@Component({
  selector: 'advr-lang',
  templateUrl: './advr-lang.component.html',
  styleUrls: ['./advr-lang.component.css']
})
export class AdvrLangComponent implements OnInit {
  @Input('companyAdvId') companyAdvId : any;
  @Input('passed_Data_to_translated') passed_Data_to_translated : any;
  languages = [];
  languageForm;
  username;
  constructor(
    private postJobService: PostJobService,
    private  fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
  ) { 

    this.route.parent.params.subscribe(res => {
      this.username = res['username'];

    });

    this.languageForm = this.fb.group({
      translated_languages_id : [ '']
    });
   }



/*--------- get the available languages ------------*/


  buildFilledForm() {
  
    this.postJobService.getOtherLanguages(this.companyAdvId).subscribe(
      (res: any[]) => {
        
        for( let i = 0 ; i < res.length ; i++){
          this.languages.push({
            'translated_language_id' : res[i].id,
            'language': res[i].name
          })
        }
        this.languages.unshift({'translated_language_id': '', 'language':''});
    
      });

      

     
  }



  ngOnInit() {
  this.buildFilledForm();

  }

  /*-------- send avertisement's data with the selected langauge's ID ------ */

  submit() {
    
    this.passed_Data_to_translated.push({
      'translated_language_id':this.languageForm.value.translated_languages_id.translated_language_id
    })


    this.postJobService.send_Data(this.passed_Data_to_translated);
    $('#advLang').modal('hide');
    this.router.navigate(['/c',this.username,'post-job']);
    

  }

}
