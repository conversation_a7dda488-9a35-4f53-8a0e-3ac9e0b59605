import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable()
export class VerificationService {
  url = '';
  logUrl = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.url = data + 'admin/verify/';
          this.baseUrl = data ;
    });
   }

  getAllTransactions() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + 'values/get_all_action_logs' , { headers });
  }

  getTransaction(id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url  + id, { headers });
  }

  getLogData() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + 'values/show_admin_verification_log', { headers });
  }

  getSpecificLog(id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + 'admin_verification_log/' + id, { headers });
  }


  saveTransaction(trans, transId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + 'values/save_transaction/' + transId, trans, { headers });
  }

  endTransaction( transId: { 'system_action_log_id': number }) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + 'values/end_transaction' , transId, { headers });
  }

  addNewSkillCategory(skillCatTrans) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + 'values/add_new_skill_category' , skillCatTrans, { headers });
  }

  addNewMajorParent(parentTrans) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + 'values/add_new_major_parent' , parentTrans, { headers });
  }

  addNeExperienceField(fieldTrans) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + 'values/add_new_experience_field' , fieldTrans, { headers });
  }



}
