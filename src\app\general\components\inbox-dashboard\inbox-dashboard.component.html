<page-navbar [navType]="'empty'"></page-navbar>

<div class="article-container">
    <h1>Inbox Dashboard</h1>

    <div class="article-img text-center">
        <img src="./assets/images/footer-pages/inbox-dashboard.webp" class="img-responsive">
    </div>

    <div class="section">
        <h2>CVeek Candidate Inbox &ndash; Smart Resume Management, Reinvented</h2>

        <p>Welcome to the <strong>heart of CVeek&rsquo;s platform</strong> &mdash; where <strong>job posting meets intelligent candidate tracking</strong>.<br />
        With the <strong>CVeek Applicant Inbox</strong>, your resume review process becomes <strong>organized, streamlined, and highly efficient</strong>.</p>
        
        <h3>A Smart Resume Inbox Built for Recruiters:</h3>
        
        <p>Imagine an interface as simple as Gmail, powered by smart recruitment logic.<br />
        <strong>Every CV is automatically sorted and displayed where it belongs &mdash; no more clutter, no missed applications.</strong></p>
        
        <ul>
            <li><strong>Main Inbox</strong>: View all resumes submitted across your job listings.</li>
            <li><strong>Job-Specific Folders</strong>: Applications are auto-assigned to the job they apply for.</li>
            <li><strong>Auto-Reject Folder</strong>: Non-qualifying resumes are filtered out automatically.</li>
            <li><strong>Favorites Folder</strong>: Save top candidates for follow-up with a single click.</li>
            <li><strong>Trash Bin</strong>: Remove irrelevant applications, with the ability to recover when needed.</li>
        </ul>
        
        <h3>Fully Customizable Hierarchical Structure:</h3>
        
        <p>Build folders by department, role, hiring stage (screening &ndash; interview &ndash; hired), or however your process flows.<br />
        <strong>Unlimited structure, total control.</strong></p>
        
        <h3>Advanced Filtering &amp; Candidate Search:</h3>
        
        <p>Search and filter resumes by:<br />
        ✔️ Years of experience<br />
        ✔️ Academic qualifications<br />
        ✔️ Skills and languages<br />
        ✔️ Location<br />
        ✔️ Any field in your custom application form.<br />
        Sort results ascending/descending to find top-fit candidates fast.</p>
        
        <h3>Tailored Resume Display:</h3>
        
        <p>Select the columns you want to see: name, title, experience, email, status, etc.<br />
        <strong>Show only what matters to you</strong> &mdash; customizable, streamlined, recruiter-friendly.</p>
        
        <p>With <strong>CVeek Inbox</strong>, handling large volumes of applicants becomes a precise and enjoyable task.<br />
        No more inbox chaos or lost attachments &mdash; just a <strong>powerful, structured, and searchable resume management system</strong>.</p>
        
        <p>This isn&rsquo;t just an inbox &mdash; <strong>it&rsquo;s your recruitment command center</strong>.</p>
        
        <p>Try <strong>CVeek Inbox</strong> now and turn resume overload into structured success.</p>
        
    </div>

    <div class="section text-center page-link">
        <h2>Check our Inbox Dashboard page</h2>
        <a *ngIf="username ===null" routerLink="/m/company/login">Inbox Dashboard</a>
        <a *ngIf="username !==null" [routerLink]="['/c', 'cvs', username]">Inbox Dashboard</a>
    </div>
</div>
