import { NgModule } from '@angular/core';
import { RouterModule , Routes } from '@angular/router';
import { LoginComponent } from './components/user/login/login.component';
import { LoginAuthGuardService } from 'shared/shared-services/login-auth-guard.service';
import {AccountVerificationComponent} from './components/user/account-verification/account-verification.component';
import {SignUpComponent} from './components/user/sign-up/sign-up.component';
import {ResetPasswordVerificationComponent} from './components/user/reset-password-verification/reset-password-verification.component';
import {ResetPasswordComponent} from './components/user/reset-password/reset-password.component';
import {MembershipWrapperComponent} from './components/membership-wrapper/membership-wrapper.component';
import {ResetPasswordCompanyComponent} from './components/company/reset-password/reset-password-company.component';
import {ResetPasswordVerificationCompanyComponent} from
    './components/company/reset-password-verification/reset-password-verification-company.component';
import {AccountVerificationCompanyComponent} from './components/company/account-verification/account-verification-company.component';
import {LoginCompanyComponent} from './components/company/login/login-company.component';
import {SignUpCompanyComponent} from './components/company/sign-up/sign-up-company.component';
import { LoginAdminComponent } from './components/admin/login/login.component';
import { ForgetPasswordComponent } from './components/user/forget-password/forget-password.component';
import { ForgetPasswordCompanyComponent } from './components/company/forget-password-company/forget-password-company.component';

const routes: Routes = [
  {
    path: '', component: MembershipWrapperComponent, children: [
      {path: 'user/login', component: LoginComponent},
      {path: 'user/sign-up', component: SignUpComponent},
      {path: 'user/verification', component: AccountVerificationComponent},
      {path: 'user/forget-password', component: ForgetPasswordComponent},
      {path: 'user/reset-password-verification', component: ResetPasswordVerificationComponent},
      {path: 'user/reset-password', component: ResetPasswordComponent},
      {path: 'company/login', component: LoginCompanyComponent},
      {path: 'company/sign-up', component: SignUpCompanyComponent},
      {path: 'company/verification', component: AccountVerificationCompanyComponent},
      {path: 'company/forget-password', component: ForgetPasswordCompanyComponent},
      {path: 'company/reset-password-verification', component: ResetPasswordVerificationCompanyComponent},
      {path: 'company/reset-password', component: ResetPasswordCompanyComponent},
      {path: 'adm/login', component: LoginAdminComponent},
    ]},
]

@NgModule({
    imports: [ RouterModule.forChild(routes) ],
    exports: [ RouterModule ]
})
export class MembershipRoutingModule { }
