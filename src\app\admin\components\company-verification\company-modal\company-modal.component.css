.modal-body-container {
  height: fit-content;
}


.modal-body{
  height: fit-content;
  max-height: unset !important;
  overflow: unset;
}

img {
  float: leftt !important;
  position: absolute;
  width: 100px;
  height: 100px;
  /* border: 1px solid darkblue; */
  border-radius: 50px;
}



.control-view,.fa.fa-angle-down , .fa.fa-angle-up {
  color: dodgerblue;
  cursor: pointer;

}

.btn.btn-done.submit-button {
  padding: 15px 45px;
  border-radius: 80px;
  background-color: darkblue;
  margin-left: 450px;
  color:white;
}

.btn.btn-done.submit-button:hover {
background-color: rgb(12, 12, 185);
}

.btn.btn-done.submit-button[disabled] {
background-color: #050588c9;
}

.btn.btn-default {
padding: 15px 25px;
border-radius: 80px;
}

.btn {
margin-bottom: 10px;
margin-top: 30px;
margin-right: 10px;
}

input {
border: none;
border-bottom: 1px dashed #bbb;
background:transparent;
width: 100%;
transition: all 0.5s ease-in-out;
/* overflow: auto; */
}

input:focus {
border: none !important;
border-bottom: 1px solid dodgerblue;
}

input.ng-dirty.ng-touched.ng-invalid {
border-radius: 5px;
border: 2px solid #ff000059;
background-color: #ffeaea66 ;
}

.alert.alert-danger {
float: left;
padding:5px;
width: 250px;
margin-left: 5px;
margin-right: 5px;
}



.alert.alert-danger.inline-alert {
padding: 0px;
padding-left: 5px;
margin: 0px;
/* margin-top: -20px; */
background-color: transparent;
border: none;
margin-right: -270px;
}

.under-control{
  margin-top: 45px !important;
}

.table>tbody>tr>td, .table>tbody>tr>th, .table>thead>tr>th {
border-top: none;
border-bottom: none;;
}



td {
color: #777;
width: 250px;
font-size: 1.1em;
}


.fa.fa-plus {
color: #276ea4;
padding-right: 5px;
}

.alert.alert-danger {
padding: 0px;
padding-left: 5px;
margin: 0px;
background-color: transparent;
border: none;
}




.table>tbody>tr>td,.table>tbody>tr>th, .table>tfoot>tr>td,
.table>tfoot>tr>th, .table>thead>tr>td,  .table>thead>tr>th {
    vertical-align: middle;
}

.badge.badge-primary {
min-width: 10px;
padding: 3px 10px;
font-size: 14px;
background-color: #393cab30;
color: midnightblue;
border-radius: 10px;
border: 2px solid #dddddd;
/* box-shadow: 0px 0px 2px 1px #aaa; */
}

.action{
border: 2px solid #f0f0f0;
border-radius: 5px;
text-align: center;
margin-left: 100px;
margin-right: 100px;
padding: 7px;
margin-bottom: 10px;
background-color: #f9f9f9;
font-size: 17px;
}


.action .badge.badge-primary{
padding: 3px 40px;
font-size: 17px;
background-color: #28a92859;
color: forestgreen;
border: none;
border-radius: 10px;
}

.action .badge.badge-default{
padding: 3px 40px;
font-size: 17px;
background-color: #a09a9a69;
color: #777;
border: none;
border-radius: 10px;
}


.table-caption {
font-weight: bold;
font-size: 1.2em;

}




.table tbody th, .table  tr th, .table  thead tr th   {
  color: midnightblue;
  text-align: right;
  width: 100px;
  font-weight: bolder;
  font-size: 1.2em;
}

.table{
/* border: 2px solid #e9e9e9; */
margin-left: 50px;
margin-right: 10px;
width: 90%;
margin-top: 25px;
}


.table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > th{
  max-width: 300px;
  }

.table tr.stared {
  background-color: aliceblue;
  border: 1px solid darkblue;

}

.btn-light.btn-assign {
background-color:#80008029;
color: purple;
margin-bottom:0px;
margin-top: 0px;
}

.btn-light.btn-comment {
background-color: #90ee9073;
color: green;
margin-bottom:0px;
margin-top: 0px;
}

.btn-light.btn-done {
background-color:#add8e6ad;
color: #0606bd;
margin-bottom:0px;
margin-top: 0px;
}


table#msg-details {
margin-top: 0px;
}

.fa.fa-user, .fa-check, .fa-comment, .fa.fa-paper-plane, .fa.fa-download {
padding-right: 5px;
}

.ui-panel-content.ui-widget-content  {
width: 870px;
margin: auto;
margin-left: 120px;
margin-bottom: 0px;
font-size: 15px;

}




.table > tbody > tr > td,
.table > tbody > tr > th,
.table > tfoot > tr > td,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > thead > tr > th {
border: none;
}


:host ::ng-deep .ui-panel-titlebar.ui-widget-header.ui-helper-clearfix.ui-corner-all {
  background-color: #f9f9f9;
  /* border: 1px solid #c5c5c5; */

}

:host ::ng-deep .ui-panel-title {
font-family: 'Exo2-Regular', sans-serif;
color: midnightblue;
font-weight: bold;

}


:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content {
font-family: 'Exo2-Regular', sans-serif;
color: #666;
font-size: 1em;
}

:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content h5 {
font-family: 'Exo2-Regular', sans-serif;
color: #333;
font-weight: bold;
}

:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content sub {
color: #aaa;
}

:host ::ng-deep .ui-dropdown label.ui-dropdown-label {
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 16px;

}


.log-table th.text-left {
  text-align: left !important;
}


td span p {
  margin-bottom: 0px;
}

p {
 margin-bottom: 0px;
}
.check-show-home{
  width: 32px;
  height: 32px;
  margin-left: 35px;
}