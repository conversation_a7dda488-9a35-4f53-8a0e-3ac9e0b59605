import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable()
export class CompanyService {
  url = '';
  tempUrl = '';
  archUrl = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.baseUrl = data + 'admin';
          this.url     = data + 'admin/company_verification';
    });
  }


  getAllCompanyData() {
      let headers = new HttpHeaders().set('Content-Type', 'application/json');
      return this.http.get(this.url + '/all_data' , { headers });
  }

  getCompanyInfo(comId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/company_info/' + comId , { headers });
}

  getAllCompanies() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/all_companies' , { headers });
}

  getCompanyLog(comId) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/log/' + comId , { headers });
  }

  getDropDownsData() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/data_procedure' , { headers });
  }

  updateCompanyStatus(status) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/status', status, { headers });
  }
  setPluginUrl(status) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post( this.url + '/set_plugin_url', status, { headers });
  }
  showCompanyInHomePage(id,showStatus){
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/show_in_home/'+id, showStatus, { headers });
  }

  // {
  //   "company_id":36,
  //   "admin_description":"good",
  //   "company_method_verification_id":1,
  //   "company_status_id":1
  // }

}


// {
//   "company_status": [
//       {
//           "id": 1,
//           "title": "verified"
//       },
//       {
//           "id": 2,
//           "title": "Not Verified"
//       },
//       {
//           "id": 3,
//           "title": "Seems Fake"
//       },
//       {
//           "id": 4,
//           "title": "fake"
//       },
//       {
//           "id": 5,
//           "title": "Not checked"
//       }
//   ],
//   "company_method_verification": [
//       {
//           "id": 1,
//           "method_verification_name": "google"
//       },
//       {
//           "id": 2,
//           "method_verification_name": "visit to company"
//       }
//   ]
// }


// {
//   "company_info": {
//       "id": 1,
//       "company_id": 36,
//       "company_website": "aa.com",
//       "company": {
//           "id": 36,
//           "user_id": 5,
//           "main_language_id": 1,
//           "advs_refresh_count": 0,
//           "refresh_advs_listing_at": null,
//           "is_verified": 1,
//           "admin_user_id": 3,
//           "company_status_id": 1,
//           "company_method_verification_id": 1,
//           "admin_description": "good",
//           "created_at": "2019-11-03 00:00:00",
//           "updated_at": "2020-03-01 08:33:48",
//           "company_status": {
//               "title": "verified",
//               "id": 1
//           },
//           "company_method_verification": {
//               "method_verification_name": "google",
//               "id": 1
//           },
//           "user": {
//               "id": 5,
//               "company email": "<EMAIL>"
//           }
//       },
//       "company_profile_translations": [
//           {
//               "id": 1,
//               "company_profile_id": 1,
//               "name": "aa",
//               "company_description": "<p>adada</p>",
//               "translated_languages_id": 1
//           }
//       ],
//       "company_industries_for_companies": [
//           {
//               "id": 1,
//               "company_industry_id": 6,
//               "company_profile_id": 1,
//               "company_industry": {
//                   "id": 6,
//                   "verified": 1,
//                   "created_at": null,
//                   "updated_at": null,
//                   "company_industry_parent_id": null,
//                   "company_industry_translation": [
//                       {
//                           "id": 6,
//                           "company_industry_id": 6,
//                           "translated_languages_id": 1,
//                           "name": "Agribusiness",
//                           "created_at": null,
//                           "updated_at": null
//                       }
//                   ]
//               }
//           }
//       ]
//   }
// }




//   {
//     "id": 36,
//     "registration_date_time": "2019-11-03 00:00:00",
//     "company_method_verification_id": 1,
//     "company_method_verification_name": "google",
//     "company_name": "aa",
//     "company_description": "<p>adada</p>",
//     "translated_languages_id": 1,
//     "company_profile_id": 1,
//     "company_main_language_id": 1,
//     "logo": null,
//     "company_website": "aa.com",
//     "company_type_id": 2,
//     "company_industry_id": 6,
//     "country": "تركيا",
//     "city": "Üsküdar",
//     "status": "verified",
//     "company_status_id": 1,
//     "company_status_title": "verified",
//     "company_admin_description": "good",
//     "handled_by": "admin.admin"
// }
