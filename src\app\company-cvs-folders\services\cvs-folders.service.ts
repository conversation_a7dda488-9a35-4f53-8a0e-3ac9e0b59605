import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class CvsFoldersService {

  url = '';

  constructor(private http:HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(baseUrl=>{
      this.url = baseUrl + 'cv_folder';
    });
  }

  getFolders() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url, {headers});
  }

  getFoldersData(){
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url+'/list', {headers});
  }

  addFolder(folder) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url, JSON.stringify(folder), {headers});
  }

  editFolder(folder,folder_id: string) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + '/' + folder_id, JSON.stringify(folder), {headers});
  }

  deleteFolder(folder_id: string) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.delete(this.url +'/'+  folder_id, {headers});
  }

}
