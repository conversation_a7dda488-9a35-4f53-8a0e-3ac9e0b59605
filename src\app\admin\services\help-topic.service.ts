import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { Observable } from 'rxjs/observable';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { HelpTopic } from '../models/help-topic';

@Injectable()
export class HelpTopicService {

  // url = 'http://localhost:8000/admin/faq';
  url = '';
  baseUrl = '';
  public faqsCount = null;
  private hTopicSource = new BehaviorSubject<HelpTopic>({id: null , main_cat_id: null, sub_cat_id: null, main_cat: '',
   sub_cat: '', title: '', description: '', order: null, active: false, slug: '', page_title: '', meta_description: '',
    meta_keywords: '', type: '', langId: null });
  newHelpTopic: Observable<HelpTopic> = this.hTopicSource.asObservable();

  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.url     = data + 'admin/helpCenter';
          this.baseUrl = data;
    });
  }


  refreshHTValue(ht) {
    this.hTopicSource.next(ht);
  }

  activateHtopic(body, id: number) {
    console.log('body, id', JSON.stringify(body), id );
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/active/' + id, JSON.stringify(body), { headers });
  }



  getHTCategories(langId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/categories/' + langId, { headers });
  }

  getAllHTCategories() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + 'admin/help_center_get_categories_data' , { headers });
  }

  gethelpTopic( htId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + htId , { headers });
  }

  gethtInSpecificLang( htId: number, langId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + htId + '/' + langId, { headers });
  }

  getHelpTopics() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url, { headers });
  }

  createHTopic( ht ) {
    // console.log('after json.stringify', JSON.stringify(ht));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url , JSON.stringify(ht), { headers } );

  }

  // addHTTranslation(ht) {
  //   console.log('after json.stringify', JSON.stringify(ht));
  //   let headers = new HttpHeaders().set('Content-Type', 'application/json');
  //   return this.http.post(this.url , JSON.stringify(ht), { headers } );
  // }

  updateHTopic( ht , id) {
    console.log('after json.stringify', JSON.stringify(ht));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + '/' + id , JSON.stringify(ht), { headers });
  }

  deleteHTopic(id) {
    console.log('id', id);
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.delete(this.url + '/' + id, { headers } );
  }

}
