import { Component, OnInit } from '@angular/core';
import {QuestionService} from '../form-services/question.service';

@Component({
  selector: 'app-basic-replies',
  templateUrl: './basic-replies.component.html',
  styleUrls: ['./basic-replies.component.css']
})
export class BasicRepliesComponent implements OnInit {
  Replies:any[]=[];

  constructor(private service:QuestionService) {


  }

  ngOnInit() {
    this.service.GetResponse().subscribe(res=>{
      for(let r in res){
        this.Replies.push(res[r]);
      }
    },
      error => {console.log("")}

      )
  }

}
