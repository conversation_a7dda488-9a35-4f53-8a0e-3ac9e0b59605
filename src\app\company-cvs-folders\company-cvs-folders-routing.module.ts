import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CompanyGuardService } from 'shared/shared-services/company-guard.service';
import { CompanyWrapperComponent } from '../company/components/company-wrapper/company-wrapper.component';
import { MainComponent } from 'app/company-cvs-folders/components/main/main.component';

const routes: Routes = [
  {path: ':username', component: CompanyWrapperComponent, children: [
    { path: '', component: MainComponent },
  ], canActivate: [CompanyGuardService]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CompanyCvsFoldersRoutingModule { }