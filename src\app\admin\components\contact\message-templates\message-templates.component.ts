import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, Validators } from '@angular/forms';
import { ContactService } from 'app/admin/services/contact.service';
import { Subject } from 'rxjs/Subject';
import { Table } from 'primeng/table';
declare var $: any;

@Component({
  selector: 'app-message-templates',
  templateUrl: './message-templates.component.html',
  styleUrls: ['./message-templates.component.css']
})
export class MessageTemplatesComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  filteredMessages = [];
  admins: {'value': number, 'label': string}[] = [];
  tempToPreview;
  operationType;
  displayMsgModal = false;
  displayAddModal = false;
  private ngUnsubscribe: Subject<any> = new Subject();
  messages = [];
  opperationNum: number;
  commentForm: any;
  assignForm: any;
  msgIdToDelete: number;
  msgId: number;
  mainCats: {'value': number, 'label': string}[] = [];
  subCats: {'value': number, 'label': string, 'main_cat_id': number }[] = [];
  templateMode = '';
  loading = true;
  main_cat;
  sub_cat;

  constructor(private contact: ContactService, private fb: FormBuilder) { }

  ngOnInit(): void {
   this.getMessages();
  //  this.getAdmins();
  }

  getMessages() {
   this.contact.getMsgTemplates().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log('res', res);
      let temp = res['template_emails'];
      let temp2 = res['ContactMainCat'];
      let temp3 = res['ContactSubCat'];
      for (let t of temp) {
          this.messages.push({
            'id'         : t.id,
            'title'      : (t.user === null) ? '.....' : t.template_title,
            'email_title': t.email_title,
            'main_cat_id': t.contact_main_cat_id,
            'main_cat'   : t.contact_main_cat,
            'sub_cat_id' : t.contact_sub_cat_id,
            'sub_cat'    : (t.contact_sub_cat === null) ? '....' : t.contact_sub_cat,
            'short_reply'   : t.short_reply,
            'detailed_reply': t.detailed_reply,
            'display'       : false
          });

      }
      this.loading = false;
      this.table.filter(1, 'id', 'startsWith');
      this.table.filter(null, 'id', 'startsWith');
      // this.filteredMessages = this.messages;
      for (let main of temp2) {
        this.mainCats.push({
          'label': main.name,
          'value': main.id
        });
      }
      this.mainCats.unshift({ 'label': '', 'value': null });
      for (let sub of temp3) {
        this.subCats.push({
          'label'      : sub.name,
          'value'      : sub.id,
          'main_cat_id': sub.contact_main_cat_id
        });
      }
      this.subCats.unshift({ 'label': '', 'value': null, 'main_cat_id': null });
      console.log('template msg', this.messages);
      console.log('main cats', this.mainCats);
      console.log('sub cats', this.subCats);

    });

  }


  addToSelected(msg) {
    console.log('selected', this.filteredMessages);
  }

  displayCreateModal() {
    this.displayMsgModal = true;
    this.templateMode = 'create';
  }

  displayEditModal(temp) {
    this.templateMode = 'update';
    this.displayMsgModal = true;
    this.tempToPreview = {
      'id'         : temp.id,
      'title'      : temp.title,
      'email_title': temp.email_title,
      'main_cat_id': temp.main_cat_id,
      'main_cat'   : temp.main_cat,
      'sub_cat_id' : temp.sub_cat_id,
      'sub_cat'    : temp.sub_cat,
      'short_reply'   : temp.short_reply,
      'detailed_reply': temp.detailed_reply,

    };
    console.log('temp to preview', this.tempToPreview);

  }

  updateTemplatesTable(event) {
    this.closeModal();
    console.log('in updateTemplatesTable',event);
    if (event['id'] === null) {
      this.messages.push({
        'id'         : event['data'].id,
        'title'      : event['data'].template_title,
        'email_title': event['data'].email_title,
        'main_cat_id': event['data'].contact_main_cat_id,
        // 'main_cat'   : event['data'].main_cat,
        'main_cat'   : this.getCatName(event['data'].contact_main_cat_id, this.mainCats),
        'sub_cat_id' : event['data'].contact_sub_cat_id,
        'sub_cat'       : this.getCatName(event['data'].contact_sub_cat_id, this.subCats),
        // 'sub_cat'    : event['data'].sub_cat,
        'short_reply'   : event['data'].short_reply,
        'detailed_reply': event['data'].detailed_reply,
        'display'       : false
      });
      this.table.filter(null, 'id', 'startsWith');
    } else {
      let index = this.getMsgIndex(event['id']);
      let newTemp = {
        'id'         : event['data'].id,
        'title'      : event['data'].template_title,
        'email_title': event['data'].email_title,
        'main_cat_id': event['data'].contact_main_cat_id,
        // 'main_cat'   : event['data'].main_cat,
        'main_cat'   : this.getCatName(event['data'].contact_main_cat_id, this.mainCats),
        'sub_cat_id' : event['data'].contact_sub_cat_id,
        // 'sub_cat'    : event['data'].sub_cat,
        'sub_cat'       : this.getCatName(event['data'].contact_sub_cat_id, this.subCats),
        'short_reply'   : event['data'].short_reply,
        'detailed_reply': event['data'].detailed_reply,
        'display'       : false
      };
      this.messages.splice(index, 1, newTemp);
    }

    console.log('messages', this.messages);
  }

  getCatName(id, catArray) {
    for (let cat of catArray) {
      if (id === cat.value) {
        return cat.label;
      }
    }
  }

  closeModal() {
    this.displayMsgModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
  }



  closeAddModal() {
    this.displayAddModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');
    $('body').removeAttr('style');
  }


  displayDeleteAlert(num, msgId?) {
    this.opperationNum = num;
    if (msgId) {
      this.msgIdToDelete = msgId;
    }
    this.displayAddModal = true;
  }

  deleteMultiMsgs() {
   let msgsIds = [];
   for (let msg of this.filteredMessages) {
      msgsIds.push(msg.id);
   }
   for (let msgId of msgsIds) {
      this.contact.removeTemplate(msgId).subscribe(res => {
        console.log('res', res);
        this.closeAddModal();
        if ((res['success'] as string) === 'false' ) {
          alert(res['messages']);
        } else {
          for (let i = 0; i < this.messages.length; i++) {
              if (this.messages[i].id === msgId) {
                this.messages.splice(i, 1);
              }
          }
          this.table._totalRecords = this.messages.length;
        }

      });
    }

    this.filteredMessages = [];

  }

  deleteMsg() {
    this.contact.removeTemplate(this.msgIdToDelete).subscribe(res => {
      console.log('res', res);
      this.closeAddModal();
      if ((res['success'] as string) === 'false' ) {
        alert(res['messages']);
      } else {
        let i = this.getMsgIndex(this.msgIdToDelete);
        this.messages.splice(i, 1);
        this.table._totalRecords = this.messages.length;
      }
    });

  }

  getMsgIndex(msgId) {
    for (let i = 0; i < this.messages.length; i++) {
      if (this.messages[i].id === msgId) {
        console.log('index', i);
        return i;
      }
    }
  }


  clearAll() {
    this.table.filter(null, 'id', 'startsWith');
    this.table.filter(null, 'title', 'contains');
    this.table.filter(null, 'main_cat_id', 'equals');
    this.table.filter(null, 'sub_cat_id', 'equals');
    this.table.filterGlobal(null, 'contains');
    $('.ui-table-globalfilter-container input').val(null);
    console.log($('.ui-column-filter').val());
    $('.ui-column-filter').val(null);
     this.main_cat = '';
     this.sub_cat = '';
   }



  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
