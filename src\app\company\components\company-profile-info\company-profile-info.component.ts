import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { CompanyFormService } from "app/company/services/company-form.service";
import { Router, ActivatedRoute, NavigationEnd, RoutesRecognized } from '@angular/router';
import { DataMap } from "shared/Models/data_map";
import { Title } from '@angular/platform-browser';
import { TranslateService} from '@ngx-translate/core';

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'company-profile-info',
  templateUrl: './company-profile-info.component.html',
  styleUrls: ['./company-profile-info.component.css']
})
export class CompanyProfileInfoComponent implements OnInit, OnDestroy {
  toggle:boolean = false;
  toggle2:boolean = false;
  toggle3:boolean = false;
  username='';
  data_map = new DataMap();
  companyId = Number (localStorage.getItem('company_id'));
  private ngUnsubscribe: Subject<any> = new Subject();
  companypro1;
  companypro2;
  translated_profiles: any [] = [];
  existing_profiles: any[] = [];
  constructor(
    private companyFormService: CompanyFormService,
    private router: Router,
    private route: ActivatedRoute,
    private title: Title,
    private translate: TranslateService,
  ) {
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
    // console.log("company profile info constructer");
    // this.getProfiles_route();
  /*   this.companyFormService.send_Data([]) */
   }

  enableDisableRule() {
    this.toggle = !this.toggle;
    this.toggle2 = !this.toggle;
    this.toggle3 = !this.toggle;
}

enableDisableRule2() {
  this.toggle2 = !this.toggle2;
  this.toggle = !this.toggle2;
  this.toggle3 = !this.toggle2;
}

enableDisableRule3() {
  this.toggle3 = !this.toggle3;
  this.toggle = !this.toggle3;
  this.toggle2 = !this.toggle3;
}

/*--- here we receive the data from the server if there is any profile data for the company ---*/

getProfiles_route() {

  this.companyFormService.getProfiles(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
  (res) => {
    if (res['translation'] !== undefined) {
      let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
      this.companyFormService.send_Data(send_Data_to_preview);
      this.router.navigate(['/c', this.username,'profile', 'preview']);

    } else if(res['data'].length === 0) {
      let send_Data_to_preview = [];
      this.companyFormService.send_Data(send_Data_to_preview);
      this.router.navigate(['/c', this.username,'profile','new']);
    }
  });
}

  ngOnInit() {
    this.title.setTitle('CVeek');
    this.translate.use("en");
  }

  ngOnDestroy() {
        this.ngUnsubscribe.next();
        this.ngUnsubscribe.complete();
      }
}
