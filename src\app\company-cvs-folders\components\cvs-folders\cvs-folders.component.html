<p-toast position="bottom-right" [style]="{marginTop: '100px'}"></p-toast>

<div class="additional-folders">
    <a class="folder-link" [routerLink]="['/c', 'cvs', username]">
        <i class="pi pi-envelope folder-icon"></i>
        <span class="folder-label">Inbox</span>
    </a>
</div>

<div class="folders-heading">
    <a class="my-folders-title" (click)="displayFolderCvs(null)">All CVs</a>
    <a class="add-folder-btn" (click)="action='Add';openAddEditFolderModal()" pTooltip="Add new folder"  tooltipPosition="top">+</a>
</div>

<!-- (onNodeSelect)="displayFolderCvs($event)" -->
<!-- selectionMode="single" 
[(selection)]="selectedFolder" -->
<p-tree 
    [value]="folders"  
    [filter]="true"
    [emptyMessage]="'No folders found'"
    [loading]="loading"
    >
    <ng-template let-node  pTemplate="default">
        <div 
            class="folder-label-div"
            cdkDropList
            (cdkDropListDropped)="dropCV($event)"
            id="cdkDropList-{{node.key}}"
        >
            <a 
            class="folder-label"
            [ngClass]="{'active-folder': node.key === activeFolder}"
            (click)="displayFolderCvs(node.key)"
            >
                <span class="name">
                    {{node.label}}
                </span> 
                <span class="count">
                    {{node.data.count}}
                </span>
            </a>

            <div class="dropdown folder-actions-dd" dropdown>
                <div class="dropdown-toggle" id="folderActionsDropdownMenuLink" aria-haspopup="true" aria-expanded="false"  dropdownToggle>
                    <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </div>
                <div class="dropdown-menu" aria-labelledby="folderActionsDropdownMenuLink" *dropdownMenu>
                    <a class="dropdown-item" (click)="editFolder(node.key)">Edit</a> 
                    <a class="dropdown-item" (click)="addSubfolder(node.key)">Add sub-folder</a> 
                    <a class="dropdown-item" (click)="deleteFolder(node.key)">Delete</a> 
                </div>
            </div>
            
        </div>
    </ng-template>
</p-tree>

<br>


<div 
    class="modal fade image-editor-modal" id="addEditFolderModal"  
    tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
    >
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeAddEditFolderModal()"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">{{action}} Folder</h4>
            </div>
            <app-add-edit-folder-modal
              
                [action]="action"
                [selectedFolder]="selectedFolder"
                [parentFolder]="parentFolder"
                (closeModalPopup)="handelPopup($event)"
            >
            </app-add-edit-folder-modal>

        </div>
    </div>
</div>



