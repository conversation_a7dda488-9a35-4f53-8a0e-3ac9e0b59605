import { Component, OnInit, NgZone, Input, HostListener} from '@angular/core';
import { AuthService } from 'shared/shared-services/auth-service';
import { ActivatedRoute} from '@angular/router';
import { UserAccountService } from '../../../user/cv-services/user-account.service';
import { GeneralService } from '../../../general/services/general.service';
// import { ChangeDetectorRef } from '@angular/core';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
// import { Subject } from 'rxjs';

@Component({
  selector: 'user-topbar',
  templateUrl: './user-topbar.component.html',
  styleUrls: ['./user-topbar.component.css']
})
export class UserTopbarComponent implements OnInit {
  searchActive = false;
  username;
  firstname;
  role = '';
  profilePicture;
  country='';
  @Input("inAdvrsInterface") inAdvrsInterface : boolean;
  @Input('inHomePage') inHomePage: boolean;
  isScrolled = false;
  
  constructor(private _authService: AuthService ,
              private route: ActivatedRoute,
              private generalService: GeneralService,
              private userAccountService:UserAccountService,
              private imageProcessingService:ImageProcessingService,
            //  private cd: ChangeDetectorRef,
              private zone:NgZone) { 
                if(localStorage.getItem("role")){
                  this.role = localStorage.getItem("role");
                  
                }
  
              //   this.generalService.internalMessage.subscribe( (data) => {
              //     if( data['mData'].inAdvrInterface) {
              //       console.log("user topbar : inside getting data from adv interface");
              //       this.activateSearch();
              //   //    this.cd.detectChanges();
              //     }
              // //   this.cd.markForCheck();
               
              //  });
              }

  authService = this._authService;
  logout(){
    this._authService.logout();
  }

  ngOnInit() {
    this.userAccountService.sharedFname.subscribe(fname => {
      this.firstname = fname;
    });
    this.firstname = localStorage.getItem('fname');

    // get country for Homepage link
    if(localStorage.getItem('country'))
      this.country = localStorage.getItem('country');

    this.route.params.subscribe(res => {
      this.username = res['username'];
    });
    if(this.username == null){
      this.username = localStorage.getItem("username");
    }
    this.profilePicture = localStorage.getItem("pic");
    
    if(this.profilePicture !== "none"){
      this.profilePicture = this.imageProcessingService.getImagePath ('cvProfilePic','small_thumbnail', this.profilePicture);
    }
  
   
    this.generalService.internalMessage.subscribe( (data) => {
      if(( data['src'] === 'resume' || data['src'] === 'personalInfo') && data['dist'] === 'navbar'){
        if(data['message'] === 'profilePictureRemoved'){
          this.profilePicture = "none";
        }
        else if(data['message'] === 'profilePictureChanged'){
          this.profilePicture = this.imageProcessingService.getImagePath ('cvProfilePic','small_thumbnail', data['mData'].profile_picture);
        }
      }
    });


    if(this.inAdvrsInterface){
      this.activateSearch();
    }
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Works for most browsers
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;

    this.isScrolled = scrollTop > 300;
  }

  activateSearch(){
    if(this.searchActive === false){
      this.searchActive = true;
    }
  }
  deactivateSearch(){
    if(this.searchActive === true){
      this.searchActive = false;
    }
  }

  displayContactModal() {
    this.generalService.notify('display Contact Modal' , 'topbar' , 'contact-us' , {'displayContactModal':true}) ;
  }
  
}
