import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ue<PERSON>iff<PERSON>, OnInit } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
import { LanguageFiltersDataModel } from './LanguageFiltersDataModel';
declare var $: any;
@Component({
  selector: 'app-language-filters',
  templateUrl: './language-filters.component.html',
  styleUrls: ['./language-filters.component.css']
})
export class LanguageFiltersComponent implements   OnInit {
  public temp: any;
  dataModel: LanguageFiltersDataModel;
  private dataModelDiffer: KeyValueDiffer<string, any>;
  @Input() filterData ;
  constructor(private generalService: GeneralService, private differs: KeyValueDiffers) {
    this.dataModel  = new  LanguageFiltersDataModel() ;
  }

  ngOnInit(): void {
    this.dataModelDiffer = this.differs.find(this.dataModel).create();
    this.dataModel.languages.push(this.dataModel.firstLang);
    ///
    this.sendInitStateToWarapper();
    this.temp = {'languages' : [] } ;
    this.temp['languages'] = this.filterData.languages ;
  }

  filterArray(event, arr) {
    this.temp[arr] = this.filterData[arr].filter(e => e.name.toLowerCase().includes(event.query.toLowerCase()));
  }

  addLanguage() {
     this.dataModel.languages.push({
      language_id  : '',
      level : '',
     });
}

removeLanguage(index) {
   this.dataModel.languages.splice(index , 1);
}


// ngDoCheck(): void {
//   const changes = this.dataModelDiffer.diff(this.dataModel);
//   if (changes) {
//     this.dataModel.setFilters();
//     //  *  here  we set src of   this.generalService.notify equals to 'education' which it's not
//     // the same of component name  "education-filters" please see filters-wrapper component
//     this.generalService.notify('filters-changes',
//       'language', 'filters-wrapper', this.dataModel.filters);
//   }
// }

sendFilters() {
  this.dataModel.setFilters();
  this.generalService.notify('filters-changes',
   'languages', 'filters-wrapper', this.dataModel.filters);
   $('#filtersModal').modal('hide');
}

sendInitStateToWarapper() {
  this.dataModel.setFilters();
  this.generalService.notify('init-filters',
    'languages', 'filters-wrapper', this.dataModel.filters);
}
hideModal() {
  $('#filtersModal').modal('hide');
}

}
