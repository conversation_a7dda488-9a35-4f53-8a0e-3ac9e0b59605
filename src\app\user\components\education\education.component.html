<app-pre-loader [show]="showLoader"></app-pre-loader>

<div id="page-content-wrapper" [style.margin-left]="marginLeft">

  <div class="page-content table-page-content">
    <div class="page-title" translate="">
      <!-- <i class="fa fa-graduation-cap" aria-hidden="true"></i>  -->
      <img src="./assets/images/sidebar-icons/blue/education.svg">
      education.title
    </div>
    <form #form="ngForm"
          (keydown.enter)="$event.preventDefault()"
          [formGroup]="educationForm" (ngSubmit)="form.valid && addToBeSubmitted(form)"
          class="form-horizontal validate-form" [appInvalidControlScroll]="'normalComponent'">
      <div class="row clearfix flex_row">

        <div class="col-xs-11 custom-col-11 prev-border add-certification">
          <p class="add-certification-p" translate>education.addCertification</p>
          <div class="form-group focus-container  has-feedback" [ngClass]="{'has-error':form.submitted && !isDDValid(degreeLevelControl)}">
            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
            </div>
            <div class="col-lg-6 col-md-8 col-sm-6  focus-no-padding" [ngClass]="{'has-val':educationForm.controls['degree_level_id'].value }">
              <p-dropdown [options]="degreeLevelValues"
                          optionLabel="name"
                          formControlName="degree_level"
                          [filter]="true"
                          (onChange)="changeDegreeLevel()"
                          [ngClass]="{'has-val':educationForm.controls['degree_level_id'].value }"
              >
                <ng-template let-degree pTemplate="item">
                  <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                    <div style="font-size:14px;float:left;margin-top:4px">{{degree.label}}</div>
                  </div>
                </ng-template>
              </p-dropdown>
              <span class="custom-underline"></span>
              <!-- <span class="glyphicon  form-control-feedback" aria-hidden="true"></span> -->
              <label class="control-label custom-control-label" translate>education.degreeLevel</label>
            </div>
            <div class="col-lg-3 col-md-2 col-sm-3">
              <span class="error-message" *ngIf="form.submitted && !isDDValid(degreeLevelControl)" translate>validationMessages.required</span>
            </div>
          </div>

          <div formGroupName="institution" class="form-group focus-container has-feedback" [ngClass]="{'has-error':form.submitted && !institutionGroup.controls['name'].valid}" >
            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
            </div>
            <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
              <ng-select
              [items]="institutionDD.items"
              [virtualScroll]="true"
              formControlName="name"
              [loading]="institutionDD.loading"
              bindLabel="name"
              [addTag]="institutionDD.addNewItem"
              addTagText="{{'shared.addNewItem' | translate}}"
              notFoundText="{{'shared.noItemsFound' | translate}}"
              [dropdownPosition]="'bottom'"
              (scrollToEnd)="institutionDD.onScrollToEnd()"
              (search)="institutionDD.search($event)"
              [searchFn]="institutionDD.customSearchFn"
              (keyup.delete)="institutionDD.deleteAutoCompleteItem(institutionGroup.controls['name'])"
              [ngClass]="{'has-val':institutionGroup.controls['name'].value}"
              class="form-control ng-select-autocomplete"
              (change)="selectUniversity($event)"
              (focus)="setHelpTip(20)"
              >
                  <ng-template ng-label-tmp let-item="item" let-clear="clear">
                      <span class="ng-value-label">{{item.name}}</span>
                      <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                          <i class="fa fa-times" aria-hidden="true"></i>
                      </span>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                      {{item.name}}
                  </ng-template>
              </ng-select>
              <span class="custom-underline"></span>

              <!-- <p-autoComplete  [suggestions]="filteredUniversities" (completeMethod)="filterUniversities($event)"
                               field="name"
                               [minLength]="1"
                               styleClass="form-control"
                               formControlName="name"
                               (onSelect)="selectUniversity($event)"
                               inputId="universities"
                               (onKeyUp)="onUniKeyUp()"
                               (onFocus)="setHelpTip(20)"
                               [ngClass]="{'has-val':institutionGroup.controls['name'].value}"
              ></p-autoComplete> -->

              <!-- <span class="custom-underline"></span> -->
              <label class="control-label custom-control-label" for="universities" translate>education.institution</label>
            </div>
            <div class="col-lg-3 col-md-2 col-sm-3 ">
              <span class="error-message" *ngIf="form.submitted && !institutionGroup.controls['name'].valid"translate>validationMessages.required</span>
            </div>
          </div>
          <!-- (onBlur)="showLocationInput()" -->

          <div  class="form-group  focus-container" formGroupName="institution"  id="location" [ngClass]="{'has-val':institutionGroup.controls['fullLocation'].value , 'has-error':form.submitted && (institutionGroup.controls['fullLocation'].value == '' || institutionGroup.controls['city'].errors || educationForm.hasError('InvalidLocationError')) }" style="">
            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
            </div>
            <div class="col-lg-6 col-md-8  col-sm-6 focus-no-padding" >
              <input
                type="text"
                class="form-control"
                id="exampleInputName2"
                #googleSearchPlace
                placeholder=""
                formControlName="fullLocation"
                (keyup)="LocationClearOnChange($event)"
                (focus)="setHelpTip(21)"
                (blur)="clearHelpMessage()">
              <span class="custom-underline"></span>

              <label class="control-label custom-control-label" translate>education.location</label>
            </div>
            <div class="col-lg-3 col-md-2 col-sm-3 ">
              <span class="error-message " *ngIf="form.submitted &&  institutionGroup.controls['fullLocation'].value == ''" translate>validationMessages.required</span>
              <span class="error-message " *ngIf="form.submitted &&  institutionGroup.controls['city'].errors?.required && !institutionGroup.controls['fullLocation'].errors  && !educationForm.hasError('InvalidLocationError')" translate>validationMessages.cityRequired</span>
              <!-- <span class="error-message " *ngIf="form.submitted &&  institutionGroup.controls['city'].errors?.required && !institutionGroup.controls['fullLocation'].value == ''  && !educationForm.hasError('InvalidLocationError')" translate>validationMessages.cityRequired</span> -->
              <span class="error-message " *ngIf="form.submitted && educationForm.hasError('InvalidLocationError')" translate>{{ educationForm.errors?.InvalidLocationError }}</span>
            </div>
          </div>


          <!-- Major or Education Field -->
          <div class="form-group  focus-container"  [ngClass]="{'has-error':form.submitted && !educationFieldControl.valid}">
            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
            </div>
            <div class="col-lg-6 col-md-8  col-sm-6 focus-no-padding" >
                <!-- (onClear)="clearValue(educationFieldControl)" -->
              <p-autoComplete  [suggestions]="filteredEducationFields" (completeMethod)="filterEducationFields($event)"
                               field="name"
                               [minLength]="1"
                               styleClass="form-control"
                               formControlName="education_field"
                               inputId="float-input"
                               [ngClass]="{'has-val':educationFieldControl.value}"
                               (onFocus)="setHelpTip(22)"
                               (onBlur)="clearHelpMessage();checkIfNewValue(educationFieldControl)"
                      ></p-autoComplete>
              <!-- <span class="custom-underline"></span> -->
              <label  class="control-label custom-control-label" for="float-input" pTooltip="{{'education.educationField' | translate}}" tooltipPosition="top" translate>
                education.major
              </label>
            </div>
            <div class="col-lg-3 col-md-2 col-sm-3 ">
                <span class="error-message " *ngIf="form.submitted && educationFieldControl.errors?.required" translate>validationMessages.required</span>
            </div>
          </div>

          <div class="form-group focus-container">
            <div class="col-lg-2 col-md-2 col-sm-3 alignment-right">

            </div>
            <div class="col-lg-8 col-md-8 col-sm-6">
              <div class="row">
                <div formGroupName="from" class="col-lg-6 margin-bo-mo-10">
                  <div class="row" style="display:flex; align-items:center;">
                    <div class="col-lg-3 col-md-2 col-xs-3 alignment-right">
                      <label class="control-label label-bot-bit" translate>education.from</label>
                    </div>
                  <div class="col-xs-4 col-no-padding-left focus-no-padding">
                      <p-dropdown [options]="fromYearOpts"
                                  formControlName="year"
                                  [filter]="true"
                                  placeholder=" YYYY "
                                  [ngClass] = " {'has-val':getValueToDD(fromDateControl.controls['year'])}"

                              >
                        <ng-template let-year pTemplate="item">
                          <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                            <div style="font-size:14px;float:left;margin-top:4px">{{year.label}}</div>
                          </div>
                        </ng-template>
                      </p-dropdown>
                      <span class="custom-underline"></span>
                    </div>
                    <div class="col-xs-4 col-no-padding-left focus-no-padding">

                      <p-dropdown [options]="monthOpts"
                                  formControlName="month"
                                  [filter]="true"
                                  placeholder="MM"
                              >
                        <ng-template let-it pTemplate="selectedItem">
                          <span  style=" vertical-align:middle; float:left;">{{it.label | translate | slice:0:3}}</span>
                        </ng-template>

                        <ng-template let-month pTemplate="item">
                          <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                            <div style="font-size:14px;float:left;margin-top:4px">{{month.label | translate}}</div>
                          </div>
                        </ng-template>
                      </p-dropdown>
                      <span class="custom-underline"></span>
                    </div>
                  </div>
                </div>

                <div  formGroupName="to" class="col-lg-6 margin-bo-mo-10">
                  <div class="row to-date" style="display:flex; align-items:center;">
                    <div class="col-lg-3 col-md-2 col-xs-3 col-lg-offset-1 alignment-right">
                      <label class="control-label label-bot-bit" translate>education.to</label>
                    </div>

                    <div class="col-xs-4 col-no-padding-left focus-no-padding" [ngClass]="{'has-error':form.submitted && !toDateControl.controls['year'].valid}">
                      <p-dropdown [options]="toYearOpts"
                                  formControlName="year"
                                  [filter]="true"
                                  placeholder=" YYYY"
                                  #toYear
                                  (onChange)="onToYearSelect(toYear.value)"
                                  [ngClass] = " {'has-val':getValueToDD(toDateControl.controls['year'])}"

                              >
                        <ng-template let-it pTemplate="selectedItem">
                          <span style=" vertical-align:middle; float:left;">{{it.label | translate}}</span>
                        </ng-template>
                        <ng-template let-year pTemplate="item">
                          <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                            <div style="font-size:14px;float:left;margin-top:4px">{{year.label | translate}}</div>
                          </div>
                        </ng-template>
                      </p-dropdown>

                      <span class="custom-underline"></span>
                    </div>
                    <div class="col-xs-4 col-no-padding-left focus-no-padding" >

                      <p-dropdown [options]="monthOpts"
                                  formControlName="month"
                                  [filter]="true"
                                  placeholder="MM"
                              >
                        <ng-template let-it pTemplate="selectedItem">
                          <span style=" vertical-align:middle; float:left;">{{it.label | translate | slice:0:3}}</span>
                        </ng-template>

                        <ng-template let-month pTemplate="item">
                          <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                            <div style="font-size:14px;float:left;margin-top:4px">{{month.label | translate }}</div>
                          </div>
                        </ng-template>
                      </p-dropdown>
                      <span class="custom-underline"></span>
                    </div>
                  </div>

                </div>

              </div>

            </div>
            <div class="col-lg-2 col-md-2 col-sm-3">
               <span class="error-message"  *ngIf="form.submitted && (
                educationForm.hasError('compareError') &&
                !((!toDateControl.controls['year'].valid && !toDateControl.hasError('monthError')
              || (fromDateControl.hasError('fromMonthError'))
              || (toDateControl.hasError('toMonthError'))
              ) )
             ) " translate> {{educationForm.errors?.compareError}}</span>


              <span   class="error-message"  *ngIf="form.submitted && (
              (!toDateControl.controls['year'].valid && !toDateControl.hasError('monthError')
              || (fromDateControl.hasError('fromMonthError'))
              || (toDateControl.hasError('toMonthError'))
              ) )" translate>validationMessages.yearRequired</span>
           </div>
          </div>
          <div class="minamize-certification"><i class="fa fa-angle-up" aria-hidden="true"></i></div>
        </div>
        <div class="col-xs-1  custom-col-1">
          <button  class="btn btn-success" pTooltip="{{'shared.save' | translate}}" tooltipPosition="top">
            <i class="fa fa-plus" aria-hidden="true"></i>
          </button>
        </div>
      </div>
      <!-- <p>  {{ educationForm.value | json }} </p> -->
    </form>
    <!-- <div class="row">
      <div class="col-sm-12">
        <p *ngIf="form.submitted && !this.educationForm.valid">errors</p>
        <p-messages severity="error" *ngIf="form.submitted && !this.educationForm.valid" (click)="hideAlert()" showTransitionOptions="0.5s ease-out" hideTransitionOptions="0.5s ease-in" closable="true">
          <ng-template pTemplate>
              <span class="custom-message" translate>errors</span>
          </ng-template>
        </p-messages>
      </div>
    </div> -->
    <!-- <div class="row">
      <div class="col-sm-12">
        <p-messages severity="error" *ngIf="displayError" (click)="hideAlert()" showTransitionOptions="0.5s ease-out" hideTransitionOptions="0.5s ease-in" closable="true">
          <ng-template pTemplate>
              <span class="custom-message" translate>{{  "personalInformation." + errorMsg }}</span>
          </ng-template>
        </p-messages>
      </div>
    </div> -->

    <div class="row div-margin-top-40">
        <div class=" col-sm-12">
          <div class="table-preview-container" [ngClass]="{'loading-custom-table': tableLoader === true , 'loaded-custom-table' : tableLoader === false }">
            <div class="loader-container">
              <p-progressSpinner  [style]="{width: '30px', height: '30px'}" strokeWidth="5"></p-progressSpinner>
            </div>
            <table class="table-preview" id="table">
              <thead>
              <tr>
                <th><span translate>education.degreeLevel</span></th>
                <th><span translate>education.institution</span></th>
                <th><span translate>education.educationField</span></th>
                <th colspan="2"><span translate>education.date</span></th>
                <th><span translate>shared.actions</span></th>
              </tr>
              </thead>
              <tbody [sortablejs]="educations" [sortablejsOptions]="options">
              <tr *ngIf="tableLoader === false && educations.length === 0 ; else tableData">
                <td class="text-center" colspan="6" class="table-no-data" translate>education.noDataEntered</td>
              </tr>
              <ng-template #tableData>
                <tr *ngFor="let education of educations; let i=index;" id="{{education.order}}">
                  <td>
                    <span class="th-mobile" translate>education.degreeLevel</span>
                    {{ education.degree_level.degree_level_translation[0].name }}
                  </td>
                  <td>
                    <span class="th-mobile" translate>education.institution</span>
                    {{ education.institution.name }}
                  </td>
                  <td>
                      <span class="th-mobile" translate>education.educationField</span>
                      <span  *ngIf="education.education_field===null">---</span>
                      <span *ngIf="education.education_field!==null">{{ education.education_field.name }}</span>
                    </td>
                  <td>
                    <span class="th-mobile" translate>education.from</span>
                    <span  *ngIf="education.from===null">---</span><span  *ngIf="education.from!==null">{{ education.from | date :'yyyy' }}</span>
                  </td>
                  <td>
                    <span class="th-mobile" translate>education.to</span>
                    <span  *ngIf="education.isPresent  != 1">{{(education.to | date: 'yyyy')}}</span>
                    <span  *ngIf="education.isPresent == 1" translate>shared.present</span>
                  </td>
                  <td>
                    <span  class="th-mobile" translate>shared.actions</span>
                    <button type="button" class="btn btn-primary btn-fa-info" data-toggle="modal"
                            data-target="#myModal" (click)="displayModal(education)"
                            pTooltip="{{'shared.addInfoEdit' | translate}}" tooltipPosition="top">
                            <img src="./assets/images/buttons/Add-Edit-btn.png" alt="{{'shared.addInfoEdit' | translate}}">
                      <!-- <i class="fa fa-edit" aria-hidden="true"></i> -->
                    </button>
                    <button class="btn btn-delete btn-trash-cust" (click)="removeEducation(education)">
                      <i class="fa fa-trash" aria-hidden="true"></i>
                    </button>
                    <!-- <i class="fa fa-arrows"></i> -->
                  </td>
                </tr>
              </ng-template>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    <div class="row div-margin-top-40">
      <button  class="btn btn-primary btn-block cust-btn">
        <i class="fa fa-pencil"></i>  <a class="editStyle" routerLink="/u/{{username}}/{{userResumeId}}/education-preview/"translate>&nbsp; shared.preview</a>
      </button>
    </div>



    <div class="modal fade" id="myModal" *ngIf="display" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" (click)="close()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModal2Label" translate>education.editEducation</h4>
          </div>
          <!-- [universitiesData]="universities" [universitiesLocationsData]="universities_locations" -->
          <app-education-modal
                               (closeModalPopup)="handlePopup($event)"
                               [education_item]="educationToBePassedToModal"
                               [schoolEducationFieldsData]="school_education_fields" [universityEducationFieldsData]="university_education_fields"
                               [degreeLevelData]="degreeLevelValues"
                               [helpTips]="helpTips"
                               [resumeLang]="resumeLang">
          </app-education-modal>
        </div>
      </div>
    </div>


    <!--&lt;!&ndash; Modal &ndash;&gt;-->
    <!--<p-dialog  [(visible)]="display"  [closable]="true" [closeOnEscape] = "true" [dismissableMask]="true"-->
              <!--[focusOnShow] ="true" [draggable]="false" [modal]="true"-->
              <!--class="modal-dialog">-->
      <!--<p-header  >-->
        <!--&lt;!&ndash;<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>&ndash;&gt;-->
        <!--<h4 class="modal-title" id="myModalLabel">Edit Certificate</h4>-->
      <!--</p-header>-->


      <!--<app-education-modal *ngIf="display"-->
                           <!--(closeModalPopup)="handlePopup($event)"-->
                           <!--[education_item]="educationToBePassedToModal" [minorData]="minors" [majorData]="majors">i</app-education-modal>-->

      <!--<div class="overlay"></div>-->

    <!--</p-dialog>-->

  </div>
</div>



