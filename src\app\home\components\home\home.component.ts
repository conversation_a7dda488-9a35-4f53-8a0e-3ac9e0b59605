// import { Component, OnInit, OnD<PERSON>roy, ɵɵInheritDefinitionFeature } from '@angular/core';
// import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from '../../../general/services/general.service';
import { HomeService } from '../../services/home.service';
import { fade, UpDown} from 'shared/animations/animations';
import { MessageService } from 'primeng/api';
import {ElementRef, ViewChild, Renderer2, AfterViewInit } from '@angular/core';
//import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
declare var $: any;

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
  providers: [MessageService],
  animations: [
     UpDown, fade 
  ]
})
export class HomeComponent implements OnInit , OnDestroy {
  mainCountriesJobs = [];
  // mobileMainCountriesJobs = [];
  responsiveOptions = [];
  templates = [];
  templatesCount: any = 35;
  role = '';
  cvTemplatesUrl = '';
  showLoader = true;
  // sliderLoader = false;
  // displayVideo = false;
  pageInfo = { 'direction': '', 'country': '' };
  showMyElement : boolean;
  
  // isMobile = window.innerWidth < 560;
  inHomePage:boolean = true;
  // private mySubscription: any;
  private ngUnsubscribe: Subject<any> = new Subject();

  goldCompanies = [];
  silverCompanies = [];
  compLogoPath = '';
  actionAfterLogin = null;
  currentApplyCompanyInfo;

  imagesSet1 = ['./assets/images/home/<USER>', './assets/images/home/<USER>'];
  imagesSet2 = ['./assets/images/home/<USER>', './assets/images/home/<USER>'];
  imagesSet3 = ['./assets/images/home/<USER>', './assets/images/home/<USER>'];
  imagesSet4 = ['./assets/images/home/<USER>', './assets/images/home/<USER>'];


  constructor(
              private title: Title,
              private meta:Meta,
              private generalService: GeneralService,
           //   private renderer: Renderer2
           //   private router: Router,
            //  private route: ActivatedRoute,
            //  private homeService:HomeService,
           //   private messageService: MessageService,
            //  private imageProcessingService : ImageProcessingService
              ) 
              {
   }

  ngOnInit() {
    if(localStorage.getItem("role")){
      this.role = localStorage.getItem("role");
    }
    else{
      this.role = 'unauth';
    }

    this.title.setTitle('CVeek | Best website to Create Free CV online Free Templates | Best Jobs');
    this.meta.updateTag({ name: 'description', content: 'CVeek | Best website to Create Free professional CV and resume online with Free Creative Templates | Find Best jobs online with high Salary in Qatar, Gulf and world'});

    this.generalService.internalMessage.subscribe((data) => {
      if( data['message'] === 'roleChanged' && data['src'] === 'auth-service') {
        this.role = data['mData'].role;}

        //logout state: refresh companies directly applied value
        // for(let company of this.goldCompanies)
        //   company.directly_applied=false;

        // for(let company of this.silverCompanies)
        //   company.directly_applied=false;
        // }

      // if (data['src'] === 'login') {
      //   if (data['message'] === 'loginSuccess' && this.actionAfterLogin === 'apply' && this.currentApplyCompanyInfo!==undefined) {
      //     $('#authModal').modal('hide');
      //     this.role = localStorage.getItem('role');
      //     this.applyToCompany(this.currentApplyCompanyInfo.company,this.currentApplyCompanyInfo.companyType,true);
      //   }
      // }
     });

  //   this.responsiveOptions = [
  //     {
  //       breakpoint: '1200px',
  //       numVisible: 4,
  //       numScroll: 0
  //   },
  //   {
  //       breakpoint: '991px',
  //       numVisible: 3,
  //       numScroll: 1
  //   },
  //   {
  //     breakpoint: '767px',
  //     numVisible: 2,
  //     numScroll: 2
  //   },
  //   {
  //       breakpoint: '560px',
  //       numVisible: 1,
  //       numScroll: 1
  //   }
  // ];

  this.mainCountriesJobs = [
    {
      countryName: 'Qatar',
      imgName: 'job-opportunities-in-qatar.webp',
      imgAlt: 'Job Opportunities in Qatar',
      jobLink: '/search-job/p/-jobs-Qatar'
    },
    {
      countryName: 'UAE',
      imgName: 'job-opportunities-in-uae.webp',
      imgAlt: 'Job Opportunities in UAE',
      jobLink: '/search-job/p/-jobs-United-Arab-Emirates'
    },
    {
      countryName: 'KSA',
      imgName: 'job-opportunities-in-ksa.webp',
      imgAlt: 'Job Opportunities in KSA',
      jobLink: '/search-job/p/-jobs-Saudi-Arabia'
    },
    {
      countryName: 'Egypt',
      imgName: 'job-opportunities-in-egypt.webp',
      imgAlt: 'Job Opportunities in Egypt',
      jobLink: '/search-job/p/-jobs-Egypt'
    },
    // {
    //   countryName: 'Syria',
    //   imgName: 'job-opportunities-in-syria.webp',
    //   imgAlt: 'Job Opportunities in UAE',
    //   jobLink: '/search-job/p/-jobs-Syria'
    // },
    // {
    //   countryName: 'Jordan',
    //   imgName: 'job-opportunities-in-jordan.webp',
    //   imgAlt: 'Job Opportunities in KSA',
    //   jobLink: '/search-job/p/-jobs-Jordan'
    // },
  ];

    //for golden and silver companies
    // this.getCompanies();
    // if (this.isMobile) {
    //   this.mobileMainCountriesJobs = this.mainCountriesJobs.slice(0, 3);
    // }
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}


