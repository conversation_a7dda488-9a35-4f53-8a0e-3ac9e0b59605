<div class="container-fluid form-horizontal">
    <div class="row">
        <div class="col-lg-11 col-xs-12">
            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Total Years Of Experience</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div">
                    <ng5-slider class="form-control" style="z-index: 1;" [options]="dataModel.workexpOptions" [(value)]="dataModel.workExperienceValue" [(highValue)]="dataModel.workExperienceHighValue"></ng5-slider>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Experience Field</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-multiSelect [options]="filterData.exp_fields" [(ngModel)]="dataModel.expField" optionLabel="name" [filter]="true" filterBy="label,value.name" [showTransitionOptions]="'1ms'" [hideTransitionOptions]="'2ms'" styleClass="cust-p-multiselect" defaultLabel="Experience Field">
                    </p-multiSelect>
                    <span class="custom-underline"></span>
                </div>
            </div>


            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Job Related Of  Experience</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div">
                    <ng5-slider class="form-control" style="z-index: 1;" [options]="dataModel.jobRelatedExpOptions" [(value)]="dataModel.jobRelatedExperienceValue" [(highValue)]="dataModel.jobRelatedExperienceHighValue"></ng5-slider>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Candidates Previous Job Title</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-autoComplete field="name" inputId="id" styleClass="form-control" [suggestions]="dataModel.temp['prevJobTitles']" (completeMethod)="dataModel.filterArray($event , 'prevJobTitles' , filterData.job_titles )" [(ngModel)]="dataModel.prevJobTitles" placeholder="Candidates Previous Job Title">
                    </p-autoComplete>
                    <span class="custom-underline"></span>
                </div>
            </div>

            <div class="form-group equal-height-row-cols">
                <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                    <span>Company Industries</span>
                </div>
                <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                    <p-multiSelect [options]="filterData.industries" [(ngModel)]="dataModel['industries']" optionLabel="name" [filter]="true" filterBy="label,value.name" [showTransitionOptions]="'1ms'" [hideTransitionOptions]="'2ms'" styleClass="cust-p-multiselect" defaultLabel="Industries Field">
                    </p-multiSelect>

                    <span class="custom-underline"></span>
                </div>
            </div>
        </div>
    </div>
    <div class="filters-buttons">
        <button (click)="sendFilters()" class="btn btn-success">Apply</button>
        <button (click)="hideModal()" class="btn btn-default">Cancel</button>
    </div>

</div>