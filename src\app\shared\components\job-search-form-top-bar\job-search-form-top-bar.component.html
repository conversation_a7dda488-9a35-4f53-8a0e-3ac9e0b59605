<div [ngClass]="{'home-search-job-form': inHomePage === true , 'navbar-search-job-form': inHomePage === false}">
    <form #form="ngForm"  [formGroup]="jobSearchForm" (ngSubmit)="form.valid && searchJob(form)" class="form-horizontal validate-form">
        <div class="form-group search-job-form focus-container has-feedback" [ngClass]="{'has-error':form.submitted && !jobSearchForm.controls['job_title'].valid}">
            <div class="search-div focus-no-padding">
                <ng-select 
                [items]="jobTitles.items"
                placeholder="Job Title"
                [virtualScroll]="true"
                formControlName="job_title" 
                [loading]="jobTitles.loading"
                bindLabel="name"
                notFoundText="No items found"
                [dropdownPosition]="'bottom'"
                [addTag]="jobTitles.addNewItem"
                addTagText="Enter to search"
                (scrollToEnd)="jobTitles.onScrollToEnd()"
                (search)="jobTitles.search($event)"
                [searchFn]="jobTitles.customSearchFn"
                (keyup.enter)="form.valid && searchJob(form)"
                [openOnEnter]="false"
                (keyup.delete)="jobTitles.deleteAutoCompleteItem(jobSearchForm.controls['job_title'])"
                [ngClass]="{'has-val':jobSearchForm.controls['job_title'].value}"
                class="form-control job-title-control"
                >
                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <span class="ng-value-label">{{item.name}}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                            <i class="fa fa-times" aria-hidden="true"></i>
                        </span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                        {{item.name}}
                    </ng-template>
                </ng-select>

                <!-- <span class="custom-underline"></span> -->
            </div>
            <div class="country-div focus-no-padding">
                <p-dropdown
                    [options]="countryOpts"
                    optionLabel="name"
                    (onChange)="changeCountry($event)"
                    [filter]="true"
                    class="country-dropdown"
                    formControlName="country"
                    filterMatchMode="startsWith"
                    (keyup.enter)="form.valid && searchJob(form)">
                    <ng-template let-it pTemplate="selectedItem">
                        <img *ngIf="inAdvrsInterface && it.label !== 'Canada'" src="./assets/images/CountryCode/{{it.value.code}}.gif" class="country-img" />
                        <img *ngIf="inAdvrsInterface && it.label === 'Canada'" src="./assets/images/CountryCode//+1(1).gif" class="country-img" />
                        <span *ngIf="inHomePage === true" class="country-name" style="vertical-align:middle">&nbsp;{{it.label}}</span>
                        <!-- <span class="countrycode" style=" vertical-align:middle; float:left;">{{it.label}}</span> -->
                    </ng-template>
                    <ng-template let-code pTemplate="it">
                        <div class="ui-helper-clearfix" style="position: relative;">
                            <div style="font-size:14px;float:left;margin-top:4px;width:275px;">
                                <span style="width:250px;display: inline-block;">
                                    {{code.label}}
                                </span>
                                <img *ngIf="code.label !== 'Canada'" src="./assets/images/CountryCode/{{code.value.code}}.gif" style="display: inline-block;width:16px;" />
                                <img *ngIf="code.label === 'Canada'" src="./assets/images/CountryCode/+1(1).gif" style="display: inline-block;width:16px;" />
                            </div>
                        </div>
                    </ng-template>

                </p-dropdown>
            </div>
            <div class="search-btn-div">
                <button class="btn search-btn">
                    <i class="fa fa-search" aria-hidden="true"></i>
                    <span>Search</span>
                </button>
            </div>
        </div>
        <!-- <p>{{ jobSearchForm.value | json }}</p> -->
    </form>
</div>