<!-- <app-pre-loader *ngIf="mainCat.length === 0 && subCat.length === 0"></app-pre-loader> -->
<app-pre-loader [show]="mainCat.length === 0 && subCat.length === 0"></app-pre-loader>
<!-- <app-pre-loader show="true"></app-pre-loader> -->

<!-- && subCat.length !== 0 -->
<div
    *ngIf="mainCat.length !== 0"
    class="container-fluid"
>   
    <form
        [formGroup]="contactForm"
        class="form-horizontal contact-form validate-form"
        #form="ngForm"
        (ngSubmit)="form.valid && send(form)"
    >
        <div class="form-group row"
            [ngClass]="{'has-error':form.submitted && email.invalid}">
            <div class="col-sm-2 col-xs-12 alignment-right"></div>
            <div
                class="col-sm-8 col-xs-12 focus-no-padding validate-input"
                
            >
            <!-- [ngClass]="{'has-error2':form.submitted && email.invalid}" -->
            <!-- [ngClass]="{'has-val':email.valid}" -->
                <input
                    class="form-control"
                    formControlName="email"
                    name="email"
                    type="email"
                    [ngClass]="{'has-val':email.value}"
                >
                <span class="custom-underline"></span>

                <label
                    class="control-label custom-control-label"
                   
                >
                    Email
                </label>
            </div>
            <div class="col-sm-2 col-xs-12">
                <span
                *ngIf="form.submitted && !email.value"
                class="error-message"
            
                >
                    Required
                </span>

                <!-- {{email.errors?.invalidEmailError}} -->
                <span *ngIf="form.submitted && email.errors?.invalidEmailError"
                    class="error-message"
                    >Invalid Email Address
                </span>
                <!-- <span *ngIf="form.submitted && email.errors?.email"
                    class="error-message"
                    translate
                    >Please insert a valid email address
                </span> -->
            </div>
        </div>
        <div *ngIf="visitor"
            class="form-group row visitorClass"
            [ngClass]="{'has-val':email_role.valid}"
            [ngClass]="{'has-error':form.submitted && email_role.invalid }"
        >
           
            <div
                class="col-sm-8 col-sm-offset-2 col-xs-12 focus-no-padding"  
            >
            <div class="row">
                <div class="col-sm-6 col-xs-6">
                        <!-- contactUs.JobSeeker -->
                        <label
                            class="container radio-choose"
                            for="job-seeker"
                        >
                            JobSeeker
                            <input
                                type="radio"
                                formControlName="email_role"
                                id="job-seeker"
                                value="ROLE_JOB_SEEKER"
                            >
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <div class="col-sm-6 col-xs-6">
                            <!-- contactUs.Employer -->
                        <label
                            class="container radio-choose"
                            for="employer"
                        >
                           Employer
                            <input
                                type="radio"
                                formControlName="email_role"
                                id="employer"
                                value="ROLE_EMPLOYER"
                            >
                            <span class="checkmark"></span>
                        </label>
                    </div>
            </div>
               
            </div>
            <div class="col-sm-2 col-xs-12">
                <span
                    *ngIf="form.submitted && email_role.invalid"
                    class="error-message"
                    >
                    Required
                    <!-- <div *ngIf="email_role.errors.required" class="error-message" translate>validationMessages.required</div> -->
                </span>
            </div>
        </div>

        <!-- start of main cat -->
        <div
            class="form-group row"
            [ngClass]="{'has-error':form.submitted && contact_main_cat_id.invalid }"
        >
            <div class="col-sm-2 col-xs-12 alignment-right"></div>
            <!-- contactUs.MainCategory -->
            <div
                class="col-sm-8 col-xs-12 focus-no-padding"
                title
                [ngClass]="{'has-val':contactForm.controls['contact_main_cat_id'].value }"
            >
            <!-- placeholder="{{ 'contactUs.MainCategory' | translate}}" -->
                <p-dropdown
                    [options]="mainCat[currentLangId - 1]"
                    formControlName="contact_main_cat_id"
                    [required]="true"
                    id="main_cat"
                    name="main_cat"
                    (onChange)="filter()"
                    (keydown)="errorMsg = null"
                    [ngClass]="{'has-val':contactForm.controls['contact_main_cat_id'].value }"
                >
                    <ng-template
                        let-category
                        pTemplate="item"
                    >
                        <div
                            class="ui-helper-clearfix"
                            style="position: relative;height: 25px;font-family: 'Exo2-Regular', sans-serif;"
                        >
                            <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                        </div>
                    </ng-template>
                </p-dropdown>
                <!-- <span class="custom-underline"></span> -->
                <label
                    class="control-label custom-control-label"
                   
                >
                    Main Category
                </label>
            </div>

            <div class="col-sm-2 col-xs-2">
                <span
                    *ngIf="form.submitted && contact_main_cat_id.invalid"
                    class="error-message"
              
                >
                    Required
                    <!-- <div *ngIf="contact_main_cat_id.errors.required" class="error-message"style="color:blue !important"  translate>validationMessages.required</div> -->
                </span>
                <span
                    *ngIf="errorMsg"
                    class="error-message2"
                    translate
                >
                    {{errorMsg}}
                </span>
            </div>

        </div>
        <!-- end of main cat -->

        <!-- start of sub cat -->
        <div class="form-group row" *ngIf="contact_main_cat_id.value !== null && filteredSubCats[currentLangId - 1].length > 1">
            <div class="col-sm-2 col-xs-12 alignment-right"></div>
            <!-- contactUs.SubCategory -->
            <div
                class="col-sm-8 col-xs-12 focus-no-padding"
                title
                [ngClass]="{'has-error':form.submitted && contact_sub_cat_id.invalid,'has-val':contactForm.controls['contact_sub_cat_id'].value}"
            >
                    <!-- <label for="sub-category" translate>contactUs.SubCategory<span *ngIf="contact_sub_cat_id.touched && contact_sub_cat_id.invalid" class="required">**</span></label> -->
                    <!-- placeholder="{{ 'contactUs.SubCategory' | translate }}" -->
                <p-dropdown
                    [options]="filteredSubCats[currentLangId - 1]"
                    formControlName="contact_sub_cat_id"
                    [required]="filteredSubCats[currentLangId - 1].length > 1"     
                    id="sub_cat"
                    name="sub_cat"
                    [ngClass]="{'has-val':contactForm.controls['contact_sub_cat_id'].value }"
                >
                    <ng-template
                        let-category
                        pTemplate="item"
                    >
                        <div
                            class="ui-helper-clearfix"
                            style="position: relative;height: 40px;"
                        >
                            <div style="font-size:14px;float:left;margin-top:4px">{{category.label}}</div>
                        </div>
                    </ng-template>
                </p-dropdown>
                <!-- <span class="custom-underline"></span> -->
                <label
                    class="control-label custom-control-label"
                   
                >
                    Sub Category
                </label>
            </div>
            <!-- style="margin-top:100px !important; margin-left: -45px !important;" -->
            <div class="col-xs-2 col-xs-12">
                <span
                *ngIf="form.submitted && contact_sub_cat_id.invalid"
                class="error-message"
                
                >
                    Required
                    <!-- <div *ngIf="contact_sub_cat_id.errors.required" class="error-message"   translate>validationMessages.required</div> -->
                </span>
            </div>
        </div>
        <!-- end of sub cat -->


        <!-- start of msg -->
        <div class="form-group row"
            [ngClass]="{'has-error':form.submitted && message.invalid }"
        >
            <div class="col-sm-2 col-xs-12 alignment-right"></div>
            <div
                style="min-height: 140px !important;"
                class="col-sm-8 col-xs-12 focus-no-padding validate-input"
                [ngClass]="{'has-val':message.valid}"
            >
                <textarea
                    class="form-control"
                    formControlName="message"
                    rows="6"
                    cols="15"
                    name="message"
                    style="border: 1px solid #ccc; border-radius: 2px;"
                ></textarea>
                <!-- <p-editor formControlName="message" [style]="{'height':'150px'}" id="summary"
                placeholder="Enter Your Message" onTextChange="render()">
               
                </p-editor> -->

                <label
                    class="control-label custom-control-label"
                    style="padding: 7px; padding-right: 0;"
               
                >
                    Message
                </label>
                <span *ngIf="contact_main_cat_id.value == 9" class="message">
                    Add the Domain you want to install the plugin to the message
                </span>
            </div>
            
            <div class="col-sm-2 col-xs-12">
                <span
                    *ngIf="form.submitted && message.invalid"
                    class="error-message"
                   
                >
                    Required
                </span>
            </div>
        </div>

        
        <div class="form-group row ">
            <div class="col-sm-2 alignment-right" style="color: #4f94df;font-size: 16px;">Attach Images</div>
            <div class="col-sm-10">
                <div class="row custom-row" formArrayName="attach_images" *ngFor="let image of attachImages.controls ; let i=index;">
                    <div class="col-sm-8 col-xs-10 col-xxs-10 margin-bo-mo-10 focus-no-padding input-file-container" [formGroupName]="i">
                        <input id="persPhoto" type="file" accept="image/jpeg, image/png" (change)="onFileChanged($event);">
                    </div>
                    <div class="col-sm-1 col-xs-2 col-xxs-2 focus-no-padding text-center">
                        <button type="button" (click)="addImage()" *ngIf="i===0" class="btn btn-gray"
                            pTooltip="{{'shared.addMoreValues' | translate}}" tooltipPosition="top">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                        </button>
                        <button type="button" (click)="removeImage(i)" *ngIf="i!=0" class="btn btn-delete btn-delete-big">
                            <i class="fa fa-trash" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
                <div class="row custom-row">
                    <div class="col-xs-12">
                        <div class="imgError-div col-xs-12">
                            <span *ngIf="imgError" class="error-message" translate>{{imgError}}</span>
                        </div> 
                    </div>
                </div>
            </div>
        </div>


        <div class="row div-margin-top-40 text-center">
            <div class="col-sm-12">
                <button
                    type="submit"
                    class="btn btn-success btn-block small-btn"
                >
                    <span>Send</span>
                </button>
            </div>
        </div>

        <!-- <p> {{ contactForm.value | json }} </p> -->

    </form>

</div>