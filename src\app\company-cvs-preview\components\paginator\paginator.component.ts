import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';

@Component({
  selector: 'app-paginator',
  templateUrl: './paginator.component.html',
  styleUrls: ['./paginator.component.css']
})
export class PaginatorComponent implements OnInit ,  OnChanges {
  @Input() totalPages ;
  @Input() currentpage ;
  blocks: any ;

  constructor(private generalService: GeneralService) {
    this.blocks = [] ;
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.blocks = [] ;
              for (let i = 1  ; i <= this.totalPages ; i++) {
                 let b = {'id' : i , 'status' : (this.currentpage === i) ? 'active' : '', 'visibility':this.checkVisibility(i) };
                   this.blocks.push(b);
              }
              
  }

  ngOnInit(): void {
    
  }

  navigate(type,id?) {
    if(type==='byId' && id)
      this.generalService.notify('pageChanged' , 'paginator' , 'cvs-table' , {'pageId' : id}) ;
    else if(type==='previous' && this.currentpage > 1)
      this.generalService.notify('pageChanged' , 'paginator' , 'cvs-table' , {'pageId' : this.currentpage-1}) ;
    else if(type==='next' && this.currentpage !== this.totalPages)
      this.generalService.notify('pageChanged' , 'paginator' , 'cvs-table' , {'pageId' : this.currentpage+1}) ;
    else if(type==='first' && this.currentpage > 1)
      this.generalService.notify('pageChanged' , 'paginator' , 'cvs-table' , {'pageId' : 1}) ;
    else if(type==='last' && this.currentpage !== this.totalPages)
      this.generalService.notify('pageChanged' , 'paginator' , 'cvs-table' , {'pageId' : this.totalPages}) ;
  }

  checkVisibility(pageIndex){
    if(this.currentpage === pageIndex-2 || this.currentpage === pageIndex-1 || this.currentpage === pageIndex || this.currentpage === pageIndex+1 || this.currentpage === pageIndex+2)
      return 'block-visible';
    else
      return 'block-hidden';
  }

}
