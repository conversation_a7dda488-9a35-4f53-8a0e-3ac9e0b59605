import { Options } from 'ng5-slider';
export class WorkExperienceFiltersDataModel {
   /////////// ------ temp : object to hold array came from backend,
   /// which is the possible values of autocomplete
temp: any;
//////// -----------
    workExperienceValue = 2;
    workExperienceHighValue = 12;
    workexpOptions: Options = {
        floor: 0,
        ceil: 30
    };
    expField = [];

    jobRelatedExperienceValue = 2 ;
    jobRelatedExperienceHighValue = 12 ;
    jobRelatedExpOptions: Options = {
        floor: 0,
        ceil: 30
    };
    prevJobTitles =  [] ;
    companyIndustries = [] ;
    ///
    ///

    filters = {};

    setFilters() {
        this.filters = {/// *
            'company_industry': {'all_mandatory' : false , 'values' : this.companyIndustries.map(el => el.id)},
            'job_title'  : this.prevJobTitles,
            'exp_field': {'all_mandatory' : false , 'values' : this.expField.map(el => el.id)},
            'from_total_years_of_exp': this.workExperienceValue,
            'to_total_years_of_exp': this.workExperienceHighValue,
            'from_years_of_exp':this.jobRelatedExperienceValue ,
            'to_years_of_exp': this.jobRelatedExperienceHighValue,
    };
    }
    ////
    ////
    constructor() {
        this.temp = {};
    }
    //// ---- for auto complete elements
    filterArray(event, arrName , arr) {
        Object.assign(this.temp, {[arrName]: arr.filter(e => e.name.toLowerCase().includes(event.query.toLowerCase()))});
    }
}
