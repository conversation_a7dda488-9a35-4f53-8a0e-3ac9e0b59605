import { Component, OnInit } from '@angular/core';
import { OAuthService } from 'angular-oauth2-oidc';
import { Router } from '@angular/router';
import { FormBuilder, Validators } from '@angular/forms';
import { AuthService } from 'shared/shared-services/auth-service';
import { Errors } from '../../../models/errors';
import { SocketioService } from 'shared/shared-services/socketio.service';
import { Title, Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginAdminComponent implements OnInit {
  emailPattern = '^[a-z0-9._%+-]+@[a-z0-9.-]+[.]+[a-z]{2,4}$';
  loginForm;
  resetMessage;
  
  type= 'password';
  show = false;
 
  constructor(
              private authService: AuthService,
              private oauthService: OAuthService,
              private router: Router,
              private  fb: FormBuilder,
              private socketSerivce: SocketioService,
              private auth: AuthService,
              private title: Title,
              private meta:Meta
              ) {
    this.loginForm = this.fb.group({
      email : ['', [Validators.required , Validators.email]],
      password : ['', Validators.required ],
    });
   }

  ngOnInit() {
    this.title.setTitle('CVeek');
    this.meta.updateTag({ name: 'robots', content: 'noindex' });
  }

  isInvalid(controlName: string) {
    return this.loginForm.controls[controlName].hasError('required');
  }

  isInvalidSyn(controlName: string) {
    return this.loginForm.controls[controlName].hasError('email');
  }

  // isInvalidMin(controlName: string) {
  //   return this.loginForm.controls[controlName].hasError('minlength');
  // }

  // isInvalidMax(controlName: string) {
  //   return this.loginForm.controls[controlName].hasError('maxlength');
  // }

  toggleShowPassword() {
    this.show = !this.show;
    if (this.show) {
        this.type = 'text';
    } else {
        this.type = 'password';
    }
  }

  login() {
    this.authService.logoutIfAlreadyLoggedin();
    
    const data = {
      'email' : this.loginForm.get('email').value,
      'password' : this.loginForm.get('password').value,
    } ;

    if (this.loginForm.valid) {
      this.oauthService.fetchTokenUsingPasswordFlow(data.email,data.password).then(
        (resp) => { 
            if(resp['error']){
              this.authService.changeError(resp['error']);
              return Promise.reject(resp['error']);   
            }
            else{
              // Loading data about the user
              return this.oauthService.loadUserProfile();
            }
      },
      (err) => {
        if (err['error']['error']){
          this.resetMessage = Errors.InvalidEmailOrPassword;
        //  console.clear();
        }
      }
      ).catch(error => {
        
      })
      .then(() => {
            // Using the loaded user data
            let claims = this.oauthService.getIdentityClaims();
            if (claims) {
              if(claims['user_info'].roles[0].name === "ROLE_ADMIN" ||claims['user_info'].roles[0].name === "ROLE_CONTACT_ADMIN"){
                localStorage.setItem('role',claims['user_info'].roles[0].name);
                localStorage.setItem("fname",claims['user_info'].first_name);
                localStorage.setItem("username",claims['user_info'].user_name);
               //  this.socketSerivce.storeUser(claims['user_info'].user_name);
            
                this.router.navigate(['/manage/dashboard']);
              }
              else{
                this.auth.logout();
              }
              
            }
      });
    }
   
  }

}
