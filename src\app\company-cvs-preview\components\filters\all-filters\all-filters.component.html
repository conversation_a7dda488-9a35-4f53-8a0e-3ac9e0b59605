<form  #formFilters="ngForm" [formGroup]="filtersForm"  class="form-horizontal validate-form" *ngIf="filtersLists !== undefined">
    <div class="container-fluid form-padding">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Skills</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <ng-select 
                            [items]="skillsDD.items"
                            [virtualScroll]="true"
                            formControlName="skills"
                            [loading]="skillsDD.loading"
                            [multiple]="true"
                            [closeOnSelect]="false"
                            bindLabel="name"
                            placeholder="Skills"
                            notFoundText="No items found"
                            [dropdownPosition]="'bottom'"
                            (scrollToEnd)="skillsDD.onScrollToEnd()"
                            (search)="skillsDD.search($event)"
                            [searchFn]="skillsDD.customSearchFn"
                            class="form-control ng-select-autocomplete"
                        >
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ng-value-label">{{item.name}}</span>
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                                    <i class="fa fa-times" aria-hidden="true"></i>
                                </span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                                {{item.name}}
                            </ng-template>
                        </ng-select> 
                        <span class="custom-underline"></span>
                    </div>
                </div>
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Languages</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <p-multiSelect
                            [options]="filtersLists.languages"                            
                            formControlName="languages"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            styleClass="cust-p-multiselect"
                            defaultLabel="Languages">
                        </p-multiSelect>
                        <span class="custom-underline"></span>
                    </div>
                </div>
                
                <!-- <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Experience</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <p-multiSelect
                            styleClass="cust-p-multiselect"
                            [options]="filtersLists.exp_fields"
                            formControlName="exp_fields"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            defaultLabel="Experience Field">
                        </p-multiSelect>
                        <span class="custom-underline"></span>
                    </div>
                </div>                 -->
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Nationality</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <p-multiSelect
                            [options]="filtersLists.nationalities"
                            formControlName="nationalities"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            styleClass="cust-p-multiselect"
                            defaultLabel="Nationality">
                        </p-multiSelect>                     
                        <span class="custom-underline"></span>
                    </div>
                </div> 
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Gender</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <p-selectButton formControlName="gender"  [options]="genderOpts" optionLabel="title" styleClass="cus-selectButton" ></p-selectButton>
                    </div>
                </div>
               
            </div>
            

             <div class="col-lg-6">           
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
                        <span>Open to work</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding"> 
                        <p-dropdown
                            [options]="filtersLists.open_to_work"
                            formControlName="open_to_work"
                            optionLabel="name"
                            [filter]="true"                     
                            placeholder="Open to work">
                            <ng-template let-degree pTemplate="item">
                                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                    <div style="font-size:14px;float:left;margin-top:4px">{{degree.label}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <span class="custom-underline"></span>
                    </div>
                </div>
                
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Job Types</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <p-multiSelect
                            [options]="filtersLists.job_types"
                            formControlName="job_types"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            styleClass="cust-p-multiselect"
                            defaultLabel="Job types">
                        </p-multiSelect>                     
                        <span class="custom-underline"></span>
                    </div>
                </div>
         
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Job Adv Title</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <ng-select 
                            [items]="jobAdvTitleDD.items"
                            [virtualScroll]="true"
                            formControlName="job_adv_titles"
                            [loading]="jobAdvTitleDD.loading"
                            [multiple]="true"
                            [closeOnSelect]="false"
                            bindLabel="name"
                            placeholder="Job Adv Title"
                            notFoundText="No items found"
                            [dropdownPosition]="'bottom'"
                            (scrollToEnd)="jobAdvTitleDD.onScrollToEnd()"
                            (search)="jobAdvTitleDD.search($event)"
                            [searchFn]="jobAdvTitleDD.customSearchFn"
                            class="form-control ng-select-autocomplete"
                        >
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ng-value-label">{{item.name}}</span>
                                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                                    <i class="fa fa-times" aria-hidden="true"></i>
                                </span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                                {{item.name}}
                            </ng-template>
                        </ng-select> 
                        <!-- <input type="text" class="form-control" formControlName="job_adv_title" placeholder="Job Advertisement Title"> -->
                        <span class="custom-underline"></span>
                    </div>
                </div>
                
                <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Age</span>
                    </div>
                    <!-- formGroupName="age" -->
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding ng5-slider-div" formGroupName="age">  
                        <ng5-slider class="form-control" style="z-index: 1;" [options]="ageOptions" formControlName="age_value"  [(value)]="ageValue" [(highValue)]="ageHighValue" (userChange)="handleAgeChange($event)"></ng5-slider>
                        <!-- <ng5-slider class="form-control" style="z-index: 1;" [options]="ageOptions" formControlName="age"  [(value)]="ageValue" [(highValue)]="ageHighValue"></ng5-slider> -->
                        <!-- <p>Range: {{ageControl.controls['age_value'].value[0]}}-{{ageControl.controls['age_value'].value[1]}}</p>
                        <p-slider formControlName="age_value" [range]="true" [min]="20" [max]="80" [step]="1" (onChange)="handleAgeChange($event)"></p-slider>  -->                                     
                    </div>
                </div> 
                
                <!-- <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
                        <span>Years of Exp</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">
                        <p-dropdown
                            [options]="filtersLists.years_of_exps"
                            formControlName="years_of_exps"
                            optionLabel="name"
                            [filter]="true"
                            placeholder="Years of experience">
                            <ng-template let-degree pTemplate="item">
                                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                    <div style="font-size:14px;float:left;margin-top:4px">{{degree.label}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <span class="custom-underline"></span>
                    </div>
                </div> -->
                
                <!-- <div class="form-group equal-height-row-cols">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed label-fixed-dd">
                        <span>Degree Level</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding">                        
                        <p-dropdown
                            [options]="filtersLists.degree_levels"
                            formControlName="degree_level"
                            optionLabel="name"
                            [filter]="true"
                            placeholder="Degree Level">
                            <ng-template let-degree pTemplate="item">
                                <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                                    <div style="font-size:14px;float:left;margin-top:4px">{{degree.label}}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                        <span class="custom-underline"></span>
                    </div>
                </div> -->
            
                <!-- <div class="form-group equal-height-row-cols override-form-control-zindex">
                    <div class="col-md-3 col-sm-4 col-xs-12 label-fixed">
                        <span>Educations</span>
                    </div>
                    <div class="col-md-8 col-sm-8 col-xs-12 focus-no-padding"> 
                        <p-multiSelect
                            [options]="filtersLists.educations"
                            formControlName="educations"
                            optionLabel="name"
                            [filter]="true"
                            filterBy="label,value.name"
                            [showTransitionOptions]="'1ms'"
                            [hideTransitionOptions]="'2ms'"
                            styleClass="cust-p-multiselect"
                            defaultLabel="Education field">
                        </p-multiSelect>
                        <span class="custom-underline"></span>
                    </div>
                </div>                              -->
            </div>
        </div>

        <div class="row group-mar" formGroupName="location">
            <div class="col-xs-12">
                <div class="form-group equal-height-row-cols">
                    <div class="col-sm-2 col-xs-12 label-fixed">
                        <span>Location</span>
                    </div>
                    <div class="col-sm-8 col-xs-12 focus-no-padding">
                        <input formControlName="full_location" placeholder=" " type="text" class="form-control" #googlelocationplaceLocation>
                        <span class="custom-underline"></span>
                    </div>
                </div>

                <div class="form-group equal-height-row-cols">
                    <div class="col-sm-2 col-xs-12 label-fixed">
                        <span>Distance Range</span>
                    </div>
                    <div class="col-sm-3 col-xs-12 focus-no-padding">
                        <input formControlName="distance" type="number" class="form-control"  style="height:100%;">
                        <span class="custom-underline"></span>
                    </div>
                    <div class="col-sm-2 col-xs-12 label-fixed mar-top-mob">
                        <span>Distance Unit</span>
                    </div>
                    <div class="col-sm-3 col-xs-12 focus-no-padding">
                        <p-dropdown [options]="distanceTool" optionLabel="label"
                            formControlName="unit_distance" placeholder="Distance Unit"
                            styleClass="unit_distance">
                        </p-dropdown>
                    </div>
                </div>

                <div class="alert alert-info" role="alert">
                        <p style="text-align:justify"><span style="font-weight:bold">Note:</span> If you want to filter based on location field, please choose both distance range and distance unit fields to get the required results</p>
                </div>
                
            </div>
        </div>



        <div class="row div-margin-bo-30">
            <div class="col-xs-12 text-center">
                <button pButton style="background-color: #3bb34b !important; color:white !important; outline: none !important; box-shadow: none !important;margin-right: 10px;" icon="pi pi-check" (click)="sendFilters()" label="Apply" class="p-button-text">
                </button>
                <p-button icon="pi pi-times" (click)="clearAllFilters()" label="Clear All Filter"></p-button>
            </div>
        </div>
    </div>
    <!-- <p>  {{ filtersForm.value | json }} </p> -->
</form>