<h3>Manage Help Topics</h3>


<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ hTopicsArray.length }}</span></p>
  <!-- <p>filtered:<span class="badge badge-primary badge-pill">{{ filteredHTopics.length }}</span></p> -->


<p-table #dt [value]="hTopicsArray" [(selection)]="filteredHTopics" dataKey="id" styleClass="ui-table-htopics" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="hTopicsArray.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['type', 'main_cat', 'sub_cat', 'title', 'order']">
    <ng-template pTemplate="caption">
         <!-- Help topics -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th  style="width:30px;"><i title="add new help topic" class="fas fa fa-plus-circle" data-toggle="modal" data-target="#topicModal" (click)="displayCreateModal()" ></i></th>
            <th pSortableColumn="type" style="width:100px;">Type <p-sortIcon field="type" ></p-sortIcon></th>
            <th pSortableColumn="main_cat" >Main Category <p-sortIcon field="main_cat" ></p-sortIcon></th>
            <th pSortableColumn="sub_cat" >Sub Category <p-sortIcon field="sub_cat" ></p-sortIcon></th>
            <th pSortableColumn="title" >Title <p-sortIcon field="title" ></p-sortIcon></th>
            <th pSortableColumn="order" style="width:100px;">Order <p-sortIcon field="order"></p-sortIcon></th>
            <th pSortableColumn="active" style="width:100px;">Status <p-sortIcon field="active" ></p-sortIcon></th>
            <th style="width:150px;">Actions</th>
        </tr>
        <tr>
            <th>
                <!-- <p-tableHeaderCheckbox></p-tableHeaderCheckbox> -->
                <!-- <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected htopics" (click)="displayDeleteAlert(4)"></i> -->
            </th>
            <th>
                <p-dropdown [options]="types" (onChange)="dt.filter($event.value, 'type', 'equals')" styleClass="ui-column-filter" [(ngModel)]="type" [filter]="true" placeholder="" [showClear]="false">
                  <ng-template let-option pTemplate="item">
                      <span [class]="">{{option.label}}</span>
                  </ng-template>
                </p-dropdown>
            </th>
            <th>
              <!-- <input pInputText type="text" (input)="dt.filter($event.target.value, 'main_cat', 'contains')" placeholder="" class="ui-column-filter"> -->
              <p-dropdown [options]="main" (onChange)="dt.filter($event.value, 'main_cat_id', 'equals')" styleClass="ui-column-filter cat" [(ngModel)]="main_cat" [filter]="true" [showClear]="false">
                <ng-template let-option pTemplate="item">
                    <span [class]="">{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th>
              <!-- <input pInputText type="text" (input)="dt.filter($event.target.value, 'sub_cat', 'contains')" placeholder="" class="ui-column-filter"> -->
              <p-dropdown [options]="sub" (onChange)="dt.filter($event.value, 'sub_cat_id', 'equals')" styleClass="ui-column-filter cat" [(ngModel)]="sub_cat" [filter]="true" [showClear]="false">
                <ng-template let-option pTemplate="item">
                    <span [class]="">{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'title', 'contains')" placeholder="" class="ui-column-filter title">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'order', 'contains')" placeholder="" class="ui-column-filter order">
            </th>
            <th>
              <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'active', 'equals')" styleClass="ui-column-filter" [(ngModel)]="status"  [showClear]="false">
                <ng-template let-option pTemplate="item">
                    <span [class]="'htopic-badge status-' + option.value">{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-htopic>
        <tr class="ui-selectable-row"    (mouseover)="htopic.display = true" (mouseleave)="htopic.display = false">
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
                <!-- <p-tableCheckbox [value]="htopic" (click)="addToSelected(htopic)"></p-tableCheckbox> -->
            </td>
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
                <i *ngIf="htopic.type === 'user help'" title="user" class="fa fa-user"></i>
                <i *ngIf="htopic.type === 'company help'" title="company" class="fa fa-suitcase"></i>
            </td>
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
              {{htopic.main_cat}}
            </td>
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
              {{htopic.sub_cat}}
            </td>
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
              {{htopic.title | summary:100}}
            </td>
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
              {{ htopic.order }}
            </td>
            <!-- <td class="inactive " [class.active-text]="htopic.active">
              {{  htopic.active | activation:'Active': 'InActive'  }}
            </td> -->
            <td data-toggle="modal" data-target="#topicModal" (click)="displayEditFormModal(htopic)">
              <span class="htopic-badge" [class.status-0]="!htopic.active" [class.status-1]="htopic.active" >{{  htopic.active | activation:'Active': 'InActive'  }}</span>
            </td>
            <td class="actions">
              <span *ngIf="htopic.display">
                <i title="preview" data-toggle="modal" data-target="#topicModal" class="fa fa-eye" (click)="displayPreviewModal(htopic)"  ></i>
                <i title="edit" data-toggle="modal" data-target="#topicModal"  class="fa fa-edit" (click)="displayEditFormModal(htopic)"></i>
                <i  title="delete" class="fa fa-trash"  data-toggle="modal" data-target="#topicModal" (click)="displayDeleteAlert(htopic)"  ></i>
                <i  [class.activate]="htopic.active" title="activation" (click)="activateHtopic(htopic)"  class="fa fa-power-off "></i>
              </span>
             </td>

        </tr>
    </ng-template>
    <ng-template pTemplate="emptyhtopic">
        <tr>
            <td colspan="7" style="text-align:center;padding:15px;">No htopics found.</td>
        </tr>
    </ng-template>
</p-table>








<!-- Start of modal-->
<div class="modal fade" *ngIf="displayModal" id="topicModal"  tabindex="-1" role="dialog" aria-labelledby="topicModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button"  (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>

        <h3 class="modal-title"  id="topicModalLabel" translate>
           <span  *ngIf="mode === 'create'"  translate>help.labels.AddNewHelpTopic</span>
           <span  *ngIf="mode === 'edit'"  translate>help.labels.Edit</span>
           <span  *ngIf="mode === 'preview'"  translate>help.labels.Preview</span>
           <span  *ngIf="mode === 'delete'"  translate>help.labels.Delete</span>
        </h3>
      </div>

     <app-edit-topic-modal *ngIf="displayModal" [htopicEn]="hTopic" [htopicId]="hTopicIdToDelete" [mode]="mode"  [openedFromSidebar]="false" (closeUpdateModal)="showUpdatedHelpTopicInTable($event)"
            [languagesArray]="languagesArray" (closeCreateModal)="showNewHelpTopicInTable($event)" [main_categories]="main_categories"
            [sub_categories]="sub_categories"  (closeDeleteModal)="removeHelpTopicFromTable($event)"></app-edit-topic-modal>

      <div class="modal-footer">
        <button class="btn btn-default"  *ngIf="mode === 'preview'"   (click)="closePreviewOpenEdit()" style="float: left;" translate>help.labels.Edit</button>
        <button type="button"  *ngIf="mode === 'preview' " class="btn btn-danger" data-dismiss="modal" (click)="closeModal()" style="float: right;" translate>help.labels.Close</button>

      </div>
    </div>
  </div>
</div>
<!-- end of  modal-->


