<app-pre-loader *ngIf="mainCategories.length === 0"></app-pre-loader>
<app-help-header [inSearchInterface]="true"></app-help-header>

<!-- Search result section -->
<div *ngIf="mainCategories.length !== 0">
  <div *ngIf="searchQuery !== ''"   class="result-container">
  
    <div class="result-card">
      <h1 class="" translate>help.SearchResult</h1><hr>

      <div class="main-categories-div" *ngIf="filteredMainCategories[currentLangId - 1].length !== 0">
        <h2 translate> help.MainCategories</h2>
        <ul class="list-group sub-group-list">
          <!-- <div *ngIf="filteredMainCategories[currentLangId - 1].length === 0"><p class="no-matches" translate>help.NoMatchesWasFound</p></div> -->
          <li  *ngFor="let mc of filteredMainCategories[currentLangId - 1]; let i = index" class="list-group-item" >
            <h3>
              <a  
              (click)="navigate('main',mc.name,mc.id)"
              >{{ mc.name }}
              </a>
            </h3>
          </li>
        </ul>
      </div>

      <div class="sub-categories-div" *ngIf="!fSCIsEmpty">
        <h2 translate> help.SubCategories</h2>
        <ul class="list-group sub-group-list ">
          <!-- <div *ngIf="fSCIsEmpty"><p class="no-matches" translate>help.NoMatchesWasFound</p></div> -->
          <div *ngFor="let minSubCatArr of filteredSubCategories[currentLangId - 1]; let j = index">
          <li *ngFor="let subCat of minSubCatArr" class="list-group-item">
            <h3>
              <a  
              (click)="navigate('sub',subCat.main_name,subCat.main_cat_id,subCat.name,subCat.id)"
              >{{ subCat.name }}
              </a>
            </h3>
          </li>
          </div>
        </ul>
      </div>

      <div class="help-topic-div" *ngIf="filteredHelpTopics[currentLangId -1].length !== 0">
        <h2 translate> help.HelpTopics</h2>
        <ul class="list-group sub-group-list">
          <!-- <div *ngIf="filteredHelpTopics[currentLangId -1].length === 0"><p class="no-matches"  translate>help.NoMatchesWasFound</p></div> -->
          <li *ngFor="let ht of filteredHelpTopics[currentLangId -1] " class="list-group-item">
            <h3>
              <a 
              (click)="navigate('slug',getMainCatName(ht.main_cat_id),null,getSubCatName(ht.sub_cat_id, ht.main_cat_id),null, ht.id, ht.slug)"
              >{{ ht.title }}
              </a>
            </h3>
          </li>
        </ul>
      </div>

      <div *ngIf="filteredMainCategories[currentLangId - 1].length === 0 && fSCIsEmpty && filteredHelpTopics[currentLangId -1].length === 0"><p class="no-matches"  translate>help.NoMatchesWasFound</p></div>
    </div>
      
  </div>
</div>
      
<!-- End of Search result section -->