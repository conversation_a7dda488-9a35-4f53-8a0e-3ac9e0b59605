<un-auth-top-navbar *ngIf="role === 'unauth'" [inHomePage]="false"></un-auth-top-navbar>
<user-topbar *ngIf="role ==='ROLE_JOB_SEEKER'" [inHomePage]="false"></user-topbar>
<company-topbar *ngIf="role === 'ROLE_EMPLOYER'" [inHomePage]="false"></company-topbar> 

<page-navbar [navType]="'empty'"></page-navbar>
<div class="container-fluid general-wrapper notfound-wrapper">
  <h1 class="title">404 Page Not Found!</h1>
  <p class="notfound-help">It looks like you've reached a URL that doesn't exist. Please use the navigation above or the following links below</p>
  <ul class="list-group help-links">
    <li class="list-group-item">
      <a routerLink="/">CVeek Home</a>
    </li>
    <li class="list-group-item">
      <a [routerLink]="['/i/help']">Help</a>
    </li>
    <li class="list-group-item">
      <a [routerLink]="['/i/faq']">Faq</a>
    </li>
    <li class="list-group-item">
      <a [routerLink]="['/search-job']">Find Jobs</a>
    </li>
  </ul>
</div>

<div class="flex-space-fix"></div>
<app-footer></app-footer>


  