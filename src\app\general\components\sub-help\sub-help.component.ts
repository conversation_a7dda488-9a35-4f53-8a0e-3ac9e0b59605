import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from 'app/general/services/help.service';
import { Title, Meta } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { LanguageService } from 'app/admin/services/language.service';
import { Subject } from 'rxjs/Subject';

@Component({
  selector: 'app-sub-help',
  templateUrl: './sub-help.component.html',
  styleUrls: ['./sub-help.component.css']
})
export class SubHelpComponent implements OnInit, OnDestroy {
  @Input('helpTopics')helpTopics: {'id': number, 'title': string, 'main_cat_id': number, 'sub_cat_id': number, 'slug': string}[][] = [];
  @Input('subCat')subCat: { 'name': string, 'id': number, 'main_cat_id': number}[] = [];
  @Input('mainCat')mainCat: { 'name': string, 'id': number, 'langId': number}[] = [];
  @Input('currentLangId')currentLangId = 1;
  @Input('openedFromHelpTopic')openedFromHelpTopic = true;
  username;
  languagesArray = [];
  id = null;
  @Output('helpHomeClicked')helpHomeClicked = new EventEmitter();
  private ngUnsubscribe: Subject<any> = new Subject();
  // @Output('helpTopicClicked')helpTopicClicked = new EventEmitter;

  constructor(private translate: TranslateService,
              private title: Title,
              private meta:Meta,
              private route: ActivatedRoute,
              private router: Router,
              private languageService: LanguageService,
              private helpService: HelpService) {

    translate.addLangs(['en', 'ar']);
    translate.setDefaultLang('en');
    const browserLang = translate.getBrowserLang();
    this.username = localStorage.getItem('username');

  }

  ngOnInit() {
    if (!this.openedFromHelpTopic) {
     // this.title.setTitle(this.subCat[this.currentLangId - 1].name);
     
      for (let h of this.helpTopics[this.currentLangId - 1]) {
        // console.log('h title', h.title);
      }
    } else {
      this.getId();
    }

  }

  getId() {
    this.route.paramMap.subscribe(params => {
      
      this.id = +params.get('subCatId');
      this.getLanguages();

    });
  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
     
      let temp = res['data'];
      for ( let lang of temp) {
        if(lang.id==1)
        this.languagesArray.push({
          'id'  : lang.id,
          'name': lang.name
        });

      }

      this.getSubCat();
    });

  }

  getSubCat() {
    this.helpService.getSubCat(this.id).takeUntil(this.ngUnsubscribe).subscribe(res => {
      
      let temp = res['sub_categories'];
      for (let i = 0; i < this.languagesArray.length; i++) {
        this.helpTopics[i] = [];
        this.subCat.push({
          'id': temp.id,
          'name': temp.help_center_sub_cat_trans[i].name,
          'main_cat_id': temp.help_center_main_cat_id
        });

        this.mainCat.push({
          'id': temp.help_center_main_cat.id,
          'name': temp.help_center_main_cat.help_center_main_cat_trans[i].name,
          'langId': temp.translated_languages_id
        });
        if (temp.help_center.length !== 0 ) {
          for (let ht of temp.help_center) {
            this.helpTopics[i].push({
              'id': ht.id,
              'title': ht.help_center_trans[i].title,
              'main_cat_id': ht.help_center_main_cat_id,
              'sub_cat_id': ht.help_center_sub_cat_id,
              'slug': ht.help_center_trans[i].slug
            });
          }
        }

      }

      if (this.openedFromHelpTopic) {
        this.title.setTitle('CVeek website  سيفيك | Help Center | ' + this.subCat[this.currentLangId - 1].name);
        this.meta.updateTag({ name: 'description', content: 'CVeek | Help Center | ' + this.subCat[this.currentLangId - 1].name });
      }
      
    });
  }


  displayMainCats() {
   this.helpHomeClicked.emit();
  }


  changeLang( langId: number) {
    this.translate.use(this.languageService.getLangAbbrev(langId));
    this.currentLangId = langId;
    this.title.setTitle('CVeek website  سيفيك | Help Center | ' + this.subCat[this.currentLangId - 1].name);
    this.meta.updateTag({ name: 'description', content: 'CVeek | Help Center | ' + this.subCat[this.currentLangId - 1].name });
  }
  
  navigate(type,mainCatName,mainCatId?,subCatName?,helpId?,helpSlug?){
    if(type==='main')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) , mainCatId ]);
    else if(type==='slug')
      this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) ,  this.friendlyUrl(subCatName) , helpId, this.friendlyUrl(helpSlug) ]);
  }

  friendlyUrl(text){
    text = text.toLowerCase();
    text = text.replace(/ /g,'-');
    return text;
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }


}
