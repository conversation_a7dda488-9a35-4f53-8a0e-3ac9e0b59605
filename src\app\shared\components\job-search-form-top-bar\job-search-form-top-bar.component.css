.navbar-search-job-form .search-btn , .navbar-search-job-form .search-btn:active , 
.navbar-search-job-form .search-btn:focus , .navbar-search-job-form .search-btn:hover{
    background: transparent;
    outline: 0;
    box-shadow: none;
}
.navbar-search-job-form .search-btn{
    margin-bottom: 0;
    padding-bottom: 0;
    padding-top: 0;
}
.navbar-search-job-form .search-btn i{
    color:#3D7BCE;
    font-size: 24px;
    margin-left: 16px;
}
.form-group{
    margin-bottom:0;
}

:host ::ng-deep .navbar-search-job-form .job-title-control{
    width:98%;
    padding-right:0;
    height:32px;
}

.navbar-search-job-form .search-div{
    display:inline-block;
    width: calc(100% - 110px);
}

.navbar-search-job-form .focus-no-padding .custom-underline{
    width: calc(100% - 118px) !important;
    bottom:19px !important; 
  }

.navbar-search-job-form .country-div{
    display:inline-block;
}
.navbar-search-job-form .search-btn-div{
    display:inline-block;
    width:50px;
}

.navbar-search-job-form .country-link{
    width: 16px;
    height: 16px;
    border-radius: 3px;
}
.navbar-search-job-form .country-dropdown .country-img{
    width:30px;
    position: absolute;
    top: -11px;
    /* float:right; */
    border-radius: 2px;
}
.navbar-search-job-form .country-div :host ::ng-deep .country-dropdown .country-name{
    display:none;
}
:host ::ng-deep  .ng-select .ng-select-container .ng-value-container .ng-placeholder ,  
:host ::ng-deep  .home-search-job-form .country-dropdown .ui-dropdown .ui-dropdown-label ,
:host ::ng-deep  .home-search-job-form .ng-select .ng-select-container{
    color: #b4b4b4;
}
:host ::ng-deep .home-search-job-form .country-dropdown .ui-dropdown{
    border-bottom: 1px solid #ccc !important;
    padding-bottom: 32px;
}
.navbar-search-job-form .search-btn-div span , .home-search-job-form .search-btn-div i{
    display: none;
}
.home-search-job-form .search-btn-div span{
    display:inline-block;
}
/* :host ::ng-deep .navbar-search-job-form .country-dropdown .ui-dropdown .ui-dropdown-label {
    display: none;
} */

.home-search-job-form .search-job-form{
    display:flex;
    margin-bottom: 0;
}
.home-search-job-form .search-div{
    width: 42%;
    margin-right: 2%;
}
.home-search-job-form .country-div{
    width: 33%;
    margin-right: 2%;
}
.home-search-job-form .search-btn-div{
    width:21%;
}
.home-search-job-form .search-btn-div .search-btn{
    width:100%;
    background: #3D7BCE;
    color:#fff;
    border-radius: 14px;
    font-size:17px;
}
.home-search-job-form .country-div :host ::ng-deep .country-dropdown img{
    display:none;
}
.home-search-job-form .country-div :host ::ng-deep .country-dropdown img{
    display:none;
}
.home-search-job-form .country-div :host ::ng-deep .country-dropdown .country-name{
    display:inline-block;
}

@media screen  and (max-width:1160px){
    .form-group {
        margin-bottom: 3px;
    }
}

@media screen  and (max-width:900px){
    .form-group {
        margin-bottom: 0px;
    }
    .form-horizontal{
        margin-top: 8px;
    }
}

@media screen  and (max-width:767px){
    /* .navbar-search-job-form .form-horizontal{
        margin-top: 18px;
    } */
    .country-dropdown .country-img{
        width:23px;
    }
    .search-btn i{
        font-size: 21px;
        margin-left: 2px;
    }
    .focus-no-padding .custom-underline {
        width: calc(100% - 115px) !important;
        bottom: 20px !important;
    }
}
@media screen  and (max-width:600px){
    .home-search-job-form .search-job-form {
        flex-wrap: wrap;
    }
    .home-search-job-form .search-div{
        width: 100%;
        margin-bottom: 15px;
    }
    .home-search-job-form .country-div{
        width: 100%;
        margin-bottom: 25px;
    }
    .home-search-job-form .search-btn-div , .home-search-job-form .search-btn-div .search-btn{
        width:100%;
    }
    /* .home-search-job-form .country-dropdown .ui-dropdown .ui-dropdown-label{
        padding-left: 10px !important;
    } */
}
@media screen  and (max-width:430px){
    :host ::ng-deep .job-title-control .ng-dropdown-panel{
        width:150%;
    }
}