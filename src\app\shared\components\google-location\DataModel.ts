import { Place } from 'shared/Models/place';
//  Declare  Google Location Component Inititial Data
export class DataModel {
  readyCurrent: boolean;
  latLocation: number = 0;
  lngLocation: number = 0;
  cityLocation: string;
  countryLocation: string;
  countryCodeLocation: string;
  streetAddressLocation: string;

  location: {};
  locationSlected;

  /////distance
  distance: number;
  distanceTool = [
    { 'value': '', 'label': '' },
    { 'value': 'km', 'label': 'KM' },
    { 'value': 'mi', 'label': 'Miles' }
  ];
  // unit_distance:
  unit_distance: any;


  initializeDistance(unit_distanse_input, distanse_input) {
    if (unit_distanse_input) {
      this.unit_distance = this.distanceTool.find(dis => dis.value === unit_distanse_input);
    }
    this.distance = distanse_input;
  }

  ///////////
  setCurrentLocationControls(place: Place) {
    this.countryLocation = place.country;
    this.cityLocation = place.city;
    this.streetAddressLocation = place.streetAddress;
    this.latLocation = place.latitude;
    this.lngLocation = place.longitude;
    this.countryCodeLocation = place.countryCode;

    this.locationSlected = this.countryLocation + ',' + this.cityLocation + ',' + this.streetAddressLocation; 
    // if (place.full == 'Dragging') {
    //   this.locationSlected = this.countryLocation + ',' + this.cityLocation + ',' + this.streetAddressLocation;
    // } else {
    //   this.locationSlected = this.countryLocation + ',' + this.cityLocation + ',' + this.streetAddressLocation;  
    // }

  }

  initializeLocation(location_input) {
    this.countryLocation = location_input.country;
    this.cityLocation = location_input.city;
    this.streetAddressLocation = location_input.streetAddress ? location_input.streetAddress : null;
    this.latLocation = location_input.latitude;
    this.lngLocation = location_input.longitude;
    this.countryCodeLocation = location_input.countryCode;
    if (location_input.country) {
      this.locationSlected = this.countryLocation + ',' + this.cityLocation;
      if (this.streetAddressLocation != null) this.locationSlected = this.locationSlected + ',' + this.streetAddressLocation
      /* this.companyLocation.controls['location'].setValue(this.setFullLocation()); */
    } else {
      this.locationSlected = '';
    }
  }

}


