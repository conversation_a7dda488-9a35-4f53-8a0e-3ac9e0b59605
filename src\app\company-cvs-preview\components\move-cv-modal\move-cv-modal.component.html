<div class="modal fade image-editor-modal" id="moveCVToFolderModal"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="cancel()"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Add CV to folder</h4>
            </div>
            <div class="modal-body">
                <app-pre-loader [show]="modalLoader" [size]="'small'"></app-pre-loader>
                <div style="margin-bottom:30px;" class="alert alert-warning" *ngIf="currentCv === null">
                    <strong>Warning!</strong> Selected CVs will be moved from their old folders to new folders
                  </div>
                <form #form="ngForm" [formGroup]="moveCVForm" (ngSubmit)="submit()" class="form-horizontal">
                    <div class="form-group has-feedback" [ngClass]="{'has-error': submitted && !moveCVForm.controls['folders_ids'].valid}">
                        <div class="col-sm-3 alignment-right">
                        </div>
                        <div class="col-sm-6 focus-no-padding">
                            <p-multiSelect
                                [options]="foldersddData"
                                formControlName="folders_ids"  
                                optionLabel="label"
                                [filter]="true"
                                filterBy="label,value.name"
                                styleClass="form-control custom-p-multiselect"
                                [showTransitionOptions]="'1ms'"
                                [hideTransitionOptions]="'2ms'"
                                defaultLabel="Select Folder"
                                [ngClass]="{'has-val':!moveCVForm.controls['folders_ids'].errors?.required}"
                                >
                            </p-multiSelect>
                            <!-- <span class="custom-underline"></span>
                            <label class="control-label custom-control-label">Select Folder</label> -->
                        </div>
                        <div class="col-sm-3">
                            <span class="error-message" *ngIf="submitted && moveCVForm.controls['folders_ids'].errors?.required" translate>validationMessages.required</span>
                        </div>
                    </div>
                    
                    <!-- <p>  {{ form.value | json }} </p> -->
                    <div class="text-center">                           
                        <button class="btn btn-success small-btn" type="submit">Save</button>&nbsp;
                        <button class="btn btn-default cust-cancel-btn small-btn" type="button" (click)="cancel()">Cancel</button>
                    </div>
                    
                </form>
                    
            </div>

        </div>
    </div>
</div>