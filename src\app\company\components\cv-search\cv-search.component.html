<div class="CForm">
   <!--  <div id="bg">
        <img src="https://demo.cmssuperheroes.com/themeforest/wp-recruitment/wp-content/uploads/2016/10/bg-feature-parallax_02.jpg" alt="">
    </div> -->
    <form #form="ngForm" (keydown.enter)="$event.preventDefault()" [formGroup]="searchForm" class="form-horizontal validate-form" (ngSubmit)="form.valid">
        <div class="col-sm-12">
        
        <div class="col-sm-3 col-sm-offset-2">
                <input type="input" placeholder="Enter Search Key">
        </div>
        <div class="col-sm-3" [ngClass]="{'has-val':searchForm.controls['location'].value}">
                <input formControlName="location" placeholder=" " type="text" class="form-control" id="location" #googlelocationplace>
                <!-- (keyup.enter)="currentLocationKeyUp($event)" -->
                <span class="custom-underline"></span>
                <!-- <span class="glyphicon  form-control-feedback" aria-hidden="true"></span> -->
                <label class="control-label custom-control-label" translate>Location</label>
            </div>
        </div>
    </form>
</div>

 

