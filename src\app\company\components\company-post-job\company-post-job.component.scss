body{
  background-color: #eee !important;
}
#page-content-wrapper {
  transition: margin-left .5s ease;
}

#page-content-wrapper .page-content {
font-family: 'Exo2-Regular', sans-serif !important;

  padding: 0px 15px 0px;
}

.table-page-content {
  position: relative;
}
.custom-checkbox{
  padding:0;
}
.custom-checkbox input {
  // width: 80%;
  width:36px;
  height: 23px !important;
  border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));

}


.table-page-content .page-title {
  position: absolute;
  background: white;
  padding-right: 30px;
  z-index: 1;
}

.CForm {
}
.majorLabel{
  margin-top: 36px;
}

.input-group[class*=col-] {
  float: left !important;
  padding-right: 5px;
  padding-left: 4px;
}

.strike2 {
  border-right: 1px solid gray
}

.pad-top {
  padding-top: 14px;
}
.pad-top6{
  padding-top:12px;
}
.editing{
color:red;
background-color: red;
}
.glyphicon{
  left:87%;
}
.pad-top1{
  padding-top: 30px;
}

// .pad-top2 {
//   padding-top: 75px;
// }
.pad-top3{
  padding-top: 35px;
}
.pad-top4{
  padding-top: 25px;
}
.custom-btn{
  // width:100px;
  background-color: #3D7BCE;
  border-color: #3D7BCE;
  border-radius: 0;
}
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after {
  background: #276ea4;
}

.e-multi-select-wrapper input.e-dropdownbase::-webkit-input-placeholder {
  color: #999;
  font-size: 16px;
  padding-left: 14px;
}

.e-multi-select-wrapper .e-searcher {
  width: 50%;
}

#title {
  display: none;
}

#subtitle {
  display: none;
}
.step4-warning{
  text-align: justify;
  margin-bottom: 30px;
  margin-right: 40px;
}
:host{
  background-color: #eee
  !important;
}
.container-fluid{
padding: 20px;
padding-top: 31px;
background-color: #eee;
background-attachment: fixed;
background-size: 100% 100%;
// height: 200vh;
min-height: 500px;
// min-height: 100vh;
/*overflow-y: scroll;*/

}
.container-fluid::after{
  background-color: #eee !important;
}
.add{
background-color: white;
position: absolute;
  width: 62%;
  left:410px;
height: 78%;
 border-radius: 10px;
 padding-top: 30px;
 padding-left: 40px;
    font-family: 'Exo2-Regular', sans-serif !important;
   }
.details{
  position: static;
  margin-left: -70px;
  padding: 30px 0 40px 40px;
  background-color: white;
  border-radius: 10px;
  font-family: 'Exo2-Regular', sans-serif !important;
  margin-left: 63px;
  margin-right: 15px;
}

.ng5{
  z-index: 1; margin-top:10px; height: 2px; width:73%;
}

.next{
 background-color: #3d7bce;
  border: #3d7bce solid 1px;
  margin-right: 4px;
}
.back{
 background-color: #959595;
 border:#959595 solid 1px;
 margin-right: 4px;
}
.back1{
  margin-left: 50px;
}
.back2{
  margin-left: 25px;
}
.finish{
 background-color: #30A03E;
  border: #30A03E solid 1px;
}
.wizardButton{
 padding:0px 0px 0px 0px;
}
.wizardButton button{
 border-radius: 2px;
//  width: 70px;
}
.wizardButton button:hover{
 opacity: 0.8;
}
.labelAdd{
  font-size: 15px;
  color: #4f94df;
   text-align: right;
}
.details .check{

}
.custom-checkbox4{
  margin-left: -26px;
}
.custom-checkbox5{
  margin-left: -22px;
}
.custom-checkbox3{
  margin-left: -18px;
}
.custom-checkbox6{
  margin-left: -14px !important;
}
.custom-checkbox7{
  margin-left: -13px !important;
}
.custom-checkbox8{
  margin-left: -21px !important;
}
.custom-checkbox9{
  margin-left: -34.3px !important;
}
/*.company-name{
text-align: center; margin-top:70px; margin-left: -60px;
}*/
.company-industry{
text-align: center; margin-left: -60px;
}
.extra-check{
  width: 20px; height: 20px; margin-top: 2px;
}
.check{
  border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));

}
.margin1{
  margin-left: -1px;
}
.margin2{
  margin-left: -2px;
}
:host ::ng-deep .ng5-slider .ng5-slider-pointer{
  width:20px;
  height: 20px;
}
:host ::ng-deep .ng5-slider .ng5-slider-pointer:after{
  top:6px;
  left:6px;

}
.salary-label{
  text-align: right;
}

 table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  border: 1px solid #ddd;
}


th,
td {
  text-align: left;
  padding: 5px;
  padding-bottom: 0px;
  background-color: white;
}

tr:nth-child(even) {
  background-color: #f2f2f2
}
th{
  text-align: center;
}


/* @media only screen and (max-width: 760px){
  th:last-of-type {
  display: none;
  }
} */
:host ::ng-deep .salary-type div{
width: 25% !important;
}
:host ::ng-deep aw-wizard {
  display: flex !important;
  justify-content: flex-start !important; }

:host ::ng-deep aw-wizard .wizard-steps {
    top: 0 !important;
    display: flex !important; }

:host ::ng-deep aw-wizard.vertical {
  flex-direction: row !important; }

:host ::ng-deep aw-wizard.vertical .wizard-steps {
    min-width: calc(100% - 280px) !important;
    width: 50% !important;
    height: 100% !important;
    flex-direction: column !important; }

    :host ::ng-deep aw-wizard-step,
    :host ::ng-deep aw-wizard-completion-step {
  height: auto !important;
  width: 100% !important; }

  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator * {
  -webkit-box-sizing: border-box !important;
  -moz-box-sizing: border-box !important;
  box-sizing: border-box !important;
  background-color: white;}
  :host ::ng-deep aw-wizard-navigation-bar ul{
    background-color: white;
    width: 310px;
    border-radius: 10px;
    //height: 70%;
    position: fixed !important;
    top: 140px !important;

  }
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator{
    margin-bottom: 100px;

  }
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li {
    margin:30px;
  position: relative !important;
  pointer-events: none !important; }
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li:hover ul.steps-indicator{
    background-color:#3D7BCE ; }
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li a .label {
    color: #3D7BCE !important;
    line-height: 20px !important;
    font-size: 16px !important;
    text-transform: none;
    text-decoration: none !important;
  font-family: 'Exo2-Regular', sans-serif !important; }

  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable {
  pointer-events: auto !important; }
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable a:hover {
    cursor: pointer !important;
  text-decoration: none; }
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li.navigable a:hover .label {
    color: #3D7BCE !important; }

  :host ::ng-deep aw-wizard-navigation-bar.vertical {
    max-width: 280px !important;
    width: 50% !important;
    height: 100% !important;
    position: sticky;
    top: 100px;
  }
  :host ::ng-deep aw-wizard-navigation-bar.vertical:hover {
    background-color: #eee !important;
  }
  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    list-style: none !important;
    margin: auto !important;
    // position: fixed !important;
    position:sticky !important;
    z-index: 101 !important;
}

  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .step-indicator {
    background-color: #3D7BCE !important;

  }


  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .li {
    background-color: #eee !important;

  }
  /*:host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a:hover .label p {
    background-color: #eee !important;

  }*/
  /*:host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.current  a .label{
    background-color: #eee;
    width: 100%;
  }
  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.current  a .label p{
    background-color: #eee;
  }
  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li.done  a .label{
    color: #30A03E !important;
  }*/
  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li:not(:last-child) {
      margin-bottom: 0 !important;
      padding-bottom: 20px !important; }
  :host ::ng-deep aw-wizard-navigation-bar.vertical ul.steps-indicator li a {
      display: flex !important;
      flex-direction: row !important;
      align-items: center !important; }
      aw-wizard-navigation-bar.vertical ul.steps-indicator li a .label {
        text-align: left !important; }
        :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical ul.steps-indicator li a .label {
          margin-left: 0 !important;
          margin-right: 15px !important;
          text-align: right !important; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator {
  padding: 5px 5px 5px 5px;
}
  :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator {
    padding: 5px 5px 5px 5px; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li:not(:last-child):after {
    background-color: #E6E6E6;
    content: '';
    position: absolute;
    left: -25px;
    top: 0px;
    height: calc(100% - 50px);
    width: 0px; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li:not(:last-child):after {
      left: auto;
      right: -25px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li a {
    min-height: 50px; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li .step-indicator {
    top: 0;
    left: -10px;
    position: absolute;
    width: 25px;
    height: 25px;
    text-align: center;
    vertical-align: middle;
    line-height: 46px;
    border-radius: 50%;
    border: 1px solid #3D7BCE; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li .step-indicator {
      left: auto;
      right: -50px;}
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.optional .step-indicator {
    border: 2px solid #30A03E; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.done .step-indicator {
    border: 2px solid #30A03E;}
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.current .step-indicator {
    border: 2px solid #808080; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.editing .step-indicator {
    border: 2px solid #FF0000; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.completed .step-indicator {
    border: 2px solid #339933; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable a:hover .step-indicator {
    position: absolute;
    width: 50px;
    height: 50px;
    text-align: center;
    vertical-align: middle;
    line-height: 46px;
    border-radius: 100%;
    border: 2px solid #3D7BCE; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty ul.steps-indicator li.navigable.optional a:hover .step-indicator {
    border: 2px solid #3D7BCE; }


    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator {
  padding: 5px 5px 5px 50px; }
  :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator {
    padding: 5px 55px 5px 5px; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li:not(:last-child):after {
    background-color: #E6E6E6;
    content: '';
    position: absolute;
    left: -25px;
    top: 50px;
    height: calc(100% - 50px);
    width: 0px; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-filled-symbols ul.steps-indicator li:not(:last-child):after {
      left: auto;
      right: -25px; }


    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator {
  padding: 0px 0px 0px 34px; }
  :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator {
    padding: 5px 5px 5px 5px; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li:not(:last-child):after {
    background-color: #E6E6E6;
    content: '';
    position: absolute;
    left: -25px;
    top: 50px;
    height: calc(100% - 50px);
    width: 0px; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li:not(:last-child):after {
      left: auto;
      right: -25px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li a {
    min-height: 50px; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
    top: 0;
    left: -50px;
    position: absolute;
    width: 50px;
    height: 50px;
    text-align: center;
    vertical-align: middle;
    line-height: 46px;
    border-radius: 100%;
    border: 2px solid #E6E6E6;
    color: #E6E6E6; }
    :host ::ng-deep [dir="rtl"] aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li .step-indicator {
      left: auto;
      right: -50px; }
      :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.optional .step-indicator {
    border: 2px solid #3D7BCE;
    color: #3D7BCE; }
    /*:host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.done .step-indicator {
    border: 2px solid #30A03E;
    color:#30A03E; }*/
    :host ::ng-deep .red aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.editing .step-indicator {
      border: 2px solid red;
      color:red; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.current .step-indicator {
    border: 2px solid white;
    color: white;
    background-color:#3D7BCE ;
      }

    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.completed .step-indicator {
    border: 2px solid red ;
    color: red; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable a:hover .step-indicator {
    position: absolute;
    width: 50px;
    height: 50px;
    text-align: center;
    vertical-align: middle;
    line-height: 46px;
    border-radius: 100%;
    border: 2px solid #eee ;
    color: #eee;
  background-color: #3D7BCE;}
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.optional a:hover .step-indicator {
    border: 2px solid #eee;
    color: #eee;
  background-color: #3D7BCE;}
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.done a:hover .step-indicator {
    border: 2px solid #eee;
    color: #eee;
  background-color: #3D7BCE; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.current a:hover .step-indicator {
    border: 2px solid #eee;
    color: #eee;
  background-color: #3D7BCE;}
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.editing a:hover .step-indicator {
    border: 2px solid #cc0000;
    color: #cc0000; }
    :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator li.navigable.red a:hover .step-indicator {
      border: 2px solid #cc0000;
      color: #cc0000; }



.focus-no-padding{
  background: transparent;
}
:host ::ng-deep .ui-table-customers {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);

  .customer-badge {
      border-radius: 2px;
      padding: .25em .5em;
      text-transform: uppercase;
      font-weight: 700;
      font-size: 12px;
      letter-spacing: .3px;

      &.status-qualified {
          background-color: #4CAF50; ;
          color: #FFF;
          padding-left: 12px;
          padding-right: 12px;
      }

      &.status-unqualified {
          background-color:#C63720 ;
          color: #FFF;
      }

      &.status-negotiation {
          background-color: #FEEDAF;
          color: #8A5340;
      }

      &.status-new {
          background-color: #B3E5FC;
          color: #23547B;
      }

      &.status-renewal {
          background-color: #ECCFFF;
          color: #694382;
      }

      &.status-proposal {
          background-color: #FFD8B2;
          color: #805B36;
      }
  }

  .flag {
      vertical-align: middle;
      width: 30px;
      height: 20px;
  }

  .ui-multiselect-representative-option {
      display: inline-block;
      vertical-align: middle;

      img {
          vertical-align: middle;
          width: 24px;
      }

      span {
          margin-top: .125em;
          vertical-align: middle;
          margin-left: .5em
      }
  }

  .ui-paginator {
      .ui-dropdown {
          float: left;
      }

      .ui-paginator-current {
          float: right;
      }
  }

  .ui-progressbar {
      height: 8px;
      background-color: #D8DADC;

      .ui-progressbar-value {
          background-color: #00ACAD;
      }
  }

  .ui-column-filter {
      display: block;
      font-weight: 500;
      width: 80px;
      border-radius: 2px;
      border:none;

      input {
          width: 100%;
          border-radius: 10px;
      }
  }

  .ui-table-globalfilter-container {
      float: right;
      font-weight: 300;
      border-radius: 5px;

      input {
          width: 200px;
          border-radius: 2px;

      }
  }

  .ui-datepicker {
      min-width: 25em;

      td {
          font-weight: 400;
      }
  }

  .ui-table-caption {
      border: 0 none;
      padding: 12px;
      text-align: left;
      font-size: 17px;
  }

  .ui-paginator {
      border: 0 none;
      padding: 1em;
  }

  .ui-table-thead > tr > th {
      border: 0 none;
      text-align: left;
      font-size: 15px;


      &.ui-filter-column {
          border-top: 1px solid #c8c8c8;
      }

      &:first-child {
          width: 5em;
          text-align: center;
      }

      &:last-child {
          width: 8em;
          text-align: center;
      }
  }

  .ui-table-tbody > tr > td {
      border: 0 none;
      cursor: auto;

      &:first-child {
          width: 3em;
          text-align: center;
      }

      &:last-child {
          width: 8em;
          text-align: center;
      }
  }

  .ui-dropdown-label:not(.ui-placeholder) {
      text-transform: uppercase;
  }

  .ui-table-tbody > tr > td .ui-column-title {
      display: none;
  }
}
//
.red{
  color:#db1a1a;
  // color:#a94442 !important;
}
/* Start page NAVBAR */

.page-navbar {
  font-size: 16px;
  width: 100%;
  list-style: none;
  background: #f2f2f2;
  z-index: 9999;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.page-navbar ul {
  margin-bottom: 0;
  padding: 0;
}

.page-navbar ul li {
  display: inline-block;
  padding: 8px 10px;
  margin: 10px 0px;
}

.page-navbar ul li:active {
  background-color: white;
}

.page-navbar ul li a {
  text-decoration: none;
  color: black;
  transition: all .4s ease;
}
.labelAdd1{
  margin-top: 18px;
}
.labelAdd2{
  margin-top: 10px;
}
.error1{
  border-bottom: 1px #a94442 solid;
  height: 30px;
}
.error2{
  border-bottom:none;
}
.e-multiselect e-input-group e-control-wrapper{
  border:none !important;
}
.green{
  color: #4CAF50 !important
}
// :host ::ng-deep .has-error .div-ejs-multiselect .custom-error-underline{
//   border-bottom-color: #a94442 !important;
// }
.custom-error-underline{
  position: absolute;
  width: calc(100% - 15px);
  height: 1px;
  left: 0;
  bottom: 4px;
  transform: scale(0);
  transform-origin: left;
  transition: all .3s ease;
}
.has-error .div-ejs-multiselect .custom-error-underline{
  background-color: #a94442;
  transform: scale(1);
}
.has-error .div-ejs-multiselect ::placeholder {
  color:#a94442;
}
// :host ::ng-deep .ui-toast{
//   top: 63px;
// }
.salary-error-message{
  position: absolute;
  top: 40px;
  left: 0;
  color:#a94442;
}

.global-error-msg{
  min-width: 255px;
  position: absolute;
  right: 13px;
  top: 12px;
}

.editor{
  padding-top: 0px; 
  padding-left: 15px; 
  padding-right: 0px;
}

.red-icon{
  background-color: #a94442 !important;
  border: 2px solid #a94442 !important;
}
.green-icon{
  background-color: #4caf50 !important;
  border: 2px solid #4caf50 !important;
}
.er-msg-major{
  top: 60px !important;
  left: 19px;
  text-align: left;
}

//added class form-control1 to minors control instead of form-control class, 
// because of style position:absolute and z-index in form-control class that was making issue
// when dropdown of majors control open
:host ::ng-deep .form-control1{
  width: 100%;
  padding: 0 15px 0 0;
}

.alignment-right{
  color: #4f94df;
  font-size:16px;
}
.company-apply-link{
  display:none;
  position:relative;
}

.company-apply-link .error-message{
  padding-left: 15px;
}

  //  Start upload image styles
  .upload-image-container{
    max-width: 180px!important;
  }
  .upload-image-container .inner-container a{
      cursor:pointer;
      text-decoration: none;
  }
  .upload-image-container .inner-container img{
    margin: auto auto 10px;
    width: 135px;
  }
  .upload-image-container .upload-actions a i{
    font-size: 21px;
    color: #337ab7;
    cursor: pointer;
  }
  .upload-image-container .upload-actions .edit-image{
    margin-right: 15px;
  }
//   End upload image styles

 /* Start logo image upload styles */
//  .persPhotoContainer{
//   max-width:180px !important;
//   position:relative;
// }
// .persPhoto{
//   margin: auto;
//   margin-bottom: 10px;
//   width: 135px;
// }
// .persPhotoLabel , .delete-photo{
//   cursor: pointer;
//   color: #337ab7;
// }
// .persPhotoLabel i , .delete-photo i{
//   font-size:21px;
//   color: #337ab7;
// }
// .delete-photo{
//   margin-left:35px;
// }
// .edit-photo{
//   position:absolute;
//   margin-top: 11px;
//   margin-left:-35px;
// }
// .imgError-div{
//   text-align: left;
//   margin-top: 7px;
// }
// .imgError .error-message{
//   color: #a94442;
// }
/* End logo image upload styles */
.flex-row-wrap{
  display:flex;
  flex-wrap:wrap;
}
.fix-form-control-height{
  min-height: 32px;
}
.long-error-message{
  left: 14px !important;
  top: 105% !important;
}

.hidden-checkbox .condition-checkbox{
  display:none;
}
.visible-checkbox .condition-checkbox{
  display:inline-block;
}
/*responsive*/
@media only screen and (min-width: 768px){
  .has-error .error-message{
    top:21px;
  }
}

// @media only screen and (max-width: 767px) 
@media only screen and (max-width: 991px) {
  :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li a .label {
    display:none !important;
  }
  :host ::ng-deep aw-wizard-navigation-bar.vertical {
    width:15% !important;
  }
  :host ::ng-deep aw-wizard.vertical .wizard-steps {
    width: 100% !important;
    // min-width: 100% !important;
    max-width: 100% !important;
  }
  :host ::ng-deep aw-wizard-navigation-bar ul{
    background-color: white;
    width: 100px;
    border-radius: 10px;
   }
   .container-fluid{
    background-color: #ffff;
    height: auto;
  }
  .details{
    margin-left: 40px;
    margin-top:10px;
  }
  .container-fluid{
    padding-top:0;
  }
}

@media screen and (max-width: 900px) {
  #salary {
      right: 10%;
  }
}
@media only screen and (min-width: 768px){
  .persPhoto-col{
    margin-top: 46px;
  }
}

@media only screen and (max-width: 767px) {
  table,
 thead,
 tbody,
 th,
 td,
 tr {
     display: block;
 }
 #page-content-wrapper .page-content {
 }
 .margin-bo-mo-10 {
     margin-bottom: 10px;
 }
 .custom-control-labels {
     padding-top: 8px;
 }

//  .custom-btn{
//      width: 70px;
//  }

 .add{
   left:160px;
   width: 78%;

 }
 .salary-label{
  text-align: left;
}
 .wizardButton button{
  //  width:60px ;
   text-align: center;
 }
 // .details{
 //   margin-left: 40px;
 //   margin-top:10px;
 // }
 :host ::ng-deep .btn-group .ui-buttonset:not(.ui-splitbutton) .ui-button{
    padding:0;
 }
 .back1{
   margin-left: 40px;
 }
.back2{
 margin-left: 0px;
}
.details{
  padding: 15px 15px 40px 15px;
  margin-right: 0;
}
 .details .labelAdd{
   font-size: 14px;
  //  padding-left:0px;
 }
 .labelAdd2{
   margin-top: 0px !important;
 }
 .details .custom-checkbox4{
   margin-left: -20px;
 }
 .details .custom-checkbox3{
   margin-left: -5px;
 }
 .details .check{
  //  width:15px !important;
  //  height: 15px !important;
   margin-left:7px;
 }
//  .details .custom-checkbox{
//    display: none;
//  }
 .exInformation .custom-checkbox{
   display: inline-block;
 }
 .company-name{
   font-size: 14px;
   margin-left: -15px;
 }
 .company-industry{
   font-size: 13px;
   margin-left: -18px;
 }
 .extra-check{
   width: 15px;
   height: 15px;
 }
 .ng5{
   width: 79%;
 }
 .companyName{
   font-size: 14px;
   margin-top: 10px;
 }

 .editor{
   height: 50% !important;
 }
 :host ::ng-deep aw-wizard.vertical .wizard-steps {
   min-width: calc(100% - 280px) !important;
 }

 :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator{
    position: absolute !important;
    top: -28px !important;
    flex-direction: row !important;
    padding-left: 175px;
 }
 :host ::ng-deep aw-wizard-navigation-bar ul.steps-indicator li{
    margin-right: 40px;
 }
 :host ::ng-deep aw-wizard.vertical .wizard-steps{
    min-width: 100% !important;
    margin-top: 27px;
 }
 .editor{
  padding-top: 44px; 
 }
//  .pad-top2{
//   padding-top: 23px;
//  }
 .pad-top-4{
  padding-top: 46px;
 }
 .details{
  margin-left: 0;
 }
 .step4-warning{
   padding:5px;
 }
 .user-pro-pic{
  color: #4f94df;
  margin-top: 30px;
 }
 .user-pro-pic img{
  width: 78px !important;
  // height: 78px !important;
 }
 .focus-no-padding {
    padding-left: 15px !important;
  }
  .relative-row{
    position:relative;
  }
  .request-new-jobtitle{
    position: absolute;
    bottom: 0;
    right: -18px;
  }
  .request-new-education{
    width: 19px;
    height: 21px;
    margin:0;
  }
  .request-new-education .fa-info{
    width: 19px;
    height: 21px;
  }
  .custom-checkbox input {
    width: 16px;
    height: 16px !important;
  }

  :host ::ng-deep aw-wizard-navigation-bar.vertical {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  :host ::ng-deep aw-wizard-navigation-bar.vertical.large-empty-symbols ul.steps-indicator{
    margin-left: -20px !important;
    padding-left: 0;
  }
  .has-error .error-message{
    left:14px;
  }
  .persPhotoContainer{
    margin: 0 auto;
  }
  .imgError-div{
    text-align:center;
  }
  .flex-order-xs-1{
    order:1;
  }
  .flex-order-xs-2{
    order:2;
  }

  .upload-image-container{
    margin:15px auto 25px auto;
  }
  .exist-logo img{
    margin:auto;
  }
}

// :host ::ng-deep .lazyload-multiselect .selected-list .c-btn{
//   border:none;
//   border-bottom: 1px solid #ccc;
// }

// modify ng-select lazyload styles
// :host ::ng-deep .ng-select{
//   width:100%;
// }
// :host ::ng-deep .form-control .ng-select-container{
//   z-index:2;
// }
// :host ::ng-deep .ng-select .ng-select-container , :host ::ng-deep .ng-select .ng-select-container:focus{
//   border:none;
//   border-radius:0;
//   box-shadow:none !important;
//   outline:none;
//   background: transparent;
// }
// :host ::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{
//   background-color: #eee;
//   padding: 3px;
//   border-radius: 25px;
// }
// :host ::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right{
//   border-radius: 50%;
//   background: #6D6D6D;
//   color:#eee;
//   border:none;
//   font-size: 11px;
//   padding: 0px 4px 2px 4px;
// }
// :host ::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{
//   font-size:14px;
// }
// :host ::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected, :host ::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked,
// :host ::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked{
//   background-color: #eee !important;
// }

