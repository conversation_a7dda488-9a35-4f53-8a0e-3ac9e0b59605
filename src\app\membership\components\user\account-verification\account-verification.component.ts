import { Component, OnInit } from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {AuthService} from 'shared/shared-services/auth-service';
import {HttpClient} from '@angular/common/http';
import {Router} from '@angular/router';
import { OAuthService } from 'angular-oauth2-oidc';
import { Errors } from '../../../models/errors';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/empty' 
import { EmptyObservable } from 'rxjs/observable/EmptyObservable';
import { GeneralService } from '../../../../general/services/general.service';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-account-verification',
  templateUrl: './account-verification.component.html',
  styleUrls: ['./account-verification.component.css']
})
export class AccountVerificationComponent implements OnInit {
  verificationForm;
  errorMessage;
  resetMessage;
  warningMessage;
  successMessage;
  loader = false;
  email:string = "";
  alreadyVerified=false;
  constructor(private authService: AuthService,
              private http: HttpClient,
              private  fb: FormBuilder,
              private router: Router,
              private oauthService: OAuthService,
              private generalService: GeneralService,
              private title: Title,
              private meta:Meta
  ) {
    this.verificationForm = this.fb.group({
      verification_code: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.authService.currentError.subscribe(errorMessage => this.resetMessage = errorMessage);

    this.title.setTitle('CVeek Website | Account verification | CVeeK');
    this.meta.updateTag({ name: 'robots', content: 'noindex' });

    // in case user coming from forget password, and his account is not verified
    if(localStorage.getItem('oldUser')){
      let userInfo = JSON.parse(localStorage.getItem('oldUser'));
      this.email = userInfo.email;
    }
    // in case user signed up and need to verify
    if(localStorage.getItem('newUser')){
      this.email = localStorage.getItem('newUser');
    }
  }

  isInvalid(controlName: string) {
    return !this.verificationForm.controls[controlName].valid;
  }

  verify() {
    if(this.email !==""){
      const data = {
        'email': this.email,
        'verification_code': this.verificationForm.get('verification_code').value,
      };
      if (this.verificationForm.valid) {
        this.loader = true;
        this.authService.verify(data).switchMap(
          res => {
            if (res['success'] == true) {
              localStorage.removeItem('newUser');
              localStorage.removeItem('futureRole');
              localStorage.setItem('access_token',res['token'].access_token);
              return this.authService.getUserInfo();
            }
            else if (res['error']) {
              this.resetMessage = res['error'];
              this.loader = false;
            return new EmptyObservable<Response>();
            }
            else if(res['type']==="already-verify"){
              this.alreadyVerified = true;
              this.loader = false;
              this.warningMessage = res['message'];
              localStorage.removeItem('newUser');
              localStorage.removeItem('futureRole');
              this.generalService.notify('verified' ,'account-verification', 'app', {'verified':true});
              setTimeout(() => {
                this.router.navigate(['/m/user/login/']);
              }, 2500);
              return new EmptyObservable<Response>();
            }
          })
          .subscribe(data => {
            localStorage.setItem("role",data['user_info'].roles[0].name);
            localStorage.setItem("fname",data['user_info'].first_name);
            localStorage.setItem("lname",data['user_info'].last_name);
            localStorage.setItem("username",data['user_info'].user_name);
            localStorage.setItem("userId",data['user_info'].id);
            localStorage.setItem("email",data['user_info'].email);
            if(data['user_info'].profile_picture){
              localStorage.setItem("pic",data['user_info'].profile_picture);
            }
            else {
              localStorage.setItem("pic","none");
            }
            this.generalService.notify(
              'roleChanged' , 'membership','contact' , 
              {'role':data['user_info'].roles[0].name , 'email':data['user_info'].email}
            );
  
            this.generalService.notify('verified' ,'account-verification', 'app', {'verified':true});
  
            this.loader = false;
            this.router.navigate(['u/',data['user_info'].user_name,'resumes']);    
          });
      }
    }
    
    // if there is no email in local storage (if the user visit verification link from another browser)
    else{
      localStorage.setItem("fromVerification","true");
      this.authService.changeError("Please you need to login first");
      this.router.navigate(['/m/user/login/']);
    }
      
  }

  sendCodeAgain() {
    this.warningMessage = "Please wait a minute before pressing send code again";
  //  let userInfo = JSON.parse(localStorage.getItem('newUser'));
    
    if(this.email !==""){
      const data = {
        'email': this.email
      };
      this.authService.sendCodeAgain(data).subscribe(res => {
        if(res['data']){
          this.resetMessage = '';
          this.warningMessage = '';
          this.successMessage = res['data'];
          setTimeout(() => {
            this.successMessage = "";
          }, 4000);
        }
        if(res['error']){
          this.resetMessage = res['error']; 
        }
      });
    }

    // if there is no email in local storage (if the user visit verification link from another browser)
    else{
      localStorage.setItem("fromVerification","true");
      this.authService.changeError("Please you need to login first");
      this.router.navigate(['/m/user/login/']);
    }
  }
}
