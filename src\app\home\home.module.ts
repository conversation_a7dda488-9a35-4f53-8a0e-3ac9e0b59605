import { NgModule } from '@angular/core';
import { SharedModule } from 'shared/shared.module';
import { HomeRoutingModule } from './home-routing.module';
import {CarouselModule} from 'primeng/carousel';
import { HomeComponent } from './components/home/<USER>';
import { ContactComponent } from './components/contact/contact.component';
import { SimpleSliderComponent } from './components/simple-slider/simple-slider.component';
// import { LazyLoadImageModule } from 'ng-lazyload-image';
@NgModule({
  imports: [
    SharedModule,
    HomeRoutingModule,
    CarouselModule,
  //  LazyLoadImageModule,
  ],
  declarations: [
    HomeComponent,
    ContactComponent,
    SimpleSliderComponent,
  ]
})
export class HomeModule { }
