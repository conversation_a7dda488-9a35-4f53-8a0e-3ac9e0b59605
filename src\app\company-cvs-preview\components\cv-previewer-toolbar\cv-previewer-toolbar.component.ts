import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { GeneralService } from '../../../general/services/general.service';
import { CvsTableService } from '../../../company-cvs-folders/services/cvs-table.service';
import { Subject } from 'rxjs';
declare var $: any;

@Component({
  selector: 'app-cv-previewer-toolbar',
  templateUrl: './cv-previewer-toolbar.component.html',
  styleUrls: ['./cv-previewer-toolbar.component.css']
})
export class CvPreviewerToolbarComponent implements OnInit {
  @Output() toolBarEvent = new EventEmitter<any>();
  @Input() currentCv: any;
  @Input() folder: any;
  @Input() sourceInterface: string;
  @Input() foldersddData = [];
  showCVeek: boolean = false;
  showToggleCVeek: boolean = true;
  resume_ids = [];
  private ngUnsubscribe: Subject<any> = new Subject();
  constructor(
    private generalService: GeneralService,
    private cvsTableService:CvsTableService) { }

  ngOnInit(): void {
    if (this.currentCv.uploaded_file == null) {
      this.showToggleCVeek = false;
      this.showCVeek = true;
    }
    this.toggleCVeek(this.showCVeek);
    // console.log('showCVeek',this.showCVeek,this.showToggleCVeek,this.currentCv);

    // this.generalService.internalMessage.takeUntil(this.ngUnsubscribe).subscribe((data) => {
    //    if (data['message'] === 'moveSelectedCVSToFolders2') {
    //     console.log("notified moveSelectedCVSToFolders2 in toolbar");
    //      console.log("inside");
    //      this.resume_ids = data['mData'].resume_ids;
    //      this.currentCv = null;
    //      this.openMoveCVToFolderModal();
    //    }
    // });
      //////////////
  
  }

  ngOnChanges(changes: SimpleChanges) {
    if(this.currentCv.cv_folders !==undefined){
      if(changes['foldersddData']){
        this.foldersddData = changes['foldersddData'].currentValue;
        
        let item;
        for (let i = 0; i < this.currentCv.cv_folders.length; i++) {
          item = this.foldersddData.filter((el) => el['key'] === this.currentCv.cv_folders[i].key );
          this.currentCv.cv_folders[i] = item[0];
        }
      }
    
      if(changes['currentCv']){
        this.currentCv = changes['currentCv'].currentValue;  
      }
    }
      
  }

  changeStatus(cv, key, value, action, confirmMessage = '') {
    let eventData;
    if( (action === 'emp_app/move_to_folder' && value===5) || key==='delete_cv' || key==='restore'){
      eventData = {
        cv, key, value, action, confirmMessage,'message': 'back'
      };
    }
    else{
      eventData = {
        cv, key, value, action, confirmMessage
      };
    }
    this.toolBarEvent.emit(eventData);
  }

  toggleCVeek(showCVeek) {
    this.showCVeek = showCVeek;
    this.generalService.notify('showCVeekChanged', 'cv-previewer-toolbar', 'cvs-table',
      { 'showCVeek': showCVeek });
  }

  back() {
    const eventData = {
      'message': 'back'
    };
    this.toolBarEvent.emit(eventData);
  }

  openMoveCVToFolderModal(){
    $('#moveCVToFolderModal').modal('show');
  }

  detachFolderFromResume(folder){
    let folder_id = +folder.key;
    let data = {"resume_id":this.currentCv.resume_id ,"folder_id":+folder_id}
    this.cvsTableService.detachFolderFromResume(data).subscribe(res => {
      this.currentCv.cv_folders = res['data'];
      
      //case user detached all custom folders, then cv will return to original adv folders
      if(res['adv_title_folders']){
        this.generalService.notify('detachFolderFromResumeUpdateCount', 'cv-previewer-toolbar', 'cvs-folders',
        { 'folder': folder, 'adv_title_folders':res['adv_title_folders'] });
        this.currentCv.folders = res['adv_title_folders'];
      }
      else{
        this.generalService.notify('detachFolderFromResumeUpdateCount', 'cv-previewer-toolbar', 'cvs-folders',
        { 'folder': folder });
      }
        
      //the following code line to trigger ngOnChanges in move-cv-modal component to see changes 
      // in currentCv object
      this.currentCv = Object.assign({}, this.currentCv);
      this.generalService.notify('cvFoldersChanged', 'cv-previewer-toolbar', 'move-cv-modal',
      { 'cv_folders': res['data'] });

    //  this.generalService.notify('detachFolderFromResume', 'cv-previewer-toolbar', 'cvs-table', {"updatedCv":this.currentCv});
      this.generalService.notify('refershTable', 'cv-previewer-toolbar', 'cvs-table', {});

      //if cv removed from all related folders, go back to cvs-table
      if(this.currentCv.cv_folders.length === 0)
        this.back();
    });
  }

  handleMoveCvPopup($event){
    let currentCv_backup = {...this.currentCv};
    if(currentCv_backup.cv_folders && currentCv_backup.cv_folders.length){
      // cvs-folders-cvs-preview-module
      this.generalService.notify('updateCVFoldersCount', 'cv-previewer-toolbar', 'cvs-folders', {
        "oldFolders":currentCv_backup.cv_folders,"newFolders":$event['cv_folders'],"foldersType":"custom-folders"
      });
    }
    else if(currentCv_backup.folders && currentCv_backup.folders.length){
      this.generalService.notify('updateCVFoldersCount', 'cv-previewer-toolbar', 'cvs-folders', {
        "oldFolders":currentCv_backup.folders,"newFolders":$event['cv_folders'],"foldersType":"original-folders"
      });
    }
    else{
      this.generalService.notify('updateCVFoldersCount', 'cv-previewer-toolbar', 'cvs-folders', {
        "newFolders":$event['cv_folders'],"foldersType":"other-original-folders"
      });
    }
    

    this.currentCv.cv_folders = $event['cv_folders'];
    this.currentCv.folders = [];
    
    this.generalService.notify('refershTable', 'cv-previewer-toolbar', 'cvs-table', {});
  }

}
