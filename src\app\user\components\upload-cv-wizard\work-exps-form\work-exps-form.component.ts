import { Component, Input, Output, EventEmitter, OnInit, AfterViewInit, QueryList, ElementRef, ViewChildren } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { UploadCvPdfService } from 'app/user/cv-services/upload-cv-pdf.service';
import { LazyloadDropdownClass } from 'shared/Models/lazyloadDropdown';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { MessageService } from 'primeng/api';
import { Router, ActivatedRoute } from '@angular/router';

declare var google: any;

@Component({
  selector: 'app-work-exps-form',
  templateUrl: './work-exps-form.component.html',
  styleUrls: ['./work-exps-form.component.css']
})
export class WorkExpsFormComponent implements OnInit, AfterViewInit {
  @Input() workExps: any[] = [];
  @Input() resumeId: number | null = null;
  @Input() languageId: number | null = 1;
  @Input() fromSteps: boolean = false;
  @Input() expFields: any[] = [];
  @Input() employmentTypes: any[] = [];
  @Input() username: string | null = null;
  @Output() workExpsChange = new EventEmitter<any[]>();
  @Output() nextStep = new EventEmitter<void>();
  @Output() prevStep = new EventEmitter<void>();

  workExpsForm: FormGroup;
  isMinimized: boolean[] = [];
  showLoader: boolean = true;

  // Dropdown options
  fromYearOpts: any[] = [];
  toYearOpts: any[] = [];
  monthOpts: any[] = [];
  job_titles: LazyloadDropdownClass;

  @ViewChildren('googlePlace') googlePlaces: QueryList<ElementRef>;

  formsWithValidationErrors: Set<number> = new Set();

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private lazyloadDropdownService: LazyloadDropdownService,
    private uploadCvPdfService: UploadCvPdfService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.workExpsForm = this.fb.group({
      workExpsList: this.fb.array([]),
      resume_id: [this.resumeId]
    });
    this.getWorkExpsData();
    // Initialize dropdown data
    this.initYearOptions();
    this.initMonthOptions();
  }

  ngAfterViewInit() {
    this.initGooglePlaces();
  }

  get workExpsControls() {
    return (this.workExpsForm.get('workExpsList') as FormArray).controls;
  }

  get jobTitleControl() {
    return (this.workExpsForm.get('workExpsList') as FormArray).controls['job_title'] as FormControl;
  }

  get expFieldControl() {
    return (this.workExpsForm.get('workExpsList') as FormArray).controls['exp_field'] as FormControl;
  }

  async getWorkExpsData() {
    this.showLoader = true;
    this.job_titles = new LazyloadDropdownClass(this.lazyloadDropdownService, 'job_titles', 10, this.languageId);
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    workExpsArr.clear();
    if (this.workExps && this.workExps.length > 0) {
      // Wait for all async createWorkExpForm calls to complete
      const formPromises = this.workExps.map(exp => this.createWorkExpForm(exp));
      const forms = await Promise.all(formPromises);
      forms.forEach(form => workExpsArr.push(form));
    } else {
      const form = await this.createWorkExpForm();
      workExpsArr.push(form);
    }
    setTimeout(() => this.initGooglePlaces(), 0);
    this.showLoader = false;
  }

  async createWorkExpForm(exp: any = null): Promise<FormGroup> {
    // Prepare fullLocation for Google Places
    let fullLocation = '';
    if (exp?.company?.city && exp?.company?.country) {
      fullLocation = `${exp.company.city}, ${exp.company.country}`;
    } else if (exp?.company?.fullLocation) {
      fullLocation = exp.company.fullLocation;
    }

    // Find the full exp_field object from expFields if possible
    let expFieldObject = null;
    if (exp?.exp_field?.id && this.expFields) {
      expFieldObject = this.expFields.find(f => f.id === exp.exp_field.id) || exp.exp_field;
    }

    // Find the full job_title object from job_titles if possible
    let jobTitleObject: any = null;

    // If job_title ID is -1, use it directly as a custom job title
    if (exp?.job_title?.id === -1) {
      jobTitleObject = {
        id: -1,
        name: exp.job_title.name
      };
      this.job_titles.items = [jobTitleObject, ...this.job_titles.items];
    } else {
      if (exp?.job_title?.id && this.job_titles?.items) {
        jobTitleObject = this.job_titles.items.find((j: any) => j.id === exp.job_title.id);

      }
      // If not found by id in local items, try to fetch by id from backend
      if (!jobTitleObject && exp?.job_title?.id) {
        const res = await this.job_titles.getById(exp.job_title.id).toPromise();
        jobTitleObject = res && res['data'] && res['data'].length ? res['data'][0] : null;
      }
      // If still not found by id, try to fetch by name
      if ((!jobTitleObject || !jobTitleObject?.id) && exp?.job_title?.name) {
        const res = await this.job_titles.getOriginalValue(exp.job_title.name).toPromise();
        jobTitleObject = res && res['data'] && res['data'].length ? res['data'][0] : { id: '', name: exp.job_title.name };
      }
      // Ensure the fetched job title is in the dropdown options
      if (jobTitleObject && jobTitleObject.id && jobTitleObject.id !== -1) {
        const exists = this.job_titles.items.some((j: any) => j.id === jobTitleObject.id);
        if (!exists) {
          this.job_titles.items = [jobTitleObject, ...this.job_titles.items];
        }
      }
    }

    // Map employment_types by ID
    let employmentTypesArray = [];
    if (exp?.employment_types && exp.employment_types.length && this.employmentTypes) {
      employmentTypesArray = exp.employment_types.map(empType => {
        // If empType is already an object with id, find the full object
        if (typeof empType === 'object' && empType.id) {
          return this.employmentTypes.find(opt => opt.id === empType.id) || empType;
        }
        // If empType is just an ID, find the object by ID
        else if (typeof empType === 'number' || typeof empType === 'string') {
          return this.employmentTypes.find(opt => opt.id === empType) || { id: empType, name: '' };
        }
        // If it's already a full object, return as is
        return empType;
      });
    }

    const toYear = (exp?.isPresent) ? 'present' : (exp?.to?.year?.toString() || '');
    const toMonth = (exp?.isPresent) ? '' : (exp?.to?.month?.toString() || '');

    const form = this.fb.group({
      company: this.fb.group({
        name: [exp?.company?.name || '', Validators.required],
        country: [exp?.company?.country || ''],
        city: [exp?.company?.city || ''],
        country_code: [exp?.company?.country_code || ''],
        fullLocation: [fullLocation]
      }),
      exp_field: [expFieldObject , Validators.required],
      job_title: [jobTitleObject , Validators.required],
      from: this.fb.group({
        year: [exp?.from?.year?.toString() || '', Validators.required],
        month: [exp?.from?.month?.toString() || '']
      }),
      to: this.fb.group({
        year: [toYear, Validators.required],
        month: [toMonth]
      }),
      isPresent: [exp?.isPresent || false],
      description: [exp?.description || ''],
      employment_types: [employmentTypesArray]
    });

    // If isPresent, disable the to.month control
    if (exp?.isPresent) {
      (form.get('to.month') as FormControl).disable();
    }

    return form;
  }

  addWorkExp(): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    this.createWorkExpForm(
      //   {
      //   company: { name: '', country: '', city: '', country_code: '', fullLocation: '' },
      //   exp_field: { id: '', name: '' },
      //   job_title: { id: '', name: '' },
      //   from: { year: '', month: '' },
      //   to: { year: '', month: '' },
      //   isPresent: false,
      //   description: '',
      //   employment_types: []
      // }
    ).then(form => workExpsArr.push(form));
    // Re-initialize Google Places for new input
    setTimeout(() => this.initGooglePlaces(), 0);
  }

  removeWorkExp(index: number): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    workExpsArr.removeAt(index);
    this.isMinimized.splice(index, 1);
  }

  minimize(index: number): void {
    this.isMinimized[index] = !this.isMinimized[index];
  }

  triggerValidationDisplay(form: FormGroup, index: number): void {
    if (!form) return;
    form.markAllAsTouched();
    this.formsWithValidationErrors.add(index);
  }

  shouldShowValidationErrors(index: number): boolean {
    return this.formsWithValidationErrors.has(index);
  }

  clearValidationErrors(index: number): void {
    this.formsWithValidationErrors.delete(index);
  }

  // Helper to map work experiences for submission: remove exp_field and add exp_field_id
  private mapWorkExpsForSubmission(workExps: any[]): any[] {
    return workExps.map(exp => {
      const { exp_field, ...rest } = exp;
      return {
        ...rest,
        exp_field_id: exp_field?.id,
      };
    });
  }

  // Direct method to emit and move to next step
  async emitAndNextStep(): Promise<void> {
    // Show loader while calling the service
    this.showLoader = true;

    const formValue = this.workExpsForm.value;
    const mappedWorkExps = this.mapWorkExpsForSubmission(formValue.workExpsList);
    const payload = {
      resume_id: formValue.resume_id,
      workExpsList: mappedWorkExps
    };

    // Wait for the service call to complete
    const response: any = await this.uploadCvPdfService.addStepData('work_experiences', payload).toPromise();

    // Check if response indicates success
    if (response && response['work_experiences']) {
      // Success - hide loader and navigate
      this.showLoader = false;

      if (this.fromSteps) {
        this.router.navigate(['u', this.username, 'resumes']);
      }
    } else {
      // Hide loader in case of error
      this.showLoader = false;
      // Show user-friendly error message
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('shared.error'),
        detail: this.translateService.instant('shared.errorSavingWorkExps') || 'Error saving work experience data. Please try again.',
        life: 5000
      });
    }

  }

  // Simplified method to handle next button click
  async nextWorkExp(): Promise<void> {

    if (!this.workExpsForm.touched) {
      this.skipWorkExp();
      return;
    }

    const workExpsArr = (this.workExpsForm.get('workExpsList') as FormArray);

    // Validate all work experience forms
    let allValid = true;
    const invalidForms: number[] = [];

    for (let i = 0; i < workExpsArr.length; i++) {
      const form = workExpsArr.at(i) as FormGroup;
      if (!form) {
        console.warn(`Form at index ${i} is undefined`);
        continue;
      }

      if (!form.valid) {
        allValid = false;
        invalidForms.push(i);
        this.triggerValidationDisplay(form, i);
      } else {
        this.clearValidationErrors(i);
      }
    }

    // If any form is invalid, show errors and return
    if (!allValid) {
      console.log('Some work experience forms are invalid:', invalidForms);

      // Show user-friendly validation error message
      this.messageService.add({
        severity: 'warn',
        summary: this.translateService.instant('validationMessages.required'),
        detail: this.translateService.instant('validationMessages.pleaseCompleteAllFields') || 'Please complete all required fields.',
        life: 5000
      });

      return;
    }

    // All forms are valid, proceed with saving and emitting
    if (this.workExpsForm.valid) {
      try {
        await this.emitAndNextStep();
      } catch (error) {
        console.error('Error in nextWorkExp:', error);
        // Error message is already shown in emitAndNextStep
      }
    }
  }

  prevWorkExp(): void {
    if (this.fromSteps) {
      this.prevStep.emit();
    }
  }

  skipWorkExp(): void {
    // Clear all work experience forms and emit next step
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    workExpsArr.clear();
    if (this.fromSteps) {
      this.nextStep.emit();
      this.router.navigate(['u', this.username, 'resumes']);
    }
  }

  // Initialize year options
  initYearOptions() {
    const currentYear = new Date().getFullYear();
    this.fromYearOpts = [];
    this.toYearOpts = [];

    // Add "Present" option to the "To" year dropdown
    this.toYearOpts.push({ label: this.translateService.instant('shared.present'), value: 'present' });

    // Add year values (current year to 50 years ago)
    for (let i = currentYear; i >= currentYear - 50; i--) {
      const yearOpt = { label: i.toString(), value: i.toString() };
      this.fromYearOpts.push(yearOpt);
      this.toYearOpts.push(yearOpt);
    }
  }

  // Initialize month options
  initMonthOptions() {
    this.monthOpts = [
      { label: this.translateService.instant('months.january') || 'January', value: '1' },
      { label: this.translateService.instant('months.february') || 'February', value: '2' },
      { label: this.translateService.instant('months.march') || 'March', value: '3' },
      { label: this.translateService.instant('months.april') || 'April', value: '4' },
      { label: this.translateService.instant('months.may') || 'May', value: '5' },
      { label: this.translateService.instant('months.june') || 'June', value: '6' },
      { label: this.translateService.instant('months.july') || 'July', value: '7' },
      { label: this.translateService.instant('months.august') || 'August', value: '8' },
      { label: this.translateService.instant('months.september') || 'September', value: '9' },
      { label: this.translateService.instant('months.october') || 'October', value: '10' },
      { label: this.translateService.instant('months.november') || 'November', value: '11' },
      { label: this.translateService.instant('months.december') || 'December', value: '12' }
    ];
  }

  // Handle location clear on change
  LocationClearOnChange(event: any, index: number): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    const companyGroup = workExpsArr.at(index).get('company');
    // Only clear if the input is actually empty
    if (!event.target.value) {
      companyGroup.patchValue({
        city: '',
        country: '',
        country_code: '',
        fullLocation: ''
      });
    }
  }

  // Handle location input change via Google Places API
  onLocationSelect(place: any, index: number): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    const companyGroup = workExpsArr.at(index).get('company');

    let city = '';
    let country = '';
    let countryCode = '';

    if (place.address_components) {
      for (const component of place.address_components) {
        if (component.types.includes('locality')) {
          city = component.long_name;
        } else if (component.types.includes('country')) {
          country = component.long_name;
          countryCode = component.short_name;
        }
      }
    }

    companyGroup.patchValue({
      city: city,
      country: country,
      country_code: countryCode,
      fullLocation: `${city}, ${country}`
    });
  }

  // Initialize Google Places for location inputs
  initGooglePlaces(): void {
    if (this.googlePlaces) {
      setTimeout(() => {
        this.googlePlaces.forEach((place, index) => {
          const autocomplete = new google.maps.places.Autocomplete(place.nativeElement, {
            types: ['(cities)']
          });

          autocomplete.addListener('place_changed', () => {
            const selectedPlace = autocomplete.getPlace();
            this.onLocationSelect(selectedPlace, index);
          });
        });
      }, 5000);
    }
  }

  selectJobTitle(selected: any, index: number): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    const jobTitleControl = workExpsArr.at(index).get('job_title') as FormControl;
    if (selected) {
      jobTitleControl.setValue(selected);
    }
  }

  selectExpField(event: any, index: number): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    const expFieldControl = workExpsArr.at(index).get('exp_field') as FormControl;
    if (event) {
      expFieldControl.setValue(event.value);
    }
  }

  loadMoreJobTitles(): void {
    if (this.job_titles) {
      this.job_titles.onScrollToEnd();
    }
  }

  addNewJobTitle(term: string): any {
    return { id: -1, name: term };
  }

  customSearchJobTitle(term: string, item: any): boolean {
    const termLower = term.toLowerCase();
    return item.name.toLowerCase().includes(termLower);
  }

  deleteJobTitleAutoComplete(control: FormControl): void {
    if (control) {
      control.setValue(null);
    }
  }

  showAllWorkExpValidationErrors(): void {
    const workExpsArr = this.workExpsForm.get('workExpsList') as FormArray;
    for (let i = 0; i < workExpsArr.length; i++) {
      const form = workExpsArr.at(i) as FormGroup;
      this.triggerValidationDisplay(form, i);
    }
  }

  onToYearSelect(index: number, workExpForm: FormGroup) {
    const toGroup = workExpForm.get('to') as FormGroup;
    const yearValue = toGroup.get('year').value;
    if (yearValue === 'present') {
      toGroup.get('month').setValue('');
      toGroup.get('month').disable();
      workExpForm.get('isPresent').setValue(true);
    } else {
      toGroup.get('month').enable();
      workExpForm.get('isPresent').setValue(false);
    }
  }
}
