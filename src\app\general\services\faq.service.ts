import { Injectable } from '@angular/core';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import {HttpClient, HttpHeaders} from '@angular/common/http';

@Injectable()
export class FaqService {

  url = '';
  baseUrl = '';
  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data ;
      this.url     = data + 'admin/faq';
    });

  }

  // getLanguages() {
  //   let headers = new HttpHeaders().set('Content-Type', 'application/json');
  //   return this.http.get(this.baseUrl + '/translated_languages', { headers });

  // }

  getFaqs(lang_id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/users/' +  lang_id , {headers});
  }

}
