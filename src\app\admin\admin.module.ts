import { NewValueService } from './services/new-value.service';
import { NgModule } from '@angular/core';
import { SharedModule } from 'shared/shared.module';
import { AdminRoutingModule } from './admin-routing.module';

import { AdminComponent } from './admin.component';
import { AdminFaqsComponent } from './components/faqs/admin-faqs/admin-faqs.component';
import { EditModalComponent } from './components/faqs/edit-modal/edit-modal.component';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { AdminHelpTopicsComponent } from './components/help/help-topics/admin-help-topics/admin-help-topics.component';
import { AdminHelpTipsComponent } from './components/help/help-tips/admin-help-tips/admin-help-tips.component';
import { EditTipModalComponent } from './components/help/help-tips/edit-tip-modal/edit-tip-modal.component';
import { EditTopicModalComponent } from './components/help/help-topics/edit-topic-modal/edit-topic-modal.component';
import { ManageComponent } from './components/verification/manage/manage.component';
import { LogComponent } from './components/verification/log/log.component';
import { TransactionComponent } from './components/verification/transaction/transaction.component';
import { MessagesComponent } from './components/contact/messages/messages.component';
import { MessageArchiveComponent } from './components/contact/message-archive/message-archive.component';
import { MessageTemplatesComponent } from './components/contact/message-templates/message-templates.component';
import { MessageModalComponent } from './components/contact/message-modal/message-modal.component';
import { ManageCompanyComponent } from './components/company-verification/manage-company/manage-company.component';
import { CompanyModalComponent } from './components/company-verification/company-modal/company-modal.component';


import { ActivationPipe } from './pipes/activation.pipe';
import { SummaryPipe } from './pipes/summary.pipe';

import { HelpTopicService } from './services/help-topic.service';
import { HelpTipService } from './services/help-tip.service';
import { FaqsService } from './services/faqs.service';
import { VerificationService } from './services/verification.service';
import { LanguageService } from './services/language.service';
import { ContactService } from './services/contact.service';
import { CompanyService } from './services/company.service';
import { ManageArtComponent } from './components/articles/manage-art/manage-art.component';
import { ArticleModalComponent } from './components/articles/article-modal/article-modal.component';
import { CommonModule } from '@angular/common';
import { NewValueModalComponent } from './components/new-values/new-value-modal/new-value-modal.component';
import { ManageNewValueComponent } from './components/new-values/manage-new-value/manage-new-value.component';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ManageAdvsComponent } from './components/manage-advs/manage-advs.component';
import { ManageAdvsService } from './services/manage-advs.service';
import { ConfirmDialogModule } from 'primeng';
// import {ClipboardModule} from '@angular/cdk/clipboard';


@NgModule({
  imports: [
    SharedModule,
    AdminRoutingModule,
    CommonModule,
    ConfirmDialogModule,
   // ClipboardModule
  ],
  declarations: [
    AdminComponent,
    SidebarComponent,
    ActivationPipe,
    SummaryPipe,
    AdminFaqsComponent,
    EditModalComponent,
    AdminHelpTopicsComponent,
    EditTopicModalComponent,
    EditTipModalComponent,
    AdminHelpTipsComponent,
    ManageComponent,
    LogComponent,
    TransactionComponent,
    MessagesComponent,
    MessageArchiveComponent,
    MessageTemplatesComponent,
    MessageModalComponent,
    ManageCompanyComponent,
    CompanyModalComponent,
    ManageArtComponent,
    ArticleModalComponent,
    NewValueModalComponent,
    ManageNewValueComponent,
    ManageAdvsComponent,

  ],
  providers: [
    FaqsService,
    HelpTopicService,
    HelpTipService,
    VerificationService,
    ContactService,
    CompanyService,
    LanguageService,
    NewValueService,
    MessageService,
    ConfirmationService,
    ManageAdvsService
  ],
  exports: [

  ]
})
export class AdminModule { }



