import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { ManagePostService } from 'app/company/services/manage-post.service';

@Component({
  selector: 'app-advr-log',
  templateUrl: './advr-log.component.html',
  styleUrls: ['./advr-log.component.css']
})
export class AdvrLogComponent implements OnInit {
  AdvId: any;
  AdvType: any;
  data_Source:any = [];
  AdvTitle: any;
  loaderStop: boolean = false;
  loading: boolean = false;
  constructor(
    private managePost: ManagePostService
  ) {
    this.managePost.advLogData.subscribe(
      (res) => {
        if (res.length) {
          this.AdvId = res[0];
          this.AdvType = res[1];
          this.getAdvrDataLog(this.AdvId, this.AdvType)};});
   }

   getAdvrDataLog(AdvId,AdvType) {
    this.loaderStop = false;
     this.managePost.getAdvrDataLog(AdvId,AdvType).subscribe(
       (res) => {
        this.AdvTitle = res['data']['job_adv_title']
        if(this.AdvType === 'template') {
          this.data_Source = res['data']['template_action_log']
        } else {
          this.data_Source = res['data']['job_adv_action_log']
        }
        this.loaderStop = true
       })
   }

  ngOnInit(): void {


  }

}
