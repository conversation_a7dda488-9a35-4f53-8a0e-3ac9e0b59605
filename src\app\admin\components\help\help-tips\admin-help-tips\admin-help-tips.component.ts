import { HelpTipService } from './../../../../services/help-tip.service';
import { Component, OnInit, OnDestroy, Input, ViewChild, Sanitizer  } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs/Subject';
import { HelpTip } from 'app/admin/models/help-tip';
import { LanguageService } from 'app/admin/services/language.service';
import { Table } from 'primeng/table';
import { DomSanitizer } from '@angular/platform-browser';
declare var $: any;
@Component({
  selector: 'app-admin-help-tips',
  templateUrl: './admin-help-tips.component.html',
  styleUrls: ['./admin-help-tips.component.css']
})

export class AdminHelpTipsComponent implements OnInit, OnDestroy {
@ViewChild('dt') table: Table;
displayModal  = false;
private ngUnsubscribe: Subject<any> = new Subject();
hTip;
helpTipToDelete = { 'id': null, 'section_id': null, 'field_id': null, 'active': null,
'section': '', 'field': '', 'description': '', 'langId': null};
helpTipsArray = [];
filteredHelpTips = [];
sections: { 'label': string, 'value': number}[] = [];
fields: { 'label': string, 'value': number, 'section_id': number}[] = [];
doneFields: { 'label': string, 'value': number, 'section_id': number}[] = [];

languagesArray: {'name': string, 'id': number}[] = [];
currentLangId;
statuses = [
  {'label': '', 'value': null},
  {'label': 'active', 'value': 1},
  {'label': 'inactive', 'value': 0}
  ];
loading = true;
mode: string;
status;
section;
field;


constructor(private languageService: LanguageService, private hTipsService: HelpTipService, public sanitizer: DomSanitizer) {}

ngOnInit() {
   this.getHelpTips();
   this.getHelpTipFromSidebar();
   this.getLanguages();
   this.getSectionsFields();

}

getHelpTips() {
    // getting help Tips
    this.hTipsService.getHelpTips().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log('res', res);
      let hTipsTemp = res['help_field'];
      for (let ht of hTipsTemp) {
            this.helpTipsArray.push({
              'id'         : ht.id,
              'section_id' : ht.section_id,
              'field_id'   : ht.field_id,
              'section'    : ht.section.name,
              'field'      : ht.field.name,
              'active'     : ht.active,
              'description': (ht.help_field_trans.length === 0) ? '' : ht.help_field_trans[0].description,
              'langId'     : (ht.help_field_trans.length === 0) ?  1 : ht.help_field_trans[0].translated_languages_id,
              'display'    : false
            });

      }
      this.loading = false;
      this.table.filter(1, 'id', 'startsWith');
      this.table.filter(null, 'id', 'startsWith');
      console.log('helptipsArray', this.helpTipsArray);
    });
    // this.filteredHelpTips = this.helpTipsArray;

}

print(event) {
  console.log(event);
}
getHelpTipFromSidebar() {
   // getting new help tip  from the sidebar
   this.hTipsService.newHelpTip.takeUntil(this.ngUnsubscribe)
   .subscribe(ht => {
       if ( ht.id !== null ) {
         console.log('new help tip from sidebar was added to the table ', ht);
         let res = {
           'data': {
             'id'          : ht.id,
             'section_id'  : ht.section_id,
             'field_id'    : ht.field_id,
             'active'      : ht.active,
             'section': { 'name': ht.section},
             'field': { 'name': ht.field},
             'help_field_trans': [
             {'description'    : ht.description,
              'translated_languages_id': ht.langId }
             ]
           }
         };
         this.showNewHelpTipInTable(res);
       }

   });

}

getLanguages() {
  this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
    console.log(res);
    let temp = res['data'];
    for ( let lang of temp) {
      this.languagesArray.push({
        'id'  : lang.id,
        'name': lang.name
      });

    }

    console.log('languages array', this.languagesArray);
    console.log('lang arr length', this.languagesArray.length);
    // this.getSectionsFields();
  });
}

getSectionsFields() {
  this.hTipsService.getSectionsFields().takeUntil(this.ngUnsubscribe).subscribe(res =>{
    console.log('res', res);
    let temp = res['sections'];
    for (let section of temp) {
      this.sections.push({
        'label': section.name,
        'value': section.id
      });
    }
    let temp2 = res['fields'];
        for (let field of temp2) {
          if (field.saved === 0) {
            this.fields.push({
              'label'     : field.name,
              'value'     : field.id,
              'section_id': field.section_id
            });
          } else  if (field.saved === 1) {
            this.doneFields.push({
              'label'     : field.name,
              'value'     : field.id,
              'section_id': field.section_id
            });
          }

        }
    this.doneFields.unshift({ label: '', value: null, section_id: null});
    this.fields.unshift({ label: '', value: null, section_id: null});
    this.sections.unshift({ label: '', value: null});
    console.log('sections', this.sections);
    console.log('fields', this.fields);
  });
}



activateHTip(ht) {
    const body = {
      'active':  (ht.active) ? 0 : 1
    };
    this.hTipsService.activateHtip(body, ht.id).subscribe(res => {
      console.log('activate res', res);
      let index = this.helpTipsArray.indexOf(ht);
      this.helpTipsArray[index].active = (res['data'] !== undefined) ? res['data'].active : false;
      if ((!ht.active) && res['data'] === undefined) {
        alert(res['error']);
      }
    });

}


showNewHelpTipInTable(event) {
  console.log('fields before add new', this.fields);
  let temp = event['data'];
  console.log('temp', temp);
  for (let i = 0; i < temp.help_field_trans.length; i++) {
    if (temp.help_field_trans[i].translated_languages_id === 1) {
        let ht = {
          'id'         : temp.id,
          'section_id' : temp.section_id,
          'field_id'   : temp.field_id,
          'active'     : temp.active,
          'section'    : temp.section.name,
          'field'      : temp.field.name,
          'description': temp.help_field_trans[0].description,
          'langId'     : temp.help_field_trans[0].translated_languages_id,
          'display'    : false
        };
        this.helpTipsArray.splice(0, 0, ht);
        console.log('newHelpTipinTable', ht);
        this.table.filter(null, 'id', 'startsWith');

        for (let field of this.fields) {
          if (field.value === temp.field_id && temp.field.name === field.label && field.section_id === temp.section_id ) {
           let index = this.fields.indexOf(field);
           this.fields.splice(index, 1);
          }
        }
        console.log('fields after add new', this.fields);
    }
  }

  this.closeModal();
  $('body').removeClass('modal-open');
  $('body').removeAttr('style');
  $('div.modal-backdrop.fade.in').remove();

}



showUpdatedHelpTipInTable(event) {
  console.log('fields before update hTip', this.fields);
  let index = this.gethTipIndex(event['old']);
  console.log(index);
  let temp = event['new'];
  let old = event['old'];
  for (let i = 0; i < temp.help_field_trans.length; i++) {
    if (temp.help_field_trans[i].translated_languages_id === 1) {

      let updatedHelpTip = {
          'id'         : temp.id,
          'section_id' : temp.section_id,
          'field_id'   : temp.field_id,
          'active'     : temp.active,
          'section'    : temp.section.name,
          'field'      : temp.field.name,
          'description': temp.help_field_trans[0].description,
          'langId'     : temp.help_field_trans[0].translated_languages_id,
          'display'    : false
      };

      console.log('updated En help Tip', this.helpTipsArray[index], updatedHelpTip);
      this.helpTipsArray.splice(index, 1, updatedHelpTip);

      // adding the field of old hTip to fields array and removing the field of updated hTip from it
      if ( old.field_id !== updatedHelpTip.field_id && old.field !== updatedHelpTip.field ) {
        for (let field of this.fields) {
          if (field.value === updatedHelpTip.field_id && field.label === updatedHelpTip.field
               && field.section_id === updatedHelpTip.section_id) {
            let index2 = this.fields.indexOf(field);
            console.log('index', index2);
            this.fields.splice(index2, 1);
            this.fields.push({ 'label': old.field, 'value': old.field_id, 'section_id': old.section_id});
          }
        }
      }
      console.log('fields after update', this.fields);
    }

  }

  this.closeModal();
  $('div.modal-backdrop.fade.in').remove();
  $('body').removeClass('modal-open');
  $('body').removeAttr('style');


}


removeHelpTipFromTable(event) {
  console.log('fields before delete hTip', this.fields);
  console.log('delete help tip', event['data']);
  let temp = event['data'];
  if ( temp !== null  || temp !== {}) {
    let index = this.gethTipIndex(this.helpTipToDelete);
    console.log('deleted help tip', this.helpTipToDelete, 'index', index);
      this.helpTipsArray.splice(index, 1);
    }
    // adding the field of the deleted help tip to fields array again
    let freeField = {'label': this.helpTipToDelete.field, 'value': this.helpTipToDelete.field_id,
                     'section_id': this.helpTipToDelete.section_id };
    this.fields.push(freeField);
    console.log('fields after deleted hTip', this.fields);
  this.closeModal();
  this.table._totalRecords = this.helpTipsArray.length;

}



displayPreviewModal(ht) {
  this.mode = 'preview';
  this.hTip = {
    'id'         : ht.id,
    'section_id' : ht.section_id,
    'field_id'   : ht.field_id,
    'active'     : ht.active,
    'section'    : ht.section,
    'field'      : ht.field,
    'description': ht.description,
    'langId'     : ht.langId
  };
  this.displayModal  = true;

}

closeModal() {
  this.displayModal  = false;
  $('#tipModal').hide();
  $('div.modal-backdrop.fade.in').remove();
}


displayCreateModal() {
  this.mode = 'create';
  this.displayModal  = true;
}


displayEditFormModal(ht) {
  this.mode = 'edit';
  this.hTip = {
    'id'         : ht.id,
    'section_id' : ht.section_id,
    'field_id'   : ht.field_id,
    'active'     : ht.active,
    'section'    : ht.section,
    'field'      : ht.field,
    'description': ht.description,
    'langId'     : ht.langId
  };
  console.log('q t up', this.hTip);
  this.displayModal  = true;

}



displayDeleteAlert(ht) {
  this.mode = 'delete';
  this.displayModal  = true;
  console.log('display delete alert ht', ht);
  this.helpTipToDelete = {
    'id'         : ht.id,
    'section_id' : ht.section_id,
    'field_id'   : ht.field_id,
    'active'     : ht.active,
    'section'    : ht.section,
    'field'      : ht.field,
    'description': ht.description,
    'langId'     : ht.langId
  };
}



closePreviewOpenEdit() {
  // this.hTip = {
  //   'id'         : this.helpTipToPreview.id,
  //   'section_id' : this.helpTipToPreview.section_id,
  //   'field_id'   : this.helpTipToPreview.field_id,
  //   'active'     : this.helpTipToPreview.active,
  //   'section'    : this.helpTipToPreview.section,
  //   'field'      : this.helpTipToPreview.field,
  //   'description': this.helpTipToPreview.description,
  //   'langId'     : this.helpTipToPreview.langId
  // };

  this.mode = 'edit';

}


gethTipIndex(htip): number {
  let index: number = null;
  for (let ht of this.helpTipsArray) {
    if (ht.id === htip.id) {
      index = this.helpTipsArray.indexOf(ht);
    }
  }
  return index;
}


clearAll() {
  // this.table.filter(null, ['section', 'section_id'], 'contains');
  // this.table.filter(null, ['field', 'field_id'], 'contains');
  this.table.filter(null, 'description', 'contains');
  this.table.filter(null, 'status', 'equals');
  this.table.filter(null, 'field_id', 'equals');
  this.table.filter(null, 'section_id', 'equals');

  this.table.filterGlobal(null, 'contains');
  $('.ui-table-globalfilter-container input').val(null);
  console.log($('.ui-column-filter').val());
  $('.ui-column-filter').val(null);
   this.status = '';
   this.section = '';
   this.field = '';
 }


ngOnDestroy(): void {
   this.ngUnsubscribe.next();
   this.ngUnsubscribe.complete();
}


}

