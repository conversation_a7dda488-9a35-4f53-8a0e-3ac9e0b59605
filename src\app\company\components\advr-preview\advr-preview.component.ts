import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import {LangChangeEvent, TranslateService} from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router, NavigationExtras } from '@angular/router';
import { MenuItem, MessageService } from 'primeng/api';
import { DataMap } from "../../../shared/Models/data_map";
import { ManagePostService } from "../../services/manage-post.service";
import { PostJobService } from "../../services/post-job.service";
import { AdvrsViewService } from "app/general/services/advrs-view.service";
import { ImageProcessingService } from "shared/shared-services/image-processing.service";
import { GeneralService } from 'app/general/services/general.service';
import { Title, Meta } from '@angular/platform-browser';
declare var $: any;


@Component({
  selector: 'app-advr-preview',
  templateUrl: './advr-preview.component.html',
  providers: [MessageService],
  styleUrls: ['./advr-preview.component.css']
})

export class AdvrPreviewComponent implements OnInit , OnDestroy {
  cv_rejected_count: any;
  cv_received_count: any;
  cv_read_count: any;
  AdvType: any;
  AdvrIdSearch: any = null;
  source: any;
  visibleSidebar4;
  job_candidate_exist;
  @Input('AdvrId') AdvrId: any;
  @Input('publicAdv') publicAdv: boolean = false;
  @Output() closeModalPopup = new EventEmitter();
  current_Date = new Date();
  // post_Date_n : number;
  /* job_advertisement_id: any = localStorage.getItem('job_advertisement_id'); ; */
  job_advertisement_id;
  translated_languages_id: any = localStorage.getItem('translated_languages_id');
  current_language = Number (localStorage.getItem('current_company_language'));
  public readmore:boolean = false;
  show_company_name;
  public buttonName:any = 'Read more';
  company_name: string = '';
  company_verified: string = '';
  company_industry_temp: any = [];
  company_industry: any = [];
  compnay_exp_field;
  company_description: string='';
  company_description_header: string = '';
  job_title: string = '';
  job_adv_title: string = '';
  location_job: string = '';
  location_exist = true;
  emp_tye: any =[];
  employment_types = [];
  emp_exist = true;
  money_job: string = '';
  money_job_exist = true;
  year_exps: string = '';
  year_exps_exist = true;
  languages_temp: any = [];
  languages: any = [];
  lang_exist:any;
  nationality_temp: any = [];
  nationality: any = [];
  nation_exist = true;
  age: string = '';
  age_exist = true;
  gender: string='';
  gender_exist = true;
  degree_level_major: string='';
  degree_level_major_exist = true;
  minor: string='';
  minor_exist = true;
  skill_type_temp: any = [];
  skill_type: any = [];
  skill_exist = true;
  certification: string = '';
  certification_exist = true;
  job_advertisement_translation: string = '';
  // post_Date: string = '';
  post_Data;
  // last_posted_renew: string = '';
  published_days_ago: number = 0;
  published_renewed_days_ago: number = null;
  ddate:string='';
  driving: string = '';
  driving_exist = true;
  template:boolean= false;
  private ngUnsubscribe: Subject<any> = new Subject();
  private ngUnsubscribe2: Subject<any> = new Subject();
  username = '';
  companyAdvId = '';
  display_google_form = false;
  data_map = new DataMap();
  display_advr_lang = false;
  passed_Data_to_translated:any;
  image_uploaded_url ;
  showShortDesciption: boolean;
  showLoader: boolean;
  appliedForJob: boolean = null;

  job_titles: any = [];
  companyUsername;
  companyId;
  role = '';
  actionAfterLogin = null;
  // showEditBtn=false;
  otherEmployer = null;
  otherEmployerLogo = null;

 alterDescriptionText() {
    this.showShortDesciption = !this.showShortDesciption
 }

  /* './assets/images/Capture.PNG' */
  constructor(
    private managePost: ManagePostService,
    private messageService: MessageService,
    private postJobService: PostJobService,
    private translate: TranslateService,
    private advrsView: AdvrsViewService,
    private route: ActivatedRoute,
    private router: Router,
  //  private get_img : ImageProcessingService,
    private imageProcessingService : ImageProcessingService,
    private generalService: GeneralService,
    private title: Title,
    private meta:Meta
  ) {
  //  this.showLoader = true;
    this.setRoutingParams();
   }

  /*--- take company's advertisement ID from the url parameters ---*/

   setRoutingParams(){
    this.route.params.subscribe(res => {
      this.title.setTitle(res['slug']);
      this.companyAdvId = res['advId'];
    });
    this.route.parent.params.subscribe(res => {
      this.username = res['username'];
    });
  }

  Display_google_form() {
    this.display_google_form = true;
  }

  closeModal_google_form() {
    this.display_google_form = false;
  }


  changeTempId(TempId){
    this.postJobService.changeTempId(TempId);
  }

  Display_advr_lang() {
    this.display_advr_lang = true;
  }
  closeModal_advr_lang() {
    this.display_advr_lang = false;
    
  }


//   calculateDiff(sentDate) {
//     var date1:any = new Date(sentDate);
//     var current_Date:any = new Date();
//     var diffDays:any = Math.floor((current_Date - date1) / (1000 * 60 * 60 * 24));

//     return diffDays;
// }

  getAdvId() {
    this.postJobService.currentadvId.subscribe(res => {
      if(res.length) {
        localStorage.setItem('translated_languages_id', res[0].translated_languages_id);
        this.translated_languages_id = localStorage.getItem('translated_languages_id');
      }
      
    });

    /*----- get the advertisement's ID using company's advertisement ID after that we get
            Advertisement's Data -----*/
    if(this.companyAdvId && this.AdvType !== 'template') {
      this.showLoader = true;
      this.postJobService.getGlobalAdvId(this.companyAdvId,this.AdvType).switchMap(
        res => {
          this.job_advertisement_id = res['data'].id;
          return this.postJobService.getPostData(this.job_advertisement_id, this.translated_languages_id,this.publicAdv);
        }).takeUntil(this.ngUnsubscribe)
        .subscribe( res => {
          this.setAdvData1(res);
        },

        (error) =>{
          if(error.error.type === 'expired-adv'){
            this.setAdvData1(error.error.data);
            this.messageService.add({
              key:"expired-adv",
              severity: "warn",
              detail: error.error.error,
              sticky:true,
              data:error.error.help
            });
          }
        }
      
      );
    //  this.showLoader = false;
    }
  }


  setAdvData1(res){
    if(res['company_info'].company_logo && (res['advertisement_info'].show_company_name || res['advertisement_info'].other_employer !== null ) ) {
      this.image_uploaded_url = this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail', res['company_info'].company_logo);

      this.meta.updateTag({ property: 'og:image:url', content: this.image_uploaded_url, itemprop: 'image' });
    }
    let x,y,z,d
    this.post_Data = res['advertisement_info'];
    if(res['company_info'].company_profile_translations.length) {
      this.company_name = res['company_info'].company_profile_translations[0].name;
      this.company_description = res['company_info'].company_profile_translations[0].company_description;
      if(this.company_description !== null && this.company_description.length >= 300){
        this.showShortDesciption = true;
      }
      else this.showShortDesciption = false;
      // d = this.company_description.split(' ', 3);
      // this.company_description_header = d[0];
    }

    if(res['company_info'].company_username)
      this.companyUsername = res['company_info'].company_username;

    if(res['company_info'].company_verified !==undefined)
      this.company_verified = res['company_info'].company_verified;

    if(res['advertisement_info']['exp_field']) {
      this.compnay_exp_field = res['advertisement_info']['exp_field'].name
    }
    this.job_adv_title = null
    if(res['advertisement_info']['job_advertisement_translation']) {
      this.job_adv_title = res['advertisement_info']['job_advertisement_translation'][0].job_adv_title
      
    }
    
    if(res['advertisement_info']['job_titles']){
      this.job_titles = res['advertisement_info']['job_titles'];
    }

     if(res['advertisement_info'].nationality.length === 0) {
      this.nation_exist = false;
    
    } else {
      
      y = res['advertisement_info'].nationality;
      this.nation_exist = true;
      this.job_candidate_exist = true;
    }
    
     if(res['advertisement_info'].international_languages.length !=0) {
      
      x = res['advertisement_info'].international_languages;        
      this.lang_exist = true;
      this.job_candidate_exist = true;
    } else {
      this.lang_exist = false;
    }


      if(res['advertisement_info'].skill_type.length === 0) {
        this.skill_exist = false;
      } else {
        z = res['advertisement_info'].skill_type;
        this.skill_exist = true;
        this.job_candidate_exist = true;
      }

    // this.post_Date = res['advertisement_info'].created_at;
    this.published_days_ago = res['advertisement_info'].published_days_ago;
    if(res['advertisement_info'].published_renewed_days_ago !== undefined){
      this.published_renewed_days_ago = res['advertisement_info'].published_renewed_days_ago;
    }
    // if( res['advertisement_info'].last_posted_renew!=null)
    //    this.last_posted_renew = res['advertisement_info'].last_posted_renew;
   
     this.company_industry_temp = res['company_info']['industries'];
     this.company_industry = this.company_industry_temp.join(', ');
     if(res['advertisement_info'].job_title_synonyms) {

      this.job_title = res['advertisement_info'].job_title_synonyms.job_title.job_title_translation[0].name
       + ' - ' + this.job_adv_title;
     }

     this.otherEmployer = null;
     if(res['advertisement_info'].other_employer !== null){
      this.otherEmployer = res['advertisement_info'].other_employer;
      this.otherEmployerLogo = this.imageProcessingService.getImagePath ('otherEmployerLogo','med_thumbnail',this.otherEmployer.logo);
    }

    if(this.otherEmployer === null){
      if(res['advertisement_info']['company_location'] === null ) {
        this.location_exist = false;
      } else {
        if(res['advertisement_info']['company_location'].country !== null) {
          if(res['advertisement_info']['company_location'].city !==null){
            if(res['advertisement_info']['company_location'].street_address !==null)
              this.location_job = res['advertisement_info']['company_location'].country + ', ' + res['advertisement_info']['company_location'].city + ', ' + res['advertisement_info']['company_location'].street_address;
            else
              this.location_job = res['advertisement_info']['company_location'].country + ', ' + res['advertisement_info']['company_location'].city;
          }
          else
            this.location_job = res['advertisement_info']['company_location'].country;
          this.location_exist = true;
        } 
        else {
          this.location_exist = false;
        }
      }
      
    }

    else{
      if(this.otherEmployer !== null && this.otherEmployer.work_location === null ){
        this.location_exist = false;
      }
      else{
        if(this.otherEmployer !== null && this.otherEmployer.work_location !== null){
          if(this.otherEmployer.work_location.city !== null){
            if(this.otherEmployer.work_location.street_address !== null)
              this.location_job = this.otherEmployer.work_location.country + ', ' + this.otherEmployer.work_location.city +', ' + this.otherEmployer.work_location.street_address;
            else
              this.location_job = this.otherEmployer.work_location.country + ', ' + this.otherEmployer.work_location.city;
          }
          else
            this.location_job = this.otherEmployer.work_location.country;
          this.location_exist = true;
        }
        else {
          this.location_exist = false;
        }
      }
     
    }

     
     if(res['advertisement_info']['is_online'] === 1 || (this.otherEmployer !== null && this.otherEmployer.remote ===true ) ){
      this.location_job = 'Remote';
      this.location_exist = true;
     }
 

    if(res['advertisement_info'].employment_type_parent.length > 0) {
  
      for( let i=0; i < res['advertisement_info'].employment_type_parent.length; i++){

        this.emp_tye.push(res['advertisement_info'].employment_type_parent[i]
        .emp_type_parent_translation[0].name);
      }
      this.employment_types = this.emp_tye.join(', ');
      this.emp_exist = true;
    } else {
      this.emp_exist = false;
    }


    if(res['advertisement_info'].salary_from !== null && res['advertisement_info'].salary_to !== null) {
      if(res['advertisement_info'].currency_code['currency_code'] !== null) {
        this.money_job = res['advertisement_info'].salary_from + ' to ' + res['advertisement_info'].salary_to + ' / ' + res['advertisement_info'].salary_type + ' / ' + res['advertisement_info'].currency_code['currency_code'];
        this.money_job_exist = true;
      } 
      // else {
      //   this.money_job = res['advertisement_info'].salary_from + ' to ' + res['advertisement_info'].salary_to + ' / ' + 'Monthly';
      // }
    }
    else {
      this.money_job_exist = false;
    } 

    if(res['advertisement_info'].year_of_exp_id !== null ) {
      this.year_exps = res['advertisement_info'].year_of_exp.code;
      this.year_exps_exist = true;
      this.job_candidate_exist = true;
    } else {
      this.year_exps_exist = false
    }
    
    if(x!== undefined) {
      for (let i = 0 ; i < x.length ; i++) {
        this.languages_temp.push(x[i].international_language_trans[0].name);
       }
       this.languages = this.languages_temp.join(', ');
       this.job_candidate_exist = true;
    }
    
     if(res['advertisement_info'].age_from !== null && res['advertisement_info'].age_to !== null) {
      this.age = res['advertisement_info'].age_from + ' - ' + res['advertisement_info'].age_to;
      this.age_exist = true;
      this.job_candidate_exist = true;
     } else {
      this.age_exist = false;
     }
     
     if( y !== undefined ) {
      for (let i = 0 ; i < y.length ; i++) {
        this.nationality_temp.push(y[i].nationality_translation[0].name);
       }
       this.nationality = this.nationality_temp.join(', ');
       this.job_candidate_exist = true;
     }

     if(res['advertisement_info'].gender !== null) {
      this.gender = res['advertisement_info'].gender;  
      this.gender_exist = true; 
      this.job_candidate_exist = true;
     } else {
      this.gender_exist = false;
     }
     
     this.degree_level_major = '';
     if( res['advertisement_info'].degree_level !== null && res['advertisement_info'].educations.length > 0) {
      this.degree_level_major = res['advertisement_info'].degree_level.degree_level_translation[0]
      .name + ' in ';
      for (let i = 0 ; i < res['advertisement_info'].educations.length ; i++) {
        this.degree_level_major = this.degree_level_major + res['advertisement_info'].educations[i].name;
        if(i < res['advertisement_info'].educations.length-1){
          this.degree_level_major = this.degree_level_major + ', ';
        } 
      }
      this.degree_level_major_exist = true;
      this.job_candidate_exist = true;
      
     } else if(res['advertisement_info'].educations.length > 0) {
      for (let i = 0 ; i < res['advertisement_info'].educations.length ; i++) {
        this.degree_level_major = this.degree_level_major + res['advertisement_info'].educations[i].name;
        if(i < res['advertisement_info'].educations.length-1){
          this.degree_level_major = this.degree_level_major + ', ';
        } 
      }
      this.degree_level_major_exist = true;
      this.job_candidate_exist = true;
      
     } else if(res['advertisement_info'].degree_level !== null) {
      this.degree_level_major = res['advertisement_info'].degree_level.degree_level_translation[0].name;
      this.degree_level_major_exist = true;
      this.job_candidate_exist = true;
     } else {
      this.degree_level_major_exist = false
     }
     
    //  if(res['advertisement_info'].minor !== null) {
    //  this.minor = res['advertisement_info'].minor.minor_translation[0].name;
    //  this.job_candidate_exist = true;
    //  } else {
    //     this.minor_exist = false;
    //  }
     
     if(z!== undefined) {
      for (let i = 0 ; i < z.length ; i++) {
        this.skill_type_temp.push(z[i].skill_type_trans[0].name);
       }
       this.skill_type = this.skill_type_temp.join(', ');
       this.job_candidate_exist = true;
     }
     if(res['advertisement_info'].certification !== null) {
      this.certification = res['advertisement_info'].certification;
      this.certification_exist = true;
     } else {
      this.certification_exist = false;
     }

     if(res['advertisement_info'].driving_license !== null) {
      this.driving = res['advertisement_info'].driving_license;
      this.driving_exist = true;
     } else {
      this.driving_exist = false;
     }
     if( res['advertisement_info'].job_advertisement_translation) {
      this.job_advertisement_translation = res['advertisement_info'].
      job_advertisement_translation[0].job_description;
     }
     this.show_company_name = res['advertisement_info'].show_company_name;
    //  this.post_Date_n = +this.post_Date;
     
    
    this.showLoader = false;

  }

  /*---- implementation for Read More / Read Less button ----*/
  readMore() {
    this.readmore = !this.readmore;
    if(this.current_language === 1) {

      if(this.readmore)  
        this.buttonName = "Read less";
      else
        this.buttonName = "Read more";
    } else {
      if(this.readmore)  
        this.buttonName = "اقرأ أقل";
      else
        this.buttonName = "اقرأ المزيد";
    }
      

  }

  edit(AdvId?) {
    if(AdvId){
      this.job_advertisement_id = AdvId;
    }
    if(this.username === undefined){
      this.route.parent.parent.params.subscribe(res => {
        this.username = res['username'];
      });
    }
    if(this.AdvType === 'template'){
      this.managePost.getCompanyTemplateAdvertisementData(this.job_advertisement_id).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['adv_temp_info']);
          this.postJobService.changeStatus(true);
          this.passed_Data_to_translated.push({
            'translated_language_id':this.current_language
          });
          this.postJobService.send_Data(this.passed_Data_to_translated);
          $('#AdvrsModal').modal('hide');
          this.router.navigate(['/c',this.username,'post-job']);
        });
    }
    else{
      this.managePost.getCompanyAdvertisementData(this.job_advertisement_id).takeUntil(this.ngUnsubscribe).subscribe(
        (res) => {
          this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['advertisement_info']);
          this.postJobService.changeStatus(true);
          this.passed_Data_to_translated.push({
            'translated_language_id':this.current_language
          });
          this.postJobService.send_Data(this.passed_Data_to_translated);
          $('#AdvrsModal').modal('hide');

          // it means after create adv , company didn't published it or saved it as draft or template
          // so after editing and saving adv navigate company user to advr preview interface 
          //and display adv saving options
          if(this.source==='post_job' || this.source === 'reload'){
            let advOptions={showAdvOptions: true};
            let navigationExtras: NavigationExtras = {
              state: {
                advOptions: advOptions
              }
            };
            this.router.navigate(['/c',this.username,'post-job'],navigationExtras);
          }
          else
            this.router.navigate(['/c',this.username,'post-job']);
        });
    }

  }
  
  fullActionAdvrsPreview(action, AdvId) {
    if(action === 'edit') {
      this.closeModalPopup.emit({'edit': true , 'AdvType': this.AdvType, 'AdvId': AdvId, 'action':action});
      $('#AdvrsModal').modal('hide');
    } else {
      if(action === 'activate'){
        this.closeModalPopup.emit({'edit': false , 'AdvType': this.AdvType, 'AdvId': AdvId, 'action':action, 'active':this.post_Data.active_status,'companyAdvId':this.companyAdvId });
      }
      else if(action ==='saveAsTemplate' || action ==='endWorkFlow' || action ==='deleteAdvr' || action ==='delete_temp' || action ==='saveAsPublish' || action ==='saveTemplateAsPublish' ){
        this.closeModalPopup.emit({'edit': false , 'AdvType': this.AdvType, 'AdvId': AdvId, 'action':action, 'companyAdvId':this.companyAdvId });
      }
      else{
        this.closeModalPopup.emit({'edit': false , 'AdvType': this.AdvType, 'AdvId': AdvId, 'action':action});
      }
      
      $('#AdvrsModal').modal('hide');
    }
    
  }

  saveAsDraft(){
    this.postJobService.saveAsDraft(this.job_advertisement_id).subscribe(
      (res) =>  {
        window.alert('Advertisement has been saved as Draft successfuly!')
        this.router.navigate(['/c',this.username,'manage_advs','draft'])
    
    }
      
    );
    
  }

  saveAsTemplate(){
    this.template = true;
    this.postJobService.saveAsTemplate(this.job_advertisement_id).subscribe(
      (res) => {
      this.changeTempId(res['data'].job_advertisement_translation[0].job_advertisement_template_id);
      window.alert('Advertisement has been saved as Template successfuly!')
      this.router.navigate(['/c',this.username,'manage_advs','template'])
    });
  }

  saveAsPublish() {
    
    this.postJobService.saveAsPublish(this.job_advertisement_id).subscribe(
      (res) => {

        this.passed_Data_to_translated = this.data_map.optimiziaion_Advr_Data(res['advertisement_info']);
        this.router.navigate(['/c',this.username,'manage_advs','published'])

        
    });

//this.display_advr_lang = true;
  }

  setcompanyLanguage(langaugeId) {
    if (langaugeId === 1) {
      this.translate.setDefaultLang('en');
    } else if (langaugeId === 2) {
      this.translate.setDefaultLang('ar');
    } else {
      this.translate.setDefaultLang('en');
    }
  }

  /* job_app() {
    this.router.navigate(['/c',this.username,'generator']);
  } */


  getAdvDataSearchJob(advId){
    let functionName;
    if(this.AdvType === 'template') {
      functionName = 'getTempData'
    } else {
      functionName = 'getPostData'
    }
    
    this.showLoader = true;
    this.postJobService[functionName](advId, this.translated_languages_id,this.publicAdv).subscribe(
      (res) => {
        this.setAdvData2(res);
      },

      (error) =>{
        if(error.error.type === 'expired-adv'){
          this.setAdvData2(error.error.data);
          this.messageService.add({
            key:"expired-adv",
            severity: "warn",
            detail: error.error.error,
            sticky:true,
            data:error.error.help
          });
        }
      }
      
      )
  }

  setAdvData2(res){
    if(this.AdvType === 'template') {
      this.companyAdvId = res['advertisement_info'].adv_template_id_by_company;
    } else {
      this.companyAdvId = res['advertisement_info'].adv_id_by_company;
    }
    let x,y,z,d
    this.image_uploaded_url = null
    
    if(res['company_info'].company_logo && (res['advertisement_info'].show_company_name || res['advertisement_info'].other_employer !== null) ) {
      this.image_uploaded_url = this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail', res['company_info'].company_logo);
      this.meta.updateTag({ property: 'og:image:url', content: this.image_uploaded_url, itemprop: 'image' });
    }
    this.post_Data = []
    this.post_Data = res['advertisement_info'];
    
    //set meta title and meta description for adv
    if(res['advertisement_info'].meta_title){
      this.title.setTitle(res['advertisement_info'].meta_title);
      this.meta.updateTag({ property: 'og:title', content: res['advertisement_info'].meta_title });
    }
    if(res['advertisement_info'].meta_description){
      this.meta.updateTag({ name: 'description', content: res['advertisement_info'].meta_description });
      this.meta.updateTag({ property: 'og:description', content: res['advertisement_info'].meta_description });
    }
    
    if(res['company_info'].company_profile_translations.length) {
      this.company_name = res['company_info'].company_profile_translations[0].name;
      this.company_description = res['company_info'].company_profile_translations[0].company_description;
      if(this.company_description !== null && this.company_description.length >= 300){
        this.showShortDesciption = true;
      }
      else this.showShortDesciption = false;
      // d = this.company_description.split(' ', 3);
      // this.company_description_header = d[0];

      this.companyId = res['company_info'].company_id;
    }
  
    if(res['company_info'].company_username)
      this.companyUsername = res['company_info'].company_username;

    if(res['company_info'].company_verified !== undefined)
      this.company_verified = res['company_info'].company_verified;

    if(res['advertisement_info']['exp_field']) {
      this.compnay_exp_field = res['advertisement_info']['exp_field'].name
    }
    
    this.job_adv_title = null
    if(res['advertisement_info']['job_advertisement_translation']) {
      this.job_adv_title = res['advertisement_info']['job_advertisement_translation'][0].job_adv_title
    }

    if(res['advertisement_info']['job_titles']){
      this.job_titles = res['advertisement_info']['job_titles'];
    }

    y = [];
     if(res['advertisement_info'].nationality.length === 0) {
      this.nation_exist = false;
    
    } else {
      
      y = res['advertisement_info'].nationality;
      this.nation_exist = true;
      this.job_candidate_exist = true;
    }
      x = []
     if(res['advertisement_info'].international_languages.length !=0) {
      
      x = res['advertisement_info'].international_languages;        
      this.lang_exist = true;
      this.job_candidate_exist = true;
    } else {
      this.lang_exist = false;
    }

    z = []
      if(res['advertisement_info'].skill_type.length === 0) {
        this.skill_exist = false;
      } else {
        z = res['advertisement_info'].skill_type;
        this.skill_exist = true;
        this.job_candidate_exist = true;
      }

    this.company_industry_temp = []
    this.job_title = '';
  
    // this.post_Date = res['advertisement_info'].created_at;
    this.published_days_ago = res['advertisement_info'].published_days_ago;
    if(res['advertisement_info'].published_renewed_days_ago !== undefined ){
      this.published_renewed_days_ago = res['advertisement_info'].published_renewed_days_ago;
    }
    // if( res['advertisement_info'].last_posted_renew!=null)
    //   this.last_posted_renew = res['advertisement_info'].last_posted_renew;

     this.company_industry_temp = res['company_info']['industries'];
     this.company_industry = this.company_industry_temp.join(', ');
     if(res['advertisement_info'].job_title_synonyms) {

      this.job_title = res['advertisement_info'].job_title_synonyms.job_title.job_title_translation[0].name
       + ' - ' + this.job_adv_title;
     }

     this.otherEmployer = null;
     if(res['advertisement_info'].other_employer !== null){
      this.otherEmployer = res['advertisement_info'].other_employer;
      this.otherEmployerLogo = this.imageProcessingService.getImagePath ('otherEmployerLogo','med_thumbnail',this.otherEmployer.logo);
    }

    if(this.otherEmployer === null){
      if(res['advertisement_info']['company_location'] === null ) {
        this.location_exist = false;
      } else {
        if(res['advertisement_info']['company_location'].country !== null) {
          if(res['advertisement_info']['company_location'].city !==null){
            if(res['advertisement_info']['company_location'].street_address !==null)
              this.location_job = res['advertisement_info']['company_location'].country + ', ' + res['advertisement_info']['company_location'].city + ', ' + res['advertisement_info']['company_location'].street_address;
            else
              this.location_job = res['advertisement_info']['company_location'].country + ', ' + res['advertisement_info']['company_location'].city;
          }
          else
            this.location_job = res['advertisement_info']['company_location'].country;
          this.location_exist = true;
        } 
        else {
          this.location_exist = false;
        }
      }
      
    }

    else{
      if(this.otherEmployer !== null && this.otherEmployer.work_location === null ){
        this.location_exist = false;
      }
      else{
        if(this.otherEmployer !== null && this.otherEmployer.work_location !== null){
          if(this.otherEmployer.work_location.city !== null){
            if(this.otherEmployer.work_location.street_address !== null)
              this.location_job = this.otherEmployer.work_location.country + ', ' + this.otherEmployer.work_location.city + ', ' + this.otherEmployer.work_location.street_address;
            else
              this.location_job = this.otherEmployer.work_location.country + ', ' + this.otherEmployer.work_location.city;
          }
          else
            this.location_job = this.otherEmployer.work_location.country;
          this.location_exist = true;
        }
        else {
          this.location_exist = false;
        }
      }
     
    }


     if(res['advertisement_info']['is_online'] === 1  ||  (this.otherEmployer !== null && this.otherEmployer.remote ===true )){
      this.location_job = 'Remote';
      this.location_exist = true;
     }
 
     this.employment_types = [];
     this.emp_tye = [];
    if(res['advertisement_info'].employment_type_parent.length > 0) {
  
      for( let i=0; i < res['advertisement_info'].employment_type_parent.length; i++){

        this.emp_tye.push(res['advertisement_info'].employment_type_parent[i]
        .emp_type_parent_translation[0].name);
      }
      this.employment_types = this.emp_tye.join(', ');
      this.emp_exist = true;
      
    } else {
      this.emp_exist = false;
    }
    this.money_job  = ''
    //this.money_job_exist = true;


    if(res['advertisement_info'].salary_from !== null && res['advertisement_info'].salary_to !== null) {
      if(res['advertisement_info'].currency_code['currency_code'] !== null) {
        this.money_job = res['advertisement_info'].salary_from + ' to ' + res['advertisement_info'].salary_to + ' / ' + res['advertisement_info'].salary_type + ' / ' + res['advertisement_info'].currency_code['currency_code'];
        this.money_job_exist = true;
      } 
      // else {
      //   this.money_job = res['advertisement_info'].salary_from + ' to ' + res['advertisement_info'].salary_to + ' / ' + 'Monthly';
      // }
    } 
    else {
      this.money_job_exist = false;
    }

    if(res['advertisement_info'].year_of_exp_id !== null ) {
      this.year_exps = res['advertisement_info'].year_of_exp.code;
      this.year_exps_exist = true;
      this.job_candidate_exist = true;
    } else {
      this.year_exps_exist = false;
    }
    
    
    if(x!== undefined) {
      this.languages_temp = [];
      this.languages = [];
      for (let i = 0 ; i < x.length ; i++) {
        this.languages_temp.push(x[i].international_language_trans[0].name);
       }
       this.languages = this.languages_temp.join(', ');
       this.job_candidate_exist = true;
    }
    
     if(res['advertisement_info'].age_from !== null && res['advertisement_info'].age_to !== null) {
      this.age = res['advertisement_info'].age_from + ' - ' + res['advertisement_info'].age_to;
      this.age_exist = true;
      this.job_candidate_exist = true;
     } else {
      this.age_exist = false;
     }
     
     if( y !== undefined ) {
      this.nationality_temp = [];
      this.nationality = [];
      for (let i = 0 ; i < y.length ; i++) {
        this.nationality_temp.push(y[i].nationality_translation[0].name);
       }
       this.nationality = this.nationality_temp.join(', ');
       this.job_candidate_exist = true;
     }
     

     if(res['advertisement_info'].gender !== null) {
      this.gender = res['advertisement_info'].gender; 
      this.gender_exist = true; 
      this.job_candidate_exist = true;
     } else {
      this.gender_exist = false;
     }

     this.degree_level_major = '';
     if( res['advertisement_info'].degree_level !== null && res['advertisement_info'].educations.length > 0) {
      this.degree_level_major = res['advertisement_info'].degree_level.degree_level_translation[0]
      .name + ' in ';
      for (let i = 0 ; i < res['advertisement_info'].educations.length ; i++) {
        this.degree_level_major = this.degree_level_major + res['advertisement_info'].educations[i].name;
        if(i < res['advertisement_info'].educations.length-1){
          this.degree_level_major = this.degree_level_major + ', ';
        } 
      }
      this.degree_level_major_exist = true;
      this.job_candidate_exist = true;
      
     } else if(res['advertisement_info'].educations.length > 0) {
      for (let i = 0 ; i < res['advertisement_info'].educations.length ; i++) {
        this.degree_level_major = this.degree_level_major + res['advertisement_info'].educations[i].name;
        if(i < res['advertisement_info'].educations.length-1){
          this.degree_level_major = this.degree_level_major + ', ';
        } 
      }
    //  this.degree_level_major = res['advertisement_info'].major.major_translation[0].name;
      this.degree_level_major_exist = true;
      this.job_candidate_exist = true;
      
     } else if(res['advertisement_info'].degree_level !== null) {
      this.degree_level_major = res['advertisement_info'].degree_level.degree_level_translation[0].name
      this.degree_level_major_exist = true;
      this.job_candidate_exist = true;
     } else {
      this.degree_level_major_exist = false;
     }
     
     
    //  if(res['advertisement_info'].minor !== null) {
    //  this.minor = res['advertisement_info'].minor.minor_translation[0].name;
    //  this.job_candidate_exist = true;
    //  } else {
    //     this.minor_exist = false;
    //  }
     
     if(z!== undefined) {
      this.skill_type = [];
      this.skill_type_temp = [];
      for (let i = 0 ; i < z.length ; i++) {
        this.skill_type_temp.push(z[i].skill_type_trans[0].name);
       }
       this.skill_type = this.skill_type_temp.join(', ');
       this.job_candidate_exist = true;
     }
     

     if(res['advertisement_info'].certification !== null) {
      this.certification = res['advertisement_info'].certification;
      this.certification_exist = true;
     } else {
      this.certification_exist = false;
     }

     if(res['advertisement_info'].driving_license !== null) {
      this.driving = res['advertisement_info'].driving_license;
      this.driving_exist = true;
     } else {
      this.driving_exist = false;
     }
     if( res['advertisement_info'].job_advertisement_translation) {
      this.job_advertisement_translation = res['advertisement_info'].
      job_advertisement_translation[0].job_description;

     }

     this.show_company_name = res['advertisement_info'].show_company_name;

     this.cv_read_count = res['advertisement_info']['cv_read_count'];
     this.cv_received_count = res['advertisement_info']['cv_received_count'];
     this.cv_rejected_count = res['advertisement_info']['cv_rejected_count'];

    //  this.post_Date_n = +this.post_Date;

     this.showLoader = false;

     if(res['advertisement_info'].applied !== null){
      this.appliedForJob = res['advertisement_info'].applied;
     }
     // if applied = null then it is a company user or admin user
     else this.appliedForJob = null;
    
  }

  //apply_for_job(advId,company_name, job_title)
  apply_for_job(advId) {
    if(this.role !== 'unauth'){
      this.applyJob(advId,false);
    }
    else{
      this.actionAfterLogin = {'message':'apply'};
    //  this.currentAdvrInfo = { 'advId': advId, 'company_name': company_name, 'job_title': job_title };
      $('#authModal').modal('toggle');
      // this.generalService.notify('loginThenApply', 'advr-preview' , 'advr-interface' , {'advId':advId , 'companyName':this.company_name,'jobAdvTitle':this.job_adv_title} );
      // this.closeModal();
    }
    
  }

  //used to apply for a job on our site
  applyJob(advId,afterLogin){
    const bc = new BroadcastChannel('searchJob-advrPreview');
    this.advrsView.applyForJob(advId).subscribe(
      (res) => {
        if(res['error']) {
          this.messageService.add({ severity:'error', summary:'Apply Job' , detail:res['error']});
          if(res['error'] === "You are submitted to this advertisement previously"){
            this.appliedForJob = true;
          }
          // send message to search job tab to update it's state
          if(afterLogin){
            bc.postMessage('loginAndNotApplied');
          }
        //  this.generalService.notify('errorApplyAfterPerview', 'advr-preview' , 'advr-interface' , {"error": res['error']} );
        } else {
          let successMessage = '';
          if(this.show_company_name === 1){
            successMessage = 'Applied Successfully to ' + this.job_adv_title + ' at ' + this.company_name;
          }
          else {
            successMessage = 'Applied Successfully to ' + this.job_adv_title;
          }
          this.messageService.add({ severity:'success', summary: 'Apply Job', detail:successMessage});
          this.appliedForJob = true;
          // send message to search job tab to update it's state
          if(afterLogin || this.role !== 'unauth'){
            bc.postMessage('loginAndApplied');
          }
          // const bc = new BroadcastChannel('test_channel');
          // bc.postMessage('GetData');

        //  this.generalService.notify('appliedAfterPerview', 'advr-preview' , 'advr-interface' , {'appliedForJob':true} );
        }
      }
    );
  }

  //apply for job on company's site
  apply_on_company_site(url){
    if (this.role !== 'unauth') {
      window.open(url, '_blank');
    }
    else{
      this.actionAfterLogin = {'message':'applyOnCompanySite','url':url};
      $('#authModal').modal('toggle');
    }
  }

  ngOnInit() {
    //this piece of code moved from constructor to ngOnInit
    this.postJobService.currentadvId.takeUntil(this.ngUnsubscribe2).subscribe(
      (res) => {
        if(res.length>0) {
          this.source = res[0].source;
          this.AdvrIdSearch=res[0].Advid;
         
          localStorage.setItem('translated_languages_id', res[0].translated_languages_id);
          this.translated_languages_id = localStorage.getItem('translated_languages_id');
          if(this.source ==='post_job' && this.AdvrIdSearch) {
            this.getAdvId();
          } else if(this.source === 'search-job' && this.AdvrIdSearch) {
            this.getAdvDataSearchJob(this.AdvrIdSearch);
          } else if(res[0].source[0] === 'manage_advs' && this.AdvrIdSearch) {
            this.source = this.source[0];
            this.AdvType =  res[0].source[1];
            this.getAdvDataSearchJob(this.AdvrIdSearch);
          } 
        } else {
          this.source = 'reload';
          this.getAdvId();
        }
      });
    
    this.setcompanyLanguage(this.current_language);

    if(localStorage.getItem("role")){
      this.role = localStorage.getItem("role");
    }
    else{
      this.role = 'unauth';
    }

    this.generalService.internalMessage.subscribe((data) => {
      if (data['src'] === 'login') {
        if (data['message'] === 'loginSuccess') {
          $('#authModal').modal('hide');
          this.role = localStorage.getItem('role');
          //single-post is the adv page that disaplay single adv with slug
          this.generalService.notify('roleChanged', 'company-preview', 'single-post', {'role':this.role });
          
          if(this.actionAfterLogin['message']){
            if(this.actionAfterLogin['message']==='apply'){
              this.route.params.subscribe(res => {
                if(res['id'] !== undefined){
                  this.applyJob(res['id'],true);
                }
              });
            }
            else if(this.actionAfterLogin['message']==='applyOnCompanySite'){
              this.apply_on_company_site(this.actionAfterLogin['url']);
            }
          }
          
          
        }
      }
    });

    //commented, not working correctly, because this component will recieve the mgs only if the tab is already opened
    // recieve message from company public preview(inside company account) tab to show edit button
    // const bc = new BroadcastChannel('company-preview-advrPreview');
    // bc.onmessage = (ev) => {
    //   if (ev.data == "company-account-public-preview") {
    //     this.showEditBtn = true;
    //   }
    // };
  }

    changeAction($event){
    let action = $event.type;
    if(action === 'publish'){
      this.saveAsPublish();
    }
    else if(action === 'template'){
      this.saveAsTemplate();
    }
    else if(action === 'draft'){
      this.saveAsDraft();
    }
    else if(action === 'edit'){
      this.edit();
    }
  }

  displayContactModal() {
    this.generalService.notify('display Contact Modal' , 'error-page' , 'contact-us' , {'displayContactModal':true}) ;
  }

  closeModal(){
    $('#AdvrsModal').modal('hide');
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.postJobService.changeAdvId_lang(null, this.translated_languages_id, 'advr_preview');
    this.ngUnsubscribe2.next();
    this.ngUnsubscribe2.complete();
  }
  handlePopup(event){

  }

}
