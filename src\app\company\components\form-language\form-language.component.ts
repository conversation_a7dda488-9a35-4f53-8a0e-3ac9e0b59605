import {Component, EventEmitter, Input, OnInit, Output, OnDestroy} from '@angular/core';
import {AbstractControl, FormBuilder, FormControl, Validators} from '@angular/forms';
import { CompanyWrapperService } from '../../services/company-wrapper.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CompanyFormService } from './../../services/company-form.service';
import { Subject } from 'rxjs';
declare var $: any;

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'form-language',
  templateUrl: './form-language.component.html',
  styleUrls: ['./form-language.component.css']
})
export class FormLanguageComponent implements OnInit, OnDestroy {

  languageForm;
  username = '';
  profile_status = true;
  @Output() closeModalPopup = new EventEmitter();
  translated_language_temp = [];
  private ngUnsubscribe: Subject<any> = new Subject();
  companyId = Number (localStorage.getItem('company_id'));
  constructor(private  fb: FormBuilder,
    private companyWrapperService: CompanyWrapperService,
    private companyFormService: CompanyFormService,
    private route: ActivatedRoute,
    private router: Router) {
    this.languageForm = this.fb.group({
      translated_languages_id : [ ''],
      main_language_id: ['']
    });

    this.route.parent.parent.params.subscribe(res => {
      this.username = res['username'];
    });
}

  ngOnInit() {
    this.companyWrapperService.getOtherLanguages(this.companyId).subscribe(
      (res) => {
        let other: any = [];
        other = res;
        if (other.length !== 0) {
          this.translated_language_temp = other;
          this.translated_language_temp.unshift({'name': '', 'id': ''});
        } else {
          this.translated_language_temp.unshift({'name': 'No other languages are available' , 'id': ''});
          this.translated_language_temp.unshift({'name': '', 'id': ''});
          
        }
      }
    );
  }

  /*--- choose the desired language for the new company profile ---*/

  submit() {
    if (this.languageForm.valid) {

      let sendData = this.languageForm.value;
      sendData.main_language_id = sendData.translated_languages_id.id;
     /*  localStorage.setItem('old_company_language',localStorage.getItem('current_company_language')); */
      localStorage.setItem('new_company_language', sendData.main_language_id);
      this.closeModalPopup.emit({'new': sendData.main_language_id});
      $('#companyModal').modal('hide');   


    }



  }
  ngOnDestroy() {
    
           this.ngUnsubscribe.next();
           this.ngUnsubscribe.complete();
    
         }
}
