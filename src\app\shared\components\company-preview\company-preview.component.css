.public-preview-page-content{
    padding : 40px;
    /* margin-top:100px; */
    background : #f8f8f8;
}

.public-preview-wrapper{
    background: white;
    box-shadow: 0 0 5px #777777;
    padding: 40px;
    color: #555;
}
.no-padding-bottom .row {
    margin-bottom: 5px;
    padding-bottom: 0;
    font-size: 16px;
}
  
.top-flex-row {
    display: flex;
    /* align-items: center; */
    flex-wrap: wrap;
}
  
.dark-background-section{
    /* background: #03253f; */
    background:#10996D;
    padding: 30px 40px;
    color: white;
    margin: -40px -40px 40px;
}
.public-preview-company-img{
    margin-bottom:20px;
}
.public-preview-company-img img{
    margin: auto;
    border: 3px solid white;
    border-radius: 5px;
    padding: 3px;
    max-width: 120px;
}
  
.public-preview-user-name h1{
    font-weight: bold;
    font-size: 26px !important;
    color: white;
    margin-top:0;
    margin-bottom: 15px;
    display: inline-block;
}
.public-preview-user-name span{
    display:inline-block;
}
.public-preview-user-name img{
    display:inline-block;
    margin:0 0 3px 8px;
} 
.secondaryp{
    color: #3d7bce;
}
.left-col-preview-align-right{
    text-align: right;
    color: #3d7bce;
    font-weight: bold;
}
fieldset {
    display: block;
    margin-left: 0;
    margin-right: 0;
    padding:15px 25px;
    min-width: -webkit-min-content;
    border: 1px solid #ddd;
    border-image: initial;
    position: relative;
}
legend {
    display: block;
    padding-left: 10px;
    padding-right: 10px;
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
    width: auto;
    font-size: 16px;
    color: #3d7bce;
    margin: 10px 10px;
    background: #fff;
    font-weight: bold;
}
.custom-fieldset{
    position:relative;
    padding:15px 25px;
    border: 1px solid #ddd;
}
.custom-legend{
    position: absolute;
    top: -12px;
    left: 18px;
    padding-left: 10px;
    padding-right: 10px;
    font-size: 16px;
    color: #3d7bce;
    background: #fff;
    font-weight: bold;
    margin: 0;
}
.fieldset-content{
    margin-top: 15px;
}
.custom-col-3{
    width: 33.33%;
    float: left;
    position: relative;
    min-height: 1px;
    padding: 0 15px;
}
/* .custom-col-6{
    width: 66.66%;
    float: left;
    position: relative;
    min-height: 1px;
    padding: 0 15px;
} */
.profile-info .row{
    margin-bottom:10px;
}
.social-media img{
    margin-right: 6px;
    width:25px;
}
.jobs-row{
    margin-bottom: 15px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 3px;
}
.jobs-row:nth-of-type(odd) {
    background: #f4f4f4;
}
.jobs-row:hover{
    background: #eee;
}
.job-title{
    color: #3d7bce;
    font-weight: bold;
    font-size: 14px;
    display: inline-block;
    margin-right: 5px;
    margin-top:0;
}
.adv-time{
    color:#30A03E;
}
.adv-label{
    color: #fff;
    background: #30A03E;
    padding: 2px 8px;
    border-radius: 100px;
}
.section-mar-bot{
    margin-bottom: 35px;
}
.adv-title-padding{
    padding:0 15px;
}

.social-media{
    margin-bottom: 35px;
}
.apply-to-company-btn{
    background: transparent;
    border: 2px solid;
    border-color: #fff;
    color: #fff;
    font-weight: bold;
}
.apply-to-company-btn:hover{
    background: #fff;
    border-color: #fff;
    color: #10996D;
}
.verified-icon{
    width:28px;
}
@media screen and (max-width:991px){
    .section-mar-bot{
        margin-bottom: 0;
    }
    .section-mar-bot-mob{
        margin-bottom: 35px;
    }
}
@media screen and (max-width:767px){
    .mar-bot-mob-10{
        margin-bottom: 10px;
    }
    .adv-title-padding{
        padding:0;
    }
    .public-preview-user-name h1{
        font-size: 20px !important;
    }
    .public-preview-company-img img{
        max-width: 98px;
    }
    .public-preview-page-content{
        padding : 40px 0 0 0;
    }
    .public-preview-wrapper {  
        padding: 15px;
    }
}
@media screen and (max-width:660px) and (min-width: 491px) {
    .no-padding-left-xs {
        padding-left: 0;
    }
}

/* xxs screens */
@media screen and (max-width:490px){
    .left-col-preview-align-right{
        text-align: left;
    }
    .public-preview-user-name h1 {
        font-size: 18px !important;
        margin-bottom: 5px;
    }
    .verified-icon{
        width:24px;
    }
    .public-preview-company-img img{
        max-width: 76px;
    }
    .apply-to-company-btn{
        font-size: 12px;
        padding: 3px;
    }
}