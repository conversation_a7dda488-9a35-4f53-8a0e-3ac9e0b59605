h3 {
  margin-top: 10px;
}
img {
  height: 35px;
  /* width: 40px; */
  padding-right: 10px;
  float:left;
  margin-top: -5px;
  margin-left: -10px;
}

/* a {
  cursor: pointer;
  text-decoration: none !important;
  display: block;
  font-family: 'Exo2-Regular', sans-serif;
}
.list-group-item {
  display: block;
}

i.fa {
  padding-right: 10px;
}


 */

 .fa-angle-up, .fa-angle-down {
  float: right;
  margin-top: 10px;
  font-size: 1.3em;
  /* transition: all .5s ease-in-out; */
}

/* .fa-angle-up:click, .fa-angle-down:click {
transform: rotateZ(180deg);
} */



.list-group-item.heading {
  color:white !important;
  border-bottom: 2px solid #3d7bce;
  font-size: 1.2em;
  min-height: 45px;
}


.active {
  color:#FCAB42;
  /* color:#ffe5b6 !important; */
}


#page-sidebar  .main-menu .list-group .list-group-item.sub-list {
  /* background-color: #043d69 !important; */
  background: #fff;
  margin-bottom: 0px;
  margin-top: 0px !important;
  padding-left: 45px;
  padding-top: 5px;
  padding-bottom: 5px;
  font-size: 0.9em;

}


#page-sidebar.collapsed .main-menu .list-group .list-group-item.sub-list {
  padding-left: 0px;
}

#page-sidebar  .main-menu .list-group .list-group-item.sub-list  .li-icon {
  font-size: 0.9em;
}


/* #page-sidebar.main-menu {
    margin-top: 0px;
    position: relative;
    overflow-y: scroll;
    overflow-x: hidden;
    scroll-behavior: smooth;

} */

.list-group {
 margin-top: 0px ;
 margin-bottom: 0px ;

}

.list-group-item {
  cursor: pointer;
  position: relative;
  display: block;
  padding: 10px 10px;
  margin-bottom: -1px;

}


nav.nav.position-fixed {
  position: fixed;
}

li {
  font-family: 'Exo2-Regular', sans-serif;
  background-color: #fff;
  border: none;
  /* transition: all 0.5s ease-in-out; */
}


/* ============================================================================================= */


/* ===================================================================================================== */




.navbar-inverse {
  background-color: #fff;
}

.navbar-toggle{
  float: left;
}




/* Start left sidebar */
#page-sidebar.collapsed{
  width: 100px;
}
#page-sidebar {

  overflow-x:hidden !important;
  color:#555;
  height: 100%;
  width: 100%;
  font-family: 'Exo2-Regular', sans-serif;
  width: 270px;
  float: left;
  height:100vh;
  margin-right: -100%;
  z-index: 120;
  position: fixed;
  top: 0;
  left: 0;
  background: #fff;
  overflow:auto;
  transition:width.5s ease;
  -webkit-overflow-y: auto;
  -moz-overflow-y: auto;
  -o-overflow-y: auto;
  overflow-y:auto;

}

#page-sidebar .main-menu{
  margin-top: 0px;
  padding: 0px 0px ;
}

#page-sidebar .main-menu ul {
  list-style-type: none;
  padding: 0;
  margin-bottom: 0;
}
#page-sidebar.collapsed .main-menu ul li{
  text-align:center;
}
#page-sidebar .main-menu ul li {
  margin:5px 0;
  text-align:left;
}
#page-sidebar .main-menu ul li a{
  /* color: #e0e0ffad; */
  color:#3d7bce;
  text-decoration:none;
  font-size: 1.1em;
}
#page-sidebar .main-menu ul li a:hover , #page-sidebar .main-menu ul li a.active{
  color:#FCAB42;
  /* color:#e0e0ff; */
  /* color: #ffe5b6; */
  text-decoration:none;
}
#page-sidebar .main-menu ul li a .li-icon {
  padding: 10px;
  margin-right: 10px;
  width: 35px;
  font-size:1.2rem;
  transition: font-size .5s ease-in-out;
}

#page-sidebar .main-menu ul li a:hover .li-icon{
  animation: zoom-it .6s ease-in-out;
}

#page-sidebar .main-menu ul li a.active .li-icon{
  font-size:1.3em;
  /* padding-left:18px; */
}

@keyframes zoom-it{
  0%{transform: scale(1);}
  50%{transform: scale(1.2);}
  100%{transform: scale(1);}
}
#page-sidebar.collapsed .main-menu ul li a .li-name{
  visibility: hidden ;
  transition-delay: 0s;
  position: absolute;
}
#page-sidebar .main-menu ul li a .li-name {
  visibility: visible;
  transition-delay: .3s;
  position: static;
  border-left: 1px solid;
  padding-left: 10px;
}
.toggle-sidebar {
  position: fixed;
  left: 270px;
  top: 190px;
  font-size: 22px;
  background: #3d7bce;
  color: white;
  z-index: 100;
  width:30px;
  height:40px;
  text-align: center;
  padding-top: 5px;
  transition: left .5s ease;
  border-bottom-right-radius: 5px;
  border-top-right-radius: 5px;
  cursor:pointer;
}
.collapsed .toggle-sidebar {
  left:100px;
}
.toggle-sidebar .fa{
  transform:rotateZ(0deg);
  transition:all .5s ease;
}
.collapsed .toggle-sidebar .fa{
  transform:rotateZ(-180deg);
}
/* End left sidebar */

/* Start Responsive */
@media screen and (max-width:1400px) {
   #page-sidebar .main-menu{
    margin-top:0px;
  }
  .toggle-sidebar {
    top: 167px;
  }

  #page-sidebar.collapsed .main-menu ul li a .li-name{
    visibility:hidden;
    display: flex;
  }

  #page-sidebar.collapsed .main-menu ul li a .li-name .li-icon{
    visibility:hidden;
  }

}
@media screen and (max-width:1024px) {
  #page-sidebar .main-menu {
    margin-top:0px;
  }
  .toggle-sidebar {
    top: 157px;
  }
  #page-sidebar.collapsed .main-menu ul li a .li-name{
    visibility:hidden;
    display: flex;
  }

  #page-sidebar.collapsed .main-menu ul li a .li-name .li-icon{
    visibility:hidden;
  }
}

@media screen and (max-width:824px) {
   #page-sidebar .main-menu{
    margin-top:0px;
  }
  .toggle-sidebar {
    top: 147px;
  }

  #page-sidebar.collapsed .main-menu ul li a .li-name{
    visibility:hidden;
    display: flex;
  }

  #page-sidebar.collapsed .main-menu ul li a .li-name .li-icon{
    visibility:hidden;
  }

}
@media screen and (max-width:768px) {
  #page-sidebar{
    width:100%;
    left:0;
    transition: left .5s ease;
  }
  #page-sidebar.collapsed{
    width:100%;
    left:-100%;
  }
  #page-sidebar.collapsed .toggle-sidebar , #page-sidebar .toggle-sidebar{
    display:none;
  }
  #page-sidebar.collapsed .main-menu ul li a .li-name{
    visibility:visible;
  }
  #page-sidebar.collapsed .main-menu ul li{
    text-align:left;
  }
  #page-sidebar .main-menu ul li a{
    font-size:1em;
  }
   #page-sidebar.collapsed {
    margin-left:0px;
  }

  .alignment-right{
    text-align:left;
  }
}
/* End Responsive */
