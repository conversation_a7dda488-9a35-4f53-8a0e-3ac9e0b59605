.header {
    top: 10px;
    padding: 10px;
    /* margin: 10px; */
    // border-top: 1px solid #f2f2f2;
    // border-bottom: 1px solid #f2f2f2;
}

.enable{
    enabled:true
}

.disable {
    disabled: true
}

.header .language {
    border-right: 1px solid black;
    border-left: 1px solid black;
    margin: 10px;
    padding: 8px;
    color: black;
    cursor: pointer
}

.CForm {
    padding-top:50px;
    padding-bottom: 15px;
}

.user-pro-pic,
.user-pro-pic:hover {
    text-decoration: none;
}

.user-pro-pic img {
    margin: auto;
    width: 180px;
}

.flex-row {
    display: flex;
    flex-wrap: wrap;
}

.flex-order-sm-1,
.flex-order-sm-2 {
    padding: 0;
}

.company_label {
    font-size: 15px;
    color: rgb(74, 183, 229);
}



::ng-deep .ui-fileupload-choose input[type=file] {
    font-size: 6.5px !important
  }

::ng-deep .ui-button.ui-button-text-icon-left .ui-button-text {
    margin: -4px
}

.e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after {
    background: #276ea4;
}

.e-multi-select-wrapper input.e-dropdownbase::-webkit-input-placeholder {
    color: #999;
    font-size: 16px;
    padding-left: 14px;
}

.e-multi-select-wrapper .e-searcher {
    width: 50%;
}

// ::ng-deep .ui-dialog-visible {
//     display: contents;
//     justify-content: center;
//     align-items: center;
// }

// ::ng-deep .ui-dialog-top .ui-dialog, .ui-dialog-bottom .ui-dialog, .ui-dialog-left .ui-dialog, .ui-dialog-right .ui-dialog, .ui-dialog-topleft .ui-dialog, .ui-dialog-topright .ui-dialog, .ui-dialog-bottomleft .ui-dialog, .ui-dialog-bottomright .ui-dialog {
//     margin: -2.25em;
//     position: relative;
// }

.close2 {
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2;
}

// Start logo image styles
.persPhotoContainer{
    max-width:180px !important;
    position:relative;
}
.persPhoto{
    margin: auto;
    margin-bottom: 10px;
    width: 135px;
    // height:160px;
    // border-radius: 50%;
}
.persPhotoLabel , .delete-photo{
    cursor: pointer;
    color: #337ab7;
}
.persPhotoLabel i , .delete-photo i{
    font-size:21px;
    color: #337ab7;
}
.delete-photo{
    margin-left:35px;
}
.edit-photo{
    position:absolute;
    // top:83%;
    margin-top: 11px;
    margin-left:-35px;
}
.imgError-div{
    text-align: left;
    margin-top: 7px;
}
.imgError .error-message{
    color: #a94442;
}
// End logo image styles

//   .fade{
//       width : 700px;
//       margin: auto;
//   }

  #companyedit .modal-header{
      border-bottom: none;
      text-align: center;
  }
  .fixed-label{
      text-align: right;
    color: #4f94df;
    font-size: 16px;
    display: inline-block;
    margin-top: 4px;
  }
  ::ng-deep .direct-apply-info-dialog{
      width:80%;
  }
  .dialog-content-container{
      padding:20px;
      text-align: justify;
  }
  .direct-apply-info-btn .fa-info{
    width: 23px;
    height: 17px;
  }

  //  Start upload image styles
  .upload-image-container{
    max-width: 180px!important;
  }
  .upload-image-container .inner-container a{
      cursor:pointer;
      text-decoration: none;
  }
  .upload-image-container .inner-container img{
    margin: auto auto 10px;
    width: 135px;
  }
  .upload-image-container .upload-actions a i{
    font-size: 21px;
    color: #337ab7;
    cursor: pointer;
  }
  .upload-image-container .upload-actions .edit-image{
    margin-right: 15px;
  }
//   End upload image styles

  /* Start Responsive */

@media screen and (max-width:767px) {
    .CForm {
        padding: 15px 15px 1px 30px;
    }
    .flex-order-sm-1 {
        order: 1;
    }
    .flex-order-sm-2 {
        order: 2;
    }
    .margin-bo-mo-10 {
        margin-bottom: 10px;
    }
    // .fade{
    //     width : 96%;
    // }

    .persPhoto-col{
        margin-bottom: 30px;
    }
    .persPhotoContainer{
        margin: 0 auto;
    }
    .imgError-div{
        text-align:center;
    }
    .fixed-label{
        font-size:14px;
        margin-left:-15px;
    }
    .direct-apply-info-btn .fa-info{
        width: 21px;
        height: 13px;
    }
    ::ng-deep .direct-apply-info-dialog{
        width:95%;
    }
    .dialog-content-container{
        padding:20px 15px;
    }

    .upload-image-container{
        margin:auto;
    }
}


/* End Responsive */