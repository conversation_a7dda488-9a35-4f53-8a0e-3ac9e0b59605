<head>
  <meta charset="UTF-8">
  <title>company form</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"
        integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
</head>


<div class="form-section" [class.active]="Active" [class.inactive]="!Active" (click)="makeActive()">
  <form (keydown.enter)="handleEnterKeyPress($event)"
        [formGroup]="myForm" novalidate>

    <div class="form-group row" style="padding: 44px;">
      <div class="col-sm-8" id="question_global">
        <div class="material-group">
          <input type="text" class="form-control" id="question"
                 placeholder="Question"
                 formControlName="label"
                 (focus)="$event.target.select()"
                 autofocus
          >
          <span class="highlight"></span>
          <span class="bar"></span>
        </div>
      </div>
      <div class="col-sm-4">
        <select class="form-control"
                formControlName="controlType"
                (change)="clearAnswers($event)">
          <option
            *ngFor="let option of list"
            [selected]="option === quest.controlType">
            {{option}}
          </option>

        </select>
      </div>
    </div>
    <div class="form-group row">
      <div class="col-xs-12">

        <div class="form-group row flex-vertical-end"
             *ngIf="controlType=== 'Short Answer' ||controlType==='Paragraph'">
          <div class="col-xs-11">
            <p class="dotted-border">Short answer text</p>
          </div>
        </div>
        <div class="form-group row flex-vertical-end"
             *ngIf="controlType=== 'Date'">
          <div class="col-xs-11">
            <p class="dotted-border">Month Year Day</p>
          </div>
        </div>
        <div class="form-group row flex-vertical-end"
             *ngIf="controlType=== 'Time' ">
          <div class="col-xs-11">
            <p class="dotted-border">Time</p>
          </div>
        </div>

        <div *ngIf="(controlType==='dropDown' ||controlType==='Multiple Choice' || controlType=='Checkboxes')">
          <div *ngFor="let option of answers.controls let i=index" class="form-group row flex-vertical-end"
               formArrayName="answers">
            <div class="col-xs-1 text-right">
              <span *ngIf="controlType =='Checkboxes'">
                <i class="fa fa-check-circle-o check-square-style" aria-hidden="true"></i>
              </span>
              <span *ngIf="controlType =='dropDown'">{{i+1}}. </span>
              <span *ngIf="controlType =='Multiple Choice'">
                <i class="fa fa-check-square-o  check-square-style" aria-hidden="true"></i>
              </span>
            </div>


            <div class="col-xs-10">
              <div class="material-group">
                <input type="text" class="form-control"
                       placeholder="Option"
                       [formControlName]="i"
                       (keyup.enter)="addAnswer()"
                       (myFocus)="isFocused && i>0"
                       (focus)="$event.target.select()"
                >
                <span class="highlight"></span>
                <span class="bar"></span>
              </div>
            </div>
            <div class="col-xs-1">
              <span *ngIf="i>0" (click)="removeAnswer(i)"><i class="fa fa-minus-circle del-fa-icon"
                                                             aria-hidden="true"></i></span>
            </div>
          </div>
        </div>
      </div>
    </div>


    <button class="my-btn add-btn"
            *ngIf="(controlType==='dropDown' ||controlType==='Multiple Choice' || controlType=='Checkboxes')"
            (click)="addAnswer()">
      <span class="icon-span"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span class="word-span">ADD</span>
    </button>
    <div class="form-group row footer-form-group">
      <div class="form-footer">
                           <span style="margin-right:20%;">
                              {{isSaved}}
                             </span>
        <button type="button" class="my-btn delete-btn" (click)="removeQuestion()">
          <span class="icon-span"><i class="fa fa-trash"></i></span>
          <span class="word-span">DELETE</span>
        </button>
        <span class="flex-span">
								<label class="switch-toggle outer">
									<input type="checkbox" formControlName="isRequired"/>
									<div></div>
                </label> Required
        </span>
      </div>
    </div>
  </form>
</div>

