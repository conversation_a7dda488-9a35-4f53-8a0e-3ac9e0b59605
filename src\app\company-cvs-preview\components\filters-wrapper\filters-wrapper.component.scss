div.header {
    position: fixed;
    /* top: 67px; */
    left:0;
    font-size:15px;
    width: 100%;
    list-style: none;
    background: #3D7BCE;
    z-index: 999;
    text-align: center;
  }
.toggle-folders-btn{
    display:none;
    position: absolute;
    left:14px;
}
.name-filter-container{
    width:305px;
}


.filters-form{
    padding: 40px 15px 20px 15px;
}
.filters-links {
    display: flex;
    justify-content: space-around;
}

.filters-links button {
    background: #fff;
    border: 1px solid #3d7bce;
    padding: 0.5rem 0.2rem;
    border-radius: 0.2rem;
    width: 120px;
}

.filters-buttons {
    // margin: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    align-content: center;
    // width: 100%;
}

.filters-buttons .btn {
    margin: 15px;
    width: 80px;
}

.form-horizontal {
    padding: 35px 20px;
}

// start custom p-multiselect styles
:host ::ng-deep .cust-p-multiselect {
    border: 0;
    border-bottom: 1px solid #ccc;
    border-radius: 0;
    width: 100%;
    background: transparent;
    .ui-corner-right {
        border: 0;
    }
    .ui-multiselect-label {
        color: #808080;
    }
}

:host ::ng-deep .cust-p-multiselect .ui-multiselect-items .ui-multiselect-item.ui-state-highlight {
    color: #333;
    background-color: #eee;
}
:host ::ng-deep .cust-p-multiselect .ui-multiselect-items .ui-multiselect-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: none;
}
:host ::ng-deep .cust-p-multiselect:not(.ui-state-disabled):hover {
    border-color: #ccc;
}

:host ::ng-deep .cust-p-multiselect:focus {
    border: 3px solid #4f94df;
}

// end custom p-multiselect styles
:host ::ng-deep .ui-dropdown-panel .ui-dropdown-items .ui-dropdown-item.ui-state-highlight{
    color: #333;
    background-color: #eee;
}
// unify placeholer style for all different controls
::placeholder,
 :host ::ng-deep .ui-dropdown label.ui-dropdown-label,
:host ::ng-deep .ui-autocomplete ::placeholder {
    /* ::placeholder Chrome, Firefox, Opera, Safari 10.1+ */
    color: #808080;
    opacity: 1;
    /* Firefox */
    font-weight: normal;
    font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
    font-size: 15px !important;
}

.tag-label {
    color: #808080;
    background-color: #f2f2f2;
    padding: 3px;
    margin-left: 5px !important;
}

.filters-container{
    position: fixed;
    z-index: 999;
    // top:71px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background: #3D7BCE;
    padding-top: 7px;
    padding-bottom: 7px;
}
.filter-btn{
    display: inline-block;
    font-family: 'Exo2' !important;
    font-size: 1.15em !important;
    color: #fff;
    padding: 0 16px;
    cursor: pointer;
    position: relative;
}

.filter-btn-icon{
    width: 22px;
    margin-right: 4px;
}
.filter-btn .dropdown-menu .dropdown-item{
    margin-bottom: 4px;
    display: inline-block;
    color:#999;
    width:100%;
}
.filter-btn .dropdown-menu .dropdown-item:hover{
    text-decoration: none;
    color:#555;
    background:#f9f9f9;
}
.filter-btn .dropdown-menu{
    min-width:200px;
    padding:5px;
}

// country filter styles
:host ::ng-deep  .country-btn .ui-multiselect-label{
    color:transparent;
    width:95px;
    font-family: "Exo2" !important;
    font-size: 1.15em !important; 
}
:host ::ng-deep  .country-btn .ui-corner-right{
    display:none;
}

:host ::ng-deep .country-btn .cust-p-multiselect {
    border-bottom: 0;
}
:host ::ng-deep .country-btn .ui-multiselect .ui-multiselect-label{
    padding: 0;
}

@media screen and (min-width: 992px){
    .name-filter-container{
        width:500px;
    }
}
@media screen and (max-width: 991px){
    .toggle-folders-btn{
        display: inline-block;
        color: #fff;
        cursor: pointer;
        padding: 6px 8px;
        margin-right: 12px;
        font-size: 17px;
    }
}
@media screen and (max-width: 900px){
    .actions-dropdown-menu{
        right:0;
        left:unset;
    }
}
@media screen and (max-width: 767px) {
  .toggle-folders-btn{
    margin-left: -10px;
    margin-right: 14px
  }
}

@media screen and (max-width: 600px) {
    .filter-btn{
        margin:0  !important;
        padding: 0 8px  !important;
    }
    .name-filter-container{
        margin-left:38px;
    }
}
// @media screen and (max-width: 470px) {
//     .name-filter-container, .search-div{
//        width:245px; 
//     }
// }
// @media screen and (max-width: 400px) {
//     .name-filter-container{
//         margin-left:30px;
//     }
// }

// @media screen and (max-width:900px){
//     .filters-container{
//         top:61px;
//     }
// }
@media screen and (max-width:767px){
    // .filters-container{
    //     top:72px;
    // }
    .filter-btn{
        margin: 0 10px;
    }
    .filter-btn-label{
        display:none;
    }
    :host ::ng-deep  .country-btn .ui-multiselect-label{    
        width:25px; 
    }
}

@media screen and (max-width:400px){
    .filter-btn{
        margin: 0;
    }
}