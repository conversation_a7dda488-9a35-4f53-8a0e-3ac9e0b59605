import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageNewValueComponent } from './manage-new-value.component';

describe('ManageNewValueComponent', () => {
  let component: ManageNewValueComponent;
  let fixture: ComponentFixture<ManageNewValueComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageNewValueComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageNewValueComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
