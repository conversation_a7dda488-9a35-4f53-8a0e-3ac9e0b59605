.slider-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%;  /* responsive 16:9 ratio */
  overflow: hidden;
}

.slide-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top;
  transition: transform 1s ease-in-out;
  border-radius: 20px;
}

/* Slide-out directions */
.slide-left-out { transform: translateX(-100%); }
.slide-right-out { transform: translateX(100%); }
.slide-up-out { transform: translateY(-100%); }
.slide-down-out { transform: translateY(100%); }

/* Slide-in always to center */
.slide-left-in,
.slide-right-in,
.slide-up-in,
.slide-down-in {
  transform: translateX(0) translateY(0);
}
