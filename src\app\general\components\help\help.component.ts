import { Component, OnInit, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from 'app/general/services/help.service';
import { Language } from 'app/admin/models/language';
import { Subject } from 'rxjs/Subject';
import { Title, Meta } from '@angular/platform-browser';
import { LanguageService } from 'app/admin/services/language.service';

@Component({
  selector: 'app-help',
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.css']
})
export class HelpComponent implements OnInit, OnDestroy {

  languagesArray: Language[] = [];
  display: boolean[] = [];
  displayCats: boolean[] = [];
  displayMainCats = true;
  displaySubCats = false;
  displayOneMainCat = false;
  mainCategories: {
    'id': number, 'name': string, 'langId': number,
    'sub_cats': { 'id': number, 'name': string, 'main_cat_id': number, }[],}[][] = [];
  subCategories: { 'id': number, 'name': string, 'main_cat_id': number, 'main_name': '' }[][][] = [];
  filteredSubCategories: { 'id': number, 'name': string, 'main_cat_id': number }[][][] = [];
  helpTopics: { 'id': number, 'title': string, 'slug': string, 'main_cat_id': number, 'sub_cat_id': number }[][] = [];
  private ngUnsubscribe: Subject<any> = new Subject();
  currentLangId: number;
  username;
  selectedSubCat: { 'id': number, 'name': string, 'main_cat_id': number }[] = [];
  selectedMainCat: { 'id': number, 'name': string, 'langId': number }[] = [];
  selectedHelpTopics: { 'id': number, 'title': string, 'slug': string, 'main_cat_id': number, 'sub_cat_id': number }[][] = [];
  filteredHelpTopics: { 'id': number, 'title': string, 'slug': string, 'main_cat_id': number, 'sub_cat_id': number }[][] = [];
  selectedHelpTopic: {
    'id': number, 'main_cat': { 'id': number, 'name': string, 'langId': number }[],
    'sub_cat': { 'id': number, 'name': string, 'main_cat_id': number }[]
  };
  displayhTopic = false;
  helpTopicTrans = [];
  filteredMainCategories: {
    'id': number, 'name': string, 'langId': number,
    'sub_cats': { 'id': number, 'name': string, 'main_cat_id': number }[]
  }[][] = [];
  searchQuery = '';
  mainCat: {
    'id': number, 'name': string, 'langId': number,
    'sub_cats': { 'id': number, 'name': string, 'main_cat_id': number }[]
  }[] = [];
  fSCIsEmpty: boolean;

  constructor(private translate: TranslateService,
    private helpService: HelpService,
    private title: Title,
    private meta:Meta,
    private languageService: LanguageService) {
  //  title.setTitle('Help | CVeek');
    this.title.setTitle('CVeek Website  سيفيك | Help Center');
    this.meta.updateTag({ name: 'description', content: 'Best practice and advice from CVeek team. CVeek help center. CVeek Help - Need help using CVeek? CVeek Help is here to help you get answers to your questions' });
    translate.addLangs(['en', 'ar']);
    translate.setDefaultLang('en');
    const browserLang = translate.getBrowserLang();

    if (localStorage.getItem('defaultLang')) {
      this.currentLangId = +localStorage.getItem('defaultLang');
    } else {
      this.currentLangId = 1;
    }
    this.username = localStorage.getItem('username');
  }

  ngOnInit() {
    this.translate.setDefaultLang('en');
    this.translate.use("en");
    this.getLanguages();
  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {

      let temp = res['data'];
      for (let lang of temp) {
        if (lang.id == 1)
          this.languagesArray.push({
            'id': lang.id,
            'name': lang.name
          });

      }

      this.getHelpData();
    });
  }

  getHelpData() {
    for (let i = 0; i < this.languagesArray.length; i++) {
      this.helpService.getData(i + 1).subscribe(res => {
        let temp = res['categories'];
        this.mainCategories[i] = [];
        this.filteredMainCategories[i] = [];
        this.subCategories[i] = [];
        this.filteredSubCategories[i] = [];
        this.helpTopics[i] = [];
        this.filteredHelpTopics[i] = [];
        for (let j = 0; j < temp.length; j++) {
          let temp2 = temp[j].help_center_sub_cat;
          let main_name = temp[j].help_center_main_cat__trans[0].name;
          this.subCategories[i][j] = [];
          this.filteredSubCategories[i][j] = [];
          // this.helpTopics[i][j]     = [];
          for (let k = 0; k < temp2.length; k++) {
            this.subCategories[i][j].push({
              'id': temp2[k].id,
              'name': temp2[k].help_center_sub_cat__trans[0].name,
              'main_cat_id': temp2[k].help_center_main_cat_id,
              'main_name': main_name
            });
          }

          let temp3 = temp[j].help_center;
          // this.helpTopics[i][j] = [];
          for (let m = 0; m < temp3.length; m++) {
            this.helpTopics[i].push({
              'id': temp3[m].id,
              'main_cat_id': temp3[m].help_center_main_cat_id,
              'sub_cat_id': temp3[m].help_center_sub_cat_id,
              'title': temp3[m].help_center_trans[0].title,
              'slug': this.validSlug(temp3[m].help_center_trans[0].slug),
            });
          }

          this.mainCategories[i].push({
            'id': temp[j].id,
            'name': temp[j].help_center_main_cat__trans[0].name,
            'langId': temp[j].help_center_main_cat__trans[0].translated_languages_id,
            'sub_cats': this.subCategories[i][j],
          });
          this.filteredMainCategories[i].push({
            'id': temp[j].id,
            'name': temp[j].help_center_main_cat__trans[0].name,
            'langId': temp[j].help_center_main_cat__trans[0].translated_languages_id,
            'sub_cats': this.subCategories[i][j]
          });
          this.display.push(false);
          this.displayCats.push(false);
        }
      });
    }
  }

  search(query) {

    this.fSCIsEmpty = false;
    this.searchQuery = query;

    this.filteredMainCategories[this.currentLangId - 1] = (query) ? this.mainCategories[this.currentLangId - 1]
      .filter(mainCat => mainCat.name.toLowerCase().includes(query.toLowerCase())) : this.mainCategories[this.currentLangId - 1];
    this.displaySubCats = false;
    this.displayMainCats = true;

    for (let i = 0; i < this.mainCategories[this.currentLangId - 1].length; i++) {
      this.filteredSubCategories[this.currentLangId - 1][i] = (query) ? this.subCategories[this.currentLangId - 1][i]
        .filter(subCat => subCat.name.toLowerCase().includes(query.toLowerCase())) : this.subCategories[this.currentLangId - 1][i];
    }

    for (let i = 0; i < this.helpTopics[this.currentLangId - 1].length; i++) {
      this.filteredHelpTopics[this.currentLangId - 1] = (query) ? this.helpTopics[this.currentLangId - 1]
        .filter(hTopic => hTopic.title.toLowerCase().includes(query.toLowerCase())) : this.helpTopics[this.currentLangId - 1];
    }

    // calculate sub re length
    let i = 0;
    for (let k of this.filteredSubCategories[this.currentLangId - 1]) {
      if (k.length === 0) {
        i++;
      }
    }
    if (i === this.filteredSubCategories[this.currentLangId - 1].length) {
      this.fSCIsEmpty = true;
    }
    //////

    if (this.displaySubCats) {
      this.displaySubCats = (query) ? false : true;
    }
    if (this.displayhTopic) {
      this.displayhTopic = (query) ? false : true;
    }

    if (this.displayMainCats) {
      this.displayMainCats = (query) ? false : true;
    }
  }

  showMore(i) {
    this.display[i] = true;
  }

  showLess(i) {
    this.display[i] = false;
  }

  displaySubHelp(event) {
    this.selectedSubCat = [];
    this.selectedMainCat = [];
    this.displaySubCats = true;
    this.displayMainCats = false;
    // this.selectedSubCat  = event['sub_cat'];
    for (let temp of event['sub_cat']) {
      this.selectedSubCat.push(temp);
    }
    this.selectedMainCat = event['main_cat'];

    for (let i = 0; i < this.languagesArray.length; i++) {
      this.selectedHelpTopics[i] = [];
      for (let h of this.helpTopics[i]) {
        if (h.main_cat_id === this.selectedMainCat[i].id && h.sub_cat_id === this.selectedSubCat[i].id) {
          this.selectedHelpTopics[i].push(h);
        }
      }
    }

  }

  displayHelpHome() {
    this.displayMainCats = true;
    this.displaySubCats = false;
    this.displayhTopic = false;
    this.displayOneMainCat = false;
  }

  displayHomeFromhTopic() {
    this.displayhTopic = false;
    this.displayMainCats = true;
  }

  changeLang(langId: number) {
    this.translate.use(this.getLangAbbrev(langId));
    this.currentLangId = langId;
    if (this.searchQuery.length !== 0) {
      this.search(this.searchQuery);
    }
  }

  getLangAbbrev(langId: number) {
    return this.languageService.getLangAbbrev(langId);
  }


  displayMainResult(index) {
    for (let i = 0; i < this.languagesArray.length; i++) {
      this.mainCat[i] = this.mainCategories[i][index];
    }

    // console.log('filteredMainCategories', this.filteredMainCategories);
    this.displayMainCats = true;
    this.searchQuery = '';
    this.displayOneMainCat = true;
  }

  displaSubResult(subCat) {
    this.displaySubCats = true;
    this.displayMainCats = false;
    this.searchQuery = '';
    for (let i = 0; i < this.languagesArray.length; i++) {
      for (let mainCat of this.mainCategories[i]) {
        if (mainCat.id === subCat.main_cat_id) {
          this.selectedMainCat[i] = {
            'id': mainCat.id,
            'name': mainCat.name,
            'langId': mainCat.langId
          };

        }
      }
    }

    for (let i = 0; i < this.languagesArray.length; i++) {
      for (let main of this.mainCategories[i]) {
        for (let subCatt of main.sub_cats) {
          if (subCat.id === subCatt.id) {
            this.selectedSubCat[i] = {
              'id': subCat.id,
              'name': subCat.name,
              'main_cat_id': subCat.main_cat_id
            };

          }
        }
      }
    }

    for (let i = 0; i < this.languagesArray.length; i++) {
      this.selectedHelpTopics[i] = [];
      for (let h of this.helpTopics[i]) {
        if (h.main_cat_id === this.selectedMainCat[i].id && h.sub_cat_id === this.selectedSubCat[i].id) {
          this.selectedHelpTopics[i].push(h);
        }
      }
    }
  }

  validSlug(slug: string) {
    let validSlug: string = '';
    for (let i = 0; i < slug.length; i++) {
      if (slug[i] === ' ') {
        validSlug = validSlug.concat('-');
      } else {
        validSlug = validSlug.concat(slug[i]);
      }

    }

    return validSlug;
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
