import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, Subject } from 'rxjs';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
@Injectable({
  providedIn: 'root'
})
////// ## jaber  ****** General Service Used  Between All Components
export class GeneralService {
  ////// ##jaber  *** Internal  Message
  //  It's A Global Message  Used To Notify Sepcific Component  That Something Was Happend
  public internalMessage = new Subject();

  public baseUrl: any = '';


  constructor(private http: HttpClient,
    private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.baseUrl = data;
    });
  }
  ///// send  message  from  component to another
  notify(message, src, dist, mData) {
    const data = {
      'message': message,
      'src': src,
      'dist': dist,
      'mData': mData
    };
    this.internalMessage.next(data);
  //  console.log("inside notify",data);
  }


  getData(action: String, query: String, queryParam: String) {
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.baseUrl + action  + query + queryParam , { headers });
  }
}
