import {Component, <PERSON><PERSON><PERSON>roy, OnInit, Input, ViewChild} from '@angular/core';
import {Question} from '../../../models/question';
import {FaqsService} from '../../../services/faqs.service';
import {Category} from '../../../models/category';
import {TranslateService} from '@ngx-translate/core';
import { Language } from 'app/admin/models/language';
import { Subject } from 'rxjs/Subject';
import { LanguageService } from 'app/admin/services/language.service';
import { Table } from 'primeng/table';
declare var $: any;

@Component({
  selector: 'app-admin-faqs',
  templateUrl: './admin-faqs.component.html',
  styleUrls: ['./admin-faqs.component.css']
})
export class AdminFaqsComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  mode;
  question;
  private ngUnsubscribe: Subject<any> = new Subject();
  private questionsTemp;
  questionsArray = [];
  filteredQuestions = [];
  languagesArray: Language[] = [];
  categories: {'user_categories': { 'value': number, 'label': string}[][], 'employer_categories': { 'value': number, 'label': string}[][], 'all': { 'value': number, 'label': string}[][]}
              = { 'user_categories': [], 'employer_categories': [],  'all': []};

  faqsCount: number;
  questionCount;
  currentLangId;
  displayModal  = false;
  types = [ {'label': '', 'value': null},
            {'label': 'User', 'value': 'User Faq'},
            {'label': 'Company', 'value': 'Company Faq'}
          ];
  statuses = [
    {'label': '', 'value': null},
    {'label': 'active', 'value': 1},
    {'label': 'inactive', 'value': 0}
    ];
  loading = true;
  status;
  type;
  category;

constructor(private translate: TranslateService, private faqsService: FaqsService, private languageService: LanguageService) {
  translate.addLangs(['en', 'ar']);
  translate.setDefaultLang('en');
  const browserLang = translate.getBrowserLang();
  translate.use(browserLang.match(/en|ar/) ? browserLang : 'en');
  if (this.translate.currentLang === 'en') {this.currentLangId = 1;
  } else { this.currentLangId = 2; }
}

ngOnInit() {
  this.getQuestions();
  this.getQuestionFromSidebar();
  this.getLanguages();

}

getQuestions() {
    // getting questions
    this.faqsService.getFaqs().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log('res', res);
      this.questionsTemp = res['feq'];
      for (let q of this.questionsTemp) {
          this.questionsArray.push({
            'id'        : q.id,
            'question'  : (q.faq_translation.length === 0) ? '' : q.faq_translation[0].question,
            'answer'    : (q.faq_translation.length === 0) ? '' : q.faq_translation[0].answer,
            'order'     : q.order,
            'activation': q.active,
            'type'      : q.faq_type,
            'langId'    : (q.faq_translation.length === 0) ? 1 : q.faq_translation[0].translated_languages_id,
            'category'  : q.faq_category.faq_category_translation[0].name,
            'catId'     : q.faq_category_id,
            'display'   : false
          });

      }
      console.log('questionsArray', this.questionsArray);
      this.loading = false;
      // this.filteredQuestions = this.questionsArray;
      this.table.filter(1, 'id', 'startsWith');
      this.table.filter(null, 'id', 'startsWith');

    });

}

getQuestionFromSidebar() {
  // getting new question from sidebar
  this.faqsService.newQuestion.takeUntil(this.ngUnsubscribe)
  .subscribe(q => {
      if ( q.id !== null ) {
        console.log('newquestion from sidebar was added to the table ', q);
        let res = {
          'data': {
            'id'             : q.id,
            'faq_category_id': q.catId,
            'order'          : q.order,
            'active'         : q.activation,
            'faq_type'       : q.type,
            'faq_category'   : { 'faq_category_translation': [ { 'name': q.category }]},
            'faq_translation': [{ 'translated_languages_id': q.langId , 'answer': q.answer, 'question': q.question }]
          }
        };
        this.showNewFaqInTable(res);
      }

  });
}




activateFaq(q) {
  const body = {
    'active': (q.activation) ? 0 : 1
  };
  this.faqsService.activateFaq(q.id, body).subscribe(res => {
     console.log('activate res', res);
     let index = this.getQuestionIndex(q);
     this.questionsArray[index].activation =  (res['data'] !== undefined) ? res['data'].active : false;
    if ((!q.activation) && res['data'] === undefined) {
      alert(res['error']);
    }
    });
}




getLanguages() {
  this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
    console.log(res);
    let temp = res['data'];
    for ( let lang of temp) {
      this.languagesArray.push({
        'id'  : lang.id,
        'name': lang.name
      });
    }

    console.log('languages array', this.languagesArray);
    console.log('lang arr length', this.languagesArray.length);
    this.getCategories();
});
}

getCategories() {
for (let i = 0; i < this.languagesArray.length; i++) {
  console.log('inside cat loop', i);
  this.faqsService.getFaqsCategories(i + 1).takeUntil(this.ngUnsubscribe).subscribe( categoriesRes => {
  console.log('cat res', categoriesRes);
  // filling user_cat array
  let categoriesTemp = categoriesRes['user_categories'];
  this.categories.user_categories[i] = [];
  this.categories.all[i] = [];
  for (let category of categoriesTemp) {
    this.categories.user_categories[i].push({
          'label': category.faq_category_translation[0].name,
          'value':  category.id,
    });
    this.categories.all[i].push({
      'label': category.faq_category_translation[0].name,
      'value': category.faq_category_translation[0].name,
    });
  }
  this.categories.user_categories[i].unshift({ 'label': ' ', 'value': null });
  console.log('user category[' + i + '] ', this.categories.user_categories[i]);

  // filling employer_cat array
  let categoriesTemp2 = categoriesRes['employer_categories'];
  this.categories.employer_categories[i] = [];
  for (let category of categoriesTemp2) {
    this.categories.employer_categories[i].push({
          'label': category.faq_category_translation[0].name,
          'value':  category.id,
    });
    this.categories.all[i].push({
      'label': category.faq_category_translation[0].name,
      'value': category.faq_category_translation[0].name,
    });
  }
this.categories.employer_categories[i].unshift({ 'label': ' ', 'value': null });
this.categories.all[i].unshift({ 'label': ' ', 'value': null });

console.log('employer category[' + i + '] ', this.categories.employer_categories[i]);


  });

}
}


showNewFaqInTable(event) {
  let temp = event['data'];
  console.log('temp', temp);
  for (let i = 0; i < temp.faq_translation.length; i++) {
    if (temp.faq_translation[i].translated_languages_id === 1) {
        let q = {
          'id'        : temp.id,
          'question'  : temp.faq_translation[i].question,
          'answer'    : temp.faq_translation[i].answer,
          'order'     : temp.order,
          'activation': temp.active,
          'type'      : temp.faq_type,
          'category'  : temp.faq_category.faq_category_translation[0].name,
          'langId'    : temp.faq_translation[i].translated_languages_id,
          'catId'     : temp.faq_category_id,
          'display'   : false
        };
        this.questionsArray.splice(0, 0, q);
        console.log('newQuestioninTable', q);
        this.table.filter(null, 'id', 'startsWith');

    }
  }

  this.closeModal();
  $('body').removeClass('modal-open');
  $('body').removeAttr('style');
  $('div.modal-backdrop.fade.in').remove();



}


showUpdatedFaqInTable(event) {
  let index = this.getQuestionIndex(event['old']);
  console.log(index);
  let temp = event['new'];
  for (let i = 0; i < temp.faq_translation.length; i++) {
    if (temp.faq_translation[i].translated_languages_id === 1) {

      let updatedFaq = {
        'id'        : temp.id,
        'question'  : temp.faq_translation[i].question,
        'answer'    : temp.faq_translation[i].answer,
        'order'     : temp.order,
        'activation': temp.active,
        'type'      : temp.faq_type,
        'category'  : temp.faq_category.faq_category_translation[0].name,
        'langId'    : temp.faq_translation[i].translated_languages_id,
        'catId'     : temp.faq_category_id,
        'display'   : false
      };

      console.log('updated En question', this.questionsArray[index], updatedFaq);
      this.questionsArray.splice(index, 1, updatedFaq);
    }

  }

  this.closeModal();
  $('div.modal-backdrop.fade.in').remove();
  $('body').removeClass('modal-open');
  $('body').removeAttr('style');


}


removeQuestionFromTable(event) {
  console.log('delete faq q', event['data']);
  let temp = event['data'];
  if ( temp !== null  || temp !== {}) {
    let index = this.getQuestionIndex(this.question);
    console.log('deleted question', this.question, 'index', index);
      this.questionsArray.splice(index, 1);

    }
  this.closeModal();
  this.table._totalRecords = this.questionsArray.length;
}



displayPreviewModal(q: Question) {
  this.mode = 'preview';
  this.question = {
    'id'        : q.id,
    'question'  : q.question,
    'answer'    : q.answer,
    'order'     : q.order,
    'activation': q.activation,
    'type'      : q.type,
    'category'  : q.category,
    'langId'    : q.langId,
    'catId'     : q.catId
  };
  this.displayModal  = true;
}

closeModal() {
  this.displayModal  = false;
  $('#faqModal').hide();
  $('div.modal-backdrop.fade.in').remove();
}


displayCreateModal() {
  this.mode = 'create';
  this.displayModal  = true;

}


displayEditFormModal(q) {
  this.mode = 'edit';
  this.question = {
    'id'        : q.id,
    'question'  : q.question,
    'answer'    : q.answer,
    'order'     : q.order,
    'activation': q.activation,
    'type'      : q.type,
    'category'  : q.category,
    'catId'     : q.catId,
    'langId'    : q.langId
  };
  console.log('q t up', this.question);
  this.displayModal  = true;

}



displayDeleteAlert(q: Question) {
  this.displayModal  = true;
  this.mode = 'delete';
  console.log('display delete alert q', q);
  this.question = {
    'id'        : q.id,
    'question'  : q.question,
    'answer'    : q.answer,
    'order'     : q.order,
    'activation': q.activation,
    'type'      : q.type,
    'langId'    : q.langId,
    'category'  : q.category,
    'catId'     : q.catId
  };
}




closePreviewOpenEdit() {
  // this.question = {
  //     'id'        : this.question.id,
  //     'question'  : this.question.question,
  //     'answer'    : this.question.answer,
  //     'order'     : this.question.order,
  //     'activation': this.question.activation,
  //     'type'      : this.question.type,
  //     'category'  : this.question.category,
  //     'catId'     : this.question.catId,
  //     'langId'    : this.question.langId
  // };

  this.mode = 'edit';

}


getQuestionIndex(question): number {
  let index: number = null;
  for (let q of this.questionsArray) {
    if (q.id === question.id) {
      index = this.questionsArray.indexOf(q);
    }
  }
  return index;
}



clearAll() {
  this.table.filter(null, 'question', 'contains');
  this.table.filter('', 'type', 'equals');
  this.table.filter('', 'category', 'equals');
  this.table.filter(null, 'order', 'startsWith');
  this.table.filter(null, 'activation', 'equals');
  this.table.filterGlobal(null, 'contains');
  $('.ui-table-globalfilter-container input').val(null);
  console.log($('.ui-column-filter').val());
  $('.ui-column-filter').val(null);
   this.status = '';
   this.type = '';
   this.category = '';
 }

ngOnDestroy(): void {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();
}


}













