h2{
  color: #444444;
}

h3.heading {
  color: #276ea4;
  margin-bottom: 30px;
}

.required{
  color:#dd1111;
  font-weight: bold;
  padding: 0;
  text-align: right;
  margin-left: 0px;
  margin-top: 0px !important;

}
.btn-primary {
  background-color: #4f94df;
}
.btn-danger {
  background-color: #dd1111;
}
.btn-danger:hover {
  background-color: #ee1111;
}
.btn-success {
  padding-left: 50px;
  padding-right: 50px;
  font-size: 1.12em;
  margin-top: 30px!important;
  margin-bottom: 5px;
  float:right;
}
form.ng-touched.ng-invalid.ng-dirty  button.btn.btn-success[type=submit],
form.ng-touched.ng-invalid.ng-pristine  button.btn.btn-success[type=submit]
{
  margin-top: 15px!important;
}

.btn-default:hover {

  background-color: #eeeeee;
}


.form-control {
  width: calc(100% - 50px);

}

.form-control.ng-invalid.ng-touched {
  width: calc(100% - 50px);


}

.form-group {
  margin-top: 10px;
}

/* .form-group.answer-form-group {
  height: 40px;
  width: 500px;
} */

div.type-form-group .col-sm-12, div.type-form-group .col-sm-6, div.type-form-group .col-sm-4 {
  padding-left: 0px;
  padding-right: 0px;
}

div.form-group.type-form-group div.row {
  width: 470px;
}

label {
  padding-top: 15px;
  padding-bottom: 10px;
}

/* label[for=answer] {
  padding-top: 10px;
} */

label[for=order] {
  padding-top: 0px;
}


.form-control:focus
{
  /* border: 1px solid #4f94df; */
  border-bottom: 1px solid #4f94df !important;
}


.modal-body-container {
  height: fit-content;
}


.modal-body{
  height: fit-content;
  max-height: unset !important;
  overflow: unset;
}


label {
    margin-bottom: 0px;
    margin-top: 10px;
    margin-right: 0px;
}

/* label[for=question] {
  padding-top: 23px;
  margin-bottom: 0px;
  margin-top: 0px;
  margin-right: 0px;
} */

label[for=category] {
  margin-bottom: 0px;
  margin-top: 0px;

}


.alert-danger {
  color: #bb1818 !important;
  font-weight: 400 !important;
  background-color: transparent !important;
  border: none !important;
  margin-top: 0px !important;
  margin-bottom: 0px;
  padding-top: 5px;
  padding-bottom: 0px;
  height: 0px;
  opacity: 0.7;

}



.form-control.ng-touched.ng-invalid:focus {
  border:1px solid lightgrey;
  border-bottom: 2px solid rgba(255,0,0,0.5);
}

.form-check-label[for=activation] {
  padding-bottom: 0px;
  margin-top: 0px;
}

.form-check-input[id=activation]{
     margin-top: 0px;
}


/* how to create tabs */
.nav.nav-tabs .active {
  border-bottom: 4px solid white;;
  padding: 6px 12px;
  border-top: 2px solid lightgrey;
  border-left: 2px solid lightgrey;
  border-right: 2px solid lightgrey;
  background-color: white;

}
.nav-tabs {
  border-bottom: 1px solid #ddd;
  margin-top: 10px;
}

.nav.nav-tabs .btn.active, .btn:active {

   -webkit-box-shadow: beige;
   box-shadow:beige;
   border-bottom-left-radius: 0%;
   border-bottom-right-radius: 0%;
}
/* end of tabs */
div.type-form-group {
  margin-top: 5px;
  margin-bottom: 30px;
}

label[for=type] {
  margin-top: 0px;
  padding-top: 2px;
  padding-left: 15px;
}

label[for=user], label[for=company] {
  /* margin-top: 0px; */
  padding-top: 0px;
}

div.form-group.order-form-group {
  width: 230px;
}

/* div.form-group.cat-form-group {
  width: 400px !important;
} */

div.form-check {
  width: 200px;
  padding-top: 20px;
  margin-left: 25px;
}
div.form-check label{
  margin-left: 0px;

}

form {
  font-size: 15px;
}



div.ui-dropdown {
  border: 1px solid #ccc !important;
  border-radius: 2px !important;
}



input {
  border: none;
  border-radius: 0%;
  border-bottom:1px solid #ccc;
}

li.btn {
  color: #888;
}

label {
  color:#4f94df;
}

div.form-group.section-id-input input{
  width: 60%;
  margin-top: 15px;
  background-color: transparent;
}


div.form-group.field-id-input input{
  width: 60%;
  margin-top: 15px;
  background-color: transparent;
}



/* ----------------------------------------- */
#inactive {
  color: darkgrey;
}
#active {
  color: limegreen;
}


.fa.fa-suitcase {
  color: #1a4b70;
}

.fa.fa-user {
  color:#257e3c ;
}



.card span {
  color: darkgrey;
}

.card .form-horizontal .form-group{
  margin-left:0;
  margin-right:0;
}

.card .content {
  width: 480px;
}
.card .content[id=answer] {
  height: 100px;
  line-height: 1.3;
  overflow-y: auto;
}
.card .content[id=question] {
  height: 50px;
  line-height: 1.3;
  overflow-y: auto;
}

.card .btn.btn-default {
  margin-left: 2px;
  margin-right: 2px;
}


.nav.nav-tabs {
  margin-top: 0px;
  width: 100% !important;
}


.card {
  width: 550px;
}


/* .card .col-md-5 {
  padding-right: 0px;
}
.card .col-md-7 {
  padding-left: 0px;
} */

.delete .btn.btn-default{
  float: right;
  /* margin-top: 15px; */
}

:host ::ng-deep .ui-dropdown-trigger  {
  background: transparent !important;
}
