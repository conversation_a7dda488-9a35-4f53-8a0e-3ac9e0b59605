import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-resume-preview',
  templateUrl: './resume-preview.component.html',
  styleUrls: ['./resume-preview.component.css']
})
export class ResumePreviewComponent implements OnInit {

  resume_Id : number;
  constructor(private route: ActivatedRoute) { }

  ngOnInit(): void {
  }

  setRoutingParams(){
    this.route.params.subscribe(res => {
      this.resume_Id = res['resumeId'];
    });
  }

}
