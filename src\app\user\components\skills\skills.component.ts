import 'rxjs/add/operator/takeUntil';

import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { forkJoin } from 'rxjs/observable/forkJoin';
import { Subject } from 'rxjs/Subject';

import { ResumeService } from '../../cv-services/resume.service';
import { SideToFormSignalService } from '../../cv-services/side-to-form-signal.service';
import { SkillsService } from '../../cv-services/skills.service';
import { HelpTipsService } from '../../cv-services/help-tips.service';

import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from "shared/Models/lazyloadDropdown";

declare var $: any;

@Component({
  selector: 'app-skills',
  templateUrl: './skills.component.html',
  styleUrls: ['./skills.component.css']
})
export class SkillsComponent implements OnInit {

  skillForm ;
  skills = [];
  //filteredSkillTypes = [];
  skillOrder = [];
  skillAfterReorder = [];
  skillTypesTrans = [];
 // skillTypesParentTrans = [];
  skillLevel = [];
  private ngUnsubscribe: Subject<any> = new Subject();
  marginLeft = '';
  resumeId: number = null;
  userResumeId:'';
  username='';
  skillToBePassedToModal;
  display: boolean = false;
  skillsDD:any;
  resumeLang:number;

   //options for ngx-sortablejs(library for reorder table rows)
   options:any;
   tableLoader: boolean = true;
  constructor(private  fb: FormBuilder,
              private skillService: SkillsService,
              private route: ActivatedRoute,
              private router: Router,
              private sideToFormShared: SideToFormSignalService,
              private resumeService: ResumeService,
              private helpTipService:HelpTipsService,
              private lazyloadDropdownService:LazyloadDropdownService
  ) {
    this.setRoutingParams();
  }


  ngOnInit() {
    this.sideToFormShared.expandFormCss.takeUntil(this.ngUnsubscribe).subscribe(value => {
      if (value === 'collapsed') {
        this.marginLeft = '100px';
      }
      if (value === 'expand') {
        this.marginLeft = '';
      }
    });

   // init dropdown first time till i get resume language
   // because if it didn't initialized here it will throw error in console
    this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,1);

    this.skillForm = this.fb.group({
      resume_id : [this.resumeId ? this.resumeId : '', Validators.required],
      'skill_types' : ['' , Validators.required],
      'skill_level' : [''],
      // 'skill_type' : this.fb.group({
      //   'name': ['']
      // }),
      'skill_level_id': ['',]
    });

  //  this.setResumeId();
   // this.render();
    this.minimize();
    this.reorderTableRows();
    this.helpTipService.nextHelpTip("");
    this.getSkillData();
  }

  setRoutingParams(){
    this.route.parent.parent.params.subscribe(res => {
      this.username = res['username'];
    });
    this.route.parent.params.subscribe(res => {
      this.userResumeId = res['resumeId'];
    });
  }

  // render(){
  //  // console.log(this.skillForm.controls['description'].value);
  // }

  // selectSkillType() {
  //   this.skillForm.controls['skill_types_id'].setValue(this.skillForm.controls['skill_types_id'].value.id);
  // }

  // setNewSkillType() {
  //   this.skillTypeControl.controls['name'].setValue(this.skillForm.controls['skill_types_id'].value.name);
  //   this.skillForm.controls['skill_types_id'].setValue(-1);
  // }

  submit(form) {
    form.submitted = false;
    if (this.skillForm.valid) {
      // if (this.skillForm.controls['skill_types_id'].value.id !== -1) {
      //   this.selectSkillType();
      // } else {
      //   this.setNewSkillType();
      // }
      let sendData = this.skillForm.value;

        this.skillService.addSkill(sendData).takeUntil(this.ngUnsubscribe).subscribe(
        (result) => {
          for(let i =0; i<this.skills.length; i++){
            this.skills[i].order += 1;
          }
          this.skills.unshift(result['data']);
          this.initSkillOrder(this.skills);
        }
      );

      this.skillForm.reset();
      this.skillForm.controls['resume_id'].setValue(this.resumeId);
    }
  }

  get skillTypeControl() {
    return this.skillForm.controls['skill_type'] as FormGroup;
  }

  getSkillData(){
    this.skills = [];
    this.resumeService.getGlobalResumeId(this.userResumeId).switchMap(res =>{
      this.resumeId  = res['data'].id;
      this.skillForm.controls['resume_id'].setValue(this.resumeId);
      this.resumeLang = res['data'].translated_languages_id;
      this.skillsDD = new LazyloadDropdownClass(this.lazyloadDropdownService,'skills',10,this.resumeLang);
      return forkJoin(
        this.skillService.getSkillsData(this.resumeId), 
        this.skillService.getSkills(this.resumeId)) 
    })
    .takeUntil(this.ngUnsubscribe).subscribe(
      (res) => {
        //skills data response
      //  this.skillTypesTrans = res[0]['skill_types_trans'];
        this.skillLevel = res[0]['skill_level'];
        this.skillLevel.unshift({'skill_level_id': '', 'name': ''});

        //skills response
        this.skills = res[1]['skill'].length !== 0 ? res[1]['skill'] : [] ;
        this.initSkillOrder(this.skills);

        this.tableLoader = false;
      });
  }

  // filterSkills(event) {
  //   this.filteredSkillTypes = [];
  //   for (let i = 0 ; i < this.skillTypesTrans.length; i++) {
  //     let skillType = this.skillTypesTrans[i].skill_type_trans[0];
  //     if (skillType['name'].toLowerCase().indexOf(event.query.toLowerCase()) == 0) {
  //       this.filteredSkillTypes.push(skillType);
  //     }
  //   }
  // }

  initSkillOrder(skills:any){
    this.skillOrder = [];
    for (let skill of skills){
      let x = {'skillId' : skill.id, 'orderId' : skill.order };
      this.skillOrder.push(x);
    }
  }


  removeSkill(skill){

    if(confirm("Are you sure to delete ")) {
      let index  : number = this.skills.indexOf(skill);
      if (index !== -1) {
        this.skillService.deleteSkill(skill.id).takeUntil(this.ngUnsubscribe).subscribe(()=>{

          this.skills.splice(index, 1);
          let order = this.skillAfterReorder.find(x=>{return x.skillId === skill.id;});
          for(let i=0; i<this.skills.length;i++){
            this.skills[i].order = i+1;
          }
          this.initSkillOrder(this.skills);
        });
      }
    }
  }

  selectSkillLevelChange(skillLevel) {
    this.skillForm.controls['skill_level_id'].setValue(skillLevel.value.skill_level_id);
  }

   //reorder table rows on drag and drop using ngx-sortablejs library
   reorderTableRows(){
    this.options = {
      onUpdate: (event: any) => {
        let orderedArray = [];
        for(let i=0; i < this.skills.length;i++){
          this.skills[i].order = i+1;
          orderedArray.push({'skillId':this.skills[i].id,'orderId':this.skills[i].order});
        }
        let orderedData ={"orderData":orderedArray};
        this.skillService.orderData(this.resumeId , orderedData).takeUntil(this.ngUnsubscribe).subscribe(console.log);
      }
    };
  }

  displayModal(skill){
    this.display = !this.display;
    this.skillToBePassedToModal = skill ;
  }

  closeModal() {
    this.display = false;
  }

  handlePopup(event: any) {
    let index = this.skills.indexOf(event['old']);
    this.skills[index] = event['new'];
    this.display = false;
  }

  minimize() {
    // Slide certeficate bottom and top
    $(document).ready(function(){
      $('.minamize-certification').click(function() {
        $( '.add-certification .form-group' ).slideToggle( 'slow' );
        $('.minamize-certification .fa').toggleClass('rotate');
      });

    });
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
