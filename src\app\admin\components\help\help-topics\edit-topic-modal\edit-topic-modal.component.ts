import { HelpTopicService } from './../../../../services/help-topic.service';
import {Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import { Subject } from 'rxjs/Subject';
import { Language } from 'app/admin/models/language';
import { Validators, FormBuilder, FormGroup, FormControl, FormArray } from '@angular/forms';
import { HelpTopic } from 'app/admin/models/help-topic';
import { LanguageService } from 'app/admin/services/language.service';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
declare var $: any;

@Component({
  selector: 'app-edit-topic-modal',
  templateUrl: './edit-topic-modal.component.html',
  styleUrls: ['./edit-topic-modal.component.css'],
})
export class EditTopicModalComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {

  @Output() closeUpdateModal = new EventEmitter();
  @Output() closeCreateModal = new EventEmitter();
  @Output() closeDeleteModal = new EventEmitter();
  @Input('htopicId') htopicId = null;
  @Input('mode')mode = 'create';
  @Input('openedFromSidebar')openedFromSidebar = true;
  @Input('htopicEn') htopicEn: HelpTopic = {id: null , main_cat_id: null, sub_cat_id: null, main_cat: '',
         sub_cat: '', title: '', description: '', order: null, active: false, slug: '', page_title: '', meta_description: '',
         meta_keywords: '', type: '', langId: null };
  @Input('languagesArray')languagesArray: Language[] = [];
  @Input('main_categories')main_categories: {'user_main_categories': { 'value': number, 'label': string}[][],
                  'company_main_categories': { 'value': number, 'label': string}[][]}
                 = { 'user_main_categories': [], 'company_main_categories': []};
  @Input('sub_categories')sub_categories: {'user_sub_categories': { 'value': number, 'label': string, 'main_cat_id': null}[][],
                  'company_sub_categories': { 'value': number, 'label': string, 'main_cat_id': null}[][]}
                 = { 'user_sub_categories': [], 'company_sub_categories': []};
                 hTopicTranslations:  { 'title': string, 'description': string, 'translated_languages_id': number,
                 'slug': string, 'page_title': string, 'meta_description': string, 'meta_keywords': string}[] = [];
  filteredSubCategories: { 'value': number, 'label': string}[][] = [];
  htopicTranslations: { 'translated_language_id': number, 'title': string, 'description': string}[] = [];
  hTopicMainCategories:    { 'label': string, 'value': number, 'translated_languages_id': number}[] = [];
  hTopicSubCategories:    { 'label': string, 'value': number, 'translated_languages_id': number}[] = [];
  private ngUnsubscribe: Subject<any> = new Subject();
  orders: { 'value': number, 'label': string}[] = [];
  currentLangId = 1;
  insertedLanguagesIds: number[] = [];
  helpTopicForm;
  oldTopic;

constructor(private fb: FormBuilder,
            private translate: TranslateService,
            private hTopicService: HelpTopicService,
            private languageService: LanguageService,
            public sanitizer: DomSanitizer,
            private router: Router) {
  translate.addLangs(['en', 'ar']);
  translate.setDefaultLang('en');
  let browserLang = translate.getBrowserLang();
  translate.use('en');



}

ngOnInit() {
  if (this.mode === 'create') {
    this.buildEmptyForm();
    if (this.openedFromSidebar) {
      this.getLanguages();
    }
  } else if (this.mode === 'edit') {
    this.buildFilledForm();
    this.oldTopic = {
      'id'              : this.htopicEn.id,
      'main_cat_id'     : this.htopicEn.main_cat_id,
      'sub_cat_id'      : this.htopicEn.sub_cat_id,
      'main_cat'        : this.htopicEn.main_cat,
      'sub_cat'         : this.htopicEn.sub_cat,
      'type'            : this.htopicEn.type.toLowerCase(),
      'order'           : this.htopicEn.order,
      'active'          : this.htopicEn.active,
      'slug'            : this.htopicEn.slug,
      'title'           : this.htopicEn.title,
      'description'     : this.htopicEn.description,
      'page_title'      : this.htopicEn.page_title,
      'meta_description': this.htopicEn.meta_description,
      'meta_keywords'   : this.htopicEn.meta_keywords,
      'langId'          : this.htopicEn.langId,
    };
  } else if (this.mode === 'preview') {
    this.initializeView();
    this.buildFilledForm();
    this.oldTopic = {
      'id'              : this.htopicEn.id,
      'main_cat_id'     : this.htopicEn.main_cat_id,
      'sub_cat_id'      : this.htopicEn.sub_cat_id,
      'main_cat'        : this.htopicEn.main_cat,
      'sub_cat'         : this.htopicEn.sub_cat,
      'type'            : this.htopicEn.type.toLowerCase(),
      'order'           : this.htopicEn.order,
      'active'          : this.htopicEn.active,
      'slug'            : this.htopicEn.slug,
      'title'           : this.htopicEn.title,
      'description'     : this.htopicEn.description,
      'page_title'      : this.htopicEn.page_title,
      'meta_description': this.htopicEn.meta_description,
      'meta_keywords'   : this.htopicEn.meta_keywords,
      'langId'          : this.htopicEn.langId,
    };
  }

}

buildEmptyForm() {
  // filling order dropdown:
  for (let order = 1; order <= 100; order++) {
    this.orders.push({ 'value': order, 'label': order.toString() });
  }
  this.orders.unshift({ 'value': null, 'label': ''});
  // console.log('orders', this.orders);


  this.helpTopicForm = this.fb.group({
    'help_center_type'             : ['user help', Validators.required],
    'help_center_main_cat_id'      : [null, Validators.required],
    'help_center_sub_cat_id'       : [null],
    'order'                        : [null, Validators.required],
    'active'                       : [false],
    'help_center_trans'            : this.fb.array([])

  });

  if (! this.openedFromSidebar) {
    for (let lang of this.languagesArray) {
      this.fillHelpTopicTrans(lang.id);
    }
  }


}

buildFilledForm() {
  // filling order dropdown:
  for (let order = 1; order <= 100; order++) {
    this.orders.push({ 'value': order, 'label': order.toString() });
  }
  this.orders.unshift({ 'value': null, 'label': ''});
  // console.log('orders', this.orders);

  this.helpTopicForm = this.fb.group({
    'id'              : [ this.htopicEn.id],
    'help_center_type'            : [ this.htopicEn.type, Validators.required],
    'help_center_main_cat_id'     : [this.htopicEn.main_cat_id, Validators.required],
    'help_center_sub_cat_id'      : [this.htopicEn.sub_cat_id],
    'order'                       : [this.htopicEn.order, Validators.required],
    'active'                      : [this.htopicEn.active, Validators.required],
    'help_center_trans'      : this.fb.array([
      new FormGroup({
          'translated_languages_id': new FormControl(1),
          'title'             : new FormControl(this.htopicEn.title, Validators.required),
          'description'       : new FormControl(this.htopicEn.description, Validators.required),
          'slug'              : new FormControl(this.htopicEn.slug, Validators.required),
          'page_title'        : new FormControl(this.htopicEn.page_title),
          'meta_description'  : new FormControl(this.htopicEn.meta_description),
          'meta_keywords'     : new FormControl(this.htopicEn.meta_keywords)
      })
  ])

  });
 
  this.insertedLanguagesIds.push(1);
  this.filter();
  this.getHTopicTranslations();
 //  this.getLanguages();

}

initializeView() {
  this.hTopicTranslations.push({
   'title'           : this.htopicEn.title,
   'description'     : this.htopicEn.description,
   'slug'            : this.htopicEn.slug,
   'page_title'      : this.htopicEn.page_title,
   'meta_description': this.htopicEn.meta_description,
   'meta_keywords'   : this.htopicEn.meta_keywords,
   'translated_languages_id': this.htopicEn.langId
   });
   this.insertedLanguagesIds.push(1);
   this.hTopicMainCategories.push({
     'label': this.htopicEn.main_cat,
     'value': this.htopicEn.main_cat_id,
     'translated_languages_id': this.htopicEn.langId
   });

   this.hTopicSubCategories.push({
     'label': this.htopicEn.sub_cat,
     'value': this.htopicEn.sub_cat_id,
     'translated_languages_id': this.htopicEn.langId
   });

   this.getQuestionTranslations();
}


getLanguages() {
  this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
    
    let temp = res['data'];
    for ( let lang of temp) {
      this.languagesArray.push({
        'id'  : lang.id,
        'name': lang.name
      });
      this.fillHelpTopicTrans(lang.id);
    }
    this.getCategories();
});
}

getCategories() {
for (let i = 0; i < this.languagesArray.length; i++) {
  this.hTopicService.getHTCategories(i + 1).takeUntil(this.ngUnsubscribe).subscribe( categoriesRes => {

  // filling user_main_cat array
  let categoriesTemp = categoriesRes['user_category'];
  this.main_categories.user_main_categories[i] = [];
  this.sub_categories.user_sub_categories[i] = [];

  for (let k = 0; k < categoriesTemp.length; k++) {
    this.main_categories.user_main_categories[i].push({
          'label': categoriesTemp[k].help_center_main_cat__trans[0].name,
          'value':  categoriesTemp[k].id,
    });
    //this.sub_categories.user_sub_categories[i] = [];
    let userSubCategories = categoriesTemp[k].help_center_sub_cat;
   
    for (let m = 0; m < userSubCategories.length; m++) {
      this.sub_categories.user_sub_categories[i].push({
            'label': userSubCategories[m].help_center_sub_cat__trans[0].name,
            'value':  userSubCategories[m].id,
            'main_cat_id': userSubCategories[m].help_center_main_cat_id,
      });
    }
    // this.sub_categories.user_sub_categories[i].unshift({ 'label': ' ', 'value': null, main_cat_id: null });
    
  }
  this.main_categories.user_main_categories[i].unshift({ 'label': ' ', 'value': null });

  // filling company_main_cat array
  let categoriesTemp2 = categoriesRes['company_category'];
  this.main_categories.company_main_categories[i] = [];
  for (let j = 0; j < categoriesTemp2.length; j++) {
    this.main_categories.company_main_categories[i].push({
          'label': categoriesTemp2[j].help_center_main_cat__trans[0].name,
          'value':  categoriesTemp2[j].id,
    });
    this.sub_categories.company_sub_categories[i] = [];
    let comSubCategories = categoriesTemp2[j].help_center_sub_cat;
   
    for (let n = 0; n < comSubCategories.length; n++) {
      this.sub_categories.company_sub_categories[i].push({
            'label': comSubCategories[n].help_center_sub_cat__trans[0].name,
            'value':  comSubCategories[n].id,
            'main_cat_id': comSubCategories[n].help_center_main_cat_id
      });
    }
    // this.sub_categories.company_sub_categories[i].unshift({ 'label': ' ', 'value': null, 'main_cat_id': null });
   
  }

this.main_categories.company_main_categories[i].unshift({ 'label': ' ', 'value': null });

  });

}
 
}

private fillHelpTopicTrans(langId) {
  this.help_center_trans.insert(this.help_center_trans.length, this.createHelpTopicTransControls(langId));
}

private createHelpTopicTransControls(langId: number) {
  if (langId === 1) {
    return new FormGroup({
      'translated_languages_id': new FormControl(langId),
      'title'                  : new FormControl('', Validators.required),
      'description'            : new FormControl('', Validators.required),
      'slug'                   : new FormControl('', Validators.required),
      'page_title'             : new FormControl('', Validators.required),
      'meta_description'       : new FormControl(''),
      'meta_keywords'          : new FormControl(''),
    });
  }
  return new FormGroup({
      'translated_languages_id': new FormControl(langId),
      'title'                  : new FormControl(''),
      'description'            : new FormControl(''),
      'slug'                   : new FormControl(''),
      'page_title'             : new FormControl(''),
      'meta_description'       : new FormControl(''),
      'meta_keywords'          : new FormControl(''),
    });

}


getHTopicTranslations() {
  this.hTopicService.gethelpTopic(this.htopicEn.id).takeUntil(this.ngUnsubscribe).subscribe(res => {
      let temp = res['help_center'];

      for ( let i = 0; i < temp.help_center_trans.length; i++) {
         if (temp.help_center_trans[i].translated_languages_id !== 1) {
            this.help_center_trans.insert(this.help_center_trans.length,
              new FormGroup({
                'translated_languages_id': new FormControl(temp.help_center_trans[i].translated_languages_id),
                'title'             : new FormControl(temp.help_center_trans[i].title),
                'description'       : new FormControl(temp.help_center_trans[i].description),
                'slug'              : new FormControl(temp.help_center_trans[i].slug),
                'page_title'        : new FormControl(temp.help_center_trans[i].page_title ),
                'meta_description'  : new FormControl(temp.help_center_trans[i].meta_description),
                'meta_keywords'     : new FormControl(temp.help_center_trans[i].meta_keywords),
              })
              );
              this.insertedLanguagesIds.push(temp.help_center_trans[i].translated_languages_id);
          }
      }
      
  });

}

getQuestionTranslations() {
  this.hTopicService.gethelpTopic(this.htopicEn.id).takeUntil(this.ngUnsubscribe).subscribe( res => {
  
    let temp = res['help_center'];
  
    for (let i = 0; i < temp.help_center_trans.length; i++) {
      if ( temp.help_center_trans[i].translated_languages_id !== 1 && !this.isAdded( temp.help_center_trans[i].translated_languages_id)) {
        this.hTopicTranslations.push({
         'title'                  : temp.help_center_trans[i].title,
         'description'            : temp.help_center_trans[i].description,
         'slug'                   : temp.help_center_trans[i].slug,
         'page_title'             : temp.help_center_trans[i].page_title,
         'meta_description'       : temp.help_center_trans[i].meta_description,
         'meta_keywords'          : temp.help_center_trans[i].meta_keywords,
         'translated_languages_id': temp.help_center_trans[i].translated_languages_id,
        });
        this.insertedLanguagesIds.push(temp.help_center_trans[i].translated_languages_id);
      
      }
    }

  
    for (let i = 0; i < this.languagesArray.length; i++) {
      if ( temp.help_center_main_cat.help_center_main_cat_trans[i].translated_languages_id !== 1) {
        this.hTopicMainCategories.push({
          'label': temp.help_center_main_cat.help_center_main_cat_trans[i].name,
          'value': temp.help_center_main_cat_id,
          'translated_languages_id': temp.help_center_main_cat.help_center_main_cat_trans[i].translated_languages_id,
        });
       }
    }

    for (let i = 0; i < this.languagesArray.length; i++) {
     if ( temp.help_center_sub_cat.help_center_sub_cat__trans[i].translated_languages_id !== 1) {
       this.hTopicSubCategories.push({
         'label': temp.help_center_sub_cat.help_center_sub_cat__trans[i].name,
         'value': temp.help_center_sub_cat_id,
         'translated_languages_id': temp.help_center_sub_cat.help_center_sub_cat__trans[i].translated_languages_id,
       });
     }
   }

  });
}


isAdded(n: number) {
  for (let i = 0; i < this.insertedLanguagesIds.length; i++) {
    if (n === this.insertedLanguagesIds[i]) {
      return true;
    }
  }
  return false;
}




changeLang( langId: number) {
  this.translate.use(this.getLangAbbrev(langId));
  this.currentLangId = langId;

}


getLangAbbrev(langId: number) {
  return this.languageService.getLangAbbrev(langId);
}

filter() {
  this.filteredSubCategories = [];
  console.log("this.sub_categories.user_sub_categories[this.currentLangId - 1]",this.sub_categories.user_sub_categories[this.currentLangId - 1]);
  if ( this.main_category_id.value !== null  && this.type.value.toLowerCase() === 'user help' &&
  this.sub_categories.user_sub_categories[this.currentLangId - 1].length !== 0) {
    for (let i = 0; i < this.languagesArray.length; i++) {
     this.filteredSubCategories[i] = [];
     for (let j = 0; j < this.sub_categories.user_sub_categories[i].length; j++) {
       if (this.main_category_id.value  ===  this.sub_categories.user_sub_categories[i][j].main_cat_id) {
         this.filteredSubCategories[i].push({
           'label':  this.sub_categories.user_sub_categories[i][j].label,
           'value':  this.sub_categories.user_sub_categories[i][j].value,
         });
       }
    }
    this.filteredSubCategories[i].unshift({ 'label': '', 'value': null});
   }
  }
  else if (this.main_category_id.value !== null  && this.type.value.toLowerCase() === 'company help' &&
   this.sub_categories.company_sub_categories[this.currentLangId - 1].length !== 0) {
     for (let i = 0; i < this.languagesArray.length; i++) {
       this.filteredSubCategories[i] = [];
       for (let j = 0; j < this.sub_categories.company_sub_categories[i].length; j++) {
         if (this.main_category_id.value === this.sub_categories.company_sub_categories[i][j].main_cat_id) {
           this.filteredSubCategories[i].push({
             'label': this.sub_categories.company_sub_categories[i][j].label,
             'value': this.sub_categories.company_sub_categories[i][j].value,
           });
         }
       }
     this.filteredSubCategories[i].unshift({ 'label': '', 'value': null});
   }
 }
 console.log("this.filteredSubCategories ",this.filteredSubCategories );
 return this.filteredSubCategories;

 }



saveQuestion(formValue) {
  if (this.helpTopicForm.valid) {
      let dataToSend = this.helpTopicForm.value;
        if (dataToSend.active) {
          let flag = false;
          for (let i = 0; i < this.languagesArray.length; i++) {
            if ((dataToSend.help_center_trans[i].title === '' ) ||
                 (dataToSend.help_center_trans[i].title === null) ||
                 (dataToSend.help_center_trans[i].page_title === '') ||
                 (dataToSend.help_center_trans[i].page_title === null) ||
                (dataToSend.help_center_trans[i].description === '') ||
                 (dataToSend.help_center_trans[i].description === null) ||
                (dataToSend.help_center_trans[i].slug === '') ||
                 (dataToSend.help_center_trans[i].slug === null) ||
                (dataToSend.help_center_trans[i].meta_keywords === '') ||
                 (dataToSend.help_center_trans[i].meta_keywords === null) ||
                 (dataToSend.help_center_trans[i].meta_description === '') ||
                 (dataToSend.help_center_trans[i].meta_description === null)) {
                      flag = true;
            }
          }
          if (flag) {
            this.active.setValue(0);
            alert('please enter Help translation in all languages before you set activation to true');
            return;
          }
        }
        dataToSend.active = (dataToSend.active === false || dataToSend.active === 0) ?  0 : 1;
        dataToSend.help_center_type = (dataToSend.help_center_type === 'User Help' ||
        dataToSend.help_center_type === 'user help') ?  'User Help' : 'Company Help';
        for (let i = 0; i < dataToSend.help_center_trans.length; i++) {
          if (dataToSend.help_center_trans[i].slug === '') {
            dataToSend.help_center_trans[i].slug = dataToSend.help_center_trans[0].slug;
          }
          if (dataToSend.help_center_trans[i].page_title === '') {
            dataToSend.help_center_trans[i].page_title = dataToSend.help_center_trans[0].page_title;
          }
          }
        if (this.mode === 'edit') {
          this.hTopicService.updateHTopic(dataToSend, this.htopicEn.id).subscribe(res => {
            
            this.closeUpdateModal.emit({'new': res['data'], 'old': this.oldTopic  });
          });
        } else if (this.mode === 'create') {
          this.hTopicService.createHTopic(dataToSend).subscribe(res => {
           
            if (this.openedFromSidebar) {
              // let temp = res['data'];
              // let newhTopic = {
              //   'id'              : temp.id,
              //   'order'           : temp.order,
              //   'main_cat_id'     : temp.help_center_main_cat_id,
              //   'sub_cat_id'      : temp.help_center_sub_cat_id,
              //   'main_cat'        : temp.help_center_main_cat.help_center_main_cat_trans[0].name,
              //   'sub_cat'         : temp.help_center_sub_cat.help_center_sub_cat__trans[0].name,
              //   'type'            : temp.help_center_type,
              //   'active'          : temp.active,
              //   'title'           : temp.help_center_trans[0].title,
              //   'description'     : temp.help_center_trans[0].description,
              //   'slug'            : temp.help_center_trans[0].slug,
              //   'page_title'      : temp.help_center_trans[0].page_title,
              //   'meta_description': temp.help_center_trans[0].meta_description,
              //   'meta_keywords'   : temp.help_center_trans[0].meta_keywords,
              //   'langId'          : temp.help_center_trans[0].translated_languages_id,
              // };
              // this.hTopicService.refreshHTValue(newhTopic);
              this.closeCreateModal.emit();
              this.router.navigateByUrl('/manage/dashboard/help-topics');
            } else {
              this.closeCreateModal.emit({'data': res['data'] });
            }
          });
        }
      } else {
           alert('please enter valid values and fill all required fields');
      }





}

deleteHTopic(id) {

  this.hTopicService.deleteHTopic(id).subscribe(res => {
    
      let temp = res['data'];
    
      this.closeDeleteModal.emit({ 'data': res['data']});
    }
     , (error) => this.closeDeleteModal.emit({ 'error': error})
  );

}


get id() {
  return this.helpTopicForm.get('id');
}

get main_category_id() {
  return this.helpTopicForm.get('help_center_main_cat_id');
}

get sub_category_id() {
  return this.helpTopicForm.get('help_center_sub_cat_id');
}

get order() {
  return this.helpTopicForm.get('order');
}

get active() {
  return this.helpTopicForm.get('active');
}


get type() {
  return this.helpTopicForm.get('help_center_type');
}

get title() {
  return this.helpTopicForm.get('help_center_trans.title') ;
}

get description() {
  return this.helpTopicForm.get('help_center_trans.description') ;
}

get help_center_trans() {
  return this.helpTopicForm.get('help_center_trans') ;
}

get help_center_transArray() {
  return this.helpTopicForm.get('help_center_trans') as FormArray ;
}

get slug() {
  return this.helpTopicForm.get('help_center_trans.slug');
}

get page_title() {
  return this.helpTopicForm.get('help_center_trans.page_title');
}


get meta_keywords() {
  return this.helpTopicForm.get('help_center_trans.meta_keywords');
}

get meta_description() {
  return this.helpTopicForm.get('help_center_trans.meta_description');
}


ngOnDestroy(): void {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();

}


}
