<div class="modal-body-container" >

  <div class="modal-body ">
    <div *ngIf="mode === 'create' || mode === 'edit'">
      <form  *ngIf="languagesArray.length !== 0" [formGroup]="helpTipForm"  (ngSubmit)="saveQuestion(helpTipForm.value)" >
        <h3 class="heading" *ngIf="mode === 'create' && openedFromSidebar" translate>help.labels.AddNewHelpTip </h3>
        <div class="row"  style="margin-top:40px">
          <div class="col-md-8 col-sm-8 col-xs-12">
            <div class="form-group cat-form-group row">
              <div class="col-md-4 text-right">
                <label for="section-name" translate>help.SectionName<span *ngIf="section_id.touched && section_id.invalid" class="required">**</span></label>
              </div>
              <div class="col-md-7">
                <p-dropdown [options]="sections" formControlName="section_id" [required]="true" (onChange)="filter(); field_id.value = null" >
                  <ng-template let-section pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{section.label}}</div>
                      </div>
                  </ng-template>
                </p-dropdown>
                <div *ngIf="section_id.touched && section_id.invalid" >
                  <div *ngIf="section_id.errors.required" class="alert alert-danger"  translate>help.errorMessages.SectionNameRequired</div>
                </div>
              </div>
             </div>
          </div>
          <div class="col-md-4 col-sm-4 col-xs-12" style="margin-left: -50px;margin-top: -5px;">
            <div class="form-group section-id-input row">
              <div class="col-md-7 text-right">
                <label style="margin-top: 14px;" for="section-id" translate>help.SectionID<span *ngIf="section_id.touched && section_id.invalid" class="required">**</span></label>
              </div>
              <div class="col-md-5">
                <input type="number" id="section-id"   [value]="section_id.value"  [disabled]="true" />
              </div>
            </div>
          </div>
        </div>


        <div class="row">
          <div class="col-md-8 col-sm-8 col-xs-12">
            <div class="form-group cat-form-group row"  >
              <div class="col-md-4  text-right">
                <label  for="field-name" translate>help.FieldName<span *ngIf="field_id.touched && field_id.invalid" class="required">**</span></label>
              </div>
              <div class="col-md-7">
                <p-dropdown [options]="filteredFields " formControlName="field_id" [required]="true" [filter]="true" >
                  <ng-template let-field pTemplate="item">
                      <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{field.label}}</div>
                      </div>
                  </ng-template>
                </p-dropdown>
                <div *ngIf="field_id.touched && field_id.invalid" >
                  <div *ngIf="field_id.errors.required" class="alert alert-danger"  translate>help.errorMessages.fieldnNameRequired</div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 col-sm-4 col-xs-12"  style="margin-left: -50px;margin-top: -5px;">
            <div class="form-group field-id-input row">
              <div class="col-md-7 text-right">
                <label style="margin-top: 14px;" for="field-id" translate>help.FieldID<span *ngIf="field_id.touched && field_id.invalid" class="required">**</span></label>
              </div>
              <div class="col-md-5">
                <input type="number" id="field-id"  [value]="field_id.value"  [disabled]="true" />
              </div>
            </div>
          </div>
        </div>




       <div class="row">
         <div class="col-md-12 col-sm-12 form-group">
          <div class="  form-check " title="you shouldn't check this until you enter all help topic translations">
            <input formControlName="active"  name="activation" type="checkbox"
                  class="form-check-input" id="activation" style="margin-right: 10px;"   />
            <label class="form-check-label" for="activation" translate> help.Activation</label>
          </div>
         </div>
       </div>




        <br><br>



        <ul class="nav nav-tabs">
            <li *ngFor="let lang of languagesArray" role="presentation" class="btn" [class.active]="lang.id === currentLangId"
                (click)="changeLang(lang.id)" translate>{{ "help.languages."+lang.name}}</li>
        </ul>




        <div  formArrayName="help_fields_trans" *ngFor="let item of languagesArray; let i= index">
          <div [formGroupName]="i" *ngIf="item.id === currentLangId ">

              <div class="form-group description-form-group row" style="margin-top: 40px;">
               <div class="">
                  <div class="col-md-2  text-right">
                    <label for="description"  translate>help.Description
                      <span *ngIf="help_fields_trans.controls[i].controls.description.touched && help_fields_trans.controls[i].controls.description.invalid" class="required">**</span>
                    </label>
                  </div>
                  <div class="col-md-8">
                    <p-editor formControlName="description" [style]="{'height':'350px'}" id="description"  onTextChange="render()" >
                        <p-header>
                            <span class="ql-formats">
                                <select class="ql-header">
                                    <option value="3">Heading 3</option>     <!-- h3 -->
                                    <option value="4">Heading 4</option>     <!-- h4-->
                                    <option selected>Normal</option>
                                </select>
                                <button class="ql-bold" aria-label="Bold"></button>
                                <button class="ql-italic" aria-label="Italic"></button>
                                <button class="ql-underline" aria-label="Underline"></button>
                                <select title="Text Alignment" class="ql-align">
                                    <option selected>Gauche</option>
                                    <option value="center" label="Center"></option>
                                    <option value="right" label="Right"></option>
                                    <option value="justify" label="Justify"></option>
                                </select>
                                
                                <button aria-label="Ordered List" class="ql-list"
                                    value="ordered" type="button"></button>
                                <button aria-label="Bullet List" class="ql-list" value="bullet"
                                    type="button"></button>
                                <span class="ql-format-separator"></span>
                            </span>
                        </p-header>
                    </p-editor>
                    <div *ngIf="help_fields_trans.controls[i].controls.description.touched && help_fields_trans.controls[i].controls.description.invalid" >
                        <div *ngIf="help_fields_trans.controls[i].controls.description.errors.required" class="alert alert-danger" translate>help.errorMessages.DescriptionRequired</div>
                    </div>
                  </div>
                </div>
              </div>
         </div>
      </div>

      <button *ngIf="languagesArray " type="submit"  class="btn btn-success" [disabled]="helpTipForm.invalid" translate>help.Save</button>

      </form>
      <!-- <p> {{ helpTipForm.value | json }} </p> -->
    </div>

    <div *ngIf="mode === 'preview'">
        <div  *ngIf="hTipTranslations.length !== 0" >
            <div class="card"  *ngIf="hTip  && hTipTranslations.length!==0">
              <div class="card-body" *ngIf="hTip.id !== null">
                <div *ngIf="hTipTranslations.length > 1  ">
                  <ul class="nav nav-tabs">
                    <li *ngFor="let lang of languagesArray" role="presentation" class="btn" [class.active]="lang.id === currentLangId"
                        (click)="changeLang(lang.id)" translate>{{ "help.languages."+lang.name}}</li>
                  </ul>
                </div>
                <br><br>

                  <div class="row">
                    <div class="col-md-3 col-sm-3 col-xs-3 text-right"><p class="card-text"><span translate>help.SectionName</span></p></div>
                    <div class="col-md-7 col-sm-7 col-xs-7"> <p class="card-text">{{ hTip.section }}</p></div>
                  </div>
                  <div class="row">
                    <div class="col-md-3  col-sm-3 col-xs-3  text-right"><p class="card-text"><span translate>help.FieldName</span></p></div>
                    <div class="col-md-9 col-sm-9 col-xs-9"><p id="field" class=" content">{{ hTip.field }}</p></div>
                  </div>

                  <div *ngFor="let lang of languagesArray">
                    <div *ngIf="hTipTranslations.length > 1 && hTipTranslations[lang.id - 1].translated_languages_id === currentLangId" >
                      <div class="row">
                        <div class="col-md-3  col-sm-3 col-xs-3  text-right"> <p class="card-text"><span translate>help.Description</span></p></div>
                        <div class="col-md-9 col-sm-9 col-xs-9"> <p id="description" class=" content user-input" [innerHTML]="sanitizer.bypassSecurityTrustHtml(hTipTranslations[lang.id - 1].description)"></p></div>
                      </div>
                  </div>
                  </div>


                  <div class="row">
                    <div class="col-md-3 col-sm-3 col-xs-3 text-right activation"><span translate>help.Activation  </span></div>
                    <div class="col-md-9 col-sm-9 col-xs-9"><i  id="inactive" *ngIf="!hTip.active" class="fa fa-check-circle"></i>
                      <i  id="active" *ngIf="hTip.active" class="fa fa-check-circle"></i></div>
                  </div>


              </div>
            </div>

        </div>
    </div>

    <div *ngIf="mode === 'delete'">
      <div class="delete" translate>
        help.errorMessages.DeleteConfirm
        <button type="button" class="btn btn-default" data-dismiss="modal"
            (click)="deleteHTip(hTipId)" translate>help.labels.Yes</button>
      </div>
    </div>

  </div>

</div>





