import { Component, OnInit, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CvsFoldersService } from '../../services/cvs-folders.service';
import {TreeNode} from 'primeng/api';
import { GeneralService } from '../../../general/services/general.service';
declare var $: any;

@Component({
  selector: 'app-add-edit-folder-modal',
  templateUrl: './add-edit-folder-modal.component.html',
  styleUrls: ['./add-edit-folder-modal.component.css']
})
export class AddEditFolderModalComponent implements OnInit {

  foldersForm: FormGroup;
  shouldUpdateCvsFolders = false;
  nameBackendError = null;
  folderBackendError = null;
  foldersddData = [];
  submitted:boolean = false;
  modalLoader=false;
  @Input('action') action: string;
  @Input('selectedFolder') selectedFolder: TreeNode;
  @Input('parentFolder') parentFolder: TreeNode;
  @Output() closeModalPopup = new EventEmitter();
  
  constructor(
    private fb: FormBuilder,
    private cvsFoldersService:CvsFoldersService,
    private generalService:GeneralService
    ) { }

  
  buildFilledForm(){
    if( this.action==='Edit' && this.selectedFolder){
      this.foldersForm.controls['name'].setValue(this.selectedFolder.label);
      if(this.parentFolder!==null)
        this.foldersForm.controls['parent'].setValue(this.getFolderWithKey(this.parentFolder.key));
    }
    else if(this.action === 'Add sub-folder' && this.selectedFolder){
      this.foldersForm.controls['parent'].setValue(this.getFolderWithKey(this.selectedFolder.key));
    }
  }

  ngOnInit(): void {
    this.foldersForm = this.fb.group({
      name: ['',Validators.required],
      parent: ['']
    });

    this.buildFilledForm();

    this.generalService.internalMessage.subscribe( (data) => {
      if(data['src'] === 'cvs-folders'){
        //it means modal get notified with folders updates, 
        //but it gets dropdown data again only if user opened the modal
        if (data['message'] === 'cvsFoldersDDReady') {
          this.foldersddData = data['mData'].foldersddData;
          this.buildFilledForm();
        }

        if (data['message'] === 'addEditFolderModalClosed') {
          this.cancel();
        }
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if(this.foldersForm!==undefined){
      this.resetFoldersFormAndMsgs();
      this.buildFilledForm();
    }
  }

  addEditFolder(){
    this.submitted = true;
    this.modalLoader=true;
    this.nameBackendError = null;
    this.folderBackendError = null;
    if(this.foldersForm.valid){
      this.submitted = false;
     // form.submitted=false;
      if(this.action === 'Add' || this.action === 'Add sub-folder'){
        this.cvsFoldersService.addFolder(this.foldersForm.value).subscribe(res => {
          this.modalLoader=false;
          if(res['error']){
            if(res['type']==='error-name')
              this.nameBackendError = res['error'];
            else if(res['type']==='error-folder-list')
              this.folderBackendError = res['error'];
          }
          else{
            if(this.foldersForm.value.parent){
              if(this.foldersForm.value.parent.key!=="")
                this.closeModalPopup.emit({'folder': res['data'],'parent_key':this.foldersForm.value.parent.key});
              else
                this.closeModalPopup.emit({'folder': res['data'],'parent_key':null});
            }
            else this.closeModalPopup.emit({'folder': res['data'],'parent_key':null});
  
            this.resetFoldersFormAndMsgs();
            $('#addEditFolderModal').modal('hide');
          }
          
        });
      }
      else if(this.action === 'Edit'){
        this.cvsFoldersService.editFolder(this.foldersForm.value,this.selectedFolder.key).subscribe(res => {
          this.modalLoader=false;
          if(res['error']){
            if(res['type']==='error-name')
              this.nameBackendError = res['error'];
            else if(res['type']==='error-folder-list')
              this.folderBackendError = res['error'];
          }
          else{
            if(this.foldersForm.value.parent){
              if(this.foldersForm.value.parent.key!=="")
                this.closeModalPopup.emit({'folder': res['data'],'parent_key':this.foldersForm.value.parent.key});
              else
                this.closeModalPopup.emit({'folder': res['data'],'parent_key':null});
            }
            else this.closeModalPopup.emit({'folder': res['data'],'parent_key':null});
  
            this.resetFoldersFormAndMsgs();
            $('#addEditFolderModal').modal('hide');
          }
        });
      }
      
    }
    
  }

  getFolderWithKey(key){
    let item =[] 
    item = this.foldersddData.filter((el) => el['key'] === key);
    if(item.length){
      return  item[0];
    }
    return null;
  
  }

  resetFoldersFormAndMsgs(){
    this.foldersForm.controls['name'].setValue("");
    this.foldersForm.controls['parent'].setValue("");
    this.nameBackendError = null;
    this.folderBackendError = null;
  }

  cancel(){
    this.submitted = false;
    $('#addEditFolderModal').modal('hide');
    this.resetFoldersFormAndMsgs();
    this.foldersForm.markAsPristine();
    this.foldersForm.markAsUntouched();
    this.buildFilledForm();
  }

}
