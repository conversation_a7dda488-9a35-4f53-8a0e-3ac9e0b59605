
.tooltip {
    position: relative;
    display: inline-block;
    opacity: 1;
    z-index: 0;
  }
  
  .tooltip .tooltiptext {
    visibility: hidden;
    width: 80px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    left: -22px;
    bottom: 17px;
  }
  
  .tooltip:hover .tooltiptext {
    visibility: visible;
  }

/* The container <div> - needed to position the dropdown content */
.dropdown {
    position: static;
    display: inline-block;

  }
  
  /* Dropdown Content (Hidden by Default) */
  .dropdown-content {
    display: none;
    position: absolute;
    background-color: #f1f1f1;
    min-width: 160px;
    z-index: 1 !important;
    right: 10px;
  }
  
  /* Links inside the dropdown */
  .dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
  }
  
  /* Change color of dropdown links on hover */
  .dropdown-content a:hover {background-color: #ddd}
  
  /* Show the dropdown menu on hover */
  .dropdown:hover .dropdown-content {
    display: block;
    z-index: 1;
  }
  
  /* Change the background color of the dropdown button when the dropdown content is shown */
  .btn:hover, .dropdown:hover .btn  {
    background-color: #0b7dda;
  }

  ::ng-deep body .ui-datepicker {
   display: block !important;
   top: 20px !important;
    }

  ::ng-deep .ui-widget .ui-widget {
    // width: 110%  commented because it is causing a strange style issue in p-multiselect in search job and recieve cvs components
    // width: 110%;
    font-size: 14px;
    }

  ::ng-deep #calendarUpdatedDates .ui-widget .ui-widget  {
    width: 93%;
    font-size: 14px;
    }
  
    
    

/* media queries */


/*@media only screen and (max-width: 760px) {
    .table-page-content {
        padding-top: 60px !important;
    }
    td:nth-of-type(1):before {
        content: "";
    }
    td:nth-of-type(2):before {
        content: "";
    }
    td:nth-of-type(3):before {
        content: "id";
    }
    td:nth-of-type(4):before {
        content: "Title";
    }
    td:nth-of-type(5):before {
        content: "Days";
    }
    td:nth-of-type(6):before {
        content: "Status";
    }
    td:nth-of-type(7):before {
        content: "Statistics";
    }
    td:nth-of-type(8):before {
        content: "Actions";
    }
}

/*@media screen and (max-width: 990px) {
    #page-content-wrapper {
        margin-left: 0px;
    }
    .options {
        margin-left: 0% !important;
        margin-bottom: 12px;
    }
}*/

/*add
.ui-table-globalfilter-container {
    float: right;}

    .ui-table-globalfilter-container  input {
     width: 200px;
    }
    :host ::ng-deep .ui-table-customers {
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);
    }    
    .ui-column-filter {
        display: block;
    }

    .ui-column-filter input {
            width: 100%;
        }*/
        :host ::ng-deep .ui-table-wrapper{
            background-color: #f4f4f4 !important;
        }
:host ::ng-deep .ui-table-customers {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);

    .customer-badge {
        border-radius: 2px;
        padding: .25em .5em;
        // text-transform: uppercase;
        font-weight: 700;
        // font-size: 12px;
        letter-spacing: .3px;
       
        &.status-qualified {
            background-color: #4CAF50; ;
            color: #FFF;
            padding-left: 12px;
            padding-right: 12px;
        }

        &.status-unqualified {
            background-color:#aaa ;
            color: #FFF;
        }

        &.status-negotiation {
            background-color: #FEEDAF;
            color: #8A5340;
        }

        &.status-new {
            background-color: #B3E5FC;
            color: #23547B;
        }

        &.status-renewal {
            background-color: #ECCFFF;
            color: #694382;
        }

        &.status-proposal {
            background-color: #FFD8B2;
            color: #805B36;
        }
    }

    .flag {
        vertical-align: middle;
        width: 30px;
        height: 20px;
    }

    .ui-multiselect-representative-option {
        display: inline-block;
        vertical-align: middle;

        img {
            vertical-align: middle;
            width: 24px;
        }

        span {
            margin-top: .125em;
            vertical-align: middle;
            margin-left: .5em
        }
    }

    .ui-paginator {
        .ui-dropdown {
            float: left;
        }

        .ui-paginator-current {
            float: right;
        }
    }

    .ui-progressbar {
        height: 8px;
        background-color: #D8DADC;

        .ui-progressbar-value {
            background-color: #00ACAD;
        }
    }

    .ui-column-filter {
        display: block;
        font-weight: 500;
        border-radius: 2px;
        border:none;

        input {
            width: 100%;
            border-radius: 10px;
        }
    }

    .ui-table-globalfilter-container {
        float: right;
        font-weight: 300;
        border-radius: 5px;
        margin:0px;
        padding: 0px;

        input {
            width: 200px;
            border-radius: 2px;
            background-color: white !important;


        }
    }

    .ui-datepicker {
        min-width: 25em;

        td {
            font-weight: 400;
        }
    }

    .ui-table-caption {
        border: 0 none;
        padding: 12px;
        text-align: left;
        font-size: 17px;
    }

    .ui-paginator {
        border: 0 none;
        padding: 1em;
    }

    .ui-table-thead > tr > th {
        border: 0 none;
        text-align: left;
        font-size: 15px;
    

        &.ui-filter-column {
            border-top: 1px solid #c8c8c8;
        }

        &:first-child {
            width: 5em;
            text-align: center;
        }

        &:last-child {
            width: 8em;
            text-align: center;
        }
    }

    .ui-table-tbody > tr > td {
        border: 0 none;
        cursor: pointer;

        &:first-child {
            width: 3em;
            text-align: center;
        }

        &:last-child {
            width: 8em;
            text-align: center;
        }
    }

    .ui-dropdown-label:not(.ui-placeholder) {
        color:#555;
        // text-transform: uppercase;
    }

    .ui-table-tbody > tr > td .ui-column-title {
        display: none;
    }
}
.head{
    margin-top: 30px;
}


:host ::ng-deep .calendar input{
    border: 1px solid #c5c5c5;
}
.no-advs{
    text-align: center;
    color: #969895;
    font-size: 1.4rem;
    margin-top: 45px;
}
.no-advs a:link{
    text-decoration: none;
}
:host ::ng-deep .ui-table table{
    display: table;
}
.dropdown-filter{
    background: #fff;
    padding-top: 0;
    max-height: 34px;
}
:host ::ng-deep .dropdown-filter .ui-dropdown-item:first-child{
    height: 31PX;
}

.disabled-row{
    pointer-events: none;
    background: #ccc !important;
}
.removed-permission span{
    color:red;font-size:19px;
}
/* Responsive */
@media screen and (max-width: 64em) {
    :host ::ng-deep .ui-table {
        &.ui-table-customers {
            .ui-table-thead > tr > th,
            .ui-table-tfoot > tr > td {
                display: none !important;
            }

            .ui-table-tbody > tr > td {
                text-align: left;
                display: block;
                border: 0 none !important;
                width: 100% !important;
                float: left;
                clear: left;
                border: 0 none;

                .ui-column-title {
                    padding: .4em;
                    min-width: 30%;
                    display: inline-block;
                    margin: -.4em 1em -.4em -.4em;
                    font-weight: bold;
                }
               
            }
            .last .tooltip{
                float: right;
                margin-left: 7px;
            }
            .last svg{
                float: right;
                margin-left: 7px;

            }
            .last .dropdown{
                float: right;
                margin-left: 7px;


            }
            
            .card{
                display: none;
            }
        }
    }
}
@media screen and (max-width: 767px){
    .no-advs{
        font-size: 1rem;
    }
}


