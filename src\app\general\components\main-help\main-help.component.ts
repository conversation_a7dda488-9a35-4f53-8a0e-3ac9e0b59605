import { Activated<PERSON>oute, Router } from '@angular/router';
import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from 'app/general/services/help.service';
import { Title, Meta } from '@angular/platform-browser';
import { Subject } from 'rxjs/Subject';
import { LanguageService } from 'app/admin/services/language.service';

@Component({
  selector: 'app-main-help',
  templateUrl: './main-help.component.html',
  styleUrls: ['./main-help.component.css']
})
export class MainHelpComponent implements OnInit, OnDestroy {
  @Input('languagesArray')languagesArray = [];
  @Input('mainCategories')mainCategories;
  @Input('subCategories')subCategories;
  @Input('displayCats')displayCats;
  displayMainCats = false;
  display2 = false;
  @Input('display')display;
  @Input('currentLangId')currentLangId = 1;
  @Input('openedFromHelpTopic')openedFromHelpTopic = true;
  @Input('displayOneMainCat')displayOneMainCat = true;
  @Input('mainCat')mainCat: {'id': number, 'name': string, 'langId': number,
  'sub_cats': {'id': number, 'name': string, 'main_cat_id': number}[] }[] = [];

  @Output('subCatClicked')subCatClicked = new EventEmitter();
  @Output('displayHomeClicked')displayHomeClicked =  new EventEmitter();
  id = null;
  private ngUnsubscribe: Subject<any> = new Subject();
  username: string;
  constructor(private translate: TranslateService,
              private helpService: HelpService,
               private title: Title,
               private meta:Meta,
               private router: Router,
               private route: ActivatedRoute,
               private languageService: LanguageService) {
    translate.addLangs(['en', 'ar']);
    translate.setDefaultLang('en');
    const browserLang = translate.getBrowserLang();
    this.username = localStorage.getItem('username');
   }

  ngOnInit() {
    if (!this.openedFromHelpTopic) {

    } else {
       this.getId();
    }

   // this.displayOneMainCat = true;
  }

  displaySubCatHelp(subCatId, mainCatId, type) {
    let subCatTrans = [], mainCatTrans = [];
    for (let i = 0; i < this.languagesArray.length; i++) {
      for (let main  of  this.mainCategories[i]) {
        for (let subCat of main.sub_cats ) {
          if (subCatId === subCat.id ) {
            subCatTrans[i] = {
              'id'        : subCat.id,
              'name'      : subCat.name,
              'main_cat_id': subCat.main_cat_id
            };

          }
        }
      }
     }
    if (type === 1) {
       for (let i = 0; i < this.languagesArray.length; i++) {
        for (let main  of  this.mainCategories[i]) {
            if (mainCatId === main.id) {
              mainCatTrans[i] = {
                'id'        : main.id,
                'name'      : main.name,
                'langId'    : main.langId,
                'sub_cats'  : main.sub_cats
              };

            }

        }
       }

    
      this.subCatClicked.emit({ 'sub_cat': subCatTrans, 'main_cat': mainCatTrans});

    } else {
     
      this.subCatClicked.emit({ 'sub_cat': subCatTrans, 'main_cat': mainCatId});

    }
    }



  showMore(i) {
    this.display[i] = true;
  }

  showLess(i) {
    this.display[i] = false;
  }

  displayHome() {
    this.displayHomeClicked.emit();
  }


  getId() {
    this.route.paramMap.subscribe(params => {
     
      this.id = +params.get('mainCatId');
      this.getLanguages();

    });
  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
  
      let temp = res['data'];
      for ( let lang of temp) {
        this.languagesArray.push({
          'id'  : lang.id,
          'name': lang.name
        });

      }

      
      this.getMainCat();
    });

  }

  getMainCat() {
    this.helpService.getMainCat(this.id).takeUntil(this.ngUnsubscribe).subscribe(res => {
     
      let temp = res['categories'];
      for (let i = 0; i < this.languagesArray.length; i++) {
        let subCats = [];
        if (temp.help_center_sub_cat.length !== 0) {
          for (let sub of temp.help_center_sub_cat) {
            subCats.push({
              'id': sub.id,
              'name': sub.help_center_sub_cat_trans[i].name,
              'main_cat_id': sub.help_center_main_cat_id
            });
          }
        }

        this.mainCat.push({
          'id': temp.id,
          'name': temp.help_center_main_cat_trans[i].name,
          'sub_cats': subCats,
          'langId' : i + 1
        });

        if(this.displayOneMainCat){
          this.title.setTitle('CVeek Website  سيفيك | Help Center | '+this.mainCat[this.currentLangId -1].name);
          this.meta.updateTag({ name: 'description', content: 'CVeek | Help Center | '+ this.mainCat[this.currentLangId -1].name });
        }
        else{
          this.title.setTitle('CVeek Website  سيفيك | Help Center');
          this.meta.updateTag({ name: 'description', content: 'Best practice and advice from CVeek team. CVeek help center. CVeek Help - Need help using CVeek? CVeek Help is here to help you get answers to your questions' });
        }
      }
      
      this.displayOneMainCat = true;

    });
  }

  changeLang( langId: number) {
    this.translate.use(this.languageService.getLangAbbrev(langId));
    this.currentLangId = langId;
    this.title.setTitle('CVeek Website  سيفيك | Help Center | '+this.mainCat[this.currentLangId -1].name);
    this.meta.updateTag({ name: 'description', content: 'CVeek | Help Center | '+ this.mainCat[this.currentLangId -1].name });
  }

  navigate(mainCatName,mainCatId,subCatName,subCatId){
     this.router.navigate(['/i/help' , this.friendlyUrl(mainCatName) , mainCatId , 's' ,  this.friendlyUrl(subCatName) , subCatId]);
  }

  friendlyUrl(text){
    text = text.toLowerCase();
    text = text.replace(/ /g,'-');
    return text;
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
