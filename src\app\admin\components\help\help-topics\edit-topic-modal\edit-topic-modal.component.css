h2{
  color: #444444;
}

h3.heading {
  color: #276ea4;
  margin-bottom: 30px;
}

.badge, .badge.badge-primary {
  cursor: pointer;
  margin-right: 5px;
  transition: all 0.5s ease-in-out;
}

.required{
  color:#dd1111;
  font-weight: bold;
  padding: 0;
  text-align: right;
  margin-left: 0px;
  margin-top: 0px !important;

}

.btn.btn-default[name=next] {
 margin-top:200px;
}

.btn.btn-default[name=previous] {
  margin-top:100px;
 }



.btn-primary, .badge-primary {
  background-color: #4f94df;
}
.btn-danger {
  background-color: #dd1111;
}
.btn-danger:hover {
  background-color: #ee1111;
}
.btn-success{
  padding-left: 50px;
  padding-right: 50px;
  font-size: 1.12em;
  margin-left: 260px;
  margin-top: 50px;
  margin-right: 30px;
  margin-bottom: 20px;
  float:right;
}


.btn-default:hover {

  background-color: #eeeeee;
}


/* .form-control {
  width: calc(100% - 50px);

}

.form-control.ng-invalid.ng-touched {
  width: calc(100% - 50px);


} */

.form-group {
  margin-top: 10px;
}

.form-group.answer-form-group {
  height: 190px;
  width: 99%px;
}

div.type-form-group .col-sm-12, div.type-form-group .col-sm-6, div.type-form-group .col-sm-4 {
  padding-left: 0px;
  padding-right: 0px;
}

div.form-group.type-form-group div.row {
  padding-top: 30px;
}

label {
  padding-top: 15px;
  padding-bottom: 10px;
}

label[for=answer] {
  padding-top: 10px;
}

label[for=order] {
  padding-top: 12px;
}

label[for=slug] {
  padding-top:10px;
}

label[for=title] {
  padding-top: 10px;
}

textarea {
  height: 91px;
  resize:none;
}

.form-control:focus
{
  /* border: 1px solid #4f94df; */
  border-bottom: 1px solid #4f94df !important;
}


.modal-body-container {
  height: fit-content;
}


.modal-body{
  height: fit-content;
  max-height: unset !important;
  overflow: unset;
}


label {
    margin-bottom: 0px;
    margin-top: 0px;
    margin-right: 0px;
}

label[for=question] {
  padding-top: 23px;
  margin-bottom: 0px;
  margin-top: 0px;
  margin-right: 0px;
}

label[for=category] {
  margin-bottom: 0px;
  margin-top: 0px;

}


.alert-danger {
  color: #bb1818 !important;
  font-weight: 400 !important;
  background-color: transparent !important;
  border: none !important;
  margin-top: 0px !important;
  margin-bottom: 0px;
  padding-top: 5px;
  padding-bottom: 0px;
  height: 0px;
  opacity: 0.7;

}

/* .form-control.ng-touched.ng-invalid {
  border-bottom: 2px solid rgba(255,0,0,0.5);
} */

.form-control.ng-touched.ng-invalid:focus {
  border:1px solid lightgrey;
  border-bottom: 2px solid rgba(255,0,0,0.5);
}

.form-check-label[for=activation] {
  padding-bottom: 0px;
  margin-top: 0px;
}

.form-check-input[id=activation]{
     margin-top: 0px;
}


/* how to create tabs */
.nav.nav-tabs .active {
  border-bottom: 4px solid white;;
  padding: 6px 12px;
  border-top: 2px solid lightgrey;
  border-left: 2px solid lightgrey;
  border-right: 2px solid lightgrey;
  background-color: white;

}
.nav-tabs {
  border-bottom: 1px solid #ddd;
  margin-top: -30px;
}

.nav.nav-tabs .btn.active, .btn:active {

   -webkit-box-shadow: beige;
   box-shadow:beige;
   border-bottom-left-radius: 0%;
   border-bottom-right-radius: 0%;
}

.nav.nav-tabs.seo-tabs {
  margin-top: 30px;
}

/* end of tabs */
div.type-form-group {
  margin-top: 5px;
  margin-bottom: 30px;
}

label[for=type] {
  margin-top: 0px;
  padding-top: 2px;
  padding-left: 15px;
}

label[for=user], label[for=company] {
  /* margin-top: 0px; */
  padding-top: 0px;
}

div.form-group.order-form-group {
  /* width: 230px; */
}

div.form-group.cat-form-group {
  width: 90%px !important;
}

div.form-check {
  width: 200px;
  padding-top: 10px;
  margin-left: 0px;
  margin-top: -36px;
}
div.form-check label{
  margin-left: 0px;

}

form {
  font-size: 15px;
}



div.ui-dropdown {
  border: 1px solid #ccc !important;
  border-radius: 2px !important;
}



input {
  border: none;
  border-radius: 0px;
  border-bottom:1px solid #ccc;
}

textarea {
  border-radius: 5px;
  border: 1px solid #ccc;
}

li.btn {
  color: #888;
}

label {
  color:#4f94df;
}

.btn.btn-default{
  float: left;
  margin-top: 60px;
}


.form-group.row {
  margin-right: 25px;
  margin-left: -5px;
}

.form-group.row .col-md-3 {
  padding-right: 0px;
    padding-left: 10px;
}








/* ---------------------------------------------------------------------------------------------------------------- */
.card #inactive {
  color: darkgrey;
}
.card #active {
  color: limegreen;
}


.fa.fa-suitcase {
  color: #1a4b70;
}

.fa.fa-user {
  color:#257e3c ;
}



.card span {
  color: darkgrey;
}
.card .error-message {
  color: #a94442;
  position: absolute;
  left: 0;
  top: 10px;
}
.card .form-horizontal .form-group{
  margin-left:0;
  margin-right:0;
}
.card .preview-content {

  border-radius: 3px;
}


.card .content {
  width: 480px;
}
.card .content[id=answer] {
  height: fit-content;
  line-height: 1.3;
  overflow-y: auto;
}
.card .content[id=question] {
  height: fit-content;
  line-height: 1.3;
  overflow-y: auto;
}

.card .btn.btn-default {
  margin-left: 2px;
  margin-right: 2px;
}



.card .nav.nav-tabs {
  border-bottom: 1px solid #ddd;
  margin-top: 0px;
  width: 550px;
}



.card {
  width: 550px;
}



.card .col-md-3 {
  padding-right: 10px;
}
.card .col-md-9 {
  padding-left: 8px;
}


.delete .btn.btn-default{
  float: right;
  /* margin-top: 15px; */
}


:host ::ng-deep .order-form-group .ui-dropdown.ui-widget.ui-state-default.ui-corner-all {
  margin-left:  -5px;
}

:host ::ng-deep .ui-dropdown-trigger  {
  background: transparent !important;
}
