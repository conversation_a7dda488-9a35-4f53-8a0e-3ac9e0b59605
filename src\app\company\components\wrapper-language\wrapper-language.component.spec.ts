import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { WrapperLanguageComponent } from './wrapper-language.component';

describe('WrapperLanguageComponent', () => {
  let component: WrapperLanguageComponent;
  let fixture: ComponentFixture<WrapperLanguageComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ WrapperLanguageComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WrapperLanguageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
