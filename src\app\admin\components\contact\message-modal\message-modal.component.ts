import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { <PERSON>idators, FormBuilder, FormArray, FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ContactService } from 'app/admin/services/contact.service';
import { Subject } from 'rxjs/Subject';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { DataMap } from 'shared/Models/data_map';
// import { Clipboard } from '@angular/cdk/clipboard';

@Component({
  selector: 'app-message-modal',
  templateUrl: './message-modal.component.html',
  styleUrls: ['./message-modal.component.css']
})
export class MessageModalComponent implements OnInit, OnDestroy {
  @Input('msg')msg;
  // @Input()template = {'id': null, 'title': '', 'email_title': '', 'main_cat_id': null, 'main_cat': null, 'sub_cat_id' : null,
  //                     'sub_cat': null, 'short_reply': '', 'detailed_reply': '' };
  @Input()template = {'id': null, 'title': '', 'main_cat_id': null, 'main_cat': null, 'sub_cat_id' : null,
  'sub_cat': null, 'detailed_reply': '' };
  @Input('mode')mode;
  @Input('templateMode')templateMode;
  @Input('main_cats')main_cats;
  @Input('sub_cats')sub_cats;
  @Input('admins')admins;
  @Input('msgLog')msgLog = [];
  @Input('templates')templates;
  @Input('template_titles')template_titles;
  @Output('msgAssigned')msgAssigned = new EventEmitter();
  @Output('commentAdded')commentAdded = new EventEmitter();
  @Output('sendMessageStatus')sendMessageStatus = new EventEmitter();
  @Output()updateTemplatesTable = new EventEmitter();
  @Output()newReplyAdded = new EventEmitter();
  filteredSubCats: { 'label': string , 'value': number, 'main_cat_id': number}[] = [];
  emails = [];
  msgForm;
  doneBtnPressed = false;
  private ngUnsubscribe: Subject<any> = new Subject();
  opperationNum: number;
  commentForm: any;
  assignForm: any;
  displayForm = false;
  displayMore = false;
  notification = '';
  viewNotific = 0;
  collapsed = true;
  baseUrl='';
  currentAttachment = null;
  currentAdminAttachment = null;
  data_map = new DataMap();
  imgError:string = null;
  constructor(private fb: FormBuilder, private contact: ContactService, public sanitizer: DomSanitizer,
    private privateSharedURL: ExportUrlService,
  //  private clipboard: Clipboard
    ) { }

  ngOnInit() {
    this.buildEmptyForm();

    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
       this.baseUrl = data;
    });
    console.log("msg",this.msg);
  }

  buildEmptyForm() {
    console.log('mode', this.mode);
    if (this.mode === 'reply_form') {
        this.msgForm = this.fb.group({
          'received_email_id'      : [ this.msg.id, Validators.required], 
          // next field represents template title
         // 'replied_email_title'    : [ ''],
          // 'replied_email_title'    : [ '', Validators.required],
        //  'short_reply'            : [ '', Validators.required],
          'detailed_reply'         : [ '', Validators.required],
          'cc'                     : [[]],
          'cc_temp'                : [[], Validators.email],
          'translated_languages_id': [ this.msg.langId, Validators.required],
          'template_title'         : [ ''],
          'attach_images': this.fb.array([this.createImage()]),
        });
        console.log('reply form', this.msgForm);
        this.collapsed = true;
    } else if (this.mode === 'template_form') {
      if (this.templateMode === 'create') {
        this.msgForm = this.fb.group({
          'template_title'     : [ '', Validators.required],
        //  'email_title'        : [ '', Validators.required],
          'contact_main_cat_id': [ null, Validators.required],
          'contact_sub_cat_id' : [ null],
        //  'short_reply'        : [ '', Validators.required],
          'detailed_reply'     : [ '', Validators.required],
        });
      } else if (this.templateMode === 'update') {
        this.msgForm = this.fb.group({
          'template_title'     : [ this.template.title, Validators.required],
        //  'email_title'        : [ this.template.email_title, Validators.required],
          'contact_main_cat_id': [ this.template.main_cat_id, Validators.required],
          'contact_sub_cat_id' : [ this.template.sub_cat_id],
        //  'short_reply'        : [ this.template.short_reply, Validators.required],
          'detailed_reply'     : [ this.template.detailed_reply, Validators.required],
        });
        this.filter();
      }
        console.log('template mode', this.templateMode);
        console.log('template form', this.msgForm);
    } else if (this.mode === 'preview_comments' || this.mode === 'preview_replys') {
      this.collapsed = false;
    }

  }

  addEmailToCc(email: string) {
    if (email.length !== 0) {
      this.emails.push(email);
      this.cc.setValue(this.emails);
      this.cc_temp.setValue('');
    }
  }

  removeEmail(email) {
    for (let i = 0; i < this.emails.length; i++) {
      if (this.emails[i] === email) {
        this.emails.splice(i, 1);
      }
    }
    this.cc.setValue(this.emails);
  }


  addNewTemplate(templateForm) {
    console.log('is form valid?', this.msgForm.valid);
    console.log(this.msgForm.value);
    if (this.msgForm.valid) {
        let dataToSend = this.msgForm.value;
        console.log('data to send', dataToSend);
        if (this.templateMode === 'create') {
          this.msgForm.reset();
          this.contact.addTemplate(dataToSend).subscribe(res => {
              console.log('add template res', res);
              if (res['data'] === 'template_title already found') {
                alert ('template title already exist , please choose a unique title');
              } else {
              // send reply to table and update status
              this.notification = 'new template was added successfully!';
              this.viewNotific = 3;
              setTimeout(() => {
                 this.viewNotific = 0;
                 this.updateTemplatesTable.emit({ 'id': null , 'data': res['data'] });
               }, 6000);
              }
          });
        } else if (this.templateMode === 'update') {
          this.contact.editTemplate(this.template.id, dataToSend).subscribe(res => {
              console.log('update template res', res);
              this.notification = 'template was updated successfully!';
              this.viewNotific = 3;
              setTimeout(() => {
                this.viewNotific = 0;
                // send reply to table and update status
                this.updateTemplatesTable.emit({ 'id': this.template.id, 'data': res['data'] });
              }, 6000);
          });
        }
    }
  }

  send(replyForm) {
    console.log('is form valid?', this.msgForm.valid);
    console.log(this.msgForm.value);
    if (this.msgForm.valid) {
        if(this.cc_temp.value.length !== 0){
          this.cc.setValue([this.cc_temp.value]);
        }

        let dataToSend = this.msgForm.value;
        this.msgForm.reset();
        if(dataToSend.attach_images[0]=="")
          dataToSend.attach_images.pop();
        
        this.contact.addReply(dataToSend).subscribe(res => {
            console.log('add reply res', res);
            // send reply to table and update status
            this.msg.status = 'replied';
            this.sendMessageStatus.emit({ 'status': 'replied', 'msgId': this.msg.id});
            this.newReplyAdded.emit({ 'msg': res});

        });
    } else {
          alert('invalid entries');
    }

  }

  saveAsTemplate(msgForm) {
    console.log('is form valid?', this.msgForm.valid);
    console.log(this.msgForm.value);
    let title = '';
    if (this.msgForm.valid) {
        let dataToSend = this.msgForm.value;
        if (dataToSend.template_title === '' || dataToSend.template_title === null) {
          alert('if you want to save reply as template please enter a new title for the template & it should be unique');
        } else {
          for (let temp of this.templates) {
            if (temp.id === dataToSend.template_title) {
              title = temp.value;
              console.log('found template', temp);
              alert('if you want to save reply as template please enter a new title for the template & it should be unique');
              return;
            }
          }
          if (title.length === 0) {
            title = dataToSend.template_title;
          }
        }
        let data = {
          'template_title'     : title,
        //  'email_title'        : dataToSend.replied_email_title,
          'contact_main_cat_id': this.msg.main_cat_id,
          'contact_sub_cat_id' : this.msg.sub_cat_id,
        //  'short_reply'        : dataToSend.short_reply,
          'detailed_reply'     : dataToSend.detailed_reply,
        };
        console.log('data');
        this.contact.addTemplate(data).subscribe(res => {
            console.log('save as res', res);
            this.notification = 'template was saved successfully!';
            this.viewNotific = 2;
            setTimeout(() => { this.viewNotific = 0; }, 6000);
        });
    } else {
          alert('invalid entries');
    }

  }

  fillFormWithTemp() {
    let tempId = this.template_title.value;
    console.log('tempId', tempId);
    for (let temp of this.templates) {
      if (temp.id === tempId) {
        console.log('found template', temp);

      //  this.replied_email_title.setValue(temp.email_title);
        this.detailed_reply.setValue(temp.detailed_reply);
      //  this.short_reply.setValue(temp.short_reply);
     //   console.log('email title', this.replied_email_title);
        console.log('detailed_reply', this.detailed_reply);
      //  console.log('short_reply', this.short_reply);
        return;
      }

    }
  }

  filter() {
    this.filteredSubCats = [];
      for (let i = 0; i < this.sub_cats.length; i++) {
        if (this.sub_cats[i].main_cat_id === this.contact_main_cat_id.value) {
          this.filteredSubCats.push({
            'value': this.sub_cats[i].value,
            'label': this.sub_cats[i].label,
            'main_cat_id': this.sub_cats[i].main_cat_id
          });
        }
      }
      this.filteredSubCats.unshift({ 'label': '' , 'value': null, 'main_cat_id': null});


    console.log('filtered sub cats', this.filteredSubCats);

  }

  buildEmptyFormCA() {
    if (this.opperationNum === 1) {
      this.commentForm = this.fb.group({
        'received_email_id': [ this.msg.id, Validators.required],
        'comment'      : ['', Validators.required]
      });
      console.log('form', this.commentForm);
    } else if (this.opperationNum === 2) {
      this.assignForm = this.fb.group({
          'received_email_id': [this.msg.id, Validators.required],
          'to_assigned_admin_user_id': [null, Validators.required]
      });
      console.log('form', this.assignForm);
    }
  }


  openAssignForm() {
    this.opperationNum = 2;
    this.displayForm = true;
    this.buildEmptyFormCA();
  //  this.msg.status = 'assigned';
  }

  openCommentForm() {
    this.opperationNum = 1;
    this.displayForm = true;
    this.buildEmptyFormCA();
    // this.msg.status = 'commented';
  }

  closeFormCA() {
    this.displayForm = false;
  }

  addNewComment() {
    let dataToSend = this.commentForm.value;
    if (this.commentForm.valid) {
      this.contact.addComment(dataToSend).subscribe((res: { 'admin_comment': any[] }) => {
        console.log('res', res);
        this.closeFormCA();
        // this.msg.status = 'commented';
        // this.msg.comments = res.admin_comment;
        // console.log('msg admin comments', this.msg);
        this.commentAdded.emit({ 'data': res });
        this.notification = 'new Comment added successfully!';
        this.viewNotific = 1;
        setTimeout(() => { this.viewNotific = 0; }, 6000);
      });
    }
  }

  assignTo() {
    let dataToSend = this.assignForm.value;
    if (this.assignForm.valid) {
      this.admin_user_id.setValue(null);
      this.admin_user_id.touched = false;
      // this.assignForm.errors = null;
      // this.admin_user_id.errors = null;
      this.contact.assignTo(dataToSend).subscribe((res: { 'admin_email_assign_log': any[]}) => {
        console.log('res', res);
        this.closeFormCA();
        // this.msg.status = 'assigned';
        // this.msg.assign_log = res.admin_email_assign_log;
        this.msgAssigned.emit({ 'data': res });
        this.notification = 'message assigned  successfully!';
        this.viewNotific = 1;
        setTimeout(() => { this.viewNotific = 0; }, 6000);
      });
    }
  }

  setAsDone() {
    this.contact.setAsDone(this.msg.id).subscribe(res => {
      console.log('res', res);
      // this.msg.status = 'done';
      this.doneBtnPressed = true;
      this.sendMessageStatus.emit({ 'status': 'done', 'msgId': this.msg.id });
    });
  }

  setAsNotDone() {
    this.contact.setAsNotDone(this.msg.id).subscribe(res => {
      console.log('res', res);
      // this.msg.status = 'done';
      this.doneBtnPressed = false;
      this.sendMessageStatus.emit({ 'status': 'not-done', 'msgId': this.msg.id });
    });
  }


  displayBiggerImageAttachment(image,replyType){
    if(replyType==='user-reply')
      this.currentAttachment = this.baseUrl+image.url+image.name;
     else if(replyType==='admin-reply')
      this.currentAdminAttachment = this.baseUrl+image.url+image.name;
  }


 // upload images functions
  onFileChanged(event: any) {
    let files = event.target.files;
    let result;
    let uploadedFiles =[];
    let image_code_to_send:{file:string, file_type:string,is_deleted:boolean} = {file:'',file_type:'',is_deleted:false};

    if(files[0].size > 1048576 || (files[0].type !== 'image/jpeg' && files[0].type !== 'image/png') ){
      if(files[0].size > 1048576){
        this.imgError = "validationMessages.imageSizeBig";
      }
      else if(files[0].type !== 'image/jpeg' && files[0].type !== 'image/png'){
        this.imgError = "validationMessages.invalidImageType";
      }
    }

   else{
    this.imgError = null;  
   
    const reader = new FileReader()
    for(let file of files) {
      uploadedFiles.push(file) ;
    }
    let file = uploadedFiles[0];
    this.data_map.upload_file(file).then(
      (res)=>{
        result = res;
        image_code_to_send = result;
        // this.uploadLabelDisplay = false;
        uploadedFiles = [];
        this.attachImages['controls'][0].setValue(image_code_to_send);
      });

    reader.onload = () => {
     // this.perPhotoSrc = reader.result as string;
    }
    reader.readAsDataURL(file)
   }
  }
  
  private createImage(image?) {
    if(image)
      return new FormControl(image);

    return new FormControl("");
  }

  addImage() {
    if(this.attachImages['controls'][0].value !==""){
      this.attachImages.insert(0, this.createImage(""));
    }
  }

  removeImage(i) {
    this.attachImages.removeAt(i);
  }

  copyToClipboard(){
    const text = document.querySelector('#messageId').innerHTML;
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    window.navigator['clipboard'].writeText(textArea.value);
    document.body.removeChild(textArea);
  }

  // onCopy(value) {
  //   this.clipboard.copy(value);
  // }

  get attachImages() {
    return this.msgForm.get('attach_images') as FormArray;
  }

  // get replied_email_title() {
  //   return this.msgForm.get('replied_email_title');
  // }

  get cc() {
    return this.msgForm.get('cc') as FormArray;
  }

  get cc_temp() {
    return this.msgForm.get('cc_temp');
  }

  get template_title() {
      return this.msgForm.get('template_title');
  }


  // get email_title() {
  //     return this.msgForm.get('email_title');
  // }

  get contact_main_cat_id() {
      return this.msgForm.get('contact_main_cat_id');
  }

  get contact_sub_cat_id() {
      return this.msgForm.get('contact_sub_cat_id');
  }

  // get short_reply() {
  //     return this.msgForm.get('short_reply');
  // }


  get detailed_reply() {
      return this.msgForm.get('detailed_reply');

  }

  get comment() {
    return this.commentForm.get('comment');
  }

  get admin_user_id() {
    return this.assignForm.get('to_assigned_admin_user_id');
  }


  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
