import { CvSearchComponent } from "app/company/components/cv-search/cv-search.component";

// Declare  <PERSON>V's Data Table Component Inititial Data
export class  DataModel {
    public folder: number = null;
    public rows: number;
    public page_number: number;
    public totalPages: number;
    public totalFilteredCvs: number;
    public lang: number;
    public filters: any;
    public fitlerFormData: any;
    public columns: any;
    ////////////////// These For Cols Manager
    public standardColumns: any = [];
    public dataColumnsWithoutStandard: any = [];
    public columnsWithoutStandard: any = [];
//////////////////////////// End
    public resumes: Cv[] ;
    public firstLoad: boolean = true;
    public showLoader: boolean;
    public currentCv: any;
    public isMobileLayout: boolean;
    public showCVeek: boolean;
    
    //cv_source,
    private dataColumns = [
        {  title: 'Photo', field: 'photo', style: '' , selected : true  , sort : 1 , standard : true , showInList : true},
        {  title: 'Name', field: 'name', style: '' , selected : true , sort : 2 , standard : true, showInList : true},
        {  title: 'Job Adv Titles', field: 'job_adv_titles' , style: '' , selected : false  , sort : 3 , standard : false, showInList : true},
        {  title: 'Job Type', field: 'job_types' , style: '' ,  selected : false  , sort : 4 , standard : false, showInList : true},
        {  title: 'Years of Experience', field: 'total_year_of_exp', style: '' , selected : false  , sort : 5 , standard : false, showInList : true},
      
        {  title: 'Country', field: 'country' , style: '' ,  selected : false , sort : 7, standard : false, showInList : true},
        {  title: 'City', field: 'city', style: '' , selected : false  , sort : 8, standard : false, showInList : true},
        {  title: 'Age', field: 'age' , style: 'age' ,  selected : false , sort : 9, standard : false, showInList : true},
        
        {  title: 'Current Location', field: 'current_location' , style: '' ,  selected : false , sort : 11, standard : false , showInList : true},
        {  title: 'Languages', field: 'languages' , style: '' ,  selected : false , sort : 12, standard : false, showInList : true},
        {  title: 'Nationality', field: 'nationality' , style: '',  selected : false , sort : 13, standard : false, showInList : true},
        {  title: 'Skills', field: 'skill' , style: '' , selected : false , sort : 14, standard : false, showInList : true},
        {  title: 'Gender', field: 'gender' , style: 'gender' ,  selected : false , sort : 15, standard : false , showInList : true},
        {  title: 'Certification', field: 'certification' , style: '' ,  selected : false , sort : 16, standard : false , showInList : true},
        {  title: 'Degree Level', field: 'degree_level' , style: ''  ,  selected : false , sort : 17, standard : false , showInList : true},
        {  title: 'Education', field: 'educations' , style: ''  ,  selected : false , sort : 18, standard : false , showInList : true},
    
        {  title: 'Open to Work', field: 'open_to_work' , style: '' ,  selected : false , sort : 20, standard : false , showInList : true},
        // {  title: 'CV Source', field: 'cv_source' , style: '' ,  selected : false , sort : 21, standard : false , showInList : true},
    ];
    keywordSearch : string = '';

    constructor() {
      this.resumes = [] ;
        // this.folder = 1 ;
        this.folder;
        this.rows = 50;
        this.lang  = 1;
        this.page_number = 1;
        this.totalPages = 0 ;
        this.totalFilteredCvs = 0;
        this.filters = {
             'folder' : this.folder
        };
        this.fitlerFormData = 1 ;
        this.columns = [];
        this.currentCv = <Cv>{};
        this.isMobileLayout = false;
        this.showLoader  = false;
        this.showCVeek = true;
    }
  //// setColumns function get colsArr which came from Backend, and  formate them
  //    as  title, attr objects
    setColumns(colsArr) {
      ///////////////  --------------------
        //// Name  column it's  a basic column and  always  exist
        this.columns = this.dataColumns.filter((el) => el.standard) ;
        const  len = colsArr.length ;
         for (let  i = 0 ; i < len ; i++ ) {
            this.dataColumns.forEach(ele => {
                 // -----Remember that data columns  are  all  possible  columns
                 /// ----And columns  are  columns which user selected
                 //// ---- So we  push to  columns  array object  which are in colsArr and it is not  name column
                 if (ele['field']  === colsArr[i] &&  ele['field']  !== 'name'  ) {
                    this.columns.push(ele) ;
                 }
            });
         }
         this.columns.sort((a, b) => (a.sort > b.sort) ? 1 : -1) ;

         ////////////////////////////// Remove This   when duplication of
         ///  columns from backend solved
          this.columns = this.columns.reduce((acc, current) => {
            const x = acc.find(item => item.field === current.field);
            if (!x) {
              return acc.concat([current]);
            } else {
              return acc;
            }
          }, []);
          ////////////////////
          this.dataColumns.forEach((el) => {
             if (this.folder >  6 && el['field'] === 'job_title') {
                 el['showInList'] = false;
             }
          });
          this.dataColumnsWithoutStandard = this.dataColumns.filter((el) =>  !el.standard  );
          this.columnsWithoutStandard = this.columns.filter((el) =>  !el.standard  );
          ///////////
    }
    /////


}

/// Encapsulate CV Data  Model
export interface Cv {
age: number;
application_id: number;
date: any;
employment_type: any;
favourite: any;
folder: any;
is_deleted: number;
job_adv_id: number;
job_title: any;
languages: any;
name: String;
photo: String;
read: number;
resume_id: number;
username:  number;
interview_date: any;
}





