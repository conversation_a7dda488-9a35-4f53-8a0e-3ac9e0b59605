<h3 class="verf-heading"> Browse Articles </h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ articles.length }}</span>
   Selected: <span class="badge badge-primary badge-pill">{{ filteredArticles.length }}</span></p>




<p-table #dt [value]="articles" [(selection)]="filteredArticles" dataKey="id" styleClass="ui-table-articles" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['id','name','type', 'language', 'date', 'main_cat', 'sub_cat', 'status']">
    <ng-template pTemplate="caption">
        Articles
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th></th>
            <th pSortableColumn="id"  style="width:70px;">ID <p-sortIcon field="id"></p-sortIcon></th>
            <th pSortableColumn="main_cat"  style="min-width:110px;">Main Cat <p-sortIcon field="main_cat" ></p-sortIcon></th>
            <th pSortableColumn="sub_cat"   style="min-width:110px;">Sub Cat  <p-sortIcon field="sub_cat"  ></p-sortIcon></th>
            <th pSortableColumn="title"     style="min-width:100px;">Title    <p-sortIcon field="title"    ></p-sortIcon></th>
            <th pSortableColumn="author"    style="min-width:100px;">Author   <p-sortIcon field="author"   ></p-sortIcon></th>
            <th pSortableColumn="type" style="width:70px;">Type <p-sortIcon field="type" ></p-sortIcon></th>
            <th pSortableColumn="language" style="width:100px;">Language <p-sortIcon field="language"></p-sortIcon></th>
            <th pSortableColumn="date" style="min-width:100px;">Date <p-sortIcon field="date"  ></p-sortIcon></th>
            <th pSortableColumn="active" style="width:100px;">Activation <p-sortIcon field="active"></p-sortIcon></th>
            <th pSortableColumn="show_in_home">Show in Home <p-sortIcon field="show_in_home"></p-sortIcon></th>
            <th pSortableColumn="status" style="width:100px;">Status <p-sortIcon field="status" ></p-sortIcon></th>
            <th pSortableColumn="handled_by">Handled By <p-sortIcon field="handled_by"></p-sortIcon></th>
            <th  style="min-width:80px;">Actions</th>
        </tr>
        <tr>
            <th>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected articles" (click)="displayDeleteAlert(4)"></i>
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'id', 'startsWith')" placeholder="" class="ui-column-filter" style="width: 40px;">
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'main_cat', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
               <input pInputText type="text" (input)="dt.filter($event.target.value, 'sub_cat', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'title', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'author', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
                <p-dropdown [options]="types" (onChange)="dt.filter($event.value, 'type', 'equals')" styleClass="ui-column-filter" placeholder="" [showClear]="true">
                  <ng-template let-option pTemplate="item">
                      <span [class]="">{{option.label}}</span>
                  </ng-template>
                </p-dropdown>
                <!-- <select #type (change)="dt.filter(type.value, 'type', 'equals')">
                  <option value=""></option>
                  <option value="ROLE_VISITOR">?</option>
                  <option value="ROLE_ADMIN">Admin</option>
                  <option value="ROLE_JOB_SEEKER">Job Seeker</option>
                  <option value="ROLE_EMPLOYER">Employer</option>
                </select> -->
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'language', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <p-calendar [(ngModel)]="rangeDates" (onSelect)="onDateSelect($event)" (onClearClick)="dt.filter('', 'date', 'contains')" [showButtonBar]="true" yearRange="2020:2030" selectionMode="range" styleClass="ui-column-filter" placeholder="" [readonlyInput]="true" dateFormat="yy-mm-dd"></p-calendar>
            </th>
            <th>
              <select #active (change)="dt.filter(active.value, 'show_in_home', 'equals')">
                <option value=""></option>
                <option value="false">Not active</option>
                <option value="true">Active</option>
              </select>
            </th>
            <th>
              <select #showHome (change)="dt.filter(showHome.value, 'show_in_home', 'equals')">
                <option value=""></option>
                <option value="false">No</option>
                <option value="true">Yes</option>
              </select>
            </th>
            <th>
                <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'status', 'equals')" styleClass="ui-column-filter"  [showClear]="false">
                    <ng-template let-option pTemplate="item">
                        <span [class]="'article-badge status-' + option.value">{{option.label}}</span>
                    </ng-template>
                </p-dropdown>
                <!-- <select #status (change)="dt.filter(status.value, 'status', 'equals')">
                  <option value=""></option>
                  <option value="new">new</option>
                  <option value="opened">opened</option>
                  <option value="commented">commented</option>
                  <option value="done">done</option>
                  <option value="replied">replied</option>
                  <option value="assigned">assigned</option>
                </select> -->
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'handled_by', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-article>
        <tr class="ui-selectable-row" [class.new-msg]="article.status === 'new'" >
            <td>

                <p-tableCheckbox [value]="article" (click)="addToSelected(article)"></p-tableCheckbox>
            </td>
            <td>
              {{article.id}}
              <i class="fa fa-info-circle" title="view article log"  data-toggle="modal" data-target="#msgModal"  (click)="displayMsgLog(article)"></i>
            </td>
            <td>
              {{ article.main_cat | summary:15}}
           </td>
           <td>
              {{ article.sub_cat | summary:15}}
           </td>
            <td>
                {{article.author | summary:15 }}
            </td>
            <td>
              {{article.title | summary:15 }}
            </td>
            <td>
              <span *ngIf="article.type === 'ROLE_JOB_SEEKER'"><i class="fa fa-user-circle" title="job seeker"></i></span>
              <span *ngIf="article.type === 'ROLE_EMPLOYER'"><i class="fa fa-suitcase" title="Employer"></i></span>
              <span *ngIf="article.type === 'ROLE_VISITOR'"><i class="fa fa-question" title="Visitor"></i></span>
              <span *ngIf="article.type === 'ROLE_ADMIN'"><i class="fa fa-user" title="Admin"></i></span>
            </td>
            <td>
               {{article.language}}
            </td>
            <td>
               {{article.date}}
            </td>
            <td>
               {{ article.active }}
            </td>
            <td>
               {{ article.show_in_home}}
            </td>
            <td>
                <span [class]="'article-badge status-' + article.status">{{article.status}}  </span>
            </td>
            <td>
              {{article.handled_by | summary:10 }}
           </td>
            <td>
                <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete this article" (click)="displayDeleteAlert(3, article.id)"></i>
                <i class="fa fa-edit"  data-toggle="modal" data-target="#msgModal"  (click)="displayModal(article)"></i>
                <i class="fa fa-power-off"    (click)="activateArticle(article)"></i>
              </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptyarticle">
        <tr>
            <td colspan="11" style="text-align:center">No articles found.</td>
        </tr>
    </ng-template>
</p-table>






<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayMsgModal" id="msgModal"  tabindex="-1" role="dialog" aria-labelledby="msgModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
         <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
         <h3>View Article <span *ngIf="mode === 'log_mode'"> Log</span> </h3>
      </div>
      <div class="modal-body">
         <app-message-modal [mode]="mode" [msg]="msgToPreview" [msgLog]="msgLog" [templates]="templates"
         [template_titles]="templateTitles" (openAssignFormClicked)="displayAssignForm($event)"
          (openCommentFormClicked)="displayCommentForm($event)" (newReplyAdded)="addReplyResult($event)" (sendMessageStatus)="updateMsgStatus($event)" >
         </app-message-modal>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<!-- end of  modal-->


<!-- add  modal-->
<div class="modal fade" *ngIf="displayAddModal" id="addValueModal"  tabindex="-2" role="dialog" aria-labelledby="addModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeAddModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="opperationNum === 1">Add Comment </h3>
        <h3 *ngIf="opperationNum === 2">Assign message to another admin </h3>
        <h3 *ngIf="opperationNum === 3 || opperationNum === 4" style="color:crimson;">Delete </h3>
      </div>
      <div class="modal-body">
        <!-- comment form -->
         <form  [formGroup]="commentForm" *ngIf="opperationNum === 1">

                 <div class="form-group">
                     <label for="comment"  >comment
                       <span *ngIf="comment.touched && comment.invalid" class="required">**</span>
                     </label>
                     <input formControlName="comment" name="comment" type="text" class="form-control"
                         id="comment" >
                     <div *ngIf="comment.touched && comment.invalid" >
                       <div *ngIf="comment.errors.required" class="alert alert-danger" >Name is Required</div>
                     </div>
                 </div>
                 <button type="submit" class="btn btn-success" [disabled]="commentForm.invalid" (click)="addNewComment()" >Add </button>

            <p>{{commentForm.value | json }}</p>
         </form>

        <!-- assign to form -->
        <form  [formGroup]="assignForm" *ngIf="opperationNum === 2">
               <div class="form-group">
                 <label for="name"  >Assign To
                   <span *ngIf="admin_user_id.touched && admin_user_id.invalid" class="required">**</span>
                 </label>
                  <p-dropdown [options]="admins" formControlName="to_assigned_admin_user_id"  [required]="true" [filter]="true" >
                    <ng-template let-admin pTemplate="item">
                        <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                            <div style="font-size:14px;float:left;margin-top:4px">{{admin.label}}</div>
                        </div>
                    </ng-template>
                  </p-dropdown>
                 <div *ngIf="admin_user_id.touched && admin_user_id.invalid" >
                   <div *ngIf="admin_user_id.errors.required" class="alert alert-danger" >Name is Required</div>
                 </div>
               </div>
               <button type="submit" class="btn btn-success" [disabled]="assignForm.invalid" (click)="assignTo()"  >Add </button>
               <p>{{ assignForm.value | json }}</p>
        </form>
       <!-- delete alert -->
        <div *ngIf="opperationNum === 3">
           <p >Are you sure you want to delete this message? </p>
           <button type="button" class="btn btn-light" (click)="deleteMsg()">Delete</button>
        </div>

        <div *ngIf="opperationNum === 4">
          <p>Are you sure you want to delete these {{ filteredArticles.length }} message? </p>
          <button type="button" class="btn btn-light" (click)="deleteMultiMsgs()">Delete</button>
       </div>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->




