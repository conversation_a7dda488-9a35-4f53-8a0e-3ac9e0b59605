<h3 class="verf-heading"> Verification Log</h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ transactionsArray.length }}</span></p>
   <!-- <p>filtered:<span class="badge badge-primary badge-pill">{{ filteredTransactions.length }}</span> </p> -->

<p-table #dt [value]="transactionsArray" [(selection)]="filteredTransactions" dataKey="id" styleClass="ui-table-questions" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="transactionsArray.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['id', 'trans_id', 'date', 'time','type', 'handled_by', 'admin_time', 'duration']">
    <ng-template pTemplate="caption">
         <i class="fa fa-remove" title="clear All" (click)="clearAll()"></i>
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="cv_id" style="width:100px;">CV ID <p-sortIcon field="cv_id" ></p-sortIcon></th>
          <th pSortableColumn="adv_id" style="width:100px;">Adv ID <p-sortIcon field="adv_id" ></p-sortIcon></th>
            <th pSortableColumn="trans_id" style="width:100px;">Transaction ID <p-sortIcon field="trans_id" ></p-sortIcon></th>
            <th pSortableColumn="name" style="width:200px;text-align: center;">name <p-sortIcon field="neme" ></p-sortIcon></th>
            <th pSortableColumn="date" >Date <p-sortIcon field="date" ></p-sortIcon></th>
            <th pSortableColumn="time">Time <p-sortIcon field="time" ></p-sortIcon></th>
            <th pSortableColumn="type" style="width:150px;">Type <p-sortIcon field="type" ></p-sortIcon></th>
            <th pSortableColumn="admin" >Handled by <p-sortIcon field="admin" ></p-sortIcon></th>
            <th pSortableColumn="admin_time" >Execution Time <p-sortIcon field="admin_time" ></p-sortIcon></th>
            <th pSortableColumn="duration">Execution duration <p-sortIcon field="duration"></p-sortIcon></th>
            <!-- <th style="width:180px;">Actions</th> -->
        </tr>
        <tr>
            <!-- <th>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected questions" (click)="displayDeleteAlert(4)"></i>
            </th> -->
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'cv_id', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'adv_id', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'trans_id', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText style="width:150px;"type="text" (input)="dt.filter($event.target.value, 'name', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <p-calendar #cl [(ngModel)]="rangeDates" (onSelect)="dt.filter(rangeDates, 'date', 'isBetween')" (onClearClick)="dt.filter('', 'date', 'contains')" [showButtonBar]="true" yearRange="2020:2030" selectionMode="range" styleClass="ui-column-filter" placeholder="" [readonlyInput]="true" dateFormat="yy-mm-dd"></p-calendar>
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'time', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
                <p-dropdown [options]="types" (onChange)="dt.filter($event.value, 'type', 'equals')" styleClass="ui-column-filter" [(ngModel)]="type" placeholder="" [showClear]="false">
                  <ng-template let-option pTemplate="item">
                      <span [class]="'question-badge status-' + option.value">{{option.label}}</span>
                  </ng-template>
                </p-dropdown>
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'admin', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'admin_time', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'duration', 'contains')" placeholder="" class="ui-column-filter">
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-trans >
        <tr  class="ui-selectable-row"  data-toggle="modal" data-target="#transModal" (click)="displayTModal(trans)"  >
            <!-- <td>
                <p-tableCheckbox [value]="trans" (click)="addToSelected(trans)"></p-tableCheckbox>
            </td> -->
            <td class="text-center" >
              {{ trans.cv_id }}
            </td>
            <td class="text-center" >
              {{ trans.adv_id }}
            </td>
            <td>
              {{ trans.trans_id }}
            </td>
            <td class="text-center">
              {{ trans.name }}
            </td>
            <td>
              {{ trans.date }}
            </td>
            <td>
              {{ trans.time }}
            </td>
            <td>
                <span [class]="'question-badge status-' + trans.type">{{ trans.type }}</span>
            </td>
            <td>
               {{trans.admin}}
            </td>
            <td>
               {{trans.admin_time}}
            </td>
            <td>
               {{ trans.duration }}
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptytrans">
        <tr>
            <td colspan="8" style="text-align:center;padding:15px;"> There are no admin verification logs </td>
        </tr>
    </ng-template>
</p-table>
        <p style="text-align:center;padding:15px;">{{ message }} </p>





<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayModal" id="transModal"  tabindex="-1" role="dialog" aria-labelledby="createModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="transactionToPreview.type === 'UNIVERSITY'"> Transaction Details- University</h3>
        <h3 *ngIf="transactionToPreview.type === 'SKILL'"> Transaction Details- Skill </h3>
        <h3 *ngIf="transactionToPreview.type === 'MAJOR'"> Transaction Details- Major </h3>
        <h3 *ngIf="transactionToPreview.type === 'MINOR'"> Transaction Details- Minor </h3>
        <h3 *ngIf="transactionToPreview.type === 'JOB_TITLE'"> Transaction Details- Job Title </h3>
        <h3 *ngIf="transactionToPreview.type === 'JOB_TITLE_SYNONYMS'"> Transaction Details- Job Title Synonyms </h3>
      </div>

       <app-transaction  [userInput]="transactionToPreview" [adminEntry]="adminEntry" [previewMode]="true" [languagesArray]="languagesArray" (closeModal)="closeModal()"></app-transaction>

     <div class="modal-footer">
         <!-- <button   type="button" class="btn btn-danger" data-dismiss="modal" (click)="closeModal()" style="float:right;" > Close</button> -->
      </div>
    </div>
  </div>
</div>
<!-- end of  create modal-->



















