import { LanguageService } from './../../../../services/language.service';
import { Component, EventEmitter, OnDestroy, OnInit, Output, Input } from '@angular/core';
import { FormGroup, FormControl, FormArray, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Language } from '../../../../models/language';
import { Subject } from 'rxjs/Subject';
import { HelpTipService } from 'app/admin/services/help-tip.service';
import { HelpTip } from 'app/admin/models/help-tip';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { ConsoleService } from '@ng-select/ng-select/lib/console.service';
declare var $: any;

@Component({
  selector: 'app-edit-tip-modal',
  templateUrl: './edit-tip-modal.component.html',
  styleUrls: ['./edit-tip-modal.component.css'],

})
export class EditTipModalComponent implements OnInit, OnD<PERSON>roy {
  helpTipForm;
  private ngUnsubscribe: Subject<any> = new Subject();
  @Input('mode') mode = 'create';
  @Input('sections') sections: { 'label': string, 'value': number }[] = [];
  @Input('fields') fields: { 'label': string, 'value': number, 'section_id': number }[] = [];
  filteredFields: { 'label': string, 'value': number }[] = [{ 'label': '', 'value': null }];
  @Input('hTip') hTip: HelpTip = {
    id: null, section_id: null, field_id: null, section: '',
    field: '', active: false, description: '', langId: null
  };
  @Input('languagesArray') languagesArray: Language[] = [];
  @Input('openedFromSidebar') openedFromSidebar = true;
  @Input('hTipId') hTipId = null;
  @Output() closeCreateModal = new EventEmitter();
  @Output() closeUpdateModal = new EventEmitter();
  @Output() closeDeleteModal = new EventEmitter();
  categories: { 'user_categories': { 'value': number, 'label': string }[][], 'employer_categories': { 'value': number, 'label': string }[][] }
    = { 'user_categories': [], 'employer_categories': [] };
  hTipTranslations: { 'description': string, 'translated_languages_id': number }[] = [];
  currentLangId = 1;
  oldTip: HelpTip;
  insertedLanguagesIds: number[] = [];


  constructor(private fb: FormBuilder,
    private translate: TranslateService,
    private hTipService: HelpTipService,
    private languageService: LanguageService,
    public sanitizer: DomSanitizer,
    private router: Router) {
    translate.addLangs(['en', 'ar']);
    translate.setDefaultLang('en');
    const browserLang = translate.getBrowserLang();
    translate.use('en');

  }

  ngOnInit() {
    if (this.mode === 'create') {
      this.buildEmptyForm();
    } else if (this.mode === 'edit') {
      this.oldTip = {
        'id': this.hTip.id,
        'section_id': this.hTip.section_id,
        'field_id': this.hTip.field_id,
        'section': this.hTip.section,
        'field': this.hTip.field,
        'active': this.hTip.active,
        'description': this.hTip.description,
        'langId': this.hTip.langId,
      };
      this.buildFilledForm();
    } else if (this.mode === 'preview') {
      this.initializeView();
      this.oldTip = {
        'id': this.hTip.id,
        'section_id': this.hTip.section_id,
        'field_id': this.hTip.field_id,
        'section': this.hTip.section,
        'field': this.hTip.field,
        'active': this.hTip.active,
        'description': this.hTip.description,
        'langId': this.hTip.langId,
      };
      this.buildFilledForm();
    }

  }


  buildEmptyForm() {
    this.helpTipForm = this.fb.group({
      'section_id': [null, Validators.required],
      'field_id': [null, Validators.required],
      'active': [false],
      'help_fields_trans': this.fb.array([])

    });
    console.log('form', this.helpTipForm);
    if (this.openedFromSidebar) {
      this.getLanguages();
    } else {
      for (let lang of this.languagesArray) {
        this.fillHelpTipTrans(lang.id);
      }
    }

  }

  getLanguages() {
    this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log(res);
      let temp = res['data'];
      for (let lang of temp) {
        this.languagesArray.push({
          'id': lang.id,
          'name': lang.name
        });
        this.fillHelpTipTrans(lang.id);
      }

      console.log('languages array', this.languagesArray);
      console.log('lang arr length', this.languagesArray.length);
      this.getSectionsFields();
    });
  }


  getSectionsFields() {
    this.hTipService.getSectionsFields().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log('res', res);
      let temp = res['sections'];
      for (let section of temp) {
        this.sections.push({
          'label': section.name,
          'value': section.id
        });
      }
      let temp2 = res['fields'];
      for (let field of temp2) {
        if (field.saved === 0) {
          this.fields.push({
            'label': field.name,
            'value': field.id,
            'section_id': field.section_id
          });
        }
      }
      this.fields.unshift({ label: '', value: null, section_id: null });
      this.sections.unshift({ label: '', value: null });
      console.log('sections', this.sections);
      console.log('fields', this.fields);
    });
  }


  private fillHelpTipTrans(langId) {
    this.help_fields_trans.insert(this.help_fields_trans.length, this.createHelpTipTransControls(langId));
  }
  private createHelpTipTransControls(langId: number) {
    if (langId === 1) {
      return new FormGroup({
        'translated_languages_id': new FormControl(langId),
        'description': new FormControl('', Validators.required)
      });
    }
    return new FormGroup({
      'translated_languages_id': new FormControl(langId),
      'description': new FormControl('', Validators.required)
    });

  }


  buildFilledForm() {
    this.helpTipForm = this.fb.group({
      'section_id': [this.hTip.section_id, Validators.required],
      'field_id': [this.hTip.field_id, Validators.required],
      'active': [this.hTip.active],
      'help_fields_trans': this.fb.array([
        new FormGroup({
          'translated_languages_id': new FormControl(1),
          'description': new FormControl(this.hTip.description, Validators.required)
        })
      ])
    });
    console.log('form', this.helpTipForm);
    this.insertedLanguagesIds.push(1);
    this.fields.push({ 'label': this.hTip.field, 'value': this.hTip.field_id, 'section_id': this.hTip.section_id });
    this.filter();
    this.getHTipTranslations();


  }


  getHTipTranslations() {
    this.hTipService.gethelpTip(this.hTip.id).takeUntil(this.ngUnsubscribe).subscribe(res => {
      let temp = res['help_field'];
      console.log('get htopic', temp);
      for (let i = 0; i < temp.help_field_trans.length; i++) {
        if (temp.help_field_trans[i].translated_languages_id !== 1) {
          this.help_fields_trans.insert(this.help_fields_trans.length,
            new FormGroup({
              'translated_languages_id': new FormControl(temp.help_field_trans[i].translated_languages_id),
              'description': new FormControl(temp.help_field_trans[i].description, Validators.required),
            })
          );
          this.insertedLanguagesIds.push(temp.help_field_trans[i].translated_languages_id);
        }
      }

      console.log('htopicForm filled', this.helpTipForm);
    });

  }


  initializeView() {
    console.log('hTip', this.hTip);
    this.hTipTranslations.push({
      'description': this.hTip.description,
      'translated_languages_id': this.hTip.langId
    });
    console.log('hTipTrans', this.hTipTranslations);
    this.insertedLanguagesIds.push(1);
    this.getHelpTipTranslations();
  }


  getHelpTipTranslations() {
    this.hTipService.gethelpTip(this.hTip.id).takeUntil(this.ngUnsubscribe)
      .subscribe(res => {
        console.log('get help tip res', res);
        let temp = res['help_field'];
        console.log('temp', temp);
        for (let i = 0; i < temp.help_field_trans.length; i++) {
          if (temp.help_field_trans[i].translated_languages_id !== 1 && !this.isAdded(temp.help_field_trans[i].translated_languages_id)) {
            this.hTipTranslations.push({
              'description': temp.help_field_trans[i].description,
              'translated_languages_id': temp.help_field_trans[i].translated_languages_id,
            });
            this.insertedLanguagesIds.push(temp.help_field_trans[i].translated_languages_id);
            console.log('inside 1 if', i);
          }
        }
        console.log('inserted lang', this.insertedLanguagesIds);
        console.log('hTipTranslations', this.hTipTranslations);

      });
  }

  isAdded(n: number) {
    let found = false;
    for (let i = 0; i < this.insertedLanguagesIds.length; i++) {
      if (n === this.insertedLanguagesIds[i]) {
        return true;
      }
    }
    if (!found) {
      return false;
    }

  }


  filter() {
    this.filteredFields = [{ 'label': '', 'value': null }];
    for (let field of this.fields) {
      if (field.section_id === this.section_id.value) {
        this.filteredFields.push({
          'label': field.label,
          'value': field.value
        });
      }
    }

    if (this.filteredFields.length === 1) {
      this.filteredFields.push({ label: 'there is no more fields in this section to add help tip to it', value: null });
    }

    console.log('filteredFields', this.filteredFields);
  }


  changeLang(langId: number) {
    this.translate.use(this.getLangAbbrev(langId));
    this.currentLangId = langId;
  }



  getLangAbbrev(langId: number) {
    return this.languageService.getLangAbbrev(langId);
  }


  saveQuestion(formValue) {
    console.log('is form valid?', this.helpTipForm.valid);
    console.log(this.helpTipForm.value);

    if (this.helpTipForm.valid) {
      let dataToSend = this.helpTipForm.value;
      if (dataToSend.section_id !== null && dataToSend.field_id !== null && dataToSend.help_fields_trans[0].description !== '') {
        // dataToSend.active = (dataToSend.active_temp == false || dataToSend.active_temp == 0) ?  0 : 1;
        dataToSend.active = (dataToSend.active == false || dataToSend.active == 0) ? 0 : 1;
        console.log('active temp', dataToSend.active_temp, ',active:', dataToSend.active);
        if (this.mode === 'create') {
          this.hTipService.createHTip(dataToSend).subscribe(res => {
            console.log('create help tip res', res);
            if (this.openedFromSidebar) {
              // let temp = res['data'];
              // let newHelpTip = {
              //   'id'         : temp.id,
              //   'section_id' : temp.section_id,
              //   'field_id'   : temp.field_id,
              //   'section'    : temp.section.name,
              //   'field'      : temp.field.name,
              //   'active'     : temp.active,
              //   'description': temp.help_field_trans[0].description,
              //   'langId'     : temp.help_field_trans[0].translated_languages_id

              // };
              // this.hTipService.refreshHTValue(newHelpTip);
              this.closeCreateModal.emit();
              this.router.navigateByUrl('/manage/dashboard/help-tips');
            } else {
              this.closeCreateModal.emit({ 'data': res['data'] });
            }

          });
        } else if (this.mode === 'edit') {
          this.hTipService.updateHTip(dataToSend, this.hTip.id).subscribe(res => {
            console.log('update help tip res', res);
            if (this.openedFromSidebar) {
              let temp = res['data'];
              let updatedHelpTip = {
                'id': temp.id,
                'section_id': temp.section_id,
                'field_id': temp.field_id,
                'section': temp.section.name,
                'field': temp.field.name,
                'active': temp.active,
                'description': temp.help_field_trans[0].description,
                'langId': temp.help_field_trans[0].translated_Languages_id

              };
              this.hTipService.refreshHTValue(updatedHelpTip);
              this.closeUpdateModal.emit();
            } else {
              this.closeUpdateModal.emit({ 'new': res['data'], 'old': this.oldTip });
            }

          });
        }

      } else {
        alert('please enter valid values, english translation(question & answer) ' +
          'must be filled at least, or close the form & reopen it');
      }

    }

  }


  deleteHTip(htId) {
    this.hTipService.deleteHTip(htId).subscribe(res => {
      console.log('delete res', res);
      let temp = res['data'];
      console.log('temp in modal', temp);
      this.closeDeleteModal.emit({ 'data': res['data'] });
    }
      , (error) => this.closeDeleteModal.emit({ 'error': error })
    );

  }



  get section_id() {
    return this.helpTipForm.get('section_id');
  }

  get field_id() {
    return this.helpTipForm.get('field_id');
  }

  get active() {
    return this.helpTipForm.get('active');
  }

  get description() {
    return this.helpTipForm.get('help_trans.description');
  }

  get help_fields_trans() {
    return this.helpTipForm.get('help_fields_trans');
  }

  get help_transArray() {
    return this.helpTipForm.get('help_fields_trans') as FormArray;
  }





  ngOnDestroy(): void {
    //  let index = this.fields.indexOf({ 'label': this.hTip.field, 'value': this.hTip.field_id, 'section_id': this.hTip.section_id});
    //  this.fields.splice(index, 1);
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}


