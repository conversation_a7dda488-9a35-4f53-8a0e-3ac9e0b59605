<page-navbar [navType]="'empty'"></page-navbar>
<div *ngIf="showLoader" class="loader-container">
  <app-pre-loader [show]="showLoader"></app-pre-loader>
</div>

<p-toast [style]="{marginTop: '100px'}">
  <ng-template let-message pTemplate="message">
    <span>{{message.detail}}</span> &nbsp;
    <a [routerLink]="ptoastDisplayLink">CV Management</a>
  </ng-template>
</p-toast>


<div class="container-fluid">
  <p-steps [model]="steps" [readonly]="false" [(activeIndex)]="currentStep"></p-steps>
  <!-- start step0 - upload pdf -->
  <div *ngIf="resumeId && currentStep === 0" id="step3" class="upload-cv-container">
    <h3 class="step-title" translate>uploadCV.title</h3>
    <p class="text-center">You can choose pdf files only</p>
    <resume-upload-cv-file [languageId]="languageId" [resumeId]="resumeId" [uploadedFile]="uploadedFileName" [fromSteps]="true"
      (closeUploadCV)="closeUploadCVStep($event)">
    </resume-upload-cv-file>
  </div>
  <!-- end step0 - upload pdf -->

  <!-- start step1 - personal and contact info -->
  <div *ngIf="currentStep === 1">
    <div class="custom-container">
      <form #form="ngForm" [formGroup]="personalForm" (ngSubmit)="submitPersonalFormStep()"
        class="form-horizontal validate-form" [appInvalidControlScroll]="'normalComponent'">
        <h3 class="step-title" translate>personalInformation.title</h3>
        <div class="row form-group flex-row">
          <div class="col-sm-9 col-xs-12 flex-order-xs-2">
            <div class="row form-group" [ngClass]="{'has-error': form.submitted && isInvalid('first_name')}">
              <div class="col-lg-3 col-sm-4 alignment-right">
              </div>
              <div class="col-lg-9 col-sm-8 focus-no-padding validate-input"
                [ngClass]="{'has-val':personalForm.controls['first_name'].value}"
                data-validate="First Name is required">
                <input type="text" formControlName="first_name" class="form-control" id="firstName">
                <span class="custom-underline"></span>
                <span *ngIf="form.submitted && personalForm['controls'].first_name.errors?.required"
                  class="error-message" translate>validationMessages.required</span>
                <span *ngIf="form.submitted && personalForm['controls'].first_name.errors?.pattern"
                  class="error-message" translate>validationMessages.namePattern</span>
                <label class="control-label custom-control-label" translate>personalInformation.firstName</label>
              </div>
            </div>

            <div class="row form-group" [ngClass]="{'has-error':form.submitted && isInvalid('last_name')}">
              <div class="col-lg-3 col-sm-4 alignment-right">
              </div>
              <div class="col-lg-9 col-sm-8 focus-no-padding validate-input"
                [ngClass]="{'has-val':personalForm.controls['last_name'].value}" data-validate="Last Name is required">
                <input type="text" formControlName="last_name" class="form-control" id="lastName">
                <span class="custom-underline"></span>
                <span *ngIf="form.submitted && personalForm['controls'].last_name.errors?.required"
                  class="error-message" translate>validationMessages.required</span>
                <span *ngIf="form.submitted && personalForm['controls'].last_name.errors?.pattern" class="error-message"
                  translate>validationMessages.namePattern</span>
                <label class="control-label custom-control-label" translate>personalInformation.lastName</label>
              </div>
            </div>

            <div class="row form-group align-bottom" [ngClass]="{'has-error': form.submitted && isInvalid('gender')}">
              <div class="col-lg-3 col-sm-4 alignment-right"></div>
              <div class="col-sm-4 col-xs-6">
                <label class="container radio-choose" translate>personalInformation.female
                  <input type="radio" formControlName="gender" id="inlineRadio1" value="female">
                  <span class="checkmark"></span>
                </label>
              </div>
              <div class="col-lg-5 col-sm-4 col-xs-6">
                <label class="container radio-choose" translate>personalInformation.male
                  <input type="radio" formControlName="gender" id="inlineRadio2" value="male">
                  <span class="checkmark"></span>
                </label>
                <span *ngIf="form.submitted && isInvalid('gender')" class="error-message"
                  translate>validationMessages.required</span>
              </div>
            </div>

            <div class="row form-group" formArrayName="nationalities">
              <div class="col-sm-12">
                <div *ngFor="let item of nationalities.controls; let i = index;"
                  [ngClass]="{'has-error':form.submitted && !isDDValid(item) || form.submitted && item.errors}"
                  class="row margin-bo-25">
                  <div class="col-lg-3 col-sm-4 alignment-right">
                  </div>
                  <div class="col-lg-8 col-sm-7 col-xs-10 col-xxx-9 focus-no-padding">
                    <p-dropdown [options]="nationalityOpts" optionLabel="name" [formControlName]="i" [filter]="true"
                      [ngClass]=" {'has-val':item.value.id}" filterMatchMode="startsWith" #selectedNat
                      (onChange)="selectNationality(i, item)">
                      <ng-template let-it pTemplate="selectedItem">
                        <img *ngIf="it.label !== ''" src="./assets/images/countries/{{it.value.id}}.gif"
                          style="width:16px;margin-top:4px; float:right;" />
                        <span style="vertical-align:middle">{{it.label}}</span>
                      </ng-template>
                      <ng-template let-nat pTemplate="it">
                        <img *ngIf="nat.label !== ''" src="./assets/images/countries/{{nat.value.id}}.gif"
                          style="float:right;margin-top:4px" />
                        <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{nat.label}}</div>
                        </div>
                      </ng-template>
                    </p-dropdown>

                    <label *ngIf="i == 0" class="control-label custom-control-label"
                      translate>personalInformation.nationality</label>
                  </div>
                  <div class="col-lg-1 col-sm-1 col-xs-2 col-xxx-3 focus-no-padding-button">
                    <button *ngIf="i == 0" (click)="addNationality($event)" class="btn btn-gray"
                      pTooltip="{{'shared.addMoreValues' | translate}}" tooltipPosition="top">
                      <i class="fa fa-plus" aria-hidden="true"></i>
                    </button>
                    <button *ngIf="i > 0" (click)="removeNationality($event, i)" class="btn btn-delete btn-delete-big">
                      <i class="fa fa-trash" aria-hidden="true"></i>
                    </button>
                  </div>
                  <span *ngIf="form.submitted && !isDDValid(item)" class="error-message"
                    translate>validationMessages.required</span>
                  <span *ngIf="form.submitted && item.errors?.incorrect" class="error-message"
                    translate>validationMessages.duplicatedNationality</span>
                </div>
              </div>
            </div>

            <div class="row form-group" formGroupName="date_of_birth" style="position:relative;"
              [ngClass]="{'has-error':form.submitted && (isInvalid('year') || isInvalid('month') || isInvalid('day')) }">
              <div class="col-lg-3 col-sm-4 alignment-right">
              </div>
              <div class="custom-col-3 col-no-padding focus-no-padding"
                [ngClass]="{'has-val':dateOfBirthControl.controls['year'].value}">
                <p-dropdown [options]="yearOpts" formControlName="year" [filter]="true" filterMatchMode="startsWith"
                  placeholder=" " [ngClass]=" {'has-val':getValueToDD(dateOfBirthControl.controls['year'])}"
                  #selectedYear (onChange)="onChangeYear(selectedYear.value, selectedMonth.value, selectedDay.value)">
                  <ng-template let-year pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{year.label}}</div>
                    </div>
                  </ng-template>
                </p-dropdown>
                <span class="custom-underline"></span>

                <label class="control-label custom-control-label">
                  <span class="not-mobile-label" translate>personalInformation.dateOfBirth</span>
                  <!-- <span class="only-mobile-label" translate>personalInformation.year</span> -->
                </label>

              </div>
              <div class="custom-col-3 col-no-padding focus-no-padding">
                <p-dropdown [options]="monthOpts" formControlName="month" [filter]="true"
                  filterBy="label,value.name,code" filterMatchMode="contains" placeholder="MM" #selectedMonth
                  (onChange)="onChangeMonth(selectedMonth.value, selectedYear.value, selectedDay.value)">
                  <ng-template let-it pTemplate="selectedItem">
                    <span style=" vertical-align:middle; float:left;">{{it.label | translate | slice:0:3}}</span>

                  </ng-template>
                  <ng-template let-month pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px" translate>{{month.label}}</div>
                    </div>
                  </ng-template>
                </p-dropdown>
                <span class="custom-underline"></span>

              </div>
              <div class="custom-col-3 focus-no-padding">
                <p-dropdown [options]="dayOpts" formControlName="day" [filter]="true" filterMatchMode="startsWith"
                  placeholder="DD" #selectedDay>
                  <ng-template let-day pTemplate="item">
                    <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                      <div style="font-size:14px;float:left;margin-top:4px">{{day.label}}</div>
                    </div>
                  </ng-template>
                </p-dropdown>
                <span class="custom-underline"></span>

              </div>
              <span *ngIf="form.submitted && (isInvalid('year') || isInvalid('month') || isInvalid('day'))"
                class="error-message" translate>validationMessages.required</span>
            </div>

            <div class="row form-group" formGroupName="current_location"
              [ngClass]="{'has-error': form.submitted && (personalForm.hasError('InvalidLocationError') || currentLocationControl.controls['fullLocation'].invalid )}">
              <div class="col-lg-3 col-sm-4 alignment-right">
              </div>
              <div class="col-lg-9 col-sm-8 focus-no-padding"
                [ngClass]="{'has-val':currentLocationControl.controls['fullLocation'].value}">
                <input formControlName="fullLocation" placeholder=" " type="text" class="form-control"
                  id="currentLocation" #currentSearch (keyup)="currentLocationKeyUp($event)"
                  (blur)="currentLocationChanged()">
                <span class="custom-underline"></span>

                <span *ngIf="form.submitted && personalForm.hasError('InvalidLocationError')"
                  class="error-message location-error-message" translate>{{ personalForm.errors?.InvalidLocationError
                  }}</span>
                <span *ngIf="form.submitted && currentLocationControl.controls['fullLocation'].value === ''"
                  class="error-message location-error-message" translate>validationMessages.required</span>
                <label class="control-label custom-control-label" translate>personalInformation.currentLocation</label>
              </div>
            </div>

            <!-- [ngClass]="{'has-error': form.submitted && !personalForm.controls['email'].valid}" -->
            <div class="row form-group"
              [ngClass]="{'has-error': form.submitted && !personalForm.controls['email'].valid}">
              <div class="col-lg-3 col-sm-4 alignment-right">
              </div>
              <div class="col-lg-9 col-sm-8 focus-no-padding"
                [ngClass]="{'has-val':personalForm.controls['email'].value}">
                <input type="email" formControlName="email" class="form-control">
                <span class="custom-underline"></span>
                <label class="control-label custom-control-label" translate>contactInformation.email</label>

                <span class="error-message" *ngIf="form.submitted && personalForm.controls['email'].errors?.required"
                  translate>validationMessages.required</span>
                <span class="error-message"
                  *ngIf="form.submitted && personalForm.controls['email'].errors?.invalidEmailError" translate>
                  {{personalForm.controls['email'].errors?.invalidEmailError}}
                </span>
                <!-- <span class="error-message"
                  *ngIf="form.submitted && personalForm.controls['email'].errors?.email" translate>
                  validationMessages.invalidEmail
                </span> -->
              </div>
            </div>

            <div class="row form-group">
              <div class="col-lg-3 col-sm-4 alignment-right">
              </div>
              <div class="col-lg-9 col-sm-8">
                <div class="row" formGroupName="contact_number"
                  [ngClass]="{'has-error': form.submitted && ( contactNumberControl.get('phone_number').hasError('contactNumberError') || contactNumberControl.hasError('fullNumberError')  || contactNumberControl.get('country_id').errors || contactNumberControl.get('phone_number').errors  ) }">
                  <!-- <div class="col-sm-4 col-xs-12 margin-bo-mo-10 focus-no-padding">
                    <p-dropdown [options]="phoneTypeOpts" optionLabel="name" formControlName="phone_type"
                      [ngClass]=" {'has-val' :contactNumberControl.get('phone_type_id').value}" [filter]="false"
                      #phoneType (onChange)="setContactId('phone_type_id', phoneType);">
                      <ng-template let-phone pTemplate="item">
                        <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{phone.label}}</div>
                        </div>
                      </ng-template>
                    </p-dropdown>

                    <span class="custom-underline"></span>
                    <label class="control-label custom-control-label">
                      <span class="not-mobile-label" translate>contactInformation.contactNumber</span>
                    </label>
                  </div> -->
                  <div class="col-sm-12 col-xs-12 col-xxs-12 margin-bo-mo-10">
                    <div class="row">
                      <div class="col-sm-5 col-xs-5 focus-no-padding ">
                        <p-dropdown [options]="countryCodeOpts" optionLabel="code" formControlName="country_code"
                          [filter]="true" filterBy="label,value.name" filterMatchMode="contains" #countryCode
                          (onChange)="setContactId('country_id', countryCode)"
                          [ngClass]=" {'has-val' :contactNumberControl.get('country_id').value}">
                          <ng-template let-it pTemplate="selectedItem">
                            <img *ngIf="it.label!=''" src="./assets/images/CountryCode/{{it.label}}.gif"
                              class="countryimg" style=" vertical-align:middle; float:right;" />
                            <span class="countrycode" style=" vertical-align:middle; float:left;">{{it.label}}</span>
                          </ng-template>
                          <ng-template let-code pTemplate="it">
                            <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                              <div style="margin-top:4px ;float: left;width:480px;">
                                <span style=" width: 55px; display: inline-block;">{{code.label }}</span>
                                <span style="width:400px;display: inline-block;">
                                  {{ (code.length>25)? ( code.value.name | slice:0:25)+'...': code.value.name }}
                                </span>
                                <img *ngIf="code.label!=''" src="./assets/images/CountryCode/{{code.label}}.gif"
                                  style="display: inline-block;width:16px;" />
                              </div>
                            </div>
                          </ng-template>
                        </p-dropdown>

                        <span class="custom-underline"></span>
                        <label class="control-label custom-control-label">
                          <span class="not-mobile-label" translate>contactInformation.contactNumber</span>
                        </label>
                      </div>
                      <div class="col-sm-7 col-xs-7 focus-no-padding margin-top-7">
                        <input type="tel" class="form-control" formControlName="phone_number" id="number1"
                          onkeypress="return (event.key.charCodeAt(0) == 8 || event.key.charCodeAt(0) == 0 || event.key.charCodeAt(0) == 13) ? null : event.key.charCodeAt(0) >= 48 && event.key.charCodeAt(0) <= 57">
                        <span class="custom-underline"></span>

                        <span
                          *ngIf="form.submitted && contactNumberControl.get('phone_number').hasError('contactNumberError')"
                          class="glyphicon  form-control-feedback" aria-hidden="true">
                        </span>
                      </div>
                    </div>
                  </div>

                  <span class="error-message">
                    <span
                      *ngIf="form.submitted && contactNumberControl.get('phone_number').hasError('contactNumberError')"
                      translate>{{contactNumberControl.get('phone_number').errors?.contactNumberError}}</span>
                    <span
                      *ngIf="(form.submitted && contactNumberControl.get('phone_number').hasError('contactNumberError')) && (form.submitted && contactNumberControl.hasError('fullNumberError'))"
                      translate>,
                    </span>
                    <span *ngIf=" form.submitted && contactNumberControl.hasError('fullNumberError')"
                      translate>{{contactNumberControl.errors?.fullNumberError}}</span>
                    <span
                      *ngIf=" form.submitted && (contactNumberControl.get('country_id').errors?.required && contactNumberControl.get('phone_number').errors?.required)"
                      translate>validationMessages.required</span>

                  </span>

                </div>
              </div>
            </div>
            <!-- <p> {{ form.value | json }} </p> -->
          </div>
          <div class="col-sm-3 col-xs-12 flex-order-xs-1 text-center">
            <div class="upload-image-container">
              <div class="inner-container">
                <a data-toggle="modal" data-target="#imageEditorModal">
                  <img [src]="perPhotoSrc" class="img-responsive">
                  <span class="upload-label" *ngIf="uploadLabelDisplay">Upload Profile Picture</span>
                </a>
              </div>
              <div class="upload-actions">
                <a *ngIf="!uploadLabelDisplay" class="edit-image" data-toggle="modal" data-target="#imageEditorModal">
                  <i class="fa fa-edit" aria-hidden="true"></i>
                </a>
                <a *ngIf="!uploadLabelDisplay" class="delete-image" (click)="deleteProfilePicture()">
                  <i class="fa fa-trash" aria-hidden="true"></i>
                </a>
              </div>
            </div>

          </div>

        </div>

        <div class="row form-group text-right div-margin-top-40">
          <div class="col-sm-10 col-xs-12">
            <button style="margin-right:20px;" type="submit" class="btn btn-primary btnNext" (click)="prevStep()">
              <i class="fa fa-arrow-left"></i> <span translate>shared.previous</span>
            </button>
            <button type="submit" class="btn btn-primary btnNext">
              <i class="fa fa-arrow-right"></i> <span translate>shared.next</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
  <!-- end step1 - personal and contact info -->

  <!-- start step2 - skills and languages -->
  <div *ngIf="currentStep === 2" class="custom-container skillsLangsForm">
    <form #form="ngForm" [formGroup]="skillsLangsForm" (ngSubmit)="form.valid && submitSkillsStep()"
      class="form-horizontal validate-form">
      <!-- languages -->
      <!-- <div style="margin-bottom:60px;"> -->
      <h3 class="step-title" translate>languages.title</h3>
      <div formArrayName="languages" *ngFor="let item of languages.controls; let i = index;"
        [ngClass]="{'has-error':form.submitted && ( item.errors || item.get('language').hasError('languagesError') || item.get('level').hasError('languagesError') )}"
        class="row form-group">
        <span [formGroupName]="i">
          <div class="col-sm-9 col-xs-10">
            <div class="row">
              <div class="col-sm-6 col-xs-12">
                <div class="row">
                  <div class="col-sm-4 alignment-right"> </div>
                  <div class="col-sm-8 col-xs-12 margin-bo-25 focus-no-padding">
                    <p-dropdown [options]="languagesOpts" optionLabel="name" formControlName="language"
                      [ngClass]="{'has-val':languages.at(i)['controls']['language'].value.id}" [filter]="true" #language
                      filterMatchMode="startsWith" (onChange)="selectLanguage(language, i);">
                      <ng-template let-language pTemplate="item">
                        <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{language.label}}</div>
                        </div>
                      </ng-template>
                    </p-dropdown>
                    <span class="custom-underline"></span>
                    <label *ngIf="i == 0" class="control-label custom-control-label"
                      translate>languages.language</label>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-xs-12">
                <div class="row">
                  <div class="col-sm-4 alignment-right"> </div>
                  <div class="col-sm-8 margin-bo-mo-10 focus-no-padding">
                    <p-dropdown [options]="languagesLevelsOpts" optionLabel="name" formControlName="level"
                      [ngClass]="{'has-val':languages.at(i)['controls']['level'].value.id}" [filter]="true" #level
                      filterMatchMode="startsWith" (onChange)="selectLanguageLevel(level, i);">
                      <ng-template let-it pTemplate="selectedItem">
                        <span style=" vertical-align:middle; float:left;">{{it.label | translate }}</span>
                      </ng-template>
                      <ng-template let-level pTemplate="item">
                        <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px" translate>{{level.label}}</div>
                        </div>
                      </ng-template>
                    </p-dropdown>
                    <span class="custom-underline"></span>
                    <label *ngIf="i == 0" class="control-label custom-control-label" translate>shared.level</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-sm-1 col-xs-2 text-right">
            <button *ngIf="i == 0" (click)="addLanguage()" class="btn btn-gray"
              pTooltip="{{'shared.addMoreValues' | translate}}" tooltipPosition="top">
              <i class="fa fa-plus" aria-hidden="true"></i>
            </button>
            <button *ngIf="i > 0" (click)="removeILanguage( i)" class="btn btn-delete btn-delete-big">
              <i class="fa fa-trash" aria-hidden="true"></i>
            </button>
          </div>
          <div class="col-sm-2 col-xs-12">
            <div style="display:inline-block;"
              *ngIf="form.submitted && item.get('level').hasError('languagesError') && form.submitted && item.get('language').hasError('languagesError')  ;else other_content"
              translate>
              <span class="error-message" translate>validationMessages.required</span>
            </div>
            <ng-template #other_content>
              <div style="display:inline-block;">
                <span class="error-message" *ngIf="form.submitted && item.get('level').hasError('languagesError')"
                  translate>{{item.get('level').errors?.languagesError}}</span>
                <span class="error-message" *ngIf="form.submitted && item.get('language').hasError('languagesError')"
                  translate>{{item.get('language').errors?.languagesError}}</span>
              </div>
            </ng-template>

            <!-- <span *ngIf="form.submitted && item.errors?.incorrect" class="error-message" translate>validationMessages.duplicatedLanguage</span> -->
          </div>
        </span>
      </div>
      <!-- </div> -->
      <!-- End languages -->

      <!-- Skills -->
      <h3 class="step-title" translate>skills.title</h3>
      <div formArrayName="skills" *ngFor="let item of skills.controls; let i = index;"
        [ngClass]="{'has-error':(form.submitted && item.errors) || (form.submitted && item.get('skill').hasError('skillsError')) || (form.submitted && item.get('level').hasError('skillsError'))}"
        class="row form-group">
        <span [formGroupName]="i">
          <div class="col-sm-9 col-xs-10">
            <div class="row">
              <div class="col-sm-6 col-xs-12">
                <div class="row">
                  <div class="col-sm-4 alignment-right"> </div>
                  <div class="col-sm-8 margin-bo-25 focus-no-padding">
                    <ng-select [items]="skillsDD.items" [virtualScroll]="true" formControlName="skill"
                      [loading]="skillsDD.loading" bindLabel="name" [addTag]="skillsDD.addNewItem"
                      [ngClass]="{'has-val':skills.at(i)['controls']['skill'].value}"
                      addTagText="{{'shared.addNewItem' | translate}}"
                      notFoundText="{{'shared.noItemsFound' | translate}}" [dropdownPosition]="'bottom'"
                      (scrollToEnd)="skillsDD.onScrollToEnd()" (search)="skillsDD.search($event)"
                      (keyup.delete)="skillsDD.deleteAutoCompleteItem(skills.at(i)['controls']['skill'])"
                      class="form-control ng-select-autocomplete">
                      <ng-template ng-label-tmp let-item="item" let-clear="clear">
                        <span class="ng-value-label">{{item.name}}</span>
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">
                          <i class="fa fa-times" aria-hidden="true"></i>
                        </span>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
                        {{item.name}}
                      </ng-template>
                    </ng-select>
                    <label class="control-label custom-control-label" translate>skills.skillName</label>
                    <span class="custom-underline"></span>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-xs-12">
                <div class="row">
                  <div class="col-sm-4 alignment-right"> </div>
                  <div class="col-sm-8 margin-bo-mo-10 focus-no-padding">
                    <p-dropdown [options]="skillsLevelsOpts" optionLabel="name" formControlName="level" [filter]="true"
                      [ngClass]="{'has-val':skills.at(i)['controls']['level'].value.id}" #level
                      filterMatchMode="startsWith" (onChange)="selectSkillLevel(level, i);">
                      <ng-template let-level pTemplate="item">
                        <div class="ui-helper-clearfix" style="position: relative;height: 25px;">
                          <div style="font-size:14px;float:left;margin-top:4px">{{level.label}}</div>
                        </div>
                      </ng-template>
                    </p-dropdown>
                    <span class="custom-underline"></span>
                    <label *ngIf="i == 0" class="control-label custom-control-label" translate>shared.level</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-sm-1 col-xs-2 text-right">
            <button *ngIf="i == 0" (click)="addSkill()" class="btn btn-gray"
              pTooltip="{{'shared.addMoreValues' | translate}}" tooltipPosition="top">
              <i class="fa fa-plus" aria-hidden="true"></i>
            </button>
            <button *ngIf="i > 0" (click)="removeISkill( i)" class="btn btn-delete btn-delete-big">
              <i class="fa fa-trash" aria-hidden="true"></i>
            </button>
          </div>
          <div class="col-sm-2 col-xs-12">
            <span class="error-message" *ngIf="form.submitted && item.get('level').hasError('skillsError')"
              translate>{{item.get('level').errors?.skillsError}}</span>
            <span class="error-message" *ngIf="form.submitted && item.get('skill').hasError('skillsError')"
              translate>{{item.get('skill').errors?.skillsError}}</span>
            <!-- <span *ngIf="form.submitted && item.errors?.incorrect" class="error-message"
                translate>validationMessages.duplicatedSkill</span> -->
          </div>
        </span>
      </div>
      <!-- End Skills -->
      <div class="row form-group text-right div-margin-top-40">
        <div class="col-sm-10 col-xs-12">
          <button style="margin-right:20px;" type="submit" class="btn btn-primary btnNext" (click)="prevStep()">
            <i class="fa fa-arrow-left"></i> <span translate>shared.previous</span>
          </button>
          <button type="submit" class="btn btn-primary btnNext">
            <i class="fa fa-arrow-right"></i> <span translate>shared.next</span>
          </button>
        </div>
      </div>
      <!-- <p> {{ form.value | json }} </p> -->
    </form>
  </div>

  <!-- end step2 - skills and languages -->

  <!-- start step3 - Eductions -->
  <div *ngIf="resumeId && currentStep === 3" class="educations">
    <h3 class="step-title" translate>education.title</h3>
    <app-educations-form [languageId]="languageId" [educations]="educations" [fromSteps]="true" [resumeId]="resumeId"
      [school_education_fields]="school_education_fields" [university_education_fields]="university_education_fields"
      [degreeLevelValues]="degreeLevelValues" (educationsChange)="onEducationsChange($event)" (prevStep)="prevStep()">
    </app-educations-form>
  </div>
  <!-- end step3 - Eductions -->

  <!-- start step4 - Work Experience -->
  <div *ngIf="resumeId && currentStep === 4" class="workexps">
    <h3 class="step-title">Work Experience</h3>
    <app-work-exps-form [languageId]="languageId" [workExps]="workExps" [resumeId]="resumeId" [fromSteps]="true" [expFields]="exp_fields" [username]="username"
      [employmentTypes]="employment_types" (workExpsChange)="workExps = $event" (prevStep)="prevStep()"
      (nextStep)="nextPage()">
    </app-work-exps-form>
  </div>
  <!-- end step4 - Work Experience -->

</div>


<div class="modal fade image-editor-modal" id="imageEditorModal" tabindex="-1" role="dialog"
  aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModal2Label">Profile Picture Editor</h4>
      </div>
      <div class="modal-body">
        <app-image-editor (closeModalPopup)="handleImageEditorPopup($event)">

        </app-image-editor>
      </div>
    </div>
  </div>
</div>
