<h3 class="faq-heading"> Manage <span title="Frequently asked questions">FAQs</span></h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ questionsArray.length }}</span></p>
  <!-- <p>filtered: <span class="badge badge-primary badge-pill">{{ filteredQuestions.length }}</span></p> -->



<p-table #dt [value]="questionsArray" [(selection)]="filteredQuestions" dataKey="id" styleClass="ui-table-questions" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="questionsArray.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['type', 'category', 'question', 'order' ]">
    <ng-template pTemplate="caption">
         <!-- questions -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th><i title="add new FAQ" class="fas fa fa-plus-circle" data-toggle="modal" data-target="#faqModal" (click)="displayCreateModal()" ></i></th>
            <th pSortableColumn="type" style="width:110px;">Type <p-sortIcon field="type" ></p-sortIcon></th>
            <th pSortableColumn="category" >Category <p-sortIcon field="category" ></p-sortIcon></th>
            <th pSortableColumn="question" >Question <p-sortIcon field="question" ></p-sortIcon></th>
            <th pSortableColumn="order" style="width:100px;">Order <p-sortIcon field="order"></p-sortIcon></th>
            <th pSortableColumn="activation" style="width:110px;">Status <p-sortIcon field="activation" ></p-sortIcon></th>
            <th style="width:150px;">Actions</th>
        </tr>
        <tr>
            <th>
                <!-- <p-tableHeaderCheckbox></p-tableHeaderCheckbox> -->
                <!-- <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete selected questions" (click)="displayDeleteAlert(4)"></i> -->
            </th>
            <th>
                <p-dropdown [options]="types" (onChange)="dt.filter($event.value, 'type', 'equals')" styleClass="ui-column-filter" [(ngModel)]="type" placeholder="" >
                  <ng-template let-option pTemplate="item">
                      <span [class]="">{{option.label}}</span>
                  </ng-template>
                </p-dropdown>
            </th>
            <th>
              <p-dropdown [options]="categories.all[0]" (onChange)="dt.filter($event.value, 'category', 'equals')" styleClass="ui-column-filter category" [(ngModel)]="category" [filter]="true"  [showClear]="false">
                <ng-template let-option pTemplate="item">
                    <span class="question-badge ">{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'question', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'order', 'contains')" placeholder="" class="ui-column-filter order">
            </th>
            <th>
              <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'activation', 'equals')" styleClass="ui-column-filter" [(ngModel)]="status"  [showClear]="false">
                <ng-template let-option pTemplate="item">
                    <span [class]="'question-badge status-' + option.value">{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-question>
        <tr class="ui-selectable-row" (mouseover)="question.display = true" (mouseleave)="question.display = false" >
            <td  data-toggle="modal" data-target="#faqModal" (click)="displayEditFormModal(question)">
                <!-- <p-tableCheckbox [value]="question" (click)="addToSelected(question)"></p-tableCheckbox> -->
            </td>
            <td  data-toggle="modal" data-target="#faqModal" (click)="displayEditFormModal(question)">
                <i *ngIf="question.type === 'User Faq'" title="user" class="fa fa-user"></i>
                <i *ngIf="question.type === 'Company Faq'" title="company" class="fa fa-suitcase"></i>
            </td>
            <td  data-toggle="modal" data-target="#faqModal" (click)="displayEditFormModal(question)">
               {{question.category}}
            </td>
            <td  data-toggle="modal" data-target="#faqModal" (click)="displayEditFormModal(question)">
               {{question.question | summary:100 }}
            </td>
            <td  data-toggle="modal" data-target="#faqModal" (click)="displayEditFormModal(question)">
               {{ question.order }}
            </td>
            <!-- <td class="inactive " [class.active-text]="question.activation">
              {{  question.activation | activation:'Active': 'InActive'  }}
            </td> -->
            <td  data-toggle="modal" data-target="#faqModal" (click)="displayEditFormModal(question)" >
              <span class="question-badge" [class.status-0]="!question.activation" [class.status-1]="question.activation" >{{  question.activation | activation:'Active': 'InActive'  }}</span>
            </td>
            <td class="actions">
              <span *ngIf="question.display">
                <i title="preview" data-toggle="modal" data-target="#faqModal" class="fa fa-eye" (click)="displayPreviewModal(question)"  ></i>
                <i title="edit" data-toggle="modal" data-target="#faqModal"  class="fa fa-edit" (click)="displayEditFormModal(question)"></i>
                <i  title="delete" class="fa fa-trash"  data-toggle="modal" data-target="#faqModal" (click)="displayDeleteAlert(question)"  ></i>
                <i  [class.activate]="question.activation" title="activation" (click)="activateFaq(question)"  class="fa fa-power-off "></i>
              </span>
            </td>
            <!-- <td>
                <i class="fa fa-trash" data-toggle="modal" data-target="#addValueModal" title="delete this question" (click)="displayDeleteAlert(3, question.id)"></i>
                <i class="fa fa-edit"  data-toggle="modal" data-target="#msgModal"  (click)="displayModal(question)"></i>
              </td> -->
        </tr>
    </ng-template>
    <ng-template pTemplate="emptyquestion">
        <tr>
            <td colspan="7" style="text-align:center;padding:15px;">No questions found.</td>
        </tr>
    </ng-template>
</p-table>



<!-- create modal-->
<div class="modal fade" *ngIf="displayModal" id="faqModal"  tabindex="-1" role="dialog" aria-labelledby="createModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 class="modal-title"  id="createModalLabel" translate>
          <span  *ngIf="mode === 'create'" translate>faqs.labels.AddNewFAQ</span>
          <span  *ngIf="mode === 'edit'" translate>faqs.labels.Edit</span>
          <span  *ngIf="mode === 'preview'" translate>faqs.labels.Preview</span>
          <span  *ngIf="mode === 'delete'" translate>faqs.labels.Delete</span>
        </h3>
      </div>

     <app-edit-modal  *ngIf="displayModal" [question]="question" [mode]="mode" [openedFromSidebar]="false" (closeUpdateModal)="showUpdatedFaqInTable($event)"
             (closeCreateModal)="showNewFaqInTable($event)" (closeDeleteModal)="removeQuestionFromTable($event)" [languagesArray]="languagesArray" [categories]="categories" ></app-edit-modal>


     <div class="modal-footer">
         <button  *ngIf="mode === 'preview'" class="btn btn-default"  data-toggle="modal" data-target="#editModal"  (click)="closePreviewOpenEdit()" style="float:left;" translate>faqs.labels.Edit</button>
         <button  *ngIf="mode === 'preview' " type="button" class="btn btn-danger" data-dismiss="modal" (click)="closeModal()" style="float:right;" translate>faqs.labels.Close</button>

      </div>
    </div>
  </div>
</div>
<!-- end of  create modal-->

















