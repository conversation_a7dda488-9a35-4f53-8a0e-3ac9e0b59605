import { Component, OnInit } from '@angular/core';
import {FormGroup, FormControl, FormBuilder} from '@angular/forms';
import 'rxjs/add/operator/debounceTime';
@Component({
  selector: 'app-test-storge',
  templateUrl: './test-storge.component.html',
  styleUrls: ['./test-storge.component.css']
})
export class TestStorgeComponent implements OnInit {
  myForm;
  constructor(private fb:FormBuilder) {

    let retrievedObject = localStorage.getItem('testObject');
      this.myForm=fb.group({
        username:[''],
        password:[''],
        remember:[''],
      });

      if(retrievedObject){
        let formObject =JSON.parse(retrievedObject);
        this.myForm.get('username').setValue(formObject.username);
        this.myForm.get('password').setValue(formObject.password);
        this.myForm.get('remember').setValue(formObject.remember);
      }




  }

  ngOnInit() {
    this.myForm.valueChanges.debounceTime(1200)
      .subscribe(()=>{
        
        localStorage.setItem('testObject', JSON.stringify(this.myForm.value));
      });
  }


}
