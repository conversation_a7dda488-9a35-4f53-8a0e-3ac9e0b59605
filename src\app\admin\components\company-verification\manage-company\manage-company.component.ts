import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { FormBuilder } from '@angular/forms';
import { CompanyService } from 'app/admin/services/company.service';
import { Table } from 'primeng/table';
import { FilterUtils } from 'primeng/utils';
import { Calendar } from 'primeng/calendar';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
import { Router } from '@angular/router';
declare var $: any;

@Component({
  selector: 'app-manage-company',
  templateUrl: './manage-company.component.html',
  styleUrls: ['./manage-company.component.css']
})
export class ManageCompanyComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table: Table;
  @ViewChild('cl') calendar: Calendar;
  rangeDates: Date[];
  filteredCompanies = [];
  admins: { 'value': number, 'label': string }[] = [];
  companyToPreview;
  operationType;
  displayCompanyModal = false;
  displayModal = false;
  private ngUnsubscribe: Subject<any> = new Subject();
  companies = [];
  opperationNum: number;
  msgIdToDelete: number;
  msgId: number;
  mode = '';
  company = {
    'id': null, 'name': '', 'username': '', 'description': '', 'website': '', 'logo': '',
    'status': '', 'email': '', 'country': '', 'city': '', 'industry': '', 'profile': '', 'handled_by': '',
    'mobile_number': '', 'message': '', 'show_in_home_page': '',
    'company_method_verification_id': '', 'admin_description': '', 'company_status_id': '', 'permissions': '','installed_plugin':0,'company_domain_url':''
  };
  companyLog: { 'name': string, 'id': number, 'log': any[] } = { 'id': null, 'name': '', 'log': [] };
  statuses = [];
  // showLogoOps = [
  //   { label: '', value: null },
  //   { label: 'Show', value: 'Show' },
  //   { label: 'Hidden', value: 'Hidden' },
  // ];

  methods = [];
  permissions = [];
  loading = true;
  status;
  constructor(private companyService: CompanyService,
    private imageProcessingService: ImageProcessingService,
              private  router: Router,
    private fb: FormBuilder) {
    FilterUtils['isBetween'] = (value: any, filter: any): boolean => {
      // value = value.substring(0, 10);
      let newDate = new Date(value);
      console.log(newDate);
      // filter = this.formatDate(filter);
      filter = new Date(filter);
      if (this.rangeDates['1'] === null) {
        if (newDate === filter) {
          // let startDate = this.formatDate(this.rangeDates['0']);
            let startDate =new Date(this.rangeDates['0']);
            if (startDate <= newDate ) {
            return true;
          }
        }
      } else {
        //  let startDate = this.formatDate(this.rangeDates['0']);
        //  let finishDate = this.formatDate(this.rangeDates['1']);
             let startDate =new Date(this.rangeDates['0']);
             let finishDate =new Date(this.rangeDates['1']);
        console.log(startDate, finishDate);
                  if (startDate <= newDate && finishDate >= newDate) {                                                 {
            return true;
          }
        }
      }
    };
  }

  ngOnInit(): void {
    this.getCompanies();
    this.getMethods();
  }

  getImageLogo(logo) {
    return (logo) ? this.imageProcessingService.getImagePath ('companyLogo','small_thumbnail',logo) : './assets/images/no-image.png' ;
  }

  getFlagImage(code) {
      return './assets/images/CountryCode/' + code + '.gif' ;
  }

  print(event) {
    console.log(event);
  }
  getCompanies() {
    this.companyService.getAllCompanies().takeUntil(this.ngUnsubscribe).subscribe(res => {
      console.log('res', res);
      let temp = res as Array<{
        'id', 'logo', 'company_name', 'company_status_id', 'email',
        'registration_date_time', 'country', 'city', 'status', 'handled_by', 'mobile_number', 'admin_description',
        'company_website', 'company_description', 'company_method_verification_name', 'show_in_home_page', 'company_permissions', 'company_permission_ids','installed_plugin':0,
      }>;
      for (let t of temp) {
        this.companies.push({
          'id': t.id,
          'logo': t.logo,
          'name': t.company_name,
          'date': t.registration_date_time,
          'country': t.country,
          'city': t.city,
          'status': this.reformStatus(t.status),
          'status_id': t.company_status_id,
          'handled_by': t.handled_by,
          'mobile_number': t.mobile_number,
          'email': t.email,
          'admin_description': t.admin_description,
          'company_website': t.company_website,
          'company_description': t.company_description,
          'verification_method': t.company_method_verification_name,
          'display': false,
          'show_in_home_page': t.show_in_home_page ? 'Shown' : 'Hidden',
          'permissions': this.reformPermissions(t.company_permissions),
          //  'permissions':t.company_permissions,
          'company_permission_ids': t.company_permission_ids,
          'installed_plugin':t.installed_plugin? 'Installed' : null,

        });

      }
      this.loading = false;
      // this.filteredCompanies = this.companies;
      this.table.filter(1, 'id', 'startsWith');
      this.table.filter(null, 'id', 'startsWith');
      console.log('companies', this.companies);
    });

  }

  reformStatus(status) {
    switch (status) {
      case 'Golden Verified' :
        return 'golden-verified';
      case 'Silver Verified' :
        return 'silver-verified';
      case 'Verified' :
        return 'verified';
      case 'Not Verified':
        return 'not-verified';
      case 'Seems Fake':
        return 'seems-fake';
      case 'fake':
        return 'fake';
      case 'Not checked':
        return 'not-checked';
      default:
        return 'undefined';
    }
  }

  reformPermissions(permissions){
    let reformedPermissions = [];
    if(permissions.length === 0)
      return '';
    else{
      for(let index in permissions){  
        if(permissions[index] === 'Job Publisher via External link' )
          reformedPermissions.push('Ex link');
        else if(permissions[index] === 'Job Publisher for other companies' )
          reformedPermissions.push('Publisher');
      }
      return reformedPermissions;
    }

  }

  getMethods() {
    this.companyService.getDropDownsData().subscribe(res => {

      // getting statuses
      let temp = res['company_status'];
      for (let t of temp) {
        this.statuses.push({
          'value': t.id,
          'label': t.title
        });
      }
      this.statuses.unshift({'value': null, 'label': ''});
      // getting methods
      let temp2 = res['company_method_verification'];
      for (let t of temp2) {
        this.methods.push({
          'value': t.id,
          'label': t.method_verification_name
        });
      }
      this.methods.unshift({'value': null, 'label': ''});

      this.permissions = res['company_permissions'];
      // let temp3 = res['company_permissions'];
      // for (let t of temp3) {
      //   this.permissions.push({
      //     'value': t.id,
      //     'label': t.name
      //   });
      // }
      //  this.permissions.unshift({'id': null, 'name': ''});
    });
  }


  addToSelected(msg) {
    console.log('selected', this.filteredCompanies);
  }

  displayCompanyLog(company) {
    this.displayCompanyModal = true;
    this.mode = 'log_mode';
    this.companyService.getCompanyLog(company.id).subscribe(res => {
      console.log('res', res);
      let temp = res['data'];
      let temp2 = [];
      for (let t of temp) {
        temp2.push({
          'admin'              : t.user?.admin_name,
          'description'  : t.admin_description,
          'method'       : t.company_method_verification?.method_verification_name,
          'status'             : t.company_status?.title,
          'date'               : t.created_at,
          'permissions': t.company_permissions
        });
      }
      this.companyLog = {
        'id': company.id,
        'name': company.name,
        'log' : temp2
      };
   //   console.log('company log', this.companyLog);
    });
  }

  previewCompanyModal(company, install_plugin = false) {
    this.displayCompanyModal = true;
    this.mode = install_plugin == true ? 'install_plugin_mode' : 'preview_mode';
    this.companyService.getCompanyInfo(company.id).subscribe(res => {
      if (res['company_info']) {
        let comp = res['company_info'];
        this.company = {
          'id': comp.company_id,
          'logo': company.logo,
          'name': comp.company_profile_translations[0].name,
          'username': comp.username,
          'description': comp.company_profile_translations[0].company_description || '',
          'website': comp.company_website,
          'status': comp.company.company_status.title,
          'email': company.email,
          'country': company.country,
          'city': company.city,
          'industry': comp.industries,
          'profile': '/i/c/user.emp/' + company.id,
          'handled_by': company.handled_by,
          'mobile_number': company.mobile_number,
          'message': '',
          'show_in_home_page': comp.company.show_in_home_page,
          'company_method_verification_id': comp.company.company_method_verification_id,
          'admin_description': comp.company.admin_description,
          'company_status_id': comp.company.company_status_id,
          'permissions': comp.company_permissions,
          'company_domain_url': comp.company_domain_url,
          'installed_plugin': comp.installed_plugin
        };
      } else if (res['message']) {
        this.company = {
          'id': null,
          'logo': '',
          'name': '',
          'username': '',
          'description': '',
          'website': '',
          'status': '',
          'email': '',
          'country': '',
          'city': '',
          'industry': '',
          'profile': '',
          'handled_by': '',
          'mobile_number': '',
          'message': res['message'],
          'show_in_home_page': '',
          'company_method_verification_id': '',
          'admin_description': '',
          'company_status_id': '',
          'permissions': '',
          'company_domain_url': '',
          'installed_plugin': 0
        };

      } else if(res['error']) {
        alert(res['error']);
      }

    });
  }



  updateCompanyStatus(event) {
    this.closeModal();
    let index = this.getCompIndex(event['company_id']);
    this.companies[index].admin_description = event['admin_description'];
    this.companies[index].status = this.reformStatus(this.getCatName(event['status_id'], this.statuses)) ;
    // this.companies[index].status = this.getCatName(event['status_id'], this.statuses);
    this.companies[index].handled_by = event['admin_name'];
    this.companies[index].company_domain_url = event['company_domain_url'];
    this.companies[index].installed_plugin = event['installed_plugin'];
    this.companies[index].permissions = this.reformPermissions(event['permissions']);
    //  this.companies[index].permissions = event['permissions'];
    this.closeModal();
  }

  openCoProfile(event) {
    this.closeModal();
    //  this.router.navigate(['/i/c/user.emp/' + event['id']]);
    window.open(('/i/c/user.emp/' + event['id']));
  }

  getCatName(id, catArray) {
    for (let cat of catArray) {
      if (id === cat.value) {
        return cat.label;
      }
    }
  }

  closeModal() {
    this.company = {
      'id': null, 'name': '', 'username': '', 'description': '', 'website': '', 'logo': '',
      'status': '', 'email': '', 'country': '', 'city': '', 'industry': '', 'profile': '', 'handled_by': '',
      'mobile_number': '', 'message': '', 'show_in_home_page': '', 'company_method_verification_id': '',
      'admin_description': '', 'company_status_id': '', 'permissions': '','installed_plugin':0,'company_domain_url':''
    };
    this.displayCompanyModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').removeClass('modal-open');

  }



  closeAddModal() {
    this.displayModal = false;
    $('div.modal-backdrop.fade.in').remove();
    $('body').addClass('modal-open');
  }


  displayDeleteAlert(num, msgId?) {
    this.opperationNum = num;
    if (msgId) {
      this.msgIdToDelete = msgId;
    }
    this.displayModal = true;
  }



  getCompIndex(msgId) {
    for (let i = 0; i < this.companies.length; i++) {
      if (this.companies[i].id === msgId) {
        console.log('index', i);
        return i;
      }
    }
  }

  onDateSelect(value) {
    this.table.filter(this.formatDate(value), 'date', 'contains');
  }

  formatDate(date) {
    let month = date.getMonth() + 1;
    let day = date.getDate();

    if (month < 10) {
      month = '0' + month;
    }

    if (day < 10) {
      day = '0' + day;
    }

    return date.getFullYear() + '-' + month + '-' + day;
  }


  clearAll() {
    this.table.filter(null, 'id', 'startsWith');
    this.table.filter(null, 'name', 'contains');
    this.table.filter(null, 'logo', 'contains');
    this.table.filter(null, 'country', 'contains');
    this.table.filter(null, 'city', 'contains');
    // this.table.filter(null, ['country', 'city' ], 'contains');
    // this.table.filter(null, 'status', 'equals');
    this.table.filter(null, 'status_id', 'equals');
    this.table.filter('', 'date', 'contains');
    this.table.filter(null, 'handled_by', 'contains');
    this.table.filterGlobal(null, 'contains');
    $('span.ui-column-filter.ui-calendar input').val(null);
    $('.ui-table-globalfilter-container input').val(null);
    console.log($('.ui-column-filter').val());
    $('.ui-column-filter').val(null);
    this.status = '';
    this.rangeDates = [new Date(''), new Date('')];
    this.calendar.onClearButtonClick('');
  }

   toggleShowInHome(company){
    let show;
     if(company.show_in_home_page === 'Hidden'){
      show = {'show_in_home_page':1};
    }
     else if(company.show_in_home_page === 'Shown'){
      show = {'show_in_home_page':0};
    }

     this.companyService.showCompanyInHomePage(company.id,show).subscribe(res=>{
      if(res['error'])
        alert(res['error']);
      else{
        if(company.show_in_home_page === 'Hidden'){
          company.show_in_home_page = 'Shown';
        }
        else if(company.show_in_home_page === 'Shown'){
          company.show_in_home_page = 'Hidden';
        }
      }
    });
  }

  deleteCompany() {

  }

  deleteMultiCompanies() {

  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
