import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { GeneralService } from '../../../../general/services/general.service';
import { DataMap } from 'shared/Models/data_map';
declare var $: any;

@Component({
  selector: 'app-location-filter',
  templateUrl: './location-filter.component.html',
  styleUrls: ['./location-filter.component.css']
})
export class LocationFilterComponent implements OnInit {
  locationForm:FormGroup;
  distanceTool = [
    { 'value': '', 'label': '' },
    { 'value': 'km', 'label': 'KM' },
    { 'value': 'mi', 'label': 'Miles' }
  ];
  initFilters = {};
  initFiltersTags = [];
  tags = [];

  //backupfilters used in case user changed some filters then clicked on cancel modal without 
  //clicking on apply filters, so we reset the filters to last applied state
  backupfilters = {
    "filters":{},
    "tags":[]
  }

  @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: any;
  data_map = new DataMap();
  
  constructor(
    private fb: FormBuilder,
    private generalService:GeneralService) {}

  ngOnInit(): void {
    this.initializeForm();
    this.sendInitStateToWarapper();

    this.generalService.internalMessage.subscribe( (data) => {
      if (data['message'] === 'removedFilters' && data['dist'] === 'location_filter') {
        this.clearFilters();
      }

      if (data['message'] === 'clearAllFilters' && data['src'] === 'filters-wrapper'){
        this.clearFilters();
      }

      if (data['message'] === 'modalCancel' && data['src'] === 'filters-wrapper'){
        this.locationForm.patchValue(this.backupfilters.filters);
        this.tags = this.backupfilters.tags;
      }
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.getLocationPlacefromLocationAutoComplete();
    }, 1);
  }

  initializeForm(){
    this.locationForm = this.fb.group({
      country:[''],
      city:[''],
      location: [''],
      latitude: [''],
      longitude: [''],
      distance: [''],
      unit_distance: [{ "value": "", "label": "" }]
    });
  }

  setTags(){
    let location = '';
    if(this.locationForm.controls['distance'].value !=='' && this.locationForm.controls['unit_distance'].value !== '')
      location = this.locationForm.controls['location'].value + ', distance:'+ this.locationForm.controls['distance'].value + this.locationForm.controls['unit_distance'].value.label;
    else 
      location = this.locationForm.controls['location'].value;

    this.tags = [
      {"name":"location" ,"title":"Location", "value":location , "type":"string"},
    ]
    return this.tags;
  }

  sendFilters() {
    this.generalService.notify('filters-changes', 'location_filter', 'filters-wrapper', {"filters":this.locationForm.value, "tags":this.setTags()});
    
    this.backupfilters.filters = this.locationForm.value;
    this.backupfilters.tags = this.tags;
    
    $('#filtersModal').modal('hide');
   }
  
   sendInitStateToWarapper() {
    this.initFilters = this.locationForm.value;
    this.initFiltersTags = this.setTags();

    this.backupfilters.filters = this.initFilters;
    this.backupfilters.tags = this.initFiltersTags;

    this.generalService.notify('init-filters','location_filter', 'filters-wrapper', {'filters':this.initFilters,'tags':this.initFiltersTags} );
   }

   clearFilters(){
    this.locationForm.patchValue(this.initFilters);
    this.tags = this.initFiltersTags;
   }

  // Start location functions
  private getLocationPlacefromLocationAutoComplete() {
    const autocomplete_location = new google.maps.places.Autocomplete(this.googlelocationplaceLocationRef.nativeElement,
      {
        types: ['country','locality'],  // 'establishment' / 'address' / 'geocode'
        fields: ['address_components','geometry']
      });

    autocomplete_location.addListener('place_changed', (res) => {
      const place_location = autocomplete_location.getPlace();
      this.getLocationAddressInfo(place_location);
    });
  }

  getLocationAddressInfo(place: object) {
    let data_location = this.data_map.getLocationDataFromGoogleMap(place);
    this.locationForm.controls['country'].setValue(data_location.country);
    this.locationForm.controls['city'].setValue(data_location.city);
    let location = data_location.country;
    if(data_location.city)
      location = location + ', ' + data_location.city;
    this.locationForm.controls['location'].setValue(location);
    this.locationForm.controls['latitude'].setValue(data_location.latitude);
    this.locationForm.controls['longitude'].setValue(data_location.longitude);
  }
  // End location functions

}
