<!-- <app-pre-loader [show]="dataModel.showLoader && dataModel.firstLoad"></app-pre-loader> -->
<div>

    <div class="alert alert-info" role="alert" *ngIf="dataModel.folder === 5">
        <p>CVs that have been in the trash for more than 30 days will be deleted automatically.</p>
    </div>

    <div class="tags-container" *ngIf="currentFiltersTags.length > 0">
        <div class="tags-col-1">
            <div class="single-tag" *ngFor="let tag of currentFiltersTags; let i=index;">
                <ng-container *ngIf="tag.type === 'string'">
                    <span class="selectLabel"> {{tag.title}}</span>
                    <span class="tag-label"> {{tag.value}}
                        <button (click)="removeFilter(tag)" class="close2">
                            <span aria-hidden="true">&#10006;</span>
                        </button>
                    </span>
                </ng-container>     
                <ng-container *ngIf="tag.type === 'array' && tag.value.length > 0">
                    <span class="selectLabel"> {{tag.title}}</span>
                    <ng-container *ngFor="let value of tag.value">
                        <span class="tag-label"> {{value.name}}
                            <button (click)="removeFilter(tag,value)" class="close2">
                                <span aria-hidden="true">&#10006;</span>
                            </button>
                        </span>
                    </ng-container>
                </ng-container>    
            </div>  
        </div>
        <div class="tags-col-2">
            <button (click)="clearAllFilters()"  class="close2" style="font-size: 24px; background-color: white;"><span aria-hidden="true">&#10006;</span></button>
        </div>
    </div>

    <p class="total-filtered-cvs">Total Filtered Cvs:  {{dataModel.totalFilteredCvs}}</p>
    
    <p-table 
    *ngIf="!cvPreviewMode" 
    [loading]="dataModel.showLoader"  
    [value]="dataModel.resumes" 
    styleClass="rec-cvs-table" 
    [rowHover]="true" 
    [responsive]="true" 
    [autoLayout]="true" 
    (sortFunction)="customSort($event)" [customSort]="true"
    cdkDropList
    [cdkDropListData]="dataModel.resumes"
    cdkDropListSortingDisabled
    (cdkDropListDropped)="drop($event)"
    >
        <ng-template pTemplate="header">
            <tr>
                <th class="table-header-settings">
                    <input type="checkbox" class="toggle-checkbox" (change)="toggleSelectAllCVs($event)">
                    <span (click)="showColsManager()" class="pi pi-cog colSelectIc"></span>
                </th>
                <th *ngFor="let col of dataModel.columns;let i = index"  [pSortableColumn]="col.field">{{col['title']}}
                    <p-sortIcon [field]="col.field"></p-sortIcon>
                    <!-- <span (click)="showColsManager()" *ngIf="i == dataModel.columns.length -1" class="pi pi-cog colSelectIc"></span> -->
                </th>
                <th style="width:92px;"></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-cv>

            <!-- cdkDrag -->
            <tr *ngIf="dataModel.isMobileLayout" [ngClass]="{ 'opened-cv':cv.read===1 , 'not-opened-cv': !cv.read}">
                <td style="position:relative;cursor: pointer;">
                   
                    <div class="cv-summary-mobile">
                        <div class="cv-summary-mob-content" (click)="changeStatus(cv, null ,'read' , 1 , 'cv_folder/read' ,'')">
                            <div class="logo-div">
                                <img src="{{getImage(cv['photo'] , cv['gender'] ) }}" class="circle-img" alt="{{cv['name']}}">
                            </div>
                            <div class="content-div">
                                <div class="comp-name">
                                    <span>
                                        {{cv['name']}}
                                    </span>
                                </div>
                               
                                <div *ngIf="cv['job_adv_titles'] && cv['job_adv_titles'].length" title="{{cv['job_adv_titles']}}">
                                    <span *ngFor="let value of cv['job_adv_titles'] | slice:0:2; let i = index">
                                        {{value}}<span *ngIf="i < cv['job_adv_titles'].length - 1">,<span *ngIf="i == 1">...</span></span>
                                    </span> 
                                    <!-- &nbsp;
                                    <a (click)="getResumeAppFolders($event,cv)" pTooltip="Job advs info"  tooltipPosition="top" class="adv-info-btn">
                                        <i id="options" class="fa fa-info" aria-hidden="true"></i>
                                    </a> -->
                                </div>
                                
                                <div title="{{cv['job_types']}}" *ngIf="cv['job_types'] && cv['job_types'].length">
                                    <img src="./assets/images/icons/emp-type.svg" alt="Job Type" style="width:18px;">
                                    <span *ngFor="let value of cv['job_types'] | slice:0:2; let i = index">
                                        {{value}}<span *ngIf="i < cv['job_types'].length - 1">,<span *ngIf="i == 1">...</span></span>
                                    </span>
                                </div>
                                <div *ngIf="cv['country']">
                                    <span>
                                        <img [src]="getFlagImage(cv['country_code'])" width="17" style="margin-right: 5px;" />
                                        {{cv['country']}}
                                    </span>
                                    <span *ngIf="cv['city']">,
                                        {{cv['city']}}
                                    </span>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                        </div>

                        <div class="info-div-mob">
                            <a (click)="getResumeAppFolders($event,cv)" pTooltip="Job advs info"  tooltipPosition="top" class="adv-info-btn">
                                <i id="options" class="fa fa-info" aria-hidden="true"></i>
                            </a>
                        </div>
                        <div class="select-cv-checkbox-div">
                            <input type="checkbox" (change)="collectSelectedCVs($event,cv)" class="select-cv-checkbox">
                        </div>
                        <div class="cv-summary-mob-options">
                                <!-- (click)="changeStatus(cv , 'delete_cv', '', 'delete_resume' , 'Are you sure you want to delete this CV?' )" -->
                            <i id="options" (click)="confirmDeleteCV(cv)"  class="fa fa-trash " aria-hidden="true " style="font-size: 16px; color: #767676;cursor:
                            pointer;padding: 5px; " pTooltip="Delete"  tooltipPosition="top"></i>

                            <i id="options " *ngIf="!cv.is_deleted " (click)="changeStatus(cv , null ,'read_button' ,cv.read?0:1 , 'cv_folder/read') " class="fa " [ngClass]="{ 'fa-envelope':cv.read===1 , 'fa-envelope-open-o': !cv.read} " aria-hidden="true
                            " style="font-size: 16px; color: #767676;cursor: pointer;padding: 5px; " pTooltip="{{cv.read===1 ? 'Mark as unread' : 'Mark as read'}}"  tooltipPosition="top"></i>
                            
                        </div>
                    </div>

                </td>
            </tr>
            <tr class="ui-selectable-row tr-hover"  *ngIf="!dataModel.isMobileLayout" style="position:relative" [ngClass]="{ 'opened-cv':cv.read===1 , 'not-opened-cv': !cv.read} " cdkDrag>
                <td class="drag-preview">
                    <div *cdkDragPreview class="cdk-drag-preview">
                        <span *ngIf="dataModel.folder !==null">Move "{{cv.name}}" CV</span>
                        <span *ngIf="dataModel.folder ===null">Copy "{{cv.name}}" CV</span>
                    </div>

                    <div *cdkDragPlaceholder></div>
                </td>
                
                <td  style="text-align:center;">
                    <input type="checkbox" (change)="collectSelectedCVs($event,cv)" class="select-cv-checkbox">
                    <a (click)="getResumeAppFolders($event,cv)" pTooltip="Job advs info"  tooltipPosition="top" class="adv-info-btn">
                        <i id="options" class="fa fa-info" aria-hidden="true"></i>
                    </a>
                    <!-- <label *ngIf="!cv.is_deleted" [id]="cv" class="cust-checkbox" pTooltip="{{cv.favourite == 1 ? 'Favourite' : 'Unfavourite'}}"  tooltipPosition="top">
                        <input
                            type="checkbox"
                            [checked]="cv.favourite"
                            (click)="makeFavourite(cv)"
                        >
                        <i  *ngIf="cv.favourite == 1; else  elseIcon" class="fa fa-star"></i>
                        <ng-template #elseIcon>
                        <i   class="fa fa-star-o"></i>
                        </ng-template>
                    </label> -->
                </td>
                <td (click)="changeStatus(cv, null, 'read' , 1 , 'cv_folder/read' ,'')"  *ngFor=" let col of dataModel.columns ">
                    <div  *ngIf=" col[ 'field']=='photo' ; else elseBlock ">
                        <img style="width:50px;" class="circle-img" src="{{getImage(cv[col[ 'field']] , cv['gender'] ) }} " alt="{{cv['name']}}">
                    </div>
                    <ng-template #elseBlock>
                        <div *ngIf="col.field !== 'skill'  && col.field !== 'languages' && col.field !== 'nationality' 
                                    && col.field !== 'educations' && col.field !== 'availability' && col.field !== 'job_adv_titles'  
                                    && col.field !== 'certification' && col.field !== 'open_to_work'">
                            <span *ngIf="col.field === 'country' && cv['country_code'] !== null">
                                <img src="./assets/images/CountryCode/{{cv['country_code']}}.gif">
                            </span>
                            {{cv[col['field']]}}
                        </div>

                        <div title="{{cv[col.field]}}" *ngIf="col.field === 'job_adv_titles' || col.field === 'skill' || col.field === 'languages' || col.field === 'educations' || col.field === 'availability'">
                            <span *ngFor="let value of cv[col.field] | slice:0:2; let i = index">
                                {{value}}<span *ngIf="i < cv[col.field].length - 1">,<span *ngIf="i == 1">...</span></span>
                            </span>
                        </div>
                        <div title="{{cv[col.field]}}" *ngIf="col.field === 'nationality'">
                            <span *ngFor="let value of cv[col.field] | slice:0:1; let i = index">
                                {{value}}<span *ngIf="i < cv[col.field].length - 1">,<span *ngIf="i == 0">...</span></span>
                            </span>
                        </div>
                        <div title="{{cv[col.field]}}" *ngIf="col.field === 'certification'">
                            {{cv[col['field']] | slice:0:20}}
                            <span *ngIf="cv[col['field']]?.length>20">...</span>
                        </div>
                        <div *ngIf="col.field === 'open_to_work'">
                            <span pTooltip="{{cv[col['field']].tips}}"  tooltipPosition="top" class="{{cv[col['field']].class}}">
                                {{cv[col['field']].value}}
                            </span>
                        </div>
                        <!-- <div>{{cv[col['field']]}}</div> -->
                    </ng-template>
                </td>
                <td class="actions-col actions-col-res">
                        <!-- (click)="changeStatus(cv , 'delete_cv', '', 'delete_resume' , 'Are you sure you want to delete this CV?' )" -->
                    <a pTooltip="Delete"  tooltipPosition="top" (click)="confirmDeleteCV(cv)">
                    <i id="options" class="fa fa-trash" aria-hidden="true" style="font-size: 18px; color: #80817f;cursor:
                    pointer;padding: 5px; "></i>
                    </a>    
                    
                    <a  (click)="changeStatus(cv , null , 'read_button' , cv.read?0:1 , 'cv_folder/read')" pTooltip="{{cv.read===1 ? 'Mark as unread' : 'Mark as read'}}"  tooltipPosition="top">
                        <i id="options "  class="fa " [ngClass]="{ 'fa-envelope':cv.read===1 , 'fa-envelope-open-o': !cv.read} " aria-hidden="true
                        " style="font-size: 17px; color: #80817f;cursor: pointer;padding: 5px; "></i>
                    </a>
                    <span *ngIf="cv.is_deleted" style="color:#80817f;font-size:11px;">Del.by user</span>
                </td>
            </tr>
        </ng-template>
       
    </p-table>

    <div class="no-resumes" *ngIf="dataModel.resumes.length == 0 && dataModel.showLoader == false && !cvPreviewMode">
        <p>No Matched Cvs Found On This Folder </p>
    </div>

    <app-paginator *ngIf="!cvPreviewMode" [totalPages]="dataModel.totalPages" [currentpage]="dataModel.page_number"></app-paginator>
    <!-------Previewer For  CV After Click On name--------->
    <!------- Tool bar  For CV function -------->
    <app-cv-previewer-toolbar 
        *ngIf="cvPreviewMode" 
        [currentCv]="dataModel.currentCv" 
        [folder]="dataModel.folder" 
        [sourceInterface]="'cvs-folders'"
        [foldersddData]="foldersddData"
        (toolBarEvent)="getCvToolBarEvent($event)">
    </app-cv-previewer-toolbar>
    <!------- CV Previewer  -------->
    <div *ngIf="cvPreviewMode">
        <!-- note: using display: inline-block here fixed issue in the div after using position:sticky in sidebar -->
        <div style="display:inline-block;;width:100%;" *ngIf="this.dataModel.showCVeek===true; else  elseShowPDF">
            <!-- [currentCv]="dataModel.currentCv"  -->
            <resume-preview  
                [resumeId]="dataModel.currentCv.resume_id"
                [fromRecieveCvsInterface]="true"
            >
            </resume-preview>
        </div>

        <ng-template #elseShowPDF>
            <input type="text" hidden placeholder="PDF src" [(ngModel)]="pdfSrc">
            <div style="position:relative;">
                <app-pre-loader [show]="pdfViewerLoader"></app-pre-loader>
                <pdf-viewer  
                    [src]="pdfSrc"  
                    [render-text]="true" 
                    [autoresize]="true"
                    [original-size]="false"
                    style="display: block;margin-top: 12px;"
                    (after-load-complete)="stopPdfViewerLoader($event)">
                </pdf-viewer>
            </div>
        </ng-template>
    </div>
    
  </div>


<div *ngIf="colsReadyPassToModal">
    <cols-management [allItems]="dataModel.dataColumnsWithoutStandard" [selectedItems]="dataModel.columnsWithoutStandard" [standardItems]="dataModel.standardColumns" [maxColAllowed]=8 [fromComponent]="'cvs-table'" (closeModalPopup)="handleColsSelection($event)">
    </cols-management>
</div>


<div class="modal fade image-editor-modal" id="jobAdvInfoModal"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Job Advs info</h4>
            </div>
            <app-job-advs-info-modal
                *ngIf="dataModel.currentCv?.resume_id !==undefined"
                [currentCv]="dataModel.currentCv"
                >
            </app-job-advs-info-modal>
        </div>
    </div>
</div>        

<div class="modal fade image-editor-modal" id="confirmDeleteCVModal"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label">Delete CV</h4>
            </div>
            <div class="modal-body">
                <app-pre-loader [show]="modalLoader" [size]="'small'"></app-pre-loader>
                <p>
                    Are you sure you want to delete this CV?
                </p>
                <div *ngIf="selectAction === null" class="text-right">
                    <button *ngIf="dataModel.currentCv.show_move_trash_btn" type="button" class="btn btn-warning" (click)="changeStatus(dataModel.currentCv, null , 'delete_cv', '', 'delete_resume' , '', 'move_to_trash' )">Move to trash</button> &nbsp;
                    <button type="button" class="btn btn-danger" (click)="changeStatus(dataModel.currentCv, null , 'delete_cv', '', 'delete_resume' , '', 'permanent_delete' )">Permanent delete</button> &nbsp;
                    <button type="button" class="btn btn-default cust-cancel-btn" (click)="cancelConfirmDeleteCVModal()">Cancel</button>
                </div>
                <div *ngIf="selectAction === 'deleteSelectedCVS'" class="text-right">
                    <!-- <button *ngIf="dataModel.currentCv.show_move_trash_btn" type="button" class="btn btn-warning" (click)="changeStatus(dataModel.currentCv , 'delete_cv', '', 'delete_resume' , '', 'move_to_trash' )">Move to trash</button> &nbsp; -->
                    <button type="button" class="btn btn-danger" (click)="changeStatus(null, resumes_ids , 'delete_cv', '', 'delete_resume' , '', 'permanent_delete' )">Permanent delete</button> &nbsp;
                    <button type="button" class="btn btn-default cust-cancel-btn" (click)="cancelConfirmDeleteCVModal()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div> 

<!-- *ngIf="selectAction === 'moveSelectedCVSToFolders' && resumes_ids.length > 0" -->
<div *ngIf="resumes_ids.length > 0">
    <app-move-cv-modal
    [currentCv]="null"
    [resumes_ids]="resumes_ids"
    [foldersddData]="foldersddData"
    [sourceInterface]="'cvs-folders'"
    (closeModalPopup)="handleMoveCvPopup($event)"
>
</app-move-cv-modal>
</div>