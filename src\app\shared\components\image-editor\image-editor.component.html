<div class="text-center">
    <div class="upload-button-container">
        <label class="image-upload-button" for="upload-image">
            <input type="file" accept="image/jpeg, image/png" (change)="fileChangeEvent($event)" (click)="fileClickEvent($event)" id="upload-image" />
            <span *ngIf="this.showCropper === false">Select image to upload</span>
            <span *ngIf="this.showCropper === true">{{imageName}}</span>
        </label>
    </div>
    
    <div class="image-edit-tools" [style.display]="showCropper ? null : 'none'">
        <button type="button" (click)="zoomIn()" class="btn btn-zoom">
            <i class="fa fa-search-plus" aria-hidden="true"></i>
        </button>
        <button type="button" (click)="zoomOut()" class="btn btn-zoom">
            <i class="fa fa-search-minus" aria-hidden="true"></i>
        </button>
    </div>
</div>

<!-- [maintainAspectRatio]="false"  //without specific aspect ratio -->
<!-- output format used is jpeg so i can add background color of white to any extra 
space added to make it squared image, for displaying purposes -->
<div *ngIf="initialStateVisibility">
    <image-cropper
        [imageChangedEvent]="imageChangedEvent"
        [maintainAspectRatio]="true"
        [containWithinAspectRatio]="true"
        [aspectRatio]="1 / 1"
        [transform]="transform"
        [onlyScaleDown]="false"
        [alignImage]="'center'"
        [format]="'jpeg'"
        [backgroundColor]="'#fff'"
        (imageCropped)="imageCropped($event)"
        (imageLoaded)="imageLoaded()"
        (cropperReady)="cropperReady()"
        (loadImageFailed)="loadImageFailed()"
        [style.display]="showCropper ? null : 'none'"
    ></image-cropper>
</div>


<!-- <img [src]="croppedImage" /> -->

<div class="imgError-div">
    <span *ngIf="imgError" class="error-message" translate>{{imgError}}</span>
</div>   

<div class="modal-btns text-center">
    <button type="button" class="btn cust-cancel-btn cust-btn" (click)="cancel()">Cancel</button> 
    <button type="button" class="btn btn-success cust-btn" (click)="doneEditing()">Done</button>
</div>