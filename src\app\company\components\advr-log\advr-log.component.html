<div class="modal-body-container">
    <div class="modal-body">
        <div class="preloader" *ngIf="!loaderStop">
            <div class="infinity">
                <div>
                    <span></span>
                </div>
                <div>
                    <span></span>
                </div>
                <div>
                    <span></span>
                </div>
            </div>
        </div>
        <div *ngIf="loaderStop" style="margin:5%; margin-top: 20px;">
            <label style="color: #7381dd;font-size: 30px;">{{ AdvTitle }}</label>
            <p-table #dt [value]="data_Source" styleClass="ui-table-customers" [reorderableColumns]="true" [rows]="4" [showCurrentPageReport]="true" [loading]="loading" [responsive]="true" [filterDelay]="0">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="header_style" translate>Action</th>

                        <th class="header_style" translate>Admin</th>

                        <th class="header_style" translate>Date / Time</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" class="body ui-table-tbody" let-data let-columns="columns" let-index="rowIndex">
                    <tr class="ui-selectable-row" id="{{ data.id }}" [pReorderableRow]="index">
                        <td style="text-align: center; font-size: 20px;">
                            <span *ngIf="AdvType != 'template' && data.job_advertisement_action"> {{ data.job_advertisement_action.job_advertisement_action_translation[0].action }}</span>
                            <span *ngIf="AdvType === 'template'"> {{ data.template_action.job_advertisement_action_translation[0].action }}</span>
                        </td>
                        <td style="text-align: center; font-size: 20px;">
                            <span> {{ data.user?.display_name }}</span>
                            <span *ngIf="data.user === null">System</span>
                        </td>
                        <td style="text-align: center; font-size: 20px;">
                            <span> {{ data.created_at }}</span>
                        </td>

                    </tr>
                </ng-template>
            </p-table>
        </div>

    </div>
</div>