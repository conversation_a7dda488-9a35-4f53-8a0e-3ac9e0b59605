.form-padding{
    padding:0 25px;
}
.label-fixed{
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    color:#4f94df;
    font-weight: normal;
}
.label-fixed-dd{
    align-items: center;
}
.equal-height-row-cols{
    display: flex;
    flex-flow: row wrap;
}
 /* unify placeholer style for all different controls */
::placeholder , :host ::ng-deep .ui-dropdown label.ui-dropdown-label ,:host ::ng-deep .ui-autocomplete ::placeholder , :host ::ng-deep .ui-inputtext { /* ::placeholder Chrome, Firefox, Opera, Safari 10.1+ */
    color: #808080;
    opacity: 1; /* Firefox */
    font-weight: normal;
    font-family: "Open Sans", "Helvetica Neue", sans-serif !important;
    font-size: 15px !important;
}

:host ::ng-deep .unit_distance{
    height: 100%;
}
@media screen and (max-width:767px){
    .label-fixed{
        padding-left:0;
        justify-content: flex-start;
    }
    .mar-top-mob{
        margin-top: 35px;
    }
}