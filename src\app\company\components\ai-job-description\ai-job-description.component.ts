import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { AiJobGeneratorService } from '../../services/ai-job-generator.service';

declare var $: any;

@Component({
  selector: 'app-ai-job-description',
  templateUrl: './ai-job-description.component.html'
})
export class AiJobDescriptionComponent implements OnInit {
  @Output() generatedJobData = new EventEmitter<any>();

  jobText: string = '';
  isLoading: boolean = false;

  constructor(private aiJobGeneratorService: AiJobGeneratorService) { }

  ngOnInit() { }

  generateDescription() {
    if (!this.jobText) return;

    this.isLoading = true;
    this.aiJobGeneratorService.generateJobDescription(this.jobText)
      .subscribe(
        (response) => {
          this.isLoading = false;
          const processedData = this.processAiResponse(response);

          // Emit the processed data
          this.generatedJobData.emit(processedData);

          // Close the modal
          $('#aiJobDescriptionModal').modal('hide');
          // this.jobText = '';
        },
        (error) => {
          this.isLoading = false;
          console.error('Error generating job description:', error);
        }
      );
  }

  open() {
    // this.jobText = '';
    $('#aiJobDescriptionModal').modal('show');
  }

  // Optimize processAiResponse method to ensure job titles are properly formatted
  private processAiResponse(response: any): any {
    if (!response) return {};

    const processedData = { ...response };

    // Ensure all fields are properly formatted

    // Job titles
    if (processedData.job_titles) {
      if (!Array.isArray(processedData.job_titles)) {
        processedData.job_titles = [processedData.job_titles];
      }
    } else {
      processedData.job_titles = [];
    }

    // Employment types
    if (processedData.employment_types) {
      if (!Array.isArray(processedData.employment_types)) {
        processedData.employment_types = [processedData.employment_types];
      }
    } else {
      processedData.employment_types = [];
    }

    // Skills
    if (processedData.skills) {
      if (!Array.isArray(processedData.skills)) {
        processedData.skills = [processedData.skills];
      }
    } else {
      processedData.skills = [];
    }

    // Nationalities
    if (processedData.nationalities) {
      if (!Array.isArray(processedData.nationalities)) {
        processedData.nationalities = [processedData.nationalities];
      }
    } else {
      processedData.nationalities = [];
    }

    // Languages
    if (processedData.languages) {
      if (!Array.isArray(processedData.languages)) {
        processedData.languages = [processedData.languages];
      }
    } else {
      processedData.languages = [];
    }

    return processedData;
  }
}





