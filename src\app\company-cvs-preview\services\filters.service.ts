import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';

@Injectable({
  providedIn: 'root'
})
export class FiltersService {
  baseUrl = '';
   
  constructor(
    private http: HttpClient ,
    private privateSharedURL: ExportUrlService) {
      this.privateSharedURL.publicUrl.take(1).subscribe(data => {
        this.baseUrl = data;
      });
     }


  searchJobSeekerName(name, type, folder_id) {
    let nameUrl = this.baseUrl + 'search_job_seeker_name';
    let params = new HttpParams();
    params = params.append('name', name);
    params = params.append('type', type);
   // params = params.append('folder_id', folder_id);
    return this.http.post(nameUrl, folder_id ,{ params: params})
   // return this.http.get(nameUrl,{ params: params})
  }

  getResumeFiltersLists(){
    return this.http.get(this.baseUrl + 'resumes_filters');
  }

  getCountriesLists(){
    return this.http.get(this.baseUrl + 'data_list?type=countries&lang_id=1&paginate=0');
  }
}
