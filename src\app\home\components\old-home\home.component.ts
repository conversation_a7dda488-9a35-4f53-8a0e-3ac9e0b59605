// import { Component, OnInit, OnD<PERSON>roy, ɵɵInheritDefinitionFeature } from '@angular/core';
// import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { Title, Meta } from '@angular/platform-browser';
import { GeneralService } from '../../../general/services/general.service';
import { HomeService } from '../../services/home.service';
import { fade, UpDown} from 'shared/animations/animations';
import { MessageService } from 'primeng/api';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
declare var $: any;

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
  providers: [MessageService],
  animations: [
     UpDown, fade 
  ]
})
export class HomeComponent implements OnInit , OnD<PERSON>roy {
  responsiveOptions = [];
  templates = [];
  templatesCount: any = 35;
  role = '';
  cvTemplatesUrl = '';
  showLoader = true;
  // sliderLoader = false;
  // displayVideo = false;
  pageInfo = { 'direction': '', 'country': '' };
  lazyloadItems : boolean[]= [false,false, false, false,false,false,false];
  showMyElement : boolean;
  
  inHomePage:boolean = true;
  // private mySubscription: any;
  private ngUnsubscribe: Subject<any> = new Subject();

  goldCompanies = [];
  silverCompanies = [];
  compLogoPath = '';
  actionAfterLogin = null;
  currentApplyCompanyInfo;
  constructor(private router: Router,
              private title: Title,
              private meta:Meta,
            //  private route: ActivatedRoute,
              private generalService: GeneralService,
              private homeService:HomeService,
              private messageService: MessageService,
              private imageProcessingService : ImageProcessingService
              ) 
              {
                  // this.router.routeReuseStrategy.shouldReuseRoute = function () {
                  //   return false;
                  // };
                  // this.mySubscription = this.router.events.takeUntil(this.ngUnsubscribe).subscribe((event) => {
                  //   if (event instanceof NavigationEnd) {
                  //     // Trick the Router into believing it's last link wasn't previously loaded
                  //     this.router.navigated = false;
                  //   }
                  // });

   }

  ngOnInit() {
    // let country = localStorage.getItem('country');
    // if(country && country !=="")
    //   this.router.navigate([country]);

    // this.getIp().subscribe(res=>{
    //   console.log(res);
    //   let detectedCountry = '';
    //   if(res['location'].country.name){
    //     detectedCountry = res['location'].country.name;
    //     console.log(detectedCountry);
    //     if(detectedCountry === 'Germany'){
    //       this.router.navigate(['/syria']);
    //     }
    //   }
    // });
    // this.getIp();

    if(localStorage.getItem("role")){
      this.role = localStorage.getItem("role");
      // if(this.role === 'ROLE_ADMIN'){
      //   this.role = 'unauth';
      // }
    }
    else{
      this.role = 'unauth';
    }

    this.title.setTitle('CVeek | Best website to Create Free CV online Free Templates | Best Jobs');
    this.meta.updateTag({ name: 'description', content: 'CVeek | Best website to Create Free professional CV and resume online with Free Creative Templates | Find Best jobs online with high Salary in Qatar, Gulf and world'});

    // set country and direction
    //function setPageInfo commented temporarily and added instead these two lines
    this.pageInfo.country= null;
    this.pageInfo.direction = 'ltr';
  //  this.setPageInfo();

    this.generalService.internalMessage.subscribe((data) => {
      if( data['message'] === 'roleChanged' && data['src'] === 'auth-service') {
        this.role = data['mData'].role;

        //logout state: refresh companies directly applied value
        for(let company of this.goldCompanies)
          company.directly_applied=false;

        for(let company of this.silverCompanies)
          company.directly_applied=false;
        }

      if (data['src'] === 'login') {
        if (data['message'] === 'loginSuccess' && this.actionAfterLogin === 'apply' && this.currentApplyCompanyInfo!==undefined) {
          $('#authModal').modal('hide');
          this.role = localStorage.getItem('role');
          this.applyToCompany(this.currentApplyCompanyInfo.company,this.currentApplyCompanyInfo.companyType,true);
        }
      }
    });


    //slide animation
    // setTimeout(() => {
    //   //$(".your").css({"-webkit-animation":"your-anim 1s linear 1","-moz-animation":"your-anim 1s linear 1","animation":"your-anim 1s linear 1","top":"20%"});
    //   $(".your").css({"-webkit-animation":"because-anim 600ms ease-in","opacity":"1"});
    // });
    // setTimeout(() => {
    //   //$(".your").css({"-webkit-animation":"your-anim 1s linear 1","-moz-animation":"your-anim 1s linear 1","animation":"your-anim 1s linear 1","top":"20%"});
    //   $(".day").css({"-webkit-animation":"day-anim 800ms ease-in","right":"3%"});
    // },700);
    // setTimeout(() => {
    //   //$(".your").css({"-webkit-animation":"your-anim 1s linear 1","-moz-animation":"your-anim 1s linear 1","animation":"your-anim 1s linear 1","top":"20%"});
    //   $(".because").css({"-webkit-animation":"because-anim 600ms ease-in","opacity":"1"});
    // },1500);
    // setTimeout(() => {
    //   //$(".your").css({"-webkit-animation":"your-anim 1s linear 1","-moz-animation":"your-anim 1s linear 1","animation":"your-anim 1s linear 1","top":"20%"});
    //   $(".colleagues").css({"-webkit-animation":"colleagues-anim 700ms ease-in","right":"5%"});
    // },2300);
    // setTimeout(() => {
    //   //$(".your").css({"-webkit-animation":"your-anim 1s linear 1","-moz-animation":"your-anim 1s linear 1","animation":"your-anim 1s linear 1","top":"20%"});
    //   $(".deal").css({"-webkit-animation":"deal-anim 700ms ease-in","opacity":"1"});
    // },3200);

    // this.privateSharedURL.publicUrl.take(1).subscribe(url => {
    //   this.cvTemplatesUrl = url + 'cvTemplates/';
    // });

    // for carousel
    // for (let i = 1; i <= this.templatesCount; i++) {
    //   this.templates.push( this.cvTemplatesUrl + 'template'+i+'.png');
    // }

  //   this.responsiveOptions = [
  //     {
  //         breakpoint: '800px',
  //         numVisible: 5,
  //         numScroll: 5
  //     },
  //     {
  //         breakpoint: '650px',
  //         numVisible: 4,
  //         numScroll: 4
  //     },
  //     {
  //         breakpoint: '400px',
  //         numVisible: 3,
  //         numScroll: 3
  //     }
  // ];

    this.getCompanies();

  //  this.animateHome();

  }

  // ngAfterViewInit() {
  //   this.showLoader = false;
  // }

  // set country and direction
  setPageInfo(){
    let urlArr=document.location.href.split('/');
    let lastItemUrlArr=urlArr[urlArr.length-1];
    //in case last item in url contains fragment
    if(lastItemUrlArr.includes('#')){
      lastItemUrlArr = lastItemUrlArr.substring(0, lastItemUrlArr.indexOf('#'));
    }
    this.pageInfo.country = lastItemUrlArr;
  //  this.pageInfo.country = this.route.snapshot.params['country'];
    
    //case1: no country passed in url
  //  if(this.pageInfo.country === undefined)
    if(this.pageInfo.country === ''){
     // check localstorage to see if there is a preffered country
      let country = localStorage.getItem('country');
      if(country && country ===""){
        this.pageInfo.country = null;
        this.pageInfo.direction = 'ltr';
      }
      else{
        this.pageInfo.country = country;
        this.setInfoBasedOnCountry();
        this.router.navigate([this.pageInfo.country]);
      }
    }  
    //case2: there is a country passed in url so we override country in localstorage
    else{
      this.setInfoBasedOnCountry();
    }
    localStorage.setItem("country",this.pageInfo.country);
    //this.showLoader = false;
  }

  // hideLoader(){
  //   if(this.sliderLoader){
  //     this.sliderLoader = false;
  //   }
  // }

  setInfoBasedOnCountry(){
    switch(this.pageInfo.country) { 
      case "syria" || "egypt": { 
        this.pageInfo.direction = 'rtl';
        break; 
      } 
      case "qatar" || "india": { 
        this.pageInfo.direction = 'ltr';
        break; 
      } 
      default: { 
        this.pageInfo.direction = 'ltr';
        break; 
      } 
    } 
  }

  // getIp() {
  //   console.log("in geIp");
  //   // if(navigator.geolocation){
  //   //   console.log("in navigator");
  //   //   navigator.geolocation.getCurrentPosition(
  //   //     (position) => {
  //   //       console.log(position);
  //   //       var latitude = position.coords.latitude;
  //   //      var longitude = position.coords.longitude;
  //   //      console.log(latitude , longitude);
  //   //     },
  //   //     (error) => {
  //   //       console.log("error",error);
  //   //     }
  //   //    );
  //   // }
  //   // else{
  //   //   console.log("not success");
  //   // }

  //   return this.http.get('http://ipinfo.io');
    
  // //  return this.http.get('https://api.ipregistry.co/?key=tryout');
  // //  return this.http.get(this.url +'/resumes/'+  resume_id + '/achievements', {headers});
  // }

  // get companies to show there logo on home page
  getCompanies(){
    this.compLogoPath = this.imageProcessingService.getImagePath ('companyLogo','med_thumbnail');

    this.homeService.getCompanies().subscribe(res=>{
      this.goldCompanies = res['companies'].filter( (el) => el['company_verified'] === 'Golden Verified' );
      this.silverCompanies = res['companies'].filter( (el) => el['company_verified'] === 'Silver Verified' );
  //    this.showLoader = false;
    });
  }

  applyToCompany(company,companyType,reloadCompanies=false){
    if (this.role !== 'unauth'){
      this.homeService.applyToCompany(company.id).subscribe(res => {
        if (res['error']) {
          this.messageService.add({ severity: 'error', summary: 'Apply to company', detail: res['error'],life:5000 });
          if(res['type']==='directly_applied')
            this.changeApplyBtnState(company,companyType);
        } 
        else {
          let successMessage = '';
          successMessage = 'Applied Successfully to ' + company.company_name;
          this.messageService.add({ severity: 'success', summary: 'Apply to company', detail: successMessage,life:5000 });
          this.changeApplyBtnState(company,companyType);
        }

        if(reloadCompanies)
          this.getCompanies();
      });
    }
    else{
      this.actionAfterLogin = 'apply';
      this.currentApplyCompanyInfo = { 'company': company, 'companyType': companyType };
      $('#authModal').modal('toggle');
    }

  }

  changeApplyBtnState(company,companyType){
    if(companyType==='goldenPartner'){
      let index : number = this.goldCompanies.indexOf(company);
      if (index !== -1) {
        this.goldCompanies[index].directly_applied = true;
      }
    }
    else if (companyType==='silverPartner'){
      let index : number = this.silverCompanies.indexOf(company);
      if (index !== -1) {
      let index : number = this.silverCompanies.indexOf(company);
        this.silverCompanies[index].directly_applied = true;
      }
    }
  }

  // animateHome(){
  //   window.addEventListener("scroll", (event)=>{
  //     if (window.pageYOffset > $(".templates-number").offset().top - 350 ) {
  //       $(".templates-number .plus").css({"-webkit-animation":"zoom-it 600ms linear"});
  //       $(".templates-number .thirty").css({"-webkit-animation":"zoom-it 600ms linear 400ms"});
  //     }
  //   });
  // }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}


