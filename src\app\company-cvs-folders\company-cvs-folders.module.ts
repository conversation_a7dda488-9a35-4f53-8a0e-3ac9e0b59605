import { NgModule } from '@angular/core';
import { SharedModule } from 'shared/shared.module';
import { CompanyCvsFoldersRoutingModule } from './company-cvs-folders-routing.module';
import { MainComponent } from './components/main/main.component';
import { CvsFoldersComponent } from './components/cvs-folders/cvs-folders.component';
import { CvsTableComponent } from './components/cvs-table/cvs-table.component';
import { CompanyFormService } from '../company/services/company-form.service';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import {TreeModule} from 'primeng/tree';
import { DragDropModule } from "@angular/cdk/drag-drop";
import { AddEditFolderModalComponent } from './components/add-edit-folder-modal/add-edit-folder-modal.component';
import { CompanyCvsPreviewModule } from '../company-cvs-preview/company-cvs-preview.module';
import { JobAdvsInfoModalComponent } from './components/job-advs-info-modal/job-advs-info-modal.component';
// import {MatMenuModule} from '@angular/material/menu';

@NgModule({
  declarations: [MainComponent, CvsFoldersComponent, CvsTableComponent, AddEditFolderModalComponent, JobAdvsInfoModalComponent],
  imports: [
    SharedModule,
    CompanyCvsFoldersRoutingModule,
    CompanyCvsPreviewModule ,
    PdfViewerModule,
    TreeModule,
    DragDropModule,
    // MatMenuModule
  ],
  providers: [
    CompanyFormService,
  ]
})
export class CompanyCvsFoldersModule { }
