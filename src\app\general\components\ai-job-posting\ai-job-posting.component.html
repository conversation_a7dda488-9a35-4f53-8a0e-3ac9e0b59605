<page-navbar [navType]="'empty'"></page-navbar>

<div class="article-container">
    <h1>Ai Job Posting</h1>

    <div class="article-img text-center">
        <img src="./assets/images/footer-pages/ai-job-posting.webp" class="img-responsive">
    </div>
    
    <div class="section">
        <h2>Let AI Do the Work &mdash; Your Job Post Practically Writes Itself!</h2>

        <p>Struggling with writing job ads over and over again?<br />
        <strong>CVeek&rsquo;s AI Job Posting</strong> feature helps you <strong>generate professional job descriptions in seconds</strong> &mdash; saving time and boosting productivity.</p>
        
        <p>Just enter a <strong>job title</strong> and a <strong>brief description</strong>, and the smart system will take care of the rest:</p>

        <p>✔️ Generate detailed <strong>job responsibilities and duties</strong><br />
            ✔️ Identify the right <strong>skills and qualifications required</strong><br />
            ✔️ Suggest a <strong>well-structured and engaging job post format</strong><br />
            ✔️ Prepare a <strong>ready-to-publish job listing</strong>, with editable fields like <strong>location</strong>, contract type, and more.</p>
            
        <!-- <ul>
            <li>✔️ Generate detailed <strong>job responsibilities and duties</strong></li>
            <li>✔️ Identify the right <strong>skills and qualifications required</strong></li>
            <li>✔️ Suggest a <strong>well-structured and engaging job post format</strong></li>
            <li>✔️ Prepare a <strong>ready-to-publish job listing</strong>, with editable fields like <strong>location</strong>, contract type, and more.</li>        
        </ul> -->

        <p>Focus on finding top talent &mdash; let <strong>CVeek AI</strong> write the job ad for you.</p>        
        <p>One click transforms your short input into a complete, professional job advertisement that attracts the right candidates &mdash; instantly.</p>
        <p><strong>Try CVeek today&hellip; and create a smart job post in under 60 seconds!</strong></p>
                    
    </div>

    <div class="section text-center page-link">
        <h2>Check our Ai job posting page</h2>
        <a *ngIf="username ===null" routerLink="/m/company/login">Ai job posting</a>
        <a *ngIf="username !==null" [routerLink]="['/c', username, 'post-job']">Ai job posting</a>
    </div>
</div>
