import { Component, OnInit, Input } from '@angular/core';
import { GeneralService } from '../../../general/services/general.service';
import { Validators, FormBuilder } from '@angular/forms';
import { EmailValidator } from 'shared/validators/email.validators';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.css']
})
export class FooterComponent implements OnInit {
  @Input('inHomePage') inHomePage: boolean;
  username = null;
//  subscriptionForm;

  constructor(
    private generalService: GeneralService,
  //  private fb: FormBuilder,
  ) { }

  ngOnInit(): void {

    if(localStorage.getItem("username")){
      this.username = localStorage.getItem("username");
    }

    // this.subscriptionForm = this.fb.group({
    //   email: ['',  [Validators.required , EmailValidator.isValidEmailFormat]  ]
    // });
  }

  displayContactModal() {
    this.generalService.notify('display Contact Modal' , 'topbar' , 'contact-us' , {'displayContactModal':true}) ;
  }
}
