import { Component, OnInit } from '@angular/core';
import { MessageService } from 'primeng';
import { UserAccountService } from '../../../user/cv-services/user-account.service';

@Component({
  selector: 'confirm-message',
  templateUrl: './confirm-message.component.html',
  styleUrls: ['./confirm-message.component.css'],
  providers: [MessageService]
})
export class ConfirmMessageComponent implements OnInit {
  username = '';
  emailState;
  mobileState;
  emailConfirmed;
  mobileConfirmed;
  closeState = true;
  role = '';
  constructor(private messageService: MessageService,
              private userAccountService:UserAccountService) { }

  ngOnInit(): void {
    if(localStorage.getItem("username")){
      this.username = localStorage.getItem("username");
    }
    if(localStorage.getItem("role")){
      this.role = localStorage.getItem("role");
    }


  }  // end ngOnInit

  ngAfterViewInit() {
    setTimeout(() => {     
      this.userAccountService.sharedConfirmMsgClosed.subscribe(state => {
        if(state === "closed"){
          this.closeState = true;
        }
        else this.closeState = false;
      });
  
      this.userAccountService.sharedEmailConfirmed.subscribe(state => {
        // console.log("inside check email subscribe");
        if(state === "confirmed"){
          this.emailState = "confirmed";
          this.emailConfirmed = true;
          this.showConfirmMessage();
        }
        else if(state === 'notConfirmed'){
          this.emailState = "notConfirmed";
          this.emailConfirmed = false;
          this.showConfirmMessage();
        }
        else{
          this.emailState = null;
        }
      });
  
      this.userAccountService.sharedMobileConfirmed.subscribe(state => {
        if(state === "confirmed"){
          this.mobileState = "confirmed";
          this.mobileConfirmed = true;
          this.showConfirmMessage();
        }
        else if(state === 'notConfirmed'){
          this.mobileState = "notConfirmed";
          this.mobileConfirmed = false;
          this.showConfirmMessage();
        }
        else{
          this.mobileState = null;
        }
      });
  
      if(this.emailState === null  || this.mobileState === null){
        // get email and mobile confirmation state from backend
        let emailConfirmedBack;
        let mobileConfirmedBack;
        this.userAccountService.checkMobileEmailConfirmed().subscribe(res => {
          emailConfirmedBack = res['email_confirmed'];
          mobileConfirmedBack = res['mobile_confirmed'];
  
          if(this.emailState === null){
            this.emailConfirmed = emailConfirmedBack;
          }
          if( this.mobileState === null){
            this.mobileConfirmed = mobileConfirmedBack;
          }
  
          this.showConfirmMessage();
        });  
      }
      else {
        this.showConfirmMessage();
      }
      
    },1);
  }

  showConfirmMessage(){
    if(!this.emailConfirmed && !this.mobileConfirmed){
      this.messageService.clear();
      this.messageService.add({ severity:'warn', detail:'You can not log in using your new email until you confirm it ... and we are wating for verification code to change your mobile , please confirm it ASAP',sticky:true});
    }
    else if(!this.emailConfirmed){         
      this.messageService.clear();
      this.messageService.add({ severity:'warn', detail:'You can not log in using your new email until you confirm it , please confirm it ASAP',sticky:true});
    }
    else if(!this.mobileConfirmed){
      this.messageService.clear();
      this.messageService.add({ severity:'warn', detail:'We are wating for verification code to change your mobile , please confirm it ASAP',sticky:true});
    }  
    else{
      this.messageService.clear();
    }
  }

  changeMsgClosedState(){
    this.userAccountService.changeConfMsgCloseState("closed");
  }

}
