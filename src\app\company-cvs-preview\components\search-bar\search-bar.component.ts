import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { GeneralService } from '../../../general/services/general.service';

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.css']
})
export class SearchBarComponent implements OnInit, OnDestroy {
  keyword = '';
  constructor(private generalService: GeneralService,) { }

  ngOnInit(): void {
  }

  search(){
    this.generalService.notify('keywordSearch' , 'search-bar' , 'cvs-table' , {"keyword":this.keyword});
  }

  toggleFolders(){
    this.generalService.notify('toggleFolders' , 'search-bar' , 'main' , {'expanded' : false}) ;
  }

  ngOnDestroy() {
    //clearing keyword and notify cvs table to clear the value of keywordsearch
    //because it is causing issues sometimes
    this.keyword = '';
    this.generalService.notify('clearKeywordOn<PERSON><PERSON>roy' , 'search-bar' , 'cvs-table' , {"keyword":this.keyword});
  }

}
