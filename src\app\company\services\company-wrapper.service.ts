import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';

@Injectable()
export class CompanyWrapperService {
  url1 = '';
  url2 = '';
  url3 = '';
  
  constructor(private http: HttpClient , private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1).subscribe(data => {
      this.url1 = data + 'company/get_translation_languages';
      this.url2 = data + 'company/main_language';
      this.url3 = data + 'company/get_other_translationLanguages';
    });
   }

   

   getLanguages() {
    return this.http.get(this.url1);
   }

   editLanguage (languageId, companyId) {
     console.log('lang Id', languageId);
    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url2 + '/' + companyId , languageId, {headers} );
   }

   getOtherLanguages(currentLanguage) {
     console.log('getOtherLanguages', this.url3 + '/' + currentLanguage);
     return this.http.get(this.url3 + '/' + currentLanguage);
   }

 /*  changeLanguage(resume, resume_id: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + '/changeLanguage/' + resume_id, JSON.stringify(resume), {headers});
  } */

}
