import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CompanyWrapperComponent } from 'app/company/components/company-wrapper/company-wrapper.component';
import { CompanyGuardService } from 'shared/shared-services/company-guard.service';
import { MainComponent } from './components/main/main.component';


const routes: Routes = [
  {path: ':username', component: CompanyWrapperComponent, children: [
    { path: '', component: MainComponent },
  ], canActivate: [CompanyGuardService]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CompanyCvsPreviewRoutingModule { }
