<app-pre-loader [show]="showLoader"></app-pre-loader>
 
 <!-- Start of header -->
 <div class="header container-fluid page-navbar">
  <div class="row search-row">
    <div class="col-md-6 col-sm-9 col-xs-12">
        <input type="text" #query class="search-input"  placeholder="Search for..." (keyup)="search(query.value)" >
    </div>
  </div>

  <!--<div>
   <div class="col-md-3 col-md-offset-9 col-sm-3 col-sm-offset-9 col-xs-5  col-xs-offset-7  lang-col">
      <span *ngFor="let lang of languagesArray"  class="languages">
        <button class="btn" translate [class.btn-primary]="lang.id === currentLangId" (click)="changeLang(lang.id)" translate>{{ "faqs.languages."+lang.name}}</button>
      </span>
    </div>
  </div>-->

 </div>
 <!-- End of header -->
 <div class="custom-container">
  <h1 class="title" translate>faqs.FrequentlyAskedQuestions</h1>
 <div>
  <app-pre-loader *ngIf="filteredCategories.length === 0"></app-pre-loader>

 <!--<div *ngIf="filteredCategories.length === 0"  class="col-md-offset-5 col-md-2">
   <p-progressSpinner    fill="transparent" animationDuration="1s"></p-progressSpinner>
 </div>-->

  <div class="row">
      <div class="col-xs-12 no-results" *ngIf="noFaqsAvailable === true">
        There is no FAQs available
      </div>
     <div class="col-xs-12 no-results" *ngIf="noResultsAvailable === true">
       There is no results with this search
     </div>
     <ng-container *ngFor="let category of filteredCategories[currentLangId - 1];let i = index">
        <div class="col-xs-12 cat-col">
          <div class="card">
           <div class="badge badge-primary category-badge" ><i class="icon-caret-right"></i>
            <h2>{{ category.name }}</h2>
          </div>
             <ul  class="list-group">
                <p-accordion (onClose)="displayAnswerr(i, $event.index)"
                (onOpen)="displayAnswerr(i, $event.index)">
                  <p-accordionTab  *ngFor="let f of filteredQuestions[currentLangId - 1][i]; let j = index" [selected]="displayAnswer[currentLangId - 1][i][j]">

                    <p-header  *ngIf="j < 5 && f.catId === category.id">
                        <h3 class="" >{{ f.question }} <b>?</b></h3>
                      </p-header>
                      <span *ngIf="j < 5 && f.catId === category.id"  class="">                 
                        <span [innerHTML]="f.answer" class="editor-description"></span>
                        <!-- <span [innerHTML]="sanitizer.bypassSecurityTrustHtml(f.answer)"></span> -->      
                      </span>

                      <p-header *ngIf="j >= 5 && display[i] && f.catId === category.id" class="hidden-faq ">
                        <h3 class="" >{{ f.question }} <b>?</b></h3>
                      </p-header>
                      <span *ngIf="j >= 5 && display[i] && f.catId === category.id" class="hidden-faq " >
                          <span [innerHTML]="f.answer" class="editor-description"></span>
                          <!-- <span [innerHTML]="sanitizer.bypassSecurityTrustHtml(f.answer)"></span> -->
                      </span>  
                    </p-accordionTab>
                </p-accordion>

              <div *ngIf="filteredQuestions[currentLangId - 1][i].length > 5 && !display[i]" ><a (click)="showAll(i)" translate>faqs.labels.ShowMore</a></div>
              <div *ngIf="filteredQuestions[currentLangId - 1][i].length > 5 && display[i]" ><a (click)="showLess(i)"translate>faqs.labels.ShowLess</a></div>
            </ul>
          </div>
        </div>
    </ng-container>
    <!-- <div *ngIf="filteredCategories.length !== 0">Count:<span class="badge badge-primary"  >{{getCount()}}</span></div> -->


  </div>
</div>
</div>



