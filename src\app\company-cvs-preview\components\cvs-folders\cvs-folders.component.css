.folders-heading{
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    text-decoration: none;
    cursor: pointer;
}
.folders-heading span{
    display: inline-block;
    font-size: 17px;
    font-weight: bold;
    margin-left:6px;
}
.my-folders-title{
    width:calc(100% - 28px);
    color:#444;
}
.add-folder-btn{
    display: inline-block;
    width: 28px;
    height: 28px;
    font-size: 24px;
    line-height: 27px;
    color: #000;
    text-align: center;
    border-radius: 50%;
}
.add-folder-btn:hover{
    background: #ddd;
}

:host ::ng-deep .ui-tree {
    width: 100%;
    background: transparent;
    border:none;
    padding:0;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-label {
    width: 80%;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content .ui-treenode-label.ui-state-highlight {
    background-color: transparent;
    color: #333;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content.ui-treenode-selectable .ui-treenode-label:not(.ui-state-highlight):hover{
    background:#fff;
    /* color:#3d7bce; */
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content:focus, :host ::ng-deep .ui-tree .ui-tree-container .ui-treenode .ui-treenode-content:hover{
    box-shadow:none;
}

/* :host ::ng-deep .ui-tree .ui-tree-wrapper, :host ::ng-deep .ui-tree .ui-tree-container {
    overflow: unset;
} */

.folder-label-div{
    width:100%;
    /* position:relative; */
}
.folder-label-div .name{
    margin-right:12px;
}
:host ::ng-deep .ui-tree .ui-tree-container .ui-treenode{
    position:relative;
}
.folder-label-div .count{
    position:absolute;
    right:3px;
    color:#3bb34b;
}