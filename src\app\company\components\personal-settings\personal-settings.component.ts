import { Component, OnInit, NgModuleFactory } from '@angular/core';
/* import {DynamicClass, DynamicArrayClass} from "app/company/components/dynamically_filters"; */
import { Observable } from "rxjs/Rx";
import { HttpClient } from "@angular/common/http";
/* import * as company from "app/company/company.module"; */
/* import('app/company/company.module').then(
  module => {
console.log(module);
    return module.CompanyModule;
  }
).then(mod => {
console.log(mod);
}); */
@Component({
  selector: 'app-personal-settings',
  templateUrl: './personal-settings.component.html',
  styleUrls: ['./personal-settings.component.css']
})
export class PersonalSettingsComponent implements OnInit {



   

  /* data_type(type,data){
    let res;
    res = data.map((response) =>{
      return new DynamicClass(type,response);
     
    })

    return res;
  } */
 
  
  

  ngOnInit() {
 
    /* console.log(this.test = new DynamicClass('Global','CompanySettingsComponent'))
    console.log(this.test.print())
    let data;
    this.getData('Blog').subscribe(
      (res) => {
        data = this.data_type('Blog',res)

        console.log('data',data)
      }) */
      
    
  }

}
