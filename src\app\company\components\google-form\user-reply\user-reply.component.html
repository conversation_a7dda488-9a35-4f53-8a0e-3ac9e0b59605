<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" integrity="sha384-9gVQ4dYFwwWSjIDZnLEWnxCjeSWFphJiwGPXr1jddIhOegiu1FwO5qRGvFXOdJZ4" crossorigin="anonymous">
<div class="top-banner">

</div>

<div style="height:30px;"> </div>



<div class="container">
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <mat-card>
        <mat-card-content>
          <form >
            <div *ngFor="let q of questionWithChoices; let i = index">
              <div class="row">
                <div class="form-group col-md-12 left">
                  {{ q.order }} - {{ q.label }}
                </div>
              </div>

              <div class="row" [ngSwitch]="q.controlType">

                <div class="form-group col-md-12 left" *ngSwitchCase="'Short Answer'">
                  <mat-form-field style="width: 100%">
                    <input matInput type="text" [disabled]="true" [value]="getTextAnswer(q.id)" >
                  </mat-form-field>
                </div>

                <div class="form-group col-md-12 left" *ngSwitchCase="'Paragraph'">
                  <mat-form-field  style="width: 100%">
                    <textarea matInput type="text"  [disabled]="true" [value]="getTextAnswer(q.id)"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-group col-md-12 left" *ngSwitchCase="'Date'">
                  <mat-form-field  style="width: 100%">
                    <input matInput type="text"  [disabled]="true" [value]="getDateAnswer(q.id)|date:'MM/dd/yyyy'">
                  </mat-form-field>
                </div>
                <div class="form-group col-md-12 left" *ngSwitchCase="'Time'">
                  <mat-form-field  style="width: 100%">
                    <input matInput type="text"  [disabled]="true" [value]="getTextAnswer(q.id)">
                  </mat-form-field>
                </div>


                <div class="form-group col-md-12 left" *ngSwitchCase="'Checkboxes'">
                  <mat-radio-group  [disabled]="true">
                    <div  *ngFor="let a of q.choices">
                      <mat-radio-button color="primary"
                                        style="cursor: pointer"
                                        [checked]="checkQADependency(q.id , a.id)"
                                        value="{{a.id}}">
                        {{ a.label }}
                      </mat-radio-button>
                    </div>
                  </mat-radio-group>
                </div>

                <div class="form-group col-md-12 left" *ngSwitchCase="'dropDown'">
                  <mat-radio-group  [disabled]="true">
                    <div  *ngFor="let a of q.choices">
                      <mat-radio-button color="primary"
                                        style="cursor: pointer"
                                        [checked]="checkQADependency(q.id , a.id)"
                                        value="{{a.id}}">
                        {{ a.label }}
                      </mat-radio-button>
                    </div>
                  </mat-radio-group>
                </div>

                <div class="form-group col-md-12 left"  *ngSwitchCase="'Multiple Choice'">
                  <div *ngFor="let a of q.choices">
                    <mat-checkbox [disabled]="true"
                      color="primary"
                      type="checkbox"
                      style="cursor: pointer"
                      [checked]="checkQADependency(q.id , a.id)"
                      value="{{a.id}}">
                      {{ a.label }}
                    </mat-checkbox>
                  </div>
                </div>

              </div>
              <hr>
            </div>
            <mat-card-footer>
            </mat-card-footer>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
  <br>
</div>

