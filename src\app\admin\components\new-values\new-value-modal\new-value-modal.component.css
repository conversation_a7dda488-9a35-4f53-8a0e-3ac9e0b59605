.modal-body-container {
  height: fit-content;
}


.modal-body{
  height: fit-content;
  max-height: unset !important;
  overflow: unset;
}


.fa {
  cursor:pointer;
}
span.emails {
  background-color: #e6e6e6;
  margin: 5px 0px !important;
  border-radius: 20px;
  padding: 2px 10px;
}

.btn.btn-done.submit-button {
    padding: 10px 45px;
    /* border-radius: 80px; */
    background-color: #0000f7a8;
    margin-left: 65px;
    color:white;
}

.btn.btn-done.submit-button:hover {
  background-color: rgb(12, 12, 185);
}

.btn.btn-done.submit-button[disabled] {
  background-color: #050588c9;
}

.btn.btn-default {
  padding: 8px 8px;
  /* border-radius: 80px; */
}

.btn {
  margin-bottom: 10px;
  margin-top: 0px;
  margin-right: 10px;
}

input {
  border: none;
  border-bottom: 1px dashed #bbb;
  background:transparent;
  width: 100%;
  transition: all 0.5s ease-in-out;
  /* overflow: auto; */
}

input:focus {
  border: none !important;
  border-bottom: 1px solid dodgerblue;
}

input.ng-dirty.ng-touched.ng-invalid {
  border-radius: 5px;
  border: 2px solid #ff000059;
  background-color: #ffeaea66 ;
}

.alert.alert-danger {
  float: left;
  padding:5px;
  width: 250px;
  margin-left: 5px;
  margin-right: 5px;
}



.alert.alert-danger.inline-alert {
  padding: 0px;
  padding-left: 5px;
  margin: 0px;
  /* margin-top: -20px; */
  background-color: transparent;
  border: none;
  margin-right: -270px;
}

.table>tbody>tr>td, .table>tbody>tr>th, .table>thead>tr>th {
  border-top: none;
  border-bottom: none;;
}



td {
  color: #777;
  /* width: 250px; */
  font-size: 1.1em;
}


.fa.fa-plus {
  color: #276ea4;
  padding-right: 5px;
}

.fa.fa-save {
  font-size: 2em;
  color: #276ea4;
}

button .fa.fa-save {
  color: white;
}

.alert.alert-danger {
  padding: 0px;
  padding-left: 5px;
  margin: 0px;
  background-color: transparent;
  border: none;
}




.table>tbody>tr>td,.table>tbody>tr>th, .table>tfoot>tr>td,
.table>tfoot>tr>th, .table>thead>tr>td,  .table>thead>tr>th {
      vertical-align: middle;
 }

 .badge.badge-primary {
  min-width: 10px;
  padding: 3px 10px;
  font-size: 14px;
  background-color: #393cab30;
  color: midnightblue;
  border-radius: 10px;
  border: 2px solid #dddddd;
  /* box-shadow: 0px 0px 2px 1px #aaa; */
}

.action{
  border: 2px solid #f0f0f0;
  border-radius: 5px;
  text-align: center;
  margin-left: 100px;
  margin-right: 100px;
  padding: 7px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  font-size: 17px;
}


.action .badge.badge-primary{
  padding: 3px 40px;
  font-size: 17px;
  background-color: #28a92859;
  color: forestgreen;
  border: none;
  border-radius: 10px;
}

.action .badge.badge-default{
  padding: 3px 40px;
  font-size: 17px;
  background-color: #a09a9a69;
  color: #777;
  border: none;
  border-radius: 10px;
}


.table-caption {
  font-weight: bold;
  font-size: 1.2em;

}


input::-webkit-input-placeholder { color: #bdbdd3;}
input:-moz-placeholder { color: #bdbdd3;}
input::-moz-placeholder { color: #bdbdd3;}
input:-ms-input-placeholder { color: #bdbdd3;}



.table  thead tr th, .table tbody th, .table  tr th  {
    color: #4f94df;
    text-align: right;
    /* width: 100px; */
    font-weight: 100;
    font-size: 16px;
}

label {
  color: #4f94df;
  text-align: right;
  font-weight: 100;
  font-size: 16px;
}


.table.log-table thead tr th, .log-table tbody th, .log-table tr th {
  text-align: left;
}

.table{
  /* border: 2px solid #e9e9e9; */
  /* margin-left: 50px; */
  margin-right: 10px;
  width: 90%;
  margin-top: 10px;
}

/* .table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th{
  max-width: 300px;
  min-width: 200px;
} */


.btn-light.btn-assign {
  background-color: #8e068e69;
  color: white;
  margin-bottom:0px;
  margin-top: 0px;
  margin-left: 5px;
}

.btn-light.btn-assign:hover {
  background-color: #8e068eef;
}

.btn-light.btn-comment {
  background-color: #f8dc76;
  color: white;
  margin-bottom:0px;
  margin-top: 0px;
  padding: 0px 5px;
  margin-left: 5px;
}

.btn-light.btn-comment:hover {
  background-color: #fdd237;
}

.btn-light.btn-done {
  background-color: #18b92cad;
  color: white;
  margin-bottom:0px;
  margin-top: 0px;
  margin-left: 165px;
}

.btn-done:hover {
  background-color:#18b92c;
}

.btn-light.btn-not-done {
  background-color: #e9144993;
  color: white;
  margin-bottom:0px;
  margin-top: 0px;
  margin-left: 10px;
}

.btn-not-done:hover {
  background-color:#e91449fd;
}


/* how to create tabs */
.nav.nav-tabs .active {
  border-bottom: 4px solid white;;
  padding: 6px 12px;
  border-top: 2px solid lightgrey;
  border-left: 2px solid lightgrey;
  border-right: 2px solid lightgrey;
  background-color: white;

}
.nav-tabs {
  margin-top: 18px;
  border-bottom: 1px solid #ddd;

}
.nav.nav-tabs .btn {
  color: #aaa;
  margin-bottom: -3px;
}
.nav.nav-tabs .btn.active, .btn:active {
   color: #251677;
   font-weight: bold;
   -webkit-box-shadow: beige;
   box-shadow:beige;
   border-bottom-left-radius: 0%;
   border-bottom-right-radius: 0%;
}
/* end of tabs */

.fa.fa-user, .fa-check, .fa.fa-paper-plane, .fa.fa-download {
  padding-right: 5px;
}

.ui-panel-content.ui-widget-content  {
  width: 870px;
  margin: auto;
  margin-left: 120px;
  margin-bottom: 0px;
  font-size: 15px;

}




.table > tbody > tr > td,
 .table > tbody > tr > th,
 .table > tfoot > tr > td,
 .table > tfoot > tr > th,
 .table > thead > tr > td,
 .table > thead > tr > th {
  border: none;
}



:host ::ng-deep .ui-dropdown label.ui-dropdown-label {
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 16px;
  /* border-bottom: 1px solid #ccc; */
}

:host ::ng-deep .ui-dropdown .ui-dropdown-label-container {
  font-family: 'Exo2-Regular', sans-serif;
  font-size: 16px;
  /* border-bottom: 1px solid #ccc; */
}

:host ::ng-deep  .ui-dropdown.ui-widget.ui-state-default.ui-corner-all input.ui-dropdown-label.ui-inputtext.ui-corner-all {
border-bottom: 1px dashed #bbb;
color: #777;
font-family: 'Exo2-Regular', sans-serif;
font-size: 16px;

}




:host ::ng-deep .ui-panel-titlebar.ui-widget-header.ui-helper-clearfix.ui-corner-all {
  background-color: #f9f9f9;
  /* border: 1px solid #c5c5c5; */

}



:host ::ng-deep .ui-panel-title {
font-family: 'Exo2-Regular', sans-serif;
color: #4f94df;
font-weight: bold;

}

:host ::ng-deep .ui-panel .ui-panel-titlebar .pi {
  color: #4f94df;
}


:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content {
font-family: 'Exo2-Regular', sans-serif;
color: #666;
font-size: 1em;
}

:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content h5 {
font-family: 'Exo2-Regular', sans-serif;
color: #333;
font-weight: bold;
}

:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content sub {
color: #aaa;
padding-left: 15px;
}

:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content .admin {
font-weight: 100;
color: #4f94df;
font-size: 16px;
padding-right: 10px;
padding-left: 15px;
}

:host ::ng-deep .ui-panel-content-wrapper .ui-panel-content.ui-widget-content p {
padding-left: 15px;
}


:host ::ng-deep p-panel .ui-widget.ui-widget-content {
    border: none;
    margin-left: 190px;
    width: 800px;
}

:host ::ng-deep p-panel .ui-panel .ui-panel-titlebar {
border: none;

}

:host ::ng-deep p-panel .ui-panel .ui-panel-content {
border: none;
border-bottom-left-radius: 15px;
border-bottom-right-radius: 15px;
border: 1px solid #eee;
/* background-color: #fdfdfd; */

}


:host ::ng-deep .ui-card.ui-widget.ui-widget-content.ui-corner-all {
  width: fit-content;
  /* margin-left: -30px; */
  box-shadow: none;
  border: none;
  /* border: 2px dashed #ddd; */
  border-radius: 2px;
  margin-top: -9px;
  margin-bottom: 3px;
  margin-left: -15px;

}

:host ::ng-deep .ui-card .ui-card-body {
  padding: 0.5em;
}


:host ::ng-deep .ui-multiselect.ui-widget.ui-state-default.ui-corner-all {
  width: 100%;
}

:host ::ng-deep .ui-multiselect-panel.ui-widget {
  width: 100%;
}

:host ::ng-deep .ui-fileupload .ui-fileupload-buttonbar {
  background-color: transparent;
  border: none;
}

:host ::ng-deep  .ui-fileupload .ui-fileupload-content {
  background-color: transparent;
  border: none;
}

:host ::ng-deep  .ui-fileupload  .ui-widget.ui-widget-content {
  border: none;
  background: transparent;
}

:host ::ng-deep  .ui-fileupload .ui-fileupload-files {
  display: none;
}

p.note {
    /* background-color: oldlace;
    padding: 6px;
    box-shadow: 0px 1px 1px 1px; */
    font-size: 14px;
}

span.active {
  border-bottom: 2px solid midnightblue;
}


:host ::ng-deep li.ui-multiselect-item.ui-corner-all {
   overflow-x: hidden !important ;
}


:host ::ng-deep  .ui-multiselect-panel .ui-multiselect-items .ui-multiselect-item {
  overflow: hidden !important;
}
