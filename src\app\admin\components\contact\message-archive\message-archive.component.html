<h3 class="verf-heading"> Messages Archive </h3>
<br><br>
<p>Count: <span class="badge badge-primary badge-pill">{{ messages.length }}</span>
   Selected: <span class="badge badge-primary badge-pill">{{ filteredMessages.length }}</span></p>


<p-table #dt [value]="messages" [(selection)]="filteredMessages" dataKey="id" styleClass="ui-table-messages" [rowHover]="true"
    [rows]="10" [showCurrentPageReport]="true" [rowsPerPageOptions]="[10,25,50]" [loading]="loading"
    [paginator]="messages.length" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [filterDelay]="0" [globalFilterFields]="['id', 'year' ,'email','type', 'language', 'date', 'main_cat', 'sub_cat', 'handled_by', 'status']">
    <ng-template pTemplate="caption">
        <!-- Archived Messages -->
        <div class="ui-table-globalfilter-container">
            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Global Search" />
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th  style="width:100px;" ></th>
            <th pSortableColumn="id" style="width:70px;">ID <p-sortIcon field="id"></p-sortIcon></th>
            <th pSortableColumn="year" style="width:70px;">Year <p-sortIcon field="year"></p-sortIcon></th>
            <th pSortableColumn="email" colspan="2">Email <p-sortIcon field="email"></p-sortIcon></th>
            <th pSortableColumn="date">Received Date <p-sortIcon field="date"></p-sortIcon></th>
            <th pSortableColumn="main_cat">Main Cat <p-sortIcon field="main_cat"></p-sortIcon></th>
            <th pSortableColumn="sub_cat">Sub Cat <p-sortIcon field="sub_cat"></p-sortIcon></th>
            <!-- <th pSortableColumn="last_reply">Last Reply <p-sortIcon field="last_reply"></p-sortIcon></th> -->
            <th pSortableColumn="status"  style="width:100px;">Status <p-sortIcon field="status"></p-sortIcon></th>
            <th pSortableColumn="handled_by">Handled By <p-sortIcon field="handled_by"></p-sortIcon></th>
            <th  style="width:100px;">Actions</th>
        </tr>
        <tr>
            <th>
                <i *ngIf="filteredMessages.length !== 0" class="fa fa-repeat" (click)="restoreMsgs()" title="restore the selected messages" style="margin-left: 0px;margin-right: 5px;"></i>
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                <i *ngIf="filteredMessages.length !== 0" class="fa fa-trash" data-toggle="modal" data-target="#alertModal" title="delete selected messages" (click)="displayDeleteAlert(4)"></i>
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'id', 'startsWith')" placeholder="" class="ui-column-filter" style="width: 40px;">
            </th>
            <th>
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'year', 'startsWith')" placeholder="" class="ui-column-filter" style="width: 40px;">
            </th>
            <th colspan="2">
                <input pInputText type="text" (input)="dt.filter($event.target.value, 'email', 'contains')" placeholder="" class="ui-column-filter">
            </th>
            <th>
              <p-calendar #cl [(ngModel)]="rangeDates" (onSelect)="dt.filter(rangeDates, 'date', 'isBetween')" (onClearClick)="dt.filter('', 'date', 'contains')" [showButtonBar]="true" yearRange="2020:2030" selectionMode="range" styleClass="ui-column-filter" placeholder="" [readonlyInput]="true" dateFormat="yy-mm-dd"></p-calendar>
            </th>
            <th>
              <p-dropdown [options]="mainCats" (onChange)="dt.filter($event.value, 'main_cat_id', 'equals')" styleClass="ui-column-filter" [(ngModel)]="main_cat" [showClear]="false" [filter]="true">
                <ng-template let-option pTemplate="item">
                    <span>{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <th>
              <p-dropdown [options]="subCats" (onChange)="dt.filter($event.value, 'sub_cat_id', 'equals')" styleClass="ui-column-filter" [(ngModel)]="sub_cat"  [showClear]="false" [filter]="true">
                <ng-template let-option pTemplate="item">
                    <span >{{option.label}}</span>
                </ng-template>
              </p-dropdown>
            </th>
            <!-- <th>
              <input pInputText type="text" (input)="dt.filter($event.target.value, 'last_reply', 'contains')" placeholder="" class="ui-column-filter">
            </th> -->
            <th>
              <!-- <p-dropdown [options]="statuses" (onChange)="dt.filter($event.value, 'status', 'equals')" styleClass="ui-column-filter"  [showClear]="false">
                  <ng-template let-option pTemplate="item">
                      <span [class]="'message-badge status-' + option.value">{{option.label}}</span>
                  </ng-template>
              </p-dropdown> -->
            </th>
            <th>
              <p-dropdown [options]="admins" (onChange)="dt.filter($event.value, 'handled_by', 'equals')" styleClass="ui-column-filter" [(ngModel)]="admin"  [showClear]="false" [filter]="true">
                  <ng-template let-option pTemplate="item">
                      <span>{{option.label}}</span>
                  </ng-template>
              </p-dropdown>
            </th>
            <th><i class="fa fa-remove" title="clear All" (click)="clearAll()"></i></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-message>
        <tr class="ui-selectable-row"  (mouseover)="message.display = true" (mouseleave)="message.display = false" >
            <td>

                <p-tableCheckbox [value]="message" (click)="addToSelected(message)"></p-tableCheckbox>
            </td>
            <td>
              {{message.id}}
            </td>
            <td>
                {{message.year}}
              </td>
            <td colspan="2">
                {{message.email}}
            </td>
            <td>
               {{message.date}}
            </td>
            <td>
               {{ message.main_cat | summary:10}}
            </td>
            <td>
               {{ message.sub_cat | summary:10}}
            </td>
            <!-- <td>
               {{(message.last_reply )? message.last_reply.short_reply : null  | summary:10 }}
            </td> -->
            <td>
              <span [class]="'message-badge status-' + message.status">{{message.status}}  </span>
              <!-- <span *ngIf="message.comments.length !== 0"><i class="fa fa-comment" ></i></span> -->
           </td>
            <td>
              {{ message.handled_by | summary:10 }}
           </td>
           <td>
               <span *ngIf="message.display">
                <i class="fa fa-trash" data-toggle="modal" data-target="#alertModal" title="delete this message" (click)="displayDeleteAlert(3, message.id)"></i>
                <i class="fa fa-repeat" (click)="restoreMsg(message)" title="restore"></i>
                <i class="fa fa-eye" data-toggle="modal" data-target="#msgModal" title="preview" (click)="displayModal(message)" ></i>
               </span>
           </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="9" style="text-align:center;padding:15px;">No messages found.</td>
        </tr>
    </ng-template>
</p-table>


<!-- Preview & edit modal-->
<div class="modal fade" *ngIf="displayMsgModal" id="msgModal"  tabindex="-1" role="dialog" aria-labelledby="msgModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
         <button type="button" (click)="closeModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
         <h3>View Message </h3>
      </div>
      <div class="modal-body">
         <app-message-modal [mode]="'preview_mode'" [msg]="msgToPreview"  ></app-message-modal>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<!-- end of  modal-->



<!-- add  modal-->
<div class="modal fade" *ngIf="displayDeleteModal" id="alertModal"  tabindex="-2" role="dialog" aria-labelledby="ModalLabel">
  <div class="modal-dialog"  role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" (click)="closeDModal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h3 *ngIf="opperationNum === 3 || opperationNum === 4" style="color:crimson;">Delete </h3>
      </div>
      <div class="modal-body">
       <!-- delete alert -->

        <div *ngIf="opperationNum === 3">
           <p>Are you sure you want to delete this message? </p>
           <button type="button" class="btn btn-light" (click)="deleteMsg()">Yes</button>
        </div>

        <div *ngIf="opperationNum === 4">
          <p>Are you sure you want to delete these {{ filteredMessages.length }} message? </p>
          <button type="button" class="btn btn-light" (click)="deleteMultiMsgs()">Yes</button>
       </div>

      </div>

    </div>
  </div>
</div>
<!-- end of  modal-->

