import { Component, OnInit } from '@angular/core';
import {AuthService} from 'shared/shared-services/auth-service';
import {HttpClient} from '@angular/common/http';
import {FormBuilder, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-reset-password-verification',
  templateUrl: './reset-password-verification-company.component.html',
  styleUrls: ['./reset-password-verification-company.component.css']
})
export class ResetPasswordVerificationCompanyComponent implements OnInit {


  verificationForm;
  resetMessage;
  warningMessage;
  successMessage;
  loader = false;
  constructor(private authService: AuthService,
              private http: HttpClient,
              private  fb: FormBuilder,
              private router: Router,
              private title:Title,
              private meta:Meta
  ) {
    this.verificationForm = this.fb.group({
      resetToken: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.title.setTitle('CVeek Website | Reset password');
    this.meta.updateTag({ name: 'robots', content: 'noindex' });
  }

  isInvalid(controlName: string) {
    return !this.verificationForm.controls[controlName].valid;
  }

  verify() {
    let userInfo = JSON.parse(localStorage.getItem('oldUser'));
    const data = {
      'email': userInfo.email,
      'resetToken': this.verificationForm.get('resetToken').value,
    };
    if (this.verificationForm.valid) {
      this.loader = true;
      this.authService.resetPasswordVerifyCompany(data).subscribe(res => {
        this.loader = false;
        if (res['data']) {
          this.router.navigate(['/m/company/reset-password']);
        }
        if (res['error']) {
          this.resetMessage = res['error'];

          // if user trying to reset password but its account is not verified yet, we navigate the user to verification page
          if(res['type']==='UnverifiedEmailRest'){
            this.authService.changeError(res['error']);
            this.router.navigate([res['help']]);
          }
        }
      });
    }
  }

  sendResetTokenAgain() {
    this.warningMessage = "Please wait a minute before pressing send code again";
    let userInfo = JSON.parse(localStorage.getItem('oldUser'));
    const data = {
      'email': userInfo.email
    };
    this.authService.sendResetTokenAgain(data).subscribe(res => {
      if(res['data']){
        this.resetMessage = '';
        this.warningMessage = '';
        this.successMessage = res['data'];
        setTimeout(() => {
          this.successMessage = "";
        }, 4000);
      }
      if(res['error']){
        this.resetMessage = res['error']; 
        
        // if user trying to reset password but its account is not verified yet, we navigate the user to verification page
        if(res['type']==='UnverifiedEmailRest'){
          this.authService.changeError(res['error']);
          this.router.navigate([res['help']]);
        }
      }
    });
  }
}
