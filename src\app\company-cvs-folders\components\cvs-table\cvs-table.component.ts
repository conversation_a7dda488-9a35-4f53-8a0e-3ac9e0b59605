import { Component, OnInit } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
import { DataModel, Cv } from './DataModel';
import { TableModule } from 'primeng/table';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
import { HttpClient } from '@angular/common/http';
import { ExportUrlService } from 'shared/shared-services/export-url.service';
import { $$rxSubscriber } from 'rxjs/internal/symbol/rxSubscriber';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { Subject, Subscription } from 'rxjs';
import { SortEvent } from 'primeng';
import { CdkDragDrop, moveItemInArray, transferArrayItem, copyArrayItem } from '@angular/cdk/drag-drop';
import { CvsTableService } from '../../services/cvs-table.service';
import { PDFDocumentProxy } from 'ng2-pdf-viewer';

declare var $: any;
@Component({
  selector: 'app-cvs-table',
  templateUrl: './cvs-table.component.html',
  styleUrls: ['./cvs-table.component.css']
})
export class CvsTableComponent implements OnInit {
  dataModel: any;
  showModal: boolean;
  showInterviewDate: boolean;
  colsReadyPassToModal = false;
  cvPreviewMode: boolean;
  currentFiltersTags: any = [];
  noResumes: boolean;
  sendData: any;
  currentCv: any;
  pdfSrc='';
  foldersddData = [];
  modalLoader=false;
  subscriptionsArray: Subscription[] = [];
  pdfViewerLoader = false;

  selectAction = null;
  resumes_ids = [];

  private ngUnsubscribe: Subject<any> = new Subject();
  ///////////////////////////////////// Constructor
  constructor(private generalService: GeneralService,
    private http: HttpClient,
    private imageProcessingService: ImageProcessingService,
    private privateSharedURL: ExportUrlService,
    private cvsTableService:CvsTableService,
    private router: Router,
    private title: Title) {

    this.dataModel = new DataModel();
    this.dataModel.standardColumns = this.dataModel.dataColumns.filter((el) => el.standard === true);
    this.showModal = false;
    this.showInterviewDate = false;

    this.cvPreviewMode = false;
    this.noResumes = false;

    if (this.router.getCurrentNavigation().extras.state) {
      let state;
      state = this.router.getCurrentNavigation().extras.state;
      if(state.folder_id){
        this.dataModel.folder = state.folder_id;
      }
    }

    ///  listen to  changes : Folder , Filter , Page Changes
    // this.generalService.internalMessage.subscribe((data) => {
    //   if (data['src'] === 'cv-previewer-toolbar' && data['message'] === 'showCVeekChanged') {
    //     this.dataModel.showCVeek = event['showCVeek'];
    //   }
    // });


  }
  /////////////////////////// End Of Constructor
  ///////////////////////////////////
  ngOnInit(): void {
    this.firstTableLoading();

    this.checkIfMobile();

    //start code moved from constructor
    this.generalService.internalMessage.takeUntil(this.ngUnsubscribe).subscribe((data) => {
      //////////////
      if (data['message'] === 'folderChanged') {
        this.dataModel.folder = data['mData']['folder_id'];
        this.dataModel.page_number = 1;
        this.resumes_ids = [];
      //  this.getTableData(data['mData']);
        this.sendData = {
          'folder_id': this.dataModel.folder,
        };
        Object.assign(this.sendData, this.dataModel.filters);
        let requirements = {
          'requirements':data['mData']['requirements']
        }
        if(data['mData']['requirements']){
          Object.assign(this.sendData, requirements);
        }
        this.getTableData(this.sendData);
        this.cvPreviewMode=false;

        this.generalService.notify('changeNameFilterList', 'cvs-table', 'name_filter', { 'folder_id': this.dataModel.folder, 'folder_label':data['mData']['folder_label'] });
      }
      
      // tslint:disable-next-line:prefer-const

      if (data['message'] === 'filtersSubmitted') {
        if(this.cvPreviewMode === true)
          this.cvPreviewMode = false;

        this.sendData = {
          'folder_id': this.dataModel.folder,
        };

        this.dataModel.filters = data['mData'];
        Object.assign(this.sendData, this.dataModel.filters);
        this.getTableData(this.sendData);
      }

      if (data['message'] === 'currentFiltersTags') {
        this.currentFiltersTags = data['mData'];
      }

      if (data['message'] === 'pageChanged') {
        this.dataModel.page_number = data['mData']['pageId'];    
        this.getTableData(this.sendData);
      }
      if (data['message'] === 'refershTable') {
        this.getTableData(this.sendData);
      }
      if (data['message'] === 'HideCvPreviewMode') {
        // this.dataModel.folder = 1;
        // this.dataModel.page_number = 1;
        // this.getTableData(data['mData']);
        // this.sendData = {
        //   'folder': this.dataModel.folder,
        // };
        this.cvPreviewMode=false;
        this.title.setTitle('CVeek');
      }
      if (data['src'] === 'cv-previewer-toolbar' && data['message'] === 'showCVeekChanged') {
        this.dataModel.showCVeek = data['mData'].showCVeek;
      }

    //   if (data['src'] === 'search-bar' && data['message'] === 'keywordSearch') {
    //     this.dataModel.keywordSearch = data['mData'].keyword;
    //     if(this.cvPreviewMode === true)
    //       this.cvPreviewMode = false;
    
    //     //when filtering with specific keyword for the --first time-- , we should search in page 1 not in the current page
    //     //so if it has rows more than one page, we could request next page result filtered with this keyword
    //     this.dataModel.page_number = 1;
    //     // we are adding the keyword to the request inside getTableData function
    //     // so that in every need for data we filter the data with searched keyword 
    //     this.getTableData({'folder_id' : this.dataModel.folder});
    //  //  this.getTableData({'folder' : this.dataModel.folder,'keyword':this.dataModel.keywordSearch});
    //   }

    //   if (data['src'] === 'search-bar' && data['message'] === 'clearKeywordOnDestroy') {
    //     this.dataModel.keywordSearch = "";
    //   }

      if (data['message'] === 'cvsFoldersDDReady' && data['src'] === 'cvs-folders') {
        this.foldersddData = data['mData'].foldersddData;
      }

      if (data['message'] === 'deleteSelectedCVS' && data['src'] === 'filters-wrapper') {
        if(this.resumes_ids.length === 0)
          alert("Please choose CVs before apply action!");
        else{
          this.selectAction = 'deleteSelectedCVS';
          this.confirmDeleteCV();
        }
      }

      if (data['message'] === 'readSelectedCVS' && data['src'] === 'filters-wrapper') {
        if(this.resumes_ids.length === 0)
          alert("Please choose CVs before apply action!");
        else{
          this.selectAction = 'readSelectedCVS';
          this.changeStatus(null , this.resumes_ids , 'read_button' , 1 , 'cv_folder/read');
          this.uncheckSelectedCVS();
        }
      }

      if (data['message'] === 'moveSelectedCVSToFolders' && data['src'] === 'filters-wrapper'){
        if(this.resumes_ids.length === 0)
          alert("Please choose CVs before apply action!");
        else{
          this.selectAction = 'moveSelectedCVSToFolders';
          $('#moveCVToFolderModal').modal('show');
          // this.generalService.notify('moveSelectedCVSToFolders2', 'cvs-table','cv-previewer-toolbar', 
          // { 'resumes_ids': this.resumes_ids });
        }
      }

      if (data['message'] === 'reduceTotalFilteredCvs' && data['src'] === 'cvs-folders'){
        this.dataModel.totalFilteredCvs = this.dataModel.totalFilteredCvs - data['mData'].reducecount;
      }

      // if (data['message'] === 'detachFolderFromResume' && data['src'] === 'cv-previewer-toolbar'){
      //   let cv = this.dataModel.resumes.find((el) => el.resume_id === data['mData']['updatedCv'].resume_id);
      //   if(cv !== null || cv !== undefined){
      //     cv.cv_folders = data['mData']['updatedCv'].cv_folders;
      //   }
      // }
      

    });

    //end code moved from constructor

  }

  

  firstTableLoading(){
    this.dataModel.page_number = 1;
   //case got folder_id from navigation extras (from recieve cvs module)
    if(this.dataModel.folder){
      this.getTableData({ 'folder_id': this.dataModel.folder });
      this.sendData = {
        'folder_id': this.dataModel.folder,
      };
      this.generalService.notify('folderChanged', 'cvs-table', 'cvs-folders', { 'folder_id': this.dataModel.folder });
    }
    else{
    //  this.generalService.notify('changeNameFilterList', 'cvs-table', 'name_filter', { 'folder_id': null });
      this.getTableData({ 'folder_id': null });
    }
      
    this.cvPreviewMode=false;
    this.title.setTitle('CVeek');
  }

  /////  row level functions

  getImage(src , gender) {
    if(src){
      return this.imageProcessingService.getImagePath ('cvProfilePic','small_thumbnail', src);
    }
    else{
      if(gender === 'male')
        return './assets/images/male.svg';
       
      else
        return './assets/images/female.svg';
    }
  }
  getUploadedFile(fileSrc) {
    return (fileSrc) ? this.privateSharedURL.awsBasicUrl + 'storage/cvs/pdf/uploaded/' + fileSrc : this.pdfSrc;
  }

  confirmDeleteCV(cv?){
    this.dataModel.currentCv = cv;
    $('#confirmDeleteCVModal').modal('toggle');
  }

  cancelConfirmDeleteCVModal(){
    $('#confirmDeleteCVModal').modal('hide');
  }

  changeStatus(cv, resumes_ids = null , key, value, action, confirmMessage = '', deleteType='') {
    let data;
    let confirmed = true;
    if(cv !== null){
      this.dataModel.currentCv = cv;
      let resume_id = cv.resume_id;
      data = {
        'resume_id': resume_id,
      };
      this.pdfSrc = this.getUploadedFile(cv.uploaded_file);
    }
    else{
      data = {
        'resumes_ids': resumes_ids,
      };
    }
  
    let temp = key;
    if (key === 'read_button') {
      temp = 'read';
    //  cv.read = value;
    }
    data[temp] = value;
    if(deleteType !==''){
      data[deleteType]=1;
    }

    //// confirmed only used when we need to view confirm message, Ex: Move  To Trush
    if (confirmMessage) {
      confirmed = confirm(confirmMessage);
    }
    ////////////// If user click on name  or photo column -> preview cv , preview mode =true
    if (key === 'read') {
      this.cvPreviewMode = true;
      this.pdfViewerLoader = true;
      this.resumes_ids = [];
      window.scroll(0,0);
    }
    //

    //////////// if function needs to be confirmed, otherwise confirmed = true;
    if (confirmed) {
      if(key==='delete_cv'){
        //deleteType: move_to_trash - permanent_delete
        this.modalLoader=true;
        this.cvsTableService.deleteCV(data).subscribe((res)=>{
          if(cv !==null){
            let index: number = this.dataModel.resumes.indexOf(cv);
          //  if (index !== -1) {
              this.dataModel.resumes.splice(index, 1);
              this.dataModel.totalFilteredCvs = this.dataModel.totalFilteredCvs -1;
              
              this.generalService.notify('deleteCvUpdateCount', 'cvs-table', 'cvs-folders', { 'cv_folders': cv.cv_folders });
              this.modalLoader=false;
              $('#confirmDeleteCVModal').modal('hide');
              
              this.cvPreviewMode = false;
              this.title.setTitle('CVeek');
          //  }
          }
          else{
            let cv2;
            let index: number;
            for(let id of resumes_ids){
              cv2 = this.dataModel.resumes.find((el) => el.resume_id === id);
              if(cv2 !==undefined){
                index = this.dataModel.resumes.indexOf(cv2);
                if (index !== -1) {
                  this.dataModel.resumes.splice(index, 1);
                  this.dataModel.totalFilteredCvs = this.dataModel.totalFilteredCvs -1;
                  this.generalService.notify('deleteCvUpdateCount', 'cvs-table', 'cvs-folders', { 'cv_folders': cv2.cv_folders });
                }
              }
            }
            this.modalLoader=false;
            $('#confirmDeleteCVModal').modal('hide');
            
            this.cvPreviewMode = false;
            this.title.setTitle('CVeek');
          }
          
        });
        this.uncheckSelectedCVS();
      }
      else
      this.sendRequest('post', this.privateSharedURL.awsBasicUrl + action, data).subscribe((res) => {
        if(cv !==null){
          if (key === 'read'){
            cv.read = 1;
          }
          if (key === 'read_button'){
            let cvOriginal = this.dataModel.resumes.find((el) => el.resume_id === cv.resume_id);
            if(cvOriginal !==undefined)
              cvOriginal.read = value;
            cv.read = value;
          }
        }
        else{
          let cv3;
          let index: number;
          for(let id of resumes_ids){
            cv3 = this.dataModel.resumes.find((el) => el.resume_id === id);
            if(cv3 !==undefined){
              index = this.dataModel.resumes.indexOf(cv3);
              if (index !== -1) {
                this.dataModel.resumes[index].read = 1;
              }
            }
          }
        }
        // in case all resumes in current page moved,go to the previous page
        if (this.dataModel.resumes.length === 0 && this.dataModel.page_number !== 1) {
          this.generalService.notify('pageChanged', 'cvs-table', 'cvs-table', { 'pageId': this.dataModel.page_number - 1 });
        }
      
      });
    }

  }


  // changeStatus(cv, key, value, action, confirmMessage = '', deleteType='') {
  //   this.dataModel.currentCv = cv;

  //   let confirmed = true;
  //   let resume_id = cv.resume_id;
  
  //   let data = {
  //     'resume_id': resume_id,
  //   };
  //   this.pdfSrc = this.getUploadedFile(cv.uploaded_file);
  
  //   let temp = key;
  //   if (key === 'read_button') {
  //     temp = 'read';
  //   //  cv.read = value;
  //   }
  //   data[temp] = value;
  //   if(deleteType !==''){
  //     data[deleteType]=1;
  //   }

  //   //// confirmed only used when we need to view confirm message, Ex: Move  To Trush
  //   if (confirmMessage) {
  //     confirmed = confirm(confirmMessage);
  //   }
  //   ////////////// If user click on name  or photo column -> preview cv , preview mode =true
  //   if (key === 'read') {
  //     this.cvPreviewMode = true;
  //     this.pdfViewerLoader = true;
  //     window.scroll(0,0);
  //   }
  //   //

  //   //////////// if function needs to be confirmed, otherwise confirmed = true;
  //   if (confirmed) {
  //     if(key==='delete_cv'){
  //       //deleteType: move_to_trash - permanent_delete
  //       this.modalLoader=true;
  //       this.cvsTableService.deleteCV(data).subscribe((res)=>{
  //         let index: number = this.dataModel.resumes.indexOf(cv);
  //         if (index !== -1) {
  //           this.dataModel.resumes.splice(index, 1);
  //           this.generalService.notify('deleteCvUpdateCount', 'cvs-table', 'cvs-folders', { 'cv_folders': cv.cv_folders });
  //           this.modalLoader=false;
  //           $('#confirmDeleteCVModal').modal('hide');
            
  //           this.cvPreviewMode = false;
  //           this.title.setTitle('CVeek');
  //         }
  //       });
  //     }
  //     else
  //     this.sendRequest('post', this.privateSharedURL.awsBasicUrl + action, data).subscribe((res) => {
  //       if (key === 'read'){
  //         cv.read = 1;
  //       }
  //       if (key === 'read_button'){
  //         cv.read = value;
  //       }
  //       // in case all resumes in current page moved,go to the previous page
  //       if (this.dataModel.resumes.length === 0 && this.dataModel.page_number !== 1) {
  //         this.generalService.notify('pageChanged', 'cvs-table', 'cvs-table', { 'pageId': this.dataModel.page_number - 1 });
  //       }
      
  //     });
  //   }

  // }
  /// ----------------

  //cancel unnecessary requests: 1-In case the user makes multiple similar requests in a short timeframe
  // 2-or if user makes a request then navigate to another page
  cancelAllRequests() {
    if (!this.subscriptionsArray) {
      return;
    }
    this.subscriptionsArray.forEach(s => s.unsubscribe());
    this.subscriptionsArray = [];
  }

  ////// global functions
  getTableData(data1) {
    this.dataModel.showLoader = true;
    this.dataModel.resumes = [];
    // let haveData = 0;
    // if(this.dataModel.firstLoad === true)
    //   haveData = 1;

    // we are adding the keyword to the request inside this function
    // so that in every need for data we filter the data with searched keyword 
    // if(this.dataModel.keywordSearch !==''){
    //   Object.assign(data1, {'keyword':this.dataModel.keywordSearch});
    // }
    // else{
    //   //if there is no search keyword , check if keyword property still in sendData object and remove it
    //   if(this.sendData && this.sendData.keyword){
    //     let exceptKeyword = Object.keys(this.sendData).reduce((acc, key) => {
    //       if (key !== 'keyword') {
    //           acc[key] = this.sendData[key]
    //       }
    //       return acc
    //     }, {})
        
    //     this.sendData = exceptKeyword;
    //   }
    // }

    this.cancelAllRequests();
    const subscription = this.cvsTableService.getTableData(
      // @params : check  data model attributes  values
      this.dataModel.rows,
      this.dataModel.page_number,
      this.dataModel.lang,
      data1,
    //  haveData,
      [] //
    ).subscribe({
      next: data => {
        this.dataModel.resumes = data['resumes']['data'];

        this.dataModel.showLoader = false;       
        if(this.dataModel.firstLoad === true){
          this.dataModel.firstLoad = false;
          this.generalService.notify('stop-loader', 'cvs-table', 'main', {'firstLoad':false});

          // //     ///// adv folders for none expired ads*/
          // let advFolders = data['filters_data']['adv_title_folders'].filter(el => !el.expired);
          // this.generalService.notify('advs-folders-loaded', 'cvs-table', 'folders-list', advFolders);
        }
        
        this.dataModel.totalPages = data['resumes']['last_page'];
        //this.generalService.notify('filter-data-from-cvs-table', 'cvs-table', 'filter-wrapper', data['filters_data']);
        this.dataModel.totalFilteredCvs = data['resumes']['total'];
        this.dataModel.setColumns(data['columns']);
        this.colsReadyPassToModal = true;
      },
      error: error => {
        console.error('There was an error!', error);
      }
    });
    this.subscriptionsArray.push(subscription);
  }


  showColsManager() {
    $('#ColsManagementModal').modal('toggle');
  }

  handleColsSelection($event) {
    let requirements = [];
    $event['new'].forEach(element => {
      requirements.push(element);
    });
    // ------------
    this.generalService.notify('folderChanged', 'cols-manager', 'cvs-table',
      { 'folder_id': this.dataModel.folder, 'requirements': requirements });

  }

  getResumeAppFolders($event,cv){
    $event.stopPropagation();
    this.dataModel.currentCv = cv;
    $('#jobAdvInfoModal').modal('show');
  }


  showInterviewModal(cv) {
    this.dataModel.currentCv = cv;
    // let currentDate = new Date();
    // let timeZoneOffset;
    // timeZoneOffset = currentDate.getTimezoneOffset();
    // timeZoneOffset = (timeZoneOffset/60)*-1;

    if (this.dataModel.currentCv.interview_date) {
      this.dataModel.currentCv.interview_date = new Date(this.dataModel.currentCv.interview_date);
    } else {
      this.dataModel.currentCv.interview_date = new Date();
    }
    // this.dataModel.currentCv.interview_date.setTime(this.dataModel.currentCv.interview_date.getTime() - timeZoneOffset * 60000);
    $('#interviewModal').modal('show');
  }
  ////
  setInterviewDate() {
    let data = { 'emp_ids': [this.dataModel.currentCv.application_id], 'date': this.dataModel.currentCv.interview_date.toString() };
    this.sendRequest('post', this.privateSharedURL.awsBasicUrl + 'emp_app/interview', data).subscribe((res) => {
      this.generalService.notify('refershTable', 'cvs-table', 'cvs-table', { 'folder': this.dataModel.folder });
    });

    $('#interviewModal').modal('hide');
  }


  //  responsive layout
  isMobileAgent() {
    const agent = navigator.userAgent || navigator.vendor || (window as any).opera;
    // tslint:disable-next-line:max-line-length
    return (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(agent) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(agent.substr(0, 4)));
  }

  checkIfMobile() {
    this.dataModel.isMobileLayout = this.isMobileAgent();
    this.dataModel.isMobileLayout = window.innerWidth <= 767;
    window.onresize = () => {
      this.dataModel.isMobileLayout = window.innerWidth <= 767;
    };
  }

  removeFilter(tag,specificValue?) {
    if(tag.type !== 'array'){
      this.currentFiltersTags = this.currentFiltersTags.filter(item => item !== tag);
      this.generalService.notify('filter-removed', 'cvs-table', 'filters-wrapper',
      { 'filtertag': tag, 'currentFiltersTags':this.currentFiltersTags });
    }
    else{
      const modifiedFilterTagIndex = this.currentFiltersTags.indexOf(tag);
      let modifiedFilterTag = this.currentFiltersTags.filter(item => item === tag)[0];
      const removedValueIndex = modifiedFilterTag.value.indexOf(specificValue);
      if (removedValueIndex > -1) { 
        modifiedFilterTag.value.splice(removedValueIndex, 1); 
        this.currentFiltersTags[modifiedFilterTagIndex] = modifiedFilterTag;

        if(this.currentFiltersTags[modifiedFilterTagIndex].value.length === 0)
          this.currentFiltersTags.splice(modifiedFilterTagIndex, 1); 

        this.generalService.notify('filter-removed', 'cvs-table', 'filters-wrapper',
        {'filtertag': tag, 'modifiedFilterTag':modifiedFilterTag, 'currentFiltersTags':this.currentFiltersTags });
      }
    }
  }
  ///////////////////////// One  Function For  Send Requests
  sendRequest(method, action, data) {
    let options = { body: data };
    if (method === 'get') {
      options['params'] = data;
    }
    return this.http.request(method, action, options);
  }
  //// ----------------------------


  getCvToolBarEvent(event) {
    if (event['message'] && event['message'] === 'back') {
      if(event.key==='delete_cv'){
        this.confirmDeleteCV(event.cv)
      }
      else{
        this.cvPreviewMode = false;
        this.title.setTitle('CVeek');
      }
    }

    if (event['action'] && event.key!=='delete_cv') {
      this.changeStatus(event.cv, null , event.key, event.value, event.action, event.confirmMessage);
    }
    if (event['showCVeek']) {
      this.dataModel.showCVeek = event['showCVeek'];
    }
  }

  hideModal() {

  }

  // Start custom sort fuctions for p-table
  customSort(event: SortEvent) {
    //event.data = Data to sort
    //event.mode = 'single' or 'multiple' sort mode
    //event.field = Sort field in single sort
    //event.order = Sort order in single sort
    //event.multiSortMeta = SortMeta array in multiple sort

    event.data.sort((data1, data2) => {
      let value1 = data1[event.field];
      let value2 = data2[event.field];
      let result = null;

      result = this.customSortNullvalues(value1, value2);
      if (result === 1 || result === -1 || result === 0) {

      }
      else if (typeof value1 === 'string' && typeof value2 === 'string') {
        result = value1.localeCompare(value2);
      }
      // to deal with with open to work column (type object)
      else if (typeof value1 === 'object' && typeof value2 === 'object' && value1.class) {
        //deal with null values
        result = this.customSortNullvalues(value1.value, value2.value);
        if (result === 1 || result === -1 || result === 0) {

        }
        //not null values
        else {
          result = value1.value.localeCompare(value2.value);
        }
      }
      else {
        result = (value1 < value2) ? -1 : (value1 > value2) ? 1 : 0;
      }

      return (event.order * result);
    });
  }

  customSortNullvalues(value1, value2) {
    let result = null;
    if (value1 == null && value2 != null)
      result = -1;
    else if (value1 != null && value2 == null)
      result = 1;
    else if (value1 == null && value2 == null)
      result = 0;

    return result;
  }
  // End custom sort fuctions for p-table

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } 
    else {
      //this piece of code is not called!!
      if(this.dataModel.folder === null){
        copyArrayItem(
          event.previousContainer.data,
          event.container.data,
          event.previousIndex,
          event.currentIndex
        );
      }
      else{
          transferArrayItem(
            event.previousContainer.data,
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );
      }
    }
  }

  getFlagImage(code) {
    return './assets/images/CountryCode/' + code + '.gif';
  }
  
  stopPdfViewerLoader(pdf: PDFDocumentProxy){
    this.pdfViewerLoader = false;
  }

  clearAllFilters(){
    this.currentFiltersTags = [];
    this.sendData.filters = {};
    this.getTableData(this.sendData);
    this.generalService.notify('clearAllFilters', 'cvs-table', 'filters-wrapper', {} );
  }
  
  collectSelectedCVs($event,cv){
    // case delete and move
    if($event.target.checked){
      this.resumes_ids.push(cv.resume_id)
    }
    else{
      const index = this.resumes_ids.indexOf(cv.resume_id, 0);
      if (index > -1) {
        this.resumes_ids.splice(index, 1);
      }
    }
  
  }

  toggleSelectAllCVs($event){
    if($('.toggle-checkbox').is(":checked")){
      for(let resume of this.dataModel.resumes){
        this.resumes_ids.push(resume.resume_id);
      }
      $(".select-cv-checkbox").prop( "checked", true );
    }
    else{
      this.resumes_ids = [];
      $(".select-cv-checkbox").prop( "checked", false );
    }
  }

  uncheckSelectedCVS(){
    this.resumes_ids = [];
    $(".select-cv-checkbox").prop( "checked", false );
    $(".toggle-checkbox").prop( "checked", false );
  }

  //duplicated from cv-previewer-toolbar component but processing another cases
  handleMoveCvPopup($event){
    let cv;
    let index: number;
    for(let id of this.resumes_ids){
      cv = this.dataModel.resumes.find((el) => el.resume_id === id);
      if(cv !==undefined){
        index = this.dataModel.resumes.indexOf(cv);
        if (index !== -1) {
          this.generalService.notify('updateCVFoldersCount', 'cvs-table', 'cvs-folders', {
            "oldFolders":cv.cv_folders,"newFolders":$event['cv_folders'],"foldersType":"custom-folders"
            });

          //copy case 
          if(this.dataModel.folder===null){
            cv.cv_folders = $event['cv_folders'];
          }
          else{
            this.dataModel.resumes.splice(index, 1);
            this.dataModel.totalFilteredCvs = this.dataModel.totalFilteredCvs -1;
          }
        }
      }
    }
    this.uncheckSelectedCVS();
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.cancelAllRequests();
  }

}
