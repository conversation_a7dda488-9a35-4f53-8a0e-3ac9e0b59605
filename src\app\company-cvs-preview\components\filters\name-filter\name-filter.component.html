<div class="search-div focus-no-padding">
    <!-- placeholder="Search for names in {{pageType}}" -->
    <ng-select 
        [items]="namesDD.items"
        [(ngModel)]="namesDD.name"
        placeholder="{{namePlaceHolder}}"
        bindLabel="name"
        notFoundText="No items found"
        [dropdownPosition]="'bottom'"
        (search)="search($event)"
        [loading]="namesDD.loading"
        [openOnEnter]="false"
        (open)="initiateNameItems()"
        class="form-control"
        (clear)="clearName();"
        (keyup.enter)="sendFilters()"
    >
        <ng-template ng-label-tmp let-item="item" let-clear="clear">
            <span class="ng-value-label">{{item.name}}</span>
        </ng-template>
        <ng-template ng-option-tmp let-item="item" let-index="item" style="position: relative;">
            {{item.name}}
        </ng-template>
    </ng-select>

    <span class="custom-underline"></span>
</div>

<button class="btn search-btn" (click)="sendFilters()"><i aria-hidden="true" class="fa fa-search"></i></button> 
