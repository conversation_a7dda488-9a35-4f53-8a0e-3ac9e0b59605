a:link , a:hover{
  cursor: pointer;
  text-decoration: none
}
div.content-col.company-content-col {
  padding-top: 130px;
}

.badge.category-badge {
  padding: 5px 10px;
  font-size: 19px;
  /* color: #465798; */
  background-color: transparent;
  cursor: pointer;
  display:left block inline ;
  text-align: left !important;
  white-space: normal;
  line-height: 1.3;
}

div, table, td,th,p,h2,form ,ul, li, ol, .breadcrumb, span, div, button {
  font-family: 'Exo2-Regular', sans-serif;
}

.container {
  width: 100%;
  height: 100%;
}

span.input-group-btn button.btn.btn-default {
  cursor: default;
  background-color: whitesmoke;
}

span.input-group-btn button.btn.btn-default:active,
 span.input-group-btn button.btn.btn-default:click,
 span.input-group-btn button.btn.btn-default:focus,
 span.input-group-btn button.btn.btn-default:hover  {
  cursor: default;
  background-color: whitesmoke;

}

span.languages button.btn {
  float: right;
  margin-top: 30px;;
}

/* .list-group-item>.badge.badge-primary.category-badge { */
  /* float: left; */

  /* margin-bottom: 40px; */
/* } */

.subcat-title{
  font-size: 23px;
  color: #3D7BCE;
  margin-bottom: 22px;
}
ul.list-group.sub-group-list {
  margin-top: 0px;

}
ul.list-group.sub-group-list  .list-group-item {
  border: none !important;
  font-size: 18px !important;
}
ul.list-group.sub-group-list  .list-group-item h2{
  font-size: 18px !important;
  margin:0;
}
ul.list-group.sub-group-list {
  margin-left: 15px;
}
a.help-topic {
  display: block;
}

a.help-topic:hover {
 text-decoration: none;
}
a.help-topic.show {
  padding-bottom: 10px;
}
div.header {
  position: fixed;
  top: 67px;
  /* top:100px; */
  font-size:15px;
  width: 100%;
  list-style: none;
  /* background: #f2f2f2; */
  background: #3D7BCE;
  z-index: 9999;
  text-align: center;
  /* border-bottom: 1px solid #ddd; */
}
/* div.header {
 
  min-height: 100px;
  height: fit-content;
  padding-bottom: 25px;
  

}
.header {
  background:#efefef;
  height: fit-content;
  padding-bottom: 25px;
 
} */
div.lang-col {
  margin-top: -50px;
  padding-right: 15px;
}

.card {
  background-color: white;
  /* margin: 70px; */
  padding:20px;
  transition: all 0.6s ease-in-out;
  
  /* margin-bottom: 10px;
  transition: all 0.6s ease-in-out; */
}
/* .card:hover {

} */

.content-row{
  margin-top: 10px;
}

.cat-col{
  padding-right: 10px;
  padding-left: 10px;
}


.breadcrumb {
  font-size: 20px;
  padding: 15px 15px;
  border-bottom: 1px solid #ddd;
}

.fa.fa-home {
  font-size: 1.3em;
}


p.note {
  padding: 10px;
  padding-left: 15px;
  background-color: white;
}


/* ---------------------------------------------------------------------- */
/* Start Responsive  */
.container-padding{
  padding-top:30px;
}

@media screen and (max-width: 850px ) {

  .nav-row {
    padding-top: 0px;

  }
  .header {
    margin-top: -40px;
  }
  /* .header {
      margin-top: -40px;
      background:#efefef;
      height: fit-content;
      padding-bottom: 25px;
  } */
}

@media screen and (min-width: 851px) and (max-width: 1014px ) {

  .nav-row {
    padding-top: 15px;

  }

  .header {
    margin-top: -30px;
  }
}

@media screen and (min-width: 1025px) and (max-width: 1399px ) {

  .nav-row {
    padding-top: 10px;

  }

  .header {
    margin-top: -20px;
  }
}

@media screen and (min-width: 1400px)  {

  .content-col {
    padding-top: 20px;

  }

  .header {
    margin-top: 0px;
  }
}
@media screen and (max-width: 767px ) {
  .badge.category-badge{
    font-size: 14px;
    padding: 5px 4px
  }
  .fa.fa-home{
    font-size: 1.1em;
  }
  .cat-col{
    padding:0;
  }
}

:host ::ng-deep p-progressSpinner svg circle {
  fill:transparent;
}
