import { Component, OnInit, NgModuleFactory } from '@angular/core';
/* import {DynamicClass, DynamicArrayClass} from "app/company/components/dynamically_filters"; */
import { Observable } from "rxjs/Rx";
import { HttpClient } from "@angular/common/http";
import {SelectItem} from 'primeng/api';
import {SelectItemGroup} from 'primeng/api';

@Component({
  selector: 'app-language-setting',
  templateUrl: './language-setting.component.html',
  styleUrls: ['./language-setting.component.scss']
})
export class LanguageSettingComponent implements OnInit {
  selectedCar: string;

    groupedCars: SelectItemGroup[];

  constructor() {
    this.groupedCars = [
      {
          label: 'English',
          items: [
              {label: 'Audi', value: 'Audi'},
              {label: 'BMW', value: 'BMW'},
              {label: 'Mercedes', value: 'Mercedes'}
          ]
      },
      {
          label: 'USA',
          items: [
              {label: 'Cadillac', value: 'Cadillac'},
              {label: 'Ford', value: 'Ford'},
              {label: 'GMC', value: 'GMC'}
          ]
      },
      {
          label: 'Japan',
          items: [
              {label: 'Honda', value: 'Honda'},
              {label: 'Mazda', value: 'Mazda'},
              {label: 'Toyota', value: 'Toyota'}
          ]
      }
  ];
   }

  ngOnInit(): void {
  }

}
