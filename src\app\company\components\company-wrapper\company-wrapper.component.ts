import { Component, OnInit, OnDestroy } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import { CompanyWrapperService } from '../../services/company-wrapper.service';
import { Subject } from 'rxjs';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import 'rxjs/add/operator/takeUntil';
import { CompanyFormService } from "app/company/services/company-form.service";
import { DataMap } from "shared/Models/data_map";
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-company-wrapper',
  templateUrl: './company-wrapper.component.html',
  styleUrls: ['./company-wrapper.component.css']
})
export class CompanyWrapperComponent implements OnInit, OnDestroy {
  
  data_map = new DataMap();
  default_lang :any = 1;
  companypro;
  companyId = Number (localStorage.getItem('company_id'));
  private ngUnsubscribe: Subject<any> = new Subject();
  username = '';

  constructor(private route: ActivatedRoute,
    private router: Router,
    private title: Title,
    private companyFormService: CompanyFormService,
  ) { 
    
    localStorage.setItem('current_company_language', this.default_lang );
    this.route.params.subscribe(res => {
      this.username = res['username'];
    });
  }

  ngOnInit() {
    this.title.setTitle('CVeek');
    // console.log("in company wrapper constructer");
  //  this.getProfiles_route();
  }

  // getProfiles_route() {
  //   this.companyFormService.getProfiles(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
  //   (res) => {
  //     console.log(res)
  //     if(res['translation'] !== undefined) {
  //       // let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
  //       // this.companyFormService.send_Data(send_Data_to_preview);
  //       this.router.navigate(['/c',this.username,'manage_advs']);
  
  //     } else if(res['data'].length === 0) {
  //       let send_Data_to_preview = [];
  //       this.companyFormService.send_Data(send_Data_to_preview);
  //       this.router.navigate(['/c',this.username,'profile','new']);
  //     }
  //   });
  // }

    ngOnDestroy() {
      this.companyFormService.send_Data([]);
      this.ngUnsubscribe.next();
      this.ngUnsubscribe.complete();
    }

}
