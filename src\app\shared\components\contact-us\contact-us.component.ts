import { GeneralService } from './../../../general/services/general.service';
import { SharedService } from './../../../company/components/google-form/form-services/shared.service';
import { Component, OnInit } from '@angular/core';
import { ContactService } from '../../shared-services/contact.service';
import { LanguageService } from '../../../admin/services/language.service';
import { FormBuilder, Validators, AbstractControl, ValidationErrors, FormArray, FormGroup, FormControl } from '@angular/forms';
import { OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { Subject } from 'rxjs/Subject';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng';
import { Title } from '@angular/platform-browser';
import { EmailValidator } from 'shared/validators/email.validators';
import { invalid } from '@angular/compiler/src/render3/view/util';
import { DataMap } from 'shared/Models/data_map';
declare var $: any;

@Component({
  selector: 'app-contact-us',
  templateUrl: './contact-us.component.html',
  styleUrls: ['./contact-us.component.css']
})
export class ContactUsComponent implements OnInit {
 contactForm;
  @Input() show = new Subject();
  @Input('isAReportMsg')isAReportMsg = false;
  @Output('closeContactForm')closeContactForm = new EventEmitter();
  role = '';
  private ngUnsubscribe: Subject<any> = new Subject();
  languagesArray: any = [];
  currentLangId = 1;
  mainCat: {'label': string, 'value': number }[][] = [];
  subCat: {'label': string, 'value': number , 'main_cat_id': number}[][] = [];
  filteredSubCats: {'label': string, 'value': number , 'main_cat_id': number}[][] = [];
  displayAlert = false;
  errorMsg;
  severity = 'success';
  visitor = false;
  userEmail = '';
  name = '';
  main_cat_id: number = null;
  sub_cat_id: number = null;
  submitted: boolean =false;
  closed: boolean=true;

  uploadedFiles : any [] = [];
  imgError:string = null;
  data_map = new DataMap();
  image_code_to_send:{file:string, file_type:string,is_deleted:boolean} = {file:'',file_type:'',is_deleted:false}
  uploadLabelDisplay = true;
  perPhotoSrc: string="./assets/images/Capture.PNG";
  // attachImages = [];

  constructor(private fb: FormBuilder,
              private languageService: LanguageService,
              private translate: TranslateService,
              private contactService: ContactService,
              private messageService: MessageService,
              private title: Title,
              private generalService: GeneralService
              ) {
                translate.addLangs(['en', 'ar']);
                translate.setDefaultLang('en');
                const browserLang = translate.getBrowserLang();

                // title.setTitle('Contact us | CEVAST');
                if (localStorage.getItem('defaultLang')) {
                  this.currentLangId = +localStorage.getItem('defaultLang');
                  if (this.currentLangId === 2) {
                    this.languageService.changeDirection('app-contact-form', 2,
                     ['.wrap-input100 textarea.input100 ',  'p-dropdown label.ui-dropdown-label', 'form']);
                  }
                }
                translate.use( this.languageService.getLangAbbrev(this.currentLangId));
              }

  ngOnInit() {
    this.translate.setDefaultLang('en');
    this.translate.use("en");

    var role= localStorage.getItem('role');
    if ( role !=='unauth') {
      this.role = localStorage.getItem('role');
      if(localStorage.getItem('email') ){
        this.userEmail = localStorage.getItem('email');
      }
      // if(localStorage.getItem('id_token_claims_obj') && JSON.parse(localStorage.getItem('id_token_claims_obj')).user_info){
      //   this.userEmail = JSON.parse(localStorage.getItem('id_token_claims_obj')).user_info.email;
      // }
      this.visitor = false;
    } else {
      this.visitor = true;
      this.role = '';
      this.userEmail = '';
    }
    
    if (this.isAReportMsg) {
      this.main_cat_id = 2;
    }

    this.buildEmptyForm();
    // this.getCategories();

    this.show.subscribe((data: any) => {
        if (data === true && this.mainCat.length === 0) {
             this.getLanguages();
        }
    });

    this.generalService.internalMessage.subscribe((res) => {
      if(res['message'] === 'roleChanged' && res['src'] === 'membership'  && res['dist'] === 'contact'){
        if ( res['mData'].role !=='unauth') {
          this.role = res['mData'].role;
          this.userEmail = res['mData'].email;
          this.visitor = false;
        } else {
          this.visitor = true;
          this.role = '';
          this.userEmail = '';
        }
      }
      this.buildEmptyForm();

      //set category if it is a special case
      if(res['message'] === 'setErrorCategory' && res['src'] === 'interceptor'  && res['dist'] === 'contact'){
        this.main_cat_id = 7;
        if(this.languagesArray.length !==0){
          this.contact_main_cat_id.setValue(this.main_cat_id);
          this.filter();
        }
      }

      if(res['dist'] === 'contact'){
        if(res['message'] === 'RequestNewValue'){
          this.main_cat_id = 5;
    
          this.contact_main_cat_id.setValue(this.main_cat_id);
          this.filter();
          if(res['mData'].subCategory !== ''){
            let subCat = res['mData'].subCategory;
            if(subCat === 'jobTitle'){
              this.sub_cat_id = 12;
              this.contact_sub_cat_id.setValue(this.sub_cat_id);
            }
            else if(subCat === 'educationField'){
              this.sub_cat_id = 8;
              this.contact_sub_cat_id.setValue(this.sub_cat_id);
            }
          }
              
          // if(this.languagesArray.length !==0){
          //   this.contact_main_cat_id.setValue(this.main_cat_id);
          //   this.filter();
          // }
           
        }

        else if(res['message'] === 'ResetCategory'){
          this.contact_main_cat_id.setValue(null);
          this.contact_sub_cat_id.setValue(null);
          this.main_cat_id = null;
          this.sub_cat_id = null;
        }

      }

    });
 
  }

  buildEmptyForm() {
    this.contactForm = this.fb.group({
      'language_id': [this.currentLangId, Validators.required],
      'email'     : [this.userEmail, [ Validators.required, EmailValidator.isValidEmailFormat ]],
   //   'email'     : [this.userEmail, [ Validators.required, Validators.email ]],
      'email_role'      : [ this.role, Validators.required],
      'contact_main_cat_id': [this.main_cat_id, Validators.required],
      'contact_sub_cat_id': [this.sub_cat_id !==null? this.sub_cat_id : null],
      'message'      : ['', Validators.required],
      'attach_images': this.fb.array([this.createImage()]),
    });
    
  //  this.getLanguages();
  }

  getCategories() {
    for (let i = 0; i < this.languagesArray.length; i++) {
     this.contactService.getAllCategories(i + 1).takeUntil(this.ngUnsubscribe).subscribe(res => {
    //    this.contactService.getAllCategories(i).takeUntil(this.ngUnsubscribe).subscribe(res => {
        this.mainCat[i] = [];
        this.subCat[i] = [];
        this.filteredSubCats[i] = [];

        // filling main_cat array
        let mainCatsTemp = res['ContactMainCat'];
        for (let category of mainCatsTemp) {
          if (category.contact_main_cat_translation.length !== 0) {
            this.mainCat[i].push({
              'label': category.contact_main_cat_translation[0].name,
              'value':  category.id,
            });
          }

        }
        this.mainCat[0].unshift({ 'label': '' , 'value': null});
        //this.mainCat[i].unshift({ 'label': '' , 'value': null});

        // filling sub_cat array
        let subCatsTemp = res['ContactSubCat'];
        for (let category of subCatsTemp) {
          if (category.contact_sub_cat_translation.length !== 0) {
            this.subCat[i].push({
              'label': category.contact_sub_cat_translation[0].name,
              'value': category.id,
              'main_cat_id': category.contact_main_cat_id
            });
          }
        }
        //this.subCat[i].unshift({ 'label': '' , 'value': null, 'main_cat_id': null});

        if(this.main_cat_id !==null){
          this.filter();
        }
      });

    }

  }

  getLanguages() {
    this.languagesArray.push({
      'id'  : + localStorage.getItem('defaultLang'),
      'name': ""
    });
    
    this.getCategories();
  //   this.languageService.getLanguages().takeUntil(this.ngUnsubscribe).subscribe(res => {
  //     let temp = res['data'];
  //     for ( let lang of temp) {
  //       this.languagesArray.push({
  //         'id'  : lang.id,
  //         'name': lang.name
  //       });
        
  //     }
  //    this.getCategories();
  // });
 }

  filter() {
    this.errorMsg = null;
    for (let j = 0; j < this.languagesArray.length; j++) {
      this.filteredSubCats[j] = [];
      for (let i = 0; i < this.subCat[j].length; i++) {
        if (this.subCat[j][i].main_cat_id === this.contact_main_cat_id.value) {
          this.filteredSubCats[j].push({
            'value': this.subCat[j][i].value,
            'label': this.subCat[j][i].label,
            'main_cat_id': this.subCat[j][i].main_cat_id
          });
        }
      }
      this.filteredSubCats[j].unshift({ 'label': '' , 'value': null, 'main_cat_id': null});

    }
   
    if (this.filteredSubCats[0] !==undefined && this.filteredSubCats[0].length <= 1 ) {
      
    //  this.contact_sub_cat_id.setValue(this.sub_cat_id);
      this.contact_sub_cat_id.setValue(null);
      this.contact_sub_cat_id.setErrors(null);
      this.contact_sub_cat_id.errors = null;
    }

  }




  send(form: any) {
  //  this.submitted=false;
  //  this.submitted=true;
    form.submitted = false;
    if (this.contactForm.valid) {
      let dataToSend = this.contactForm.value;

      if ( dataToSend.contact_main_cat_id !== null  ) {
        dataToSend.language_id = this.currentLangId;
            
        if(dataToSend.attach_images[0] === ""){
          this.attachImages.removeAt(0);
          dataToSend.attach_images.shift();
        }
       
        this.contactService.sendMessage(dataToSend).subscribe
        ((res) => {
          if(res['message']){
            $("#contactModal").modal("hide");
            this.main_cat_id = null;
            this.sub_cat_id = null;
            this.contact_main_cat_id.setValue(null);
            this.contact_sub_cat_id.setValue(null);
            this.message.setValue('');
            this.generalService.notify('success' , 'contact','app' , {'message':res['message']});
          }
          else if(res['error']){
            this.errorMsg = res['error']
          }
        });
      
         
        }

        else {
             //alert('please enter valid values');
          //   this.submitted=false;

        }
    }

  }


 showSuccess() {
    this.severity = 'success';
    this.displayAlert = true;
    //this.msg = 'MsgSentSuccessfully';
 }

 showError() {
    this.severity = 'error';
    this.displayAlert = true;
    //this.msg = 'FailToSendMsg';
 }

 hideAlert() {
  this.displayAlert = false;
 }


  get email() {
    return this.contactForm.get('email');
  }

  get email_role() {
    return this.contactForm.get('email_role');
  }

  get contact_main_cat_id() {
    return this.contactForm.get('contact_main_cat_id');
  }

  get contact_sub_cat_id() {
    return this.contactForm.get('contact_sub_cat_id');
  }

  get message() {
    return this.contactForm.get('message');
  }

  onFileChanged(event: any) {
    let files = event.target.files;
    let result;
    
    if(files[0].size > 1048576 || (files[0].type !== 'image/jpeg' && files[0].type !== 'image/png') ){
      if(files[0].size > 1048576){
        this.imgError = "validationMessages.imageSizeBig";
      }
      else if(files[0].type !== 'image/jpeg' && files[0].type !== 'image/png'){
        this.imgError = "validationMessages.invalidImageType";
      }
    }

   else{
    this.imgError = null;  
   
    const reader = new FileReader()
    for(let file of files) {
      this.uploadedFiles.push(file) ;
    }
    let file = this.uploadedFiles[0];
    this.data_map.upload_file(file).then(
      (res)=>{
        result = res;
        this.image_code_to_send = result;
        this.uploadLabelDisplay = false;
        this.uploadedFiles = [];
        this.attachImages['controls'][0].setValue(this.image_code_to_send);
      });

    reader.onload = () => {
      this.perPhotoSrc = reader.result as string;
    }
    reader.readAsDataURL(file)
   }
  }
  

  // private createImage() {
  //   return new FormGroup({
  //     'attach_images': new FormControl(''),
  //   });
  // }

  private createImage(image?) {
    if(image)
      return new FormControl(image);

    return new FormControl("");
  }

  addImage() {
    if(this.attachImages['controls'][0].value !==""){
      this.attachImages.insert(0, this.createImage(""));
    }
  }

  removeImage(i) {
    this.attachImages.removeAt(i);
  }

  get attachImages() {
    return this.contactForm.get('attach_images') as FormArray;
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
   }

}
