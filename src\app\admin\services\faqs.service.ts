import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import {Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ExportUrlService} from 'shared/shared-services/export-url.service';
import { Question } from '../models/question';
import { Observable } from 'rxjs/observable';

@Injectable()
export class FaqsService  {
  // url = 'http://localhost:8000/admin/faq';
  url = '';
  baseUrl = '';
  private questionSource = new BehaviorSubject<Question>({id: null , category: '', question: '', answer: '', order: null,
  activation: false, type: '', langId: null, catId: null });
  newQuestion: Observable<Question> = this.questionSource.asObservable();

  constructor(private http: HttpClient, private privateSharedURL: ExportUrlService) {
    this.privateSharedURL.publicUrl.take(1)
    .subscribe(data => {
          this.url     = data + 'admin/faq';
          this.baseUrl = data;
    });
  }

  refreshQuestionValue(q: Question) {
     this.questionSource.next(q);
  }

  getFaqsCategories(langId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/get_faq_categories_by_language_id/' + langId, { headers });
  }

  getFaq( faqId: number) {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url + '/' + faqId , { headers });
  }


  getFaqs() {
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.get(this.url, { headers });
  }

  createFaq( q ) {
    console.log('after json.stringify', JSON.stringify(q));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url , JSON.stringify(q), { headers } );

  }

  addFaqTranslation(q) {
    console.log('after json.stringify', JSON.stringify(q));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url , JSON.stringify(q), { headers } );
  }

  updateFaq( q , id) {
    console.log('after json.stringify', JSON.stringify(q));
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.put(this.url + '/' + id , JSON.stringify(q), { headers });
  }

  deleteFaq(id) {
    console.log('id', id);
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.delete(this.url + '/' + id, { headers } );
  }

  activateFaq( id, body) {
    console.log('body, id', JSON.stringify(body), id );
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this.http.post(this.url + '/active/' + id, JSON.stringify(body), { headers });
  }


}
