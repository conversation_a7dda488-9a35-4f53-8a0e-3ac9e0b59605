<div id="page-content-wrapper">

        <p-toast position="bottom-right"></p-toast>

    <!--  [style.margin-left]="marginLeft" -->
    <div class="page-content table-page-content">
        <form #form="ngForm" (keydown.enter)="$event.preventDefault()" [formGroup]="companyLocation" class="form-horizontal validate-form" (ngSubmit)="form.valid && submit(form)" [appInvalidControlScroll]="'normalComponent'"
        >
            <div class="row clearfix flex_row">

                <div class="col-md-offset-1 col-md-10 col-xs-12 custom-col-11 prev-border add-certification" style="
                padding: 30px;">
                    <p style="color: #276ea4;" class="add-certification-p" translate>company_location.AddLocation</p>
                    <div class="location">
                        <!-- <div class="form-group focus-container has-feedback">
                            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                            </div>
                            <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding">
                                <input id="check-verify" type="checkbox" (change)="changAutoComplete()">

                                <p style="display: inline;">Search Company with Google.</p>
                            </div>
                        </div> -->
                        <div class="form-group focus-container has-feedback" [ngClass]="{'has-error': form.submitted && !companyLocation.controls['name'].value }">
                            <!-- [ngClass]="{'has-error':form.submitted && !isDDValid('language_id')}" -->
                            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                            </div>
                            <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding"
                             [ngClass]="{'has-val':companyLocation.controls['name'].value}">
                                <input formControlName="name" placeholder=" " type="text" class="form-control">
                            
                                <span class="custom-underline"></span>
                                <span *ngIf="form.submitted && !companyLocation.controls['name'].value" class="form-control-feedback" aria-hidden="true"></span>
                                <span *ngIf="form.submitted && !companyLocation.controls['name'].value" style="left: 100%;
                                top: 8px;" class="error-message" translate>validationMessages.required</span>
                                <label class="control-label custom-control-label" translate>company_location.Name</label>
                            </div>
                        
                        </div>

                        <div class="form-group focus-container" [ngClass]="{'has-error': form.submitted && ( !companyLocation.controls['location'].value || !companyLocation.controls['country_code'].value) }">
                            <!-- [ngClass]="{'has-error':form.submitted && !languageForm.controls['type'].valid}" -->
                            <div class="col-lg-3 col-md-2 col-sm-3 alignment-right">
                            </div>
                            <div class="col-lg-6 col-md-8 col-sm-6 focus-no-padding" [ngClass]="{'has-val':companyLocation.controls['location'].value}">
                                <input formControlName="location" placeholder=" " type="text" class="form-control" id="location" #googlelocationplace  (keyup)="clearLocationData()">
                                <!-- (keyup.enter)="currentLocationKeyUp($event)" -->
                                <span class="custom-underline"></span>
                                <span *ngIf="form.submitted && !companyLocation.controls['location'].value" class="form-control-feedback" aria-hidden="true"></span>
                                <span *ngIf="form.submitted && !companyLocation.controls['location'].value" style="left: 100%;
                                top: 8px;" class="error-message" translate>validationMessages.required</span>
                                <span *ngIf="form.submitted && ( companyLocation.controls['location'].value && !companyLocation.controls['country_code'].value)" 
                                 class="error-message" translate>validationMessages.ChooseAutoCompleteSuggestionsError</span>
                                <label class="control-label custom-control-label" translate>company_location.Location</label>
                                
                            </div>
                        </div>
                      <!-- has-feedback other-lang -->
                        <!-- <div *ngIf="check_location" class="form-group focus-container" style="padding: 20px;">
                            <app-map [sub]="currentSub" birthOrCurrent="current" [type]="currentType" [search]="googlelocationplaceRef">
                                
                            </app-map>
                        </div> -->
                    </div>
                    <div class="minamize-certification"><i class="fa fa-angle-up" aria-hidden="true"></i></div>
                </div>
                <div class="col-xs-1  custom-col-1">
                    <button class="btn btn-success add-btn" type="submit" pTooltip="{{'shared.save' | translate}}" tooltipPosition="top">
                        <i class="fa fa-plus" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
        </form>
        <div class="row div-margin-top-40">
            <div class="col-md-offset-1 col-md-10 col-xs-12">
                <div class="table-preview-container" [ngClass]="{'loading-custom-table': tableLoader === true , 'loaded-custom-table' : tableLoader === false }">
                    <div class="loader-container">
                        <p-progressSpinner  [style]="{width: '30px', height: '30px'}" strokeWidth="5"></p-progressSpinner>
                    </div> 
                    <table class="table-preview">
                        <thead>
                            <tr>
                                <th><span translate>company_location.MainOffice</span></th>
                                <th><span translate>company_location.Name</span></th>
                                <th><span translate>company_location.Location</span></th>
                                <th><span translate>company_location.Actions</span></th>
                            </tr>
                        </thead>
                        <tbody [sortablejs]="companyLocationData" [sortablejsOptions]="options">
                            <tr *ngIf="tableLoader === false && companyLocationData.length === 0 ; else tableData">
                                <td class="text-center" colspan="4" class="table-no-data" translate>company_location.noDataEntered</td>
                            </tr>
                            <ng-template #tableData>
                                <tr *ngFor="let data of companyLocationData; let i=index;" id=" {{data.order}} ">
                                    <!-- *ngFor="let language of languages; let i=index;" id="{{language.order}}" -->
                                    <td (click)="setMainOffice(data)">
                                        <span class="th-mobile" translate>company_location.MainOffice</span>
                                        <input type="radio" name='radioBtn' [checked]="(data.is_main_office) ? true:false">
                                    </td>
                                    <!-- {{ language.international_language.international_language_trans[0].name }} -->
                                    <td>
                                        <span class="th-mobile" translate>company_location.Name</span>
                                        <span translate>{{ data.name }}</span>
                                    </td>
                                    <td>
                                        <span class="th-mobile" translate>company_location.Location</span>
                                        <span *ngIf=" data.city &&  data.country && data.street_address">
                                            {{ data.country }}, {{ data.city }}, {{ data.street_address }}
                                        </span>
                                        <span *ngIf=" data.city &&  data.country && data.street_address===null">
                                            {{ data.country }}, {{ data.city }}
                                        </span>
                                        <span *ngIf=" data.city &&  data.country===null && data.street_address">
                                            {{ data.city }}, {{ data.street_address }}
                                        </span>
                                        <span *ngIf=" data.city===null &&  data.country && data.street_address===null">
                                            {{ data.country }}
                                        </span>
                                        <span *ngIf=" data.city===null &&  data.country && data.street_address">
                                            {{ data.country }}, {{ data.street_address }}
                                        </span>
                                        <span *ngIf=" data.city===null &&  data.country===null && data.street_address">
                                            {{ data.street_address }}
                                        </span>
                                        <span *ngIf=" data.city===null &&  data.country===null && data.street_address===null">
                                            
                                        </span>
                                        
                                    </td>
                                    <td>
                                        <span class="th-mobile" translate>company_location.Actions</span>
                                        <button type="button" class="btn btn-primary btn-fa-info" 
                                        data-toggle="modal" data-target="#locationModal" (click)="display_Location_Modal(data)"
                                        pTooltip="Add more info & edit" tooltipPosition="top">
                                        <img src="./assets/images/buttons/Add-Edit-btn.png" alt="{{'shared.addInfoEdit' | translate}}">
                                        <!-- (click)="displayModal(language)" -->
                                            <!-- <i class="fa fa-edit" aria-hidden="true"></i> -->
                                        </button>
                                        <button class="btn btn-delete btn-trash-cust" (click)="removeCompanyLocation(data)">
                                        <!-- (click)="removeLanguage(language)" -->
                                            <i class="fa fa-trash" aria-hidden="true"></i>
                                        </button>

                                        <!-- <i class="fa fa-arrows"></i> -->

                                    </td>
                                </tr>
                            </ng-template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
    <!-- <p> {{ form.value | json }} </p> -->
</div>

<div class="modal fade" id="locationModal" *ngIf="display_location_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" (click)="close_Location_Modal()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModal2Label" translate>company_location.EditLocation</h4>
            </div>
            <location-modal [location_item]="locationToBePassedToModal"
            (closeModalPopup)="handlePopup($event)">
            </location-modal>
        </div>
    </div>
</div>