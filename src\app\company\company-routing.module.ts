import { NgModule } from '@angular/core';
import { RouterModule , Routes } from '@angular/router';
import { CompanyProfileInfoComponent } from './components/company-profile-info/company-profile-info.component';
import { CompanyInformationPreviewComponent } from './components/company-information-preview/company-information-preview.component';
import { FormEditComponent } from './components/form-edit/form-edit.component';
//import { AuthGuardService } from 'shared/shared-services/auth-guard.service';
import { CompanyLocationComponent } from './components/company-location/company-location.component';
import { LocationModalComponent } from './components/location-modal/location-modal.component';
import { CompanySettingsComponent } from './components/company-settings/company-settings.component';
import { CompanyGuardService } from 'shared/shared-services/company-guard.service';
import { CompanyAdverComponent } from './components/company-adver/company-adver.component';
import { CompanyPostJobComponent } from './components/company-post-job/company-post-job.component';
import { AdvrPreviewComponent } from "app/company/components/advr-preview/advr-preview.component";
import { ArrayStorageComponent } from "app/company/components/google-form/array-storage/array-storage.component";
import { ChartComponent } from "app/company/components/google-form/chart/chart.component";
import { BasicRepliesComponent } from "app/company/components/google-form/basic-replies/basic-replies.component";
import { FormPreviewComponent } from "app/company/components/google-form/form-preview/form-preview.component";
import { FullFormCustomerGeneratorComponent } from "app/company/components/google-form/full-form-customer-generator/full-form-customer-generator.component";
import { TestStorgeComponent } from "app/company/components/google-form/test-storge/test-storge.component";
import { UserReplyComponent } from "app/company/components/google-form/user-reply/user-reply.component";
import { ManageAdvsComponent } from "app/company/components/manage-advs/manage-advs.component";
import { CvSearchComponent } from './components/cv-search/cv-search.component';
import { PersonalSettingsComponent } from './components/personal-settings/personal-settings.component';
import { LanguageSettingComponent} from './components/language-setting/language-setting.component'
import { CompanyWrapperComponent } from "app/company/components/company-wrapper/company-wrapper.component";
import { CompanyFormComponent } from "app/company/components/company-form/company-form.component";
import { CompanyAccountSettingsComponent } from './components/company-account-settings/company-account-settings.component';
import { PublicPreviewComponent } from './components/public-preview/public-preview.component';
import { AdvsManageComponent } from './components/advs-manage/advs-manage.component';

const routes: Routes = [
    /* { path: 'generator', component: FullFormCustomerGeneratorComponent }, */
    { path: 'array-storage', component: ArrayStorageComponent },
    { path: 'chart', component: ChartComponent },
    { path: 'replies', component: BasicRepliesComponent },
    { path: 'preview/:id', component: FormPreviewComponent },
    { path: 'user-replies/:id', component: UserReplyComponent },
    { path: 'test-storage', component: TestStorgeComponent },
    {path: ':username', component: CompanyWrapperComponent, children: [
        {path: '', component: AdvsManageComponent},
        { path: 'manage_advs', component: AdvsManageComponent },
        { path: 'manage_advs/:status', component: AdvsManageComponent },
        { path: 'manage_advs', component: AdvsManageComponent },
        { path: 'cv_search', component: CvSearchComponent },
    // {path: 'wrapper-language', component: WrapperLanguageComponent},
        {path: 'post-job', component: CompanyPostJobComponent},
        /* { path: 'generator', component: FullFormCustomerGeneratorComponent }, */
        {path: 'adv/:advId', component: AdvrPreviewComponent},
     //   {path: 'settings', component: CompanySettingsComponent , children: [
          //  {path :'language-setting' , component: LanguageSettingComponent},
            {path:'account-settings', component:CompanyAccountSettingsComponent} ,
     //   ]},
        {path: 'personal-settings', component: PersonalSettingsComponent},

        {path: 'profile', component: CompanyProfileInfoComponent, children: [
            {path: 'new', component: CompanyFormComponent},
            {path: 'preview', component: CompanyInformationPreviewComponent },
            {path: 'edit', component: CompanyFormComponent},
        //    {path: 'edit', component: FormEditComponent },
            {path: 'location', component: CompanyLocationComponent},
            {path: 'public-preview', component: PublicPreviewComponent },
            //  {path: 'location-modal', component: LocationModalComponent},

    ]}
], canActivate: [CompanyGuardService]},

];


@NgModule({
    imports: [ RouterModule.forChild(routes) ],
exports: [ RouterModule ]
})
export class CompanyRoutingModule {

}
