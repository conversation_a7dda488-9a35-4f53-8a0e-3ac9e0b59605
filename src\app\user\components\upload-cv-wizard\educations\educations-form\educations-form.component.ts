
import { Component, OnInit, Input, Output, EventEmitter, ViewChildren, QueryList, ElementRef, AfterViewInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/takeUntil';
import 'rxjs/add/operator/switchMap';
import { LazyloadDropdownService } from 'shared/shared-services/lazyload-dropdown.service';
import { LazyloadDropdownClass } from 'shared/Models/lazyloadDropdown';
import { EducationValidators } from 'app/user/components/education/education.validators';
import { UploadCvPdfService } from 'app/user/cv-services/upload-cv-pdf.service';
import { MessageService } from 'primeng/api';

declare var google: any;

@Component({
  selector: 'app-educations-form',
  templateUrl: './educations-form.component.html',
  styleUrls: ['./educations-form.component.css']
})
export class EducationsFormComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() educations: any[] = [];
  @Input() resumeId: number | null = null;
  @Input() languageId: number | null = 1;
  @Input() fromSteps: boolean = false;
  @Input() school_education_fields: any[] = [];
  @Input() university_education_fields: any[] = [];
  @Input() degreeLevelValues: any[] = [];
  @Output() educationsChange = new EventEmitter<any[]>();
  @Output() nextStep = new EventEmitter<void>();
  @Output() prevStep = new EventEmitter<void>();

  @ViewChildren('googlePlace') googlePlaces: QueryList<ElementRef>;

  educationsForm: FormGroup;

  // Dropdown options
  fromYearOpts: any[] = [];
  toYearOpts: any[] = [];
  monthOpts: any[] = [];
  jobTitles: any[] = [];


  // Education fields data
  filteredEducationFields: any[] = [];
  filteredUniversities: any[] = [];

  // Institution autocomplete config
  institutionDD: LazyloadDropdownClass;

  // For cleanup
  private ngUnsubscribe = new Subject<void>();

  showLoader: boolean = true;

  formsWithValidationErrors: Set<number> = new Set();
  isMinimized: boolean[] = [];

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private lazyloadDropdownService: LazyloadDropdownService,
    private uploadCvPdfService: UploadCvPdfService,
    private cdRef: ChangeDetectorRef,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
    this.educationsForm = this.fb.group({
      educationsList: this.fb.array([]),
      resume_id: [this.resumeId]
    });

    // Initialize dropdown data
    this.initYearOptions();
    this.initMonthOptions();

    // Use resumeId input directly
    this.getEducationData();
  }

  ngAfterViewInit() {
    this.initGooglePlaces();

    // Re-initialize Google Places when new education forms are added
    this.googlePlaces.changes.subscribe(() => {
      this.initGooglePlaces();
    });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  // Form array getter
  get educationControls() {
    return (this.educationsForm.get('educationsList') as FormArray).controls;
  }

  // Get education data
  async getEducationData() {
    this.showLoader = true;
    this.institutionDD = new LazyloadDropdownClass(
      this.lazyloadDropdownService,
      'institutions',
      10,
      this.languageId
    );
    this.educationsForm.get('resume_id').setValue(this.resumeId);
    // Populate FormArray with existing educations or add a blank one
    const educationsArr = (this.educationsForm.get('educationsList') as FormArray);
    educationsArr.clear();
    if (this.educations && this.educations.length > 0) {
      // Wait for all async createEducationForm calls to complete
      const formPromises = this.educations.map(education => {
        // Find the degree_level object with the correct reference from our options list
        const degreeLevelObject = this.degreeLevelValues.find(
          dl => dl.degree_level_id === education.degree_level?.degree_level_id
        );

        const educationForForm = {
          ...education,
          degree_level: degreeLevelObject,
        };
        return this.createEducationForm(educationForForm);
      });
      const forms = await Promise.all(formPromises);
      forms.forEach(form => educationsArr.push(form));
    } else {
      const form = await this.createEducationForm();
      educationsArr.push(form);
    }
    this.showLoader = false;
  }

  // Initialize year options
  initYearOptions() {
    const currentYear = new Date().getFullYear();
    this.fromYearOpts = [];
    this.toYearOpts = [];

    // Add "Present" option to the "To" year dropdown
    this.toYearOpts.push({ label: this.translateService.instant('shared.present'), value: 'present' });

    // Add year values (current year to 50 years ago)
    for (let i = currentYear; i >= currentYear - 50; i--) {
      const yearOpt = { label: i.toString(), value: i.toString() };
      this.fromYearOpts.push(yearOpt);
      this.toYearOpts.push(yearOpt);
    }
  }

  // Initialize month options
  initMonthOptions() {
    this.monthOpts = [
      { label: this.translateService.instant('months.january'), value: '1' },
      { label: this.translateService.instant('months.february'), value: '2' },
      { label: this.translateService.instant('months.march'), value: '3' },
      { label: this.translateService.instant('months.april'), value: '4' },
      { label: this.translateService.instant('months.may'), value: '5' },
      { label: this.translateService.instant('months.june'), value: '6' },
      { label: this.translateService.instant('months.july'), value: '7' },
      { label: this.translateService.instant('months.august'), value: '8' },
      { label: this.translateService.instant('months.september'), value: '9' },
      { label: this.translateService.instant('months.october'), value: '10' },
      { label: this.translateService.instant('months.november'), value: '11' },
      { label: this.translateService.instant('months.december'), value: '12' }
    ];
  }

  // Create a new education form group
  async createEducationForm(education: any = null): Promise<FormGroup> {
    // Find the degree_level object with the correct reference from our options list
    const degreeLevelObject = this.degreeLevelValues.find(
      dl => dl.degree_level_id === education?.degree_level?.degree_level_id
    );

    // Find the education_field object from the correct list if possible
    let educationFieldObject = education?.education_field || null;
    if (education?.education_field?.id && education?.education_field?.id !== -1) {
      // Determine which fields to use based on degree level
      let fieldsToSearch = this.university_education_fields;
      if (degreeLevelObject && degreeLevelObject.is_school) {
        fieldsToSearch = this.school_education_fields;
      }
      educationFieldObject = fieldsToSearch.find(f => f.id === education.education_field.id) || education.education_field;
    } else {
      educationFieldObject = education?.education_field || null;
    }

    // Find the institution object from institutionDD if possible
    let institutionObject: any = null;

    // If institution ID is -1, use it directly as a custom institution
    if (education?.institution?.id === -1) {
      institutionObject = education?.institution;
      this.institutionDD.items = [institutionObject, ...this.institutionDD.items];
      // institutionObject = {
      //   id: -1,
      //   name: education.institution.name,
      //   city: education.institution.city || null,
      //   country: education.institution.country || null,
      //   country_code: education.institution.country_code || null,
      //   url: education.institution.url || null,
      //   fullLocation: education.institution.fullLocation ||
      //     (education.institution.city && education.institution.country
      //       ? `${education.institution.city}, ${education.institution.country}`
      //       : null)
      // };
      console.log('Custom institution created:', institutionObject);
    } else {
      if (education?.institution?.id && this.institutionDD?.items) {
        institutionObject = this.institutionDD.items.find((i: any) => i.id === education?.institution.id);
      }
      // If not found by id in local items, try to fetch by id from backend
      if (!institutionObject && education?.institution?.id) {
        const res = await this.institutionDD.getById(education.institution.id).toPromise();
        institutionObject = res && res['data'] && res['data'].length ? res['data'][0] : null;
      }
      // If still not found by id, try to fetch by name
      if ((!institutionObject || !institutionObject?.id) && education?.institution?.name) {
        const res = await this.institutionDD.getOriginalValue(education.institution.name).toPromise();
        institutionObject = res && res['data'] && res['data'].length ? res['data'][0] : { id: '', name: education.institution.name };
      }
      // Ensure the fetched institution is in the dropdown options
      if (institutionObject && institutionObject.id) {
        const exists = this.institutionDD.items.some((i: any) => i.id === institutionObject.id);
        if (!exists) {
          this.institutionDD.items = [institutionObject, ...this.institutionDD.items];
        }
      }
    }

    const toYear = (education?.isPresent) ? 'present' : (education?.to?.year?.toString() || null);
    const toMonth = (education?.isPresent) ? null : (education?.to?.month?.toString() || null);
    const educationGroup = this.fb.group({
      degree_level: [degreeLevelObject || null, Validators.required],
      degree_level_id: [degreeLevelObject?.degree_level_id || null],
      education_field: [educationFieldObject || null, Validators.required],
      institution: this.fb.group({
        id: [institutionObject?.id || null],
        name: [institutionObject?.name || null, Validators.required],
        city: [institutionObject?.city || null, Validators.required],
        country: [institutionObject?.country || null],
        country_code: [institutionObject?.country_code || null],
        url: [institutionObject?.url || null],
        fullLocation: [
          (institutionObject?.city && institutionObject?.country)
            ? `${institutionObject.city}, ${institutionObject.country}`
            : institutionObject?.fullLocation || null,
          Validators.required
        ]
      }),
      from: this.fb.group({
        year: [education?.from?.year?.toString() || null],
        month: [education?.from?.month?.toString() || null]
      }),
      to: this.fb.group({
        year: [toYear, Validators.required],
        month: [toMonth]
      }),
      isPresent: [education?.isPresent || false],
      description: [education?.description || '']
    }, { validators: [EducationValidators.compareDatesValidator] });

    // If isPresent, disable the to.month control
    if (education?.isPresent) {
      (educationGroup.get('to.month') as FormControl).disable();
    }

    this.cdRef.detectChanges();
    return educationGroup;
  }

  // Add a new education
  addEducation(): void {
    const educationsArr = this.educationsForm.get('educationsList') as FormArray;
    this.createEducationForm().then(newEducation => educationsArr.push(newEducation));
  }

  // Remove an education
  removeEducation(index: number): void {
    const educationsArr = (this.educationsForm.get('educationsList') as FormArray);

    // Remove the form from the validation errors set
    this.clearValidationErrors(index);

    // Remove the form from the form array
    educationsArr.removeAt(index);

    // Update the validation errors set for remaining forms
    // We need to shift down the indices for forms that come after the removed one
    const newValidationErrors = new Set<number>();
    this.formsWithValidationErrors.forEach(oldIndex => {
      if (oldIndex < index) {
        // Keep the same index for forms before the removed one
        newValidationErrors.add(oldIndex);
      } else if (oldIndex > index) {
        // Shift down the index for forms after the removed one
        newValidationErrors.add(oldIndex - 1);
      }
      // Skip the removed index
    });
    this.formsWithValidationErrors = newValidationErrors;
  }

  // Update the parent component with valid educations
  updateEducations(): void {
    const educationsList = this.educationsForm.get('educationsList') as FormArray;
    const validEducations = [];

    for (let i = 0; i < educationsList.length; i++) {
      const educationForm = educationsList.at(i) as FormGroup;
      if (this.isEducationValid(educationForm)) {
        validEducations.push(educationForm.value);
      }
    }

    this.educationsChange.emit(validEducations);
  }

  // Add to be submitted (called when + button is clicked)
  addToBeSubmitted(form: any): void {
    // Find the form group in the form array
    const educationsList = this.educationsForm.get('educationsList') as FormArray;
    let educationForm: FormGroup = null;
    let index = -1;

    // Find which education form was submitted
    for (let i = 0; i < educationsList.length; i++) {
      const currentForm = educationsList.at(i) as FormGroup;
      if (currentForm === form) {
        educationForm = currentForm;
        index = i;
        break;
      }
    }

    if (educationForm && educationForm.valid) {
      const educationData = educationForm.value;

      // Prepare data for submission
      if (educationData.degree_level) {
        educationData.degree_level_id = educationData.degree_level.degree_level_id;
      }

      // Update educations list
      this.updateEducations();
    }
  }

  // Check if an education form has the required fields
  isEducationValid(form: FormGroup): boolean {
    return form.valid;
  }

  // Filter education fields based on degree level
  filterEducationFields(event: any, index: number): void {
    const query = event.query;
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
    const degreeLevel = educationForm.get('degree_level').value;

    if (query.length < 2) {
      this.filteredEducationFields = [];
      return;
    }

    // Determine which fields to use based on degree level
    let fieldsToSearch = this.university_education_fields;
    if (degreeLevel && degreeLevel.is_school) {
      fieldsToSearch = this.school_education_fields;
    }

    // Filter fields based on query
    this.filteredEducationFields = fieldsToSearch.filter(field =>
      field.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  loadMoreInstitutions(): void {
    if (this.institutionDD) {
      this.institutionDD.onScrollToEnd();
    }
  }

  addNewInstitution(term: string): any {
    return { id: -1, name: term };
  }

  customSearchInstitution(term: string, item: any): boolean {
    const termLower = term.toLowerCase();
    return item.name.toLowerCase().includes(termLower);
  }

  deleteInstitutionAutoComplete(control: FormControl): void {
    if (control) {
      control.setValue(null);
    }
  }

  // Select university/institution
  selectUniversity(event: any, index: number): void {
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
    const institutionGroup = educationForm.get('institution');

    if (event) {
      // When an item is selected, patch the entire institution group
      institutionGroup.patchValue({
        id: event.id,
        // name: event.name, // The name for display
        city: event.city || '',
        country: event.country || '',
        country_code: event.country_code || '',
        url: event.url || '',
        fullLocation: event.city && event.country ? `${event.city}, ${event.country}` : ''
      });
    }
  }

  // Handle on university key up event
  onUniKeyUp(index: number): void {
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
    const institutionGroup = educationForm.get('institution');

    institutionGroup.patchValue({
      id: null,
      city: '',
      country: '',
      country_code: '',
      url: '',
      fullLocation: ''
    });
  }

  // Handle location input change via Google Places API
  onLocationSelect(place: any, index: number): void {
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
    const institutionGroup = educationForm.get('institution');

    let city = '';
    let country = '';
    let countryCode = '';

    // Extract city and country from place components
    if (place.address_components) {
      for (const component of place.address_components) {
        if (component.types.includes('locality')) {
          city = component.long_name;
        } else if (component.types.includes('country')) {
          country = component.long_name;
          countryCode = component.short_name;
        }
      }
    }

    institutionGroup.patchValue({
      city: city,
      country: country,
      country_code: countryCode,
      fullLocation: `${city}, ${country}`
    });
  }

  // Handle location clear on change
  LocationClearOnChange(event: any, index: number): void {
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
    const institutionGroup = educationForm.get('institution');

    institutionGroup.patchValue({
      city: '',
      country: '',
      country_code: ''
    });
  }

  // Initialize Google Places for location inputs
  initGooglePlaces(): void {
    if (this.googlePlaces) {
      setTimeout(() => {
        this.googlePlaces.forEach((place, index) => {
          const autocomplete = new google.maps.places.Autocomplete(place.nativeElement, {
            types: ['(cities)']
          });

          autocomplete.addListener('place_changed', () => {
            const selectedPlace = autocomplete.getPlace();
            this.onLocationSelect(selectedPlace, index);
          });
        });
      }, 500);
    }
  }

  // Handle change in degree level
  changeDegreeLevel(index: number): void {
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
    const degreeLevel = educationForm.get('degree_level').value;

    if (degreeLevel) {
      educationForm.patchValue({
        degree_level_id: degreeLevel.degree_level_id,
        education_field: null // Reset education field when degree level changes
      });
    }
  }

  // Check if education field is a new value
  checkIfNewValue(control: FormControl, index: number): void {
    if (control && control.value && typeof control.value === 'string') {
      const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);
      educationForm.patchValue({
        education_field: { id: null, name: control.value }
      });
    }
  }

  // Handle to year select
  onToYearSelect(value: any, index: number): void {
    const educationForm = (this.educationsForm.get('educationsList') as FormArray).at(index);

    if (value === 'present') {
      educationForm.patchValue({
        isPresent: true
      });
    } else {
      educationForm.patchValue({
        isPresent: false
      });
    }
  }

  // Get value for dropdown display
  getValueToDD(control: FormControl): boolean {
    return control && control.value ? true : false;
  }

  // Dropdown validation
  isDDValid(control: FormControl): boolean {
    return control && control.valid;
  }

  // Get all valid educations for the parent component
  getEducations(): any[] {
    const educationsList = this.educationsForm.get('educationsList') as FormArray;
    const validEducations = [];

    for (let i = 0; i < educationsList.length; i++) {
      const educationForm = educationsList.at(i) as FormGroup;
      if (this.isEducationValid(educationForm)) {
        validEducations.push(educationForm.value);
      }
    }

    return validEducations;
  }

  // Trigger validation display for a specific form
  triggerValidationDisplay(form: FormGroup, index: number): void {
    // Check if form exists before trying to mark it as touched
    if (!form) {
      console.warn(`Form at index ${index} is undefined`);
      return;
    }

    // Mark all controls as touched to show validation errors
    form.markAllAsTouched();

    // Add this form index to the set of forms that should show validation errors
    this.formsWithValidationErrors.add(index);
  }

  // Check if a form should show validation errors
  shouldShowValidationErrors(index: number): boolean {
    return this.formsWithValidationErrors.has(index);
  }

  // Clear validation errors for a specific form
  clearValidationErrors(index: number): void {
    this.formsWithValidationErrors.delete(index);
  }

  // Direct method to emit and move to next step
  async emitAndNextStep(): Promise<void> {
    try {
      // Show loader while calling the service
      this.showLoader = true;

      // Emit the current educations data to parent component
      this.educationsChange.emit(this.getEducations());

      // Wait for the service call to complete
      await this.uploadCvPdfService.addStepData('educations', this.educationsForm.value).toPromise();

      // Hide loader after service call completes
      this.showLoader = false;

      if (this.fromSteps) {
        this.nextStep.emit();
      }
    } catch (error) {
      // Hide loader in case of error
      this.showLoader = false;
      console.error('Error saving educations data:', error);

      // Show user-friendly error message
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('shared.error'),
        detail: this.translateService.instant('shared.errorSavingEducations') || 'Error saving education data. Please try again.',
        life: 5000
      });

      throw error;
    }
  }

  // Simplified method to handle next button click
  async nextEducation(): Promise<void> {
    const educationsArr = (this.educationsForm.get('educationsList') as FormArray);

    // Validate all education forms
    let allValid = true;
    const invalidForms: number[] = [];

    for (let i = 0; i < educationsArr.length; i++) {
      const form = educationsArr.at(i) as FormGroup;
      if (!form) {
        console.warn(`Form at index ${i} is undefined`);
        continue;
      }

      // Process institution name if it's an object
      const institutionControl = form.get('institution');
      if (institutionControl && institutionControl.value) {
        const institution = institutionControl.value;
        if (institution.name && typeof institution.name === 'object') {
          institution.name = institution.name.name || institution.name.toString();
          institutionControl.setValue(institution);
        }
      }

      if (!this.isEducationValid(form)) {
        allValid = false;
        invalidForms.push(i);
        this.triggerValidationDisplay(form, i);
      } else {
        this.clearValidationErrors(i);
      }
    }

    // If any form is invalid, show errors and return
    if (!allValid) {
      console.log('Some education forms are invalid:', invalidForms);

      // Show user-friendly validation error message
      this.messageService.add({
        severity: 'warn',
        summary: this.translateService.instant('shared.validationError'),
        detail: this.translateService.instant('shared.pleaseCompleteAllFields') || 'Please complete all required fields.',
        life: 5000
      });

      return;
    }

    // All forms are valid, proceed with saving and emitting
    if (this.educationsForm.valid) {
      try {
        await this.emitAndNextStep();
      } catch (error) {
        console.error('Error in nextEducation:', error);
        // Error message is already shown in emitAndNextStep
      }
    }
  }

  prevEducation(): void {
    console.log('prevEdu', this.fromSteps)
    if (this.fromSteps) {
      this.prevStep.emit();
    }
  }

  minimize(index: number): void {
    this.isMinimized[index] = !this.isMinimized[index];
  }
}
