<div class="cv-actions-toolbar">
    <i class="fa fa-arrow-left" style="color: #969895;" (click)="back()" pTooltip="Back"  tooltipPosition="top"></i>

    <i *ngIf="sourceInterface === 'receive-cvs'" (click)="changeStatus(currentCv ,
     'to_folder', 5, 'emp_app/move_to_folder' , 'Are you sure you want to delete this CV?' )" 
     class="fa fa-trash " aria-hidden="true " style="color: #767676;" pTooltip="Delete"  tooltipPosition="top"></i>

     <i *ngIf="sourceInterface === 'cvs-folders'" (click)="changeStatus(currentCv , 'delete_cv', '', 'delete_cv' , '' )" 
     class="fa fa-trash " aria-hidden="true " style="color: #767676;" pTooltip="Delete"  tooltipPosition="top"></i>

     <i *ngIf="sourceInterface === 'receive-cvs' && folder===5" (click)="changeStatus(currentCv , 'restore' , '' , 'emp_app/restore')"
     class="fa fa-undo" aria-hidden="true " style="color: #767676;" pTooltip="Restore"  tooltipPosition="top"></i>

    <!-- <i  *ngIf="!currentCv.is_deleted && folder == 1"
     (click)="changeStatus(currentCv , 'to_folder',2 , 'emp_app/move_to_folder' ,
      'Are you sure you want to interview this CV ?' )" class="fa fa-calendar-plus-o"
       aria-hidden="true "
        style="font-size: 20px; color: #2929eb;cursor: pointer;padding:5px 10px;"></i> -->
    <!-- <i  *ngIf="!currentCv.is_deleted && folder == 2" 
    class="fa fa-clock-o " (click)="changeStatus(currentCv , 'interview' , '' , '' )"></i> -->
    <!-- <i  *ngIf="!currentCv.is_deleted " (click)="changeStatus(currentCv , 'read' ,currentCv.read?0:1 , 
    'emp_app/read') " class="fa " [ngClass]="{ 'fa-envelope-open-o':currentCv.read===1 ,
     'fa-envelope': !currentCv.read}" 
    style="font-size: 20px; color: #767676;cursor: pointer;padding: 5px 10px; "></i> -->
    <i *ngIf="sourceInterface === 'receive-cvs'" (click)="changeStatus(currentCv , 'read_button' ,currentCv.read?0:1 , 
    'emp_app/read') " class="fa " [ngClass]="{ 'fa-envelope':currentCv.read===1 ,
     'fa-envelope-open-o': !currentCv.read}" 
    style="color: #767676;" pTooltip="{{currentCv.read===1 ? 'Mark as unread' : 'Mark as read'}}"  tooltipPosition="top"></i>

    <i *ngIf="sourceInterface === 'cvs-folders'" (click)="changeStatus(currentCv , 'read_button' ,currentCv.read?0:1 , 
    'cv_folder/read') " class="fa " [ngClass]="{ 'fa-envelope':currentCv.read===1 ,
     'fa-envelope-open-o': !currentCv.read}" 
    style="color: #767676;" pTooltip="{{currentCv.read===1 ? 'Mark as unread' : 'Mark as read'}}"  tooltipPosition="top"></i>

    <img src="./assets/images/icons/move-folder.svg"  (click)="openMoveCVToFolderModal()"
           pTooltip="Add cv to folder"  tooltipPosition="top">

    <!-- <img *ngIf="sourceInterface === 'cvs-folders'" src="./assets/images/icons/move-folder.svg"  (click)="openMoveCVToFolderModal()"
           pTooltip="Copy cv to folder"  tooltipPosition="top"> -->

    <div *ngIf="showToggleCVeek==true" class="switchCveek-container">
        <span (click)="toggleCVeek(false)" class="switchCveek" [ngClass]="showCVeek === false ? 'cveekActive' : ''">Uploaded CV</span>
        <span (click)="toggleCVeek(true)" class="switchCveek" [ngClass]="showCVeek === true ? 'cveekActive' : ''">CVeek</span>
    </div>
    
    <span *ngIf="currentCv.is_deleted" style="color: #767676;font-size: 16px;">&nbsp;Deleted by user</span>
</div>

<div *ngIf="currentCv.cv_folders" class="folders-tags">
    <span *ngFor="let folder of currentCv.cv_folders" style="float:left;">
        <span class="tag-label" *ngIf="folder">
            <span>{{folder.label}}</span>
            <button (click)="detachFolderFromResume(folder)" class="delete-tag">
                <span aria-hidden="true">&#10006;</span>
            </button>
        </span>
        <span class="tag-separator" *ngIf="folder">   </span>
    </span>
    <span style="clear:left;display: inline-block;"></span>
</div>


<app-move-cv-modal
    [currentCv]="currentCv"
    [foldersddData]="foldersddData"
    [sourceInterface]="sourceInterface"
    (closeModalPopup)="handleMoveCvPopup($event)"
>
</app-move-cv-modal>

       