import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
import {DataModel} from './DataModel';
import { Subject } from 'rxjs';

declare var $: any;
@Component({
  selector: 'app-filters-wrapper',
  templateUrl: './filters-wrapper.component.html',
  styleUrls: ['./filters-wrapper.component.scss']
})
export class FiltersWrapperComponent implements OnInit, OnDestroy {
  dataModel: any;
  @Input() pageType;
  private ngUnsubscribe: Subject<any> = new Subject();
  currentFolder:number;

  constructor(private generalService: GeneralService) {
    
  }

  ngOnInit(): void {
    this.dataModel = new DataModel();

    this.generalService.internalMessage.takeUntil(this.ngUnsubscribe).subscribe( (data) => { 
      if (data['message'] === 'filter-data-from-cvs-table' ) {
        this.dataModel.filterData = data['mData'];
      }

      if (data['message'] === 'filters-changes' ) {
         /// * note that src equals of key  in filters object  which will be send to the backend
         this.dataModel.filters[data['src']] = data['mData'].filters;
         this.dataModel.currentFilters[data['src']] = data['mData'].tags;
         this.generalService.notify('filtersSubmitted' ,
         'filters-wrapper' , 'cvs-table', {'filters':this.dataModel.filters}) ;
       
          this.dataModel.findDiffer(
              this.dataModel.initFiltersTags,
              this.dataModel.currentFilters);
          
        //  this.generalService.notify('currentFiltersTags' ,
        //  'filters-wrapper' , 'cvs-table', this.dataModel.currentFilters) ;
        this.generalService.notify('currentFiltersTags' ,'filters-wrapper' , 'cvs-table', this.dataModel.currentFilterTags) ;
        }

      if (data['message'] === 'init-filters' ) {
        /// * note that src equals of key  in filters object  which will be send to the backend
        this.dataModel.initFilters[data['src']] = data['mData'].filters ;
        this.dataModel.initFiltersTags[data['src']] = data['mData'].tags ;
     }
      /////////////////
     if (data['message'] === 'filter-removed' ) {
      this.dataModel.currentFiltersTags = data['mData']['currentFiltersTags'];
      let removedFilters = [];
      let removedArrayTypeFilters = [];
      let tagTitle = data['mData']['filtertag']['title'];
      let keys = Object.keys(this.dataModel.currentFilters);
      keys.forEach(el => {
        // if(el === 'location_filter' && tagTitle === 'Location'){
        //   this.dataModel.filters[el] = this.dataModel.initFilters[el];
        //   this.dataModel.currentFilters[el] = this.dataModel.initFiltersTags[el];
        //   this.generalService.notify('removedFilters' , 'filters-wrapper' , el , {"removedFilters":removedFilters, "removedArrayTypeFilters":removedArrayTypeFilters});
        // }
     //   else if(el !== 'location_filter' && tagTitle !== 'Location'){
          let checked = false;
          for(let i=0; i<this.dataModel.currentFilters[el].length; i++){
            if(this.dataModel.currentFilters[el][i].title === tagTitle){
              checked = true;
              if(this.dataModel.currentFilters[el][i].type !== 'array'){
                this.dataModel.currentFilters[el][i] = this.dataModel.initFiltersTags[el][i];
                const filterName = this.dataModel.currentFilters[el][i].name;
                this.dataModel.filters[el][filterName] = this.dataModel.initFilters[el][filterName];
                removedFilters.push(filterName);
              }
              else {
                this.dataModel.currentFilters[el][i] = data['mData']['modifiedFilterTag'];
                const filterName = this.dataModel.currentFilters[el][i].name;
                this.dataModel.filters[el][filterName] = data['mData']['modifiedFilterTag'].value;
                removedArrayTypeFilters.push({"filterName":filterName, "filterValue":data['mData']['modifiedFilterTag'].value});
              }
            }
          }
          if(checked === true)
            this.generalService.notify('removedFilters' , 'filters-wrapper' , el , {"removedFilters":removedFilters, "removedArrayTypeFilters":removedArrayTypeFilters});
     //   }
        
        
      });
      console.log("this.dataModel.currentFilters",this.dataModel.currentFilters);
      console.log("this.dataModel.filters",this.dataModel.filters);
        
      this.generalService.notify('filtersSubmitted' , 'filters-wrapper' , 'cvs-table', {'filters':this.dataModel.filters});
          
    }


    if (data['message'] === 'clearAllFiltersModal' ) {
      /// * note that src equals of key  in filters object  which will be send to the backend
      this.dataModel.filters[data['src']] = this.dataModel.initFilters[data['src']];
      this.dataModel.currentFilters[data['src']] = this.dataModel.initFiltersTags[data['src']];
      //reset tags that belongs to all filters
      this.dataModel.findDiffer(
        this.dataModel.initFiltersTags,
        this.dataModel.currentFilters);
      this.generalService.notify('filtersSubmitted' , 'filters-wrapper' , 'cvs-table', {'filters':this.dataModel.filters}) ;
      this.generalService.notify('currentFiltersTags' ,'filters-wrapper' , 'cvs-table', this.dataModel.currentFilterTags) ;
    }

    if (data['message'] === 'clearAllFilters' && data['src'] === 'cvs-table'){
      this.dataModel.filters = {};
      this.dataModel.currentFilters = {};
      this.dataModel.currentFilterTags = [];
      this.generalService.notify('clearAllFilters' ,'filters-wrapper' , 'all_filters', {}) ;
    }


   });

    $('#filtersModal').on('show.bs.modal', (e) =>  { this.dataModel.setFiltersTitle(); });
  }

  showFiltersModal(filterSection) {
    this.dataModel.filterSection = filterSection ;
    $('#filtersModal').modal('show');
  }


  hideModal() {
    $('#filtersModal').modal('hide');
  }

  toggleFolders(){
    this.generalService.notify('toggleFolders' , 'filters-wrapper' , 'main' , {'expanded' : false}) ;
  }

  onModalCancel(){
    this.generalService.notify('modalCancel' , 'filters-wrapper' , this.dataModel.filterSection , {}) ;
  }

  notifySelectedAction(action){
    if(action === 'delete'){
      this.generalService.notify('deleteSelectedCVS' , 'filters-wrapper' , 'cvs-table', {}) ;
    }
    else if(action === 'read'){
      this.generalService.notify('readSelectedCVS' , 'filters-wrapper' , 'cvs-table', {}) ;
    }

    else if(action === 'move'){
      this.generalService.notify('moveSelectedCVSToFolders' , 'filters-wrapper' , 'cvs-table', {}) ;
    }

    

  }

  ngOnDestroy(){
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
