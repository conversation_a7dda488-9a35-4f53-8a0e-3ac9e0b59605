.btn {
  cursor: pointer;
}

table.table.table-responsive {
  background-color: white;
  box-shadow: -1px 0px 3px 0px #ccc;
}

th {
  color: #276ea4;
  background-color: #e5e5e5;
  font-size: 1.1em;
}

h3 {
  color: #276ea4;
}

.btn-primary {
  color: #276ea4;
}
.btn-danger {
  background-color: #be0303;
}

.badge-primary {
  background-color: #4f94df;
}

i.fa:hover {
  cursor: pointer;
}

.fa.fa-suitcase {
  color: #1a4b70;
}

.fa.fa-user {
  color:#257e3c ;
}

.fa.fa-user-circle {
  color: #257e3c ;
}

.fa-edit, .fa-repeat {
  color: #276ea4;
  padding-left: 5px;
  font-size: 1.2em;
  transition: all 0.5s ease-in-out ;
}

.fa-repeat:hover {
  color: dodgerblue;
  font-weight: bold;
  transform: rotateZ(360deg);
}

.fa-eye {
  padding-left: 5px;
  font-size: 1.2em;
  color: darkblue;
}
 .fa-question,  .fa-trash {
  padding-left: 5px;
  color: #b78f81;
  font-size: 1.4em;
}

.fa-trash:hover {
  color: #a04d30;
}

.fa.fa-info-circle {
  color: #3945d8ba;
  padding-left: 5px;
}

td select {
  width: 40px;
  height: 25px;
  padding: 0px;
  border-radius: 3px;
  border: 1px solid lightgrey;
}

td input[type="text"] {
  width: 60px;
  border-radius: 3px;
  border: 1px solid lightgrey;
}

td input[type="number"] {
  width: 60px;
  border-radius: 3px;
  border: 1px solid lightgrey;
}

td input:focus {
 background-color: #f5f5f5;
}


.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
  padding: 8px;
  line-height: 1.2;
  vertical-align: top;
  border-top: 1px solid #ddd;
}

 .actions {
  padding-left: 8px !important;
}


/* modals start */
.modal-title {
  color: #be0303;
}


.modal-content {
  border-radius: 3%;
  /* width: 600px; */
}

.modal-body-container {
  height: fit-content;
}


.modal-body{
  height: fit-content;
  max-height: unset !important;
  overflow: unset;
}

.modal-header {
  padding-bottom: 10px;
}

.modal-header h3 {
  margin-top: 0px;

}

#alertModal .modal-dialog {
  width: 50%;
}

.modal-header .close {
  font-size: 35px;
  margin-top: -8px;

}

.modal-header .close:hover {
  color: black;
}


div.modal-header>button>span {
  font-size: 30px;
}

/* end of modals  */

/*new */
.list-group {
  max-width: 100%;
}
.table>tbody>tr {
  color: #777;
  transition: all 0.5s ease-in-out;
}

.table>tbody>tr:hover {
  background-color: #f9f9f9;
  color: dimgrey;
  font-weight: bold;
}
.table>tbody>tr.search-row:hover
{
  background-color: white!important;
}
table.table.table-responsive
{
  overflow-y: auto !important;
}


.counter {
  width: 20px;
  color: #aaa;
  background-color: #e5e5e575;
}

.counter-th {
  width: 20px;
}

/* website primary color: #276ea4 */

/* =========================================================================================== */

div, table, td,th,p,h3,form, .modal-header, .modal-title, .modal-body, .modal-footer, .caption {
  font-family: 'Exo2-Regular', sans-serif;
}

input::-webkit-input-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}
input:-moz-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}
input::-moz-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}
input:-ms-input-placeholder { color: #bdbdd3; font-family: 'Exo2-Regular', sans-serif;}






/*================================================================================================= */
/* start of primeng table styling */

.ui-table .ui-table-thead>tr>th,
 .ui-table .ui-table-tbody>tr>td,
  .ui-table .ui-table-tfoot>tr>td {
  padding: .5em .75em !important;
}

:host ::ng-deep .ui-table-messages {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12);
  /* Responsive */
}
:host ::ng-deep .ui-table-messages .message-badge {
  border-radius: 2px;
  padding: 0.25em 0.5em;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 12px;
  letter-spacing: 0.3px;
}
:host ::ng-deep .ui-table-messages .message-badge.status-done {
  background-color: #c8e6c9;
  color: #256029;
}
:host ::ng-deep .ui-table-messages .message-badge.status-not-done {
  background-color: #ffcdd2;
  color: #c63737;
}
:host ::ng-deep .ui-table-messages .message-badge.status-commented {
  background-color: #feedaf;
  color: #8a5340;
}
:host ::ng-deep .ui-table-messages .message-badge.status-replied {
  background-color: #b3e5fc;
  color: #23547b;
}
:host ::ng-deep .ui-table-messages .message-badge.status-opened {
  background-color: #eccfff;
  color: #694382;
}
:host ::ng-deep .ui-table-messages .message-badge.status-new {
  background-color: #ffd8b2;
  color: #805b36;
}

:host ::ng-deep .ui-table-messages .message-badge.status-deleted {
  background-color: #fcacda;
  color: #ac0614;
}

:host ::ng-deep .ui-table-messages .flag {
  vertical-align: middle;
  width: 30px;
  height: 20px;
}

:host ::ng-deep .ui-table-messages .ui-paginator .ui-dropdown {
  float: left;
  width: 60px;
}

:host ::ng-deep .ui-table-messages .ui-paginator .ui-dropdown.ui-widget.ui-state-default.ui-corner-all {
  width: 70px !important;
}

:host ::ng-deep .ui-table-messages .ui-paginator .ui-paginator-current {
  float: right;
}

:host ::ng-deep .ui-table-messages .ui-column-filter {
  display: block;
}
:host ::ng-deep .ui-table-messages  input.ui-column-filter  {
  width: 70px;
  background: transparent;
  border: none;
  border-bottom: 1px solid #ccc;

}

:host ::ng-deep .ui-table-messages input.ui-column-filter:focus {
  width: 80px;
  border:2px solid darkblue;
}

:host ::ng-deep .ui-table-messages .ui-table-globalfilter-container {
  float: right;
}
:host ::ng-deep .ui-table-messages .ui-table-globalfilter-container input {
  width: 200px;
  border-radius: 3px;
}
:host ::ng-deep .ui-table-messages .ui-datepicker {
  min-width: 25em;
}
:host ::ng-deep .ui-table-messages .ui-datepicker td {
  font-weight: 400;
}
:host ::ng-deep .ui-table-messages .ui-table-caption {
  border: 0 none;
  padding: 12px;
  text-align: left;
  font-size: 20px;
  font-family: 'Exo2-Regular', sans-serif
}
:host ::ng-deep .ui-table-messages .ui-paginator {
  border: 0 none;
  padding: 1em;
  font-family: 'Exo2-Regular', sans-serif;
}
:host ::ng-deep .ui-table-messages .ui-table-thead > tr > th {
  border: 0 none;
  text-align: left;
  vertical-align: top;
}
:host ::ng-deep .ui-table-messages .ui-table-thead > tr > th.ui-filter-column {
  border-top: 1px solid #c8c8c8;
}
:host ::ng-deep .ui-table-messages .ui-table-thead > tr > th:first-child {
  width: 5em;
  text-align: center;
}
:host ::ng-deep .ui-table-messages .ui-table-thead > tr > th:last-child {
  width: 8em;
  text-align: center;
}
:host ::ng-deep .ui-table-messages .ui-table-tbody > tr > td {
  border: 0 none;
  cursor: auto;
}


/* ++++++ */
:host ::ng-deep .ui-table-messages .ui-table-tbody > tr > td:first-child {
  width: 3em;
  text-align: center;
}
:host ::ng-deep .ui-table-messages .ui-table-tbody > tr > td:last-child {
  width: 8em;
  text-align: center;
}

:host ::ng-deep .ui-table-companies .ui-dropdown-label-container {
  margin-top: -9px;
}

:host ::ng-deep .ui-table-messages .ui-dropdown-label:not(.ui-placeholder) {
  text-transform: lowercase;
  text-overflow: clip;
  overflow: hidden;
  width: 80px;
  color: #888;
  background-color: transparent;
}
:host ::ng-deep .ui-table-messages .ui-dropdown-trigger.ui-state-default.ui-corner-right {
  /* left: 50px; */
  background: transparent;
}

:host ::ng-deep .ui-table-messages .ui-table-tbody > tr > td .ui-column-title {
  display: none;
}
@media screen and (max-width: 64em) {
  :host ::ng-deep .ui-table.ui-table-messages .ui-table-thead > tr > th, :host ::ng-deep .ui-table.ui-table-messages .ui-table-tfoot > tr > td {
    display: none !important;
  }
  :host ::ng-deep .ui-table.ui-table-messages .ui-table-tbody > tr > td {
    text-align: left;
    display: block;
    border: 0 none !important;
    width: 100% !important;
    float: left;
    clear: left;
    border: 0 none;
  }
  :host ::ng-deep .ui-table.ui-table-messages .ui-table-tbody > tr > td .ui-column-title {
    padding: 0.4em;
    min-width: 30%;
    display: inline-block;
    margin: -0.4em 1em -0.4em -0.4em;
    font-weight: bold;
    font-family: 'Exo2-Regular', sans-serif;
  }
}


:host ::ng-deep .ui-table .ui-sortable-column:not(.ui-state-highlight):hover {
  background-color: #e0e0e0;
  color: darkblue;
}

:host ::ng-deep .ui-table .ui-table-thead > tr > th {
  color: darkblue;
  background-color: #f4f4f4;
}

:host ::ng-deep .ui-table  p-calendar  .ui-datepicker.ui-widget.ui-widget-content.ui-corner-all{
  display: block;
  transform: scale(0.75);
 }

 :host ::ng-deep .ui-table  p-calendar input {
    width: 80px;
    height: 27px;
    /* border: 1px solid black; */
    border-radius: 0px;
    border: none;
    border-bottom: 1px solid #b9b7b7;
    background-color: transparent;
    margin-top: 7px;
}
