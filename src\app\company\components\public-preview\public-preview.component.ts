import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CompanyFormService } from '../../services/company-form.service';
import { DataMap } from "shared/Models/data_map";
import {Subject} from 'rxjs/Subject';
import 'rxjs/add/operator/takeUntil';
@Component({
  selector: 'app-public-preview',
  templateUrl: './public-preview.component.html',
  styleUrls: ['./public-preview.component.css']
})
export class PublicPreviewComponent implements OnInit {
  companyId = Number (localStorage.getItem('company_id'));
  username = '';
  have_Data = [];
  data_map = new DataMap();
  private ngUnsubscribe: Subject<any> = new Subject();
 // profileData;
  constructor(private route: ActivatedRoute,
    private companyFormService: CompanyFormService,
             private router: Router,
              ) { }

  ngOnInit(): void {

    
    this.route.parent.parent.params.subscribe(res => {
      
      this.username = res['username'];
    });
    this.getProfiles_route()
    // this.companyFormService.getProfiles(this.companyId).subscribe(res => {
    //   //send profile data to edit profile page
    //   this.profileData = res;
    // });
  }

  getProfiles_route() {

    this.companyFormService.getProfiles(this.companyId).takeUntil(this.ngUnsubscribe).subscribe(
    (res) => {
      
      if(res['translation'] !== undefined) {
        let send_Data_to_preview = this.data_map.optimiziaion_Data(res);
        this.have_Data = send_Data_to_preview;
      }
  
    });
  }

  edit_profile() {
    this.companyFormService.send_Data(this.have_Data);
    this.companyFormService.changeStatus(true);
    this.router.navigate(['/c',this.username,'profile','new']);
  }

  // goToProfile(){
  //   if(this.profileData){
  //     this.companyFormService.send_Data(this.profileData);
  //     this.companyFormService.changeStatus(true);
  //     this.router.navigate(['/c',this.username,'profile','new']);
  //   }
  // }

  ngOnDestroy() {
    
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
