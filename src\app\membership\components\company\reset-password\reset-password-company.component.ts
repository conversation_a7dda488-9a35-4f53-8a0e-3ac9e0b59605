import { Component, OnInit } from '@angular/core';
import {AuthService} from 'shared/shared-services/auth-service';
import {HttpClient} from '@angular/common/http';
import {FormBuilder, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import { OAuthService } from 'angular-oauth2-oidc';
import { GeneralService } from '../../../../general/services/general.service';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password-company.component.html',
  styleUrls: ['./reset-password-company.component.css']
})
export class ResetPasswordCompanyComponent implements OnInit {
  resetPasswordForm;
 // errorMessage;
  type= 'password';
  show = false;
  username = '';
  loader = false;
  constructor(private authService: AuthService,
              private http: HttpClient,
              private  fb: FormBuilder,
              private router: Router,
              private oauthService: OAuthService,
              private generalService: GeneralService,
              private title:Title,
              private meta:Meta
  ) {
    this.resetPasswordForm = this.fb.group({
      password: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.title.setTitle('CVeek Website | Reset password');
    this.meta.updateTag({ name: 'robots', content: 'noindex' });
  }

  isInvalid(controlName: string) {
    return !this.resetPasswordForm.controls[controlName].valid;
  }

  verify() {
    let userInfo = JSON.parse(localStorage.getItem('oldUser'));
    const data = {
      'email': userInfo.email,
      'password': this.resetPasswordForm.get('password').value,
    };
    if (this.resetPasswordForm.valid) {
      this.loader = true;
      this.authService.resetPasswordCompany(data).subscribe(res => {
        if (res['data']) {
          localStorage.removeItem('oldUser');


          this.oauthService.fetchTokenUsingPasswordFlow(data.email,data.password).then(
            (resp) => {           
              if(resp['error']){
                this.authService.changeError(resp['error']);
                return Promise.reject(resp['error']);   
              }
              else{
                 // Loading data about the user
                return this.oauthService.loadUserProfile();
              }
          },
          (err) => {
            this.loader = false;
            // console.log(err);
          }
          ).catch(error => {
            this.loader = false;
            // console.log(error);
          })
          .then(() => {
                // Using the loaded user data
                let claims = this.oauthService.getIdentityClaims();
                this.loader = false;
                if (claims) {
                  localStorage.setItem('role',claims['user_info'].roles[0].name);
                  localStorage.setItem("username",claims['user_info'].user_name);
                  this.username = claims['user_info'].user_name;
                  localStorage.setItem('company_id',claims['company_id']);
                  localStorage.setItem('company_name',claims['company_name']);
                  localStorage.setItem("email",claims['user_info'].email);
                  //company logo
                  if(claims['user_info'].profile_picture){
                    localStorage.setItem("pic",claims['user_info'].profile_picture);
                  }
                  else {
                    localStorage.setItem("pic","none");
                  }

                  this.generalService.notify(
                    'roleChanged' , 'membership','contact' , 
                    {'role':claims['user_info'].roles[0].name , 'email':claims['user_info'].email}
                  );
                  localStorage.setItem("have_profile",claims['have_profile']);
                  if(data['have_profile'] === 0){
                    this.router.navigate(['c/',this.username,'profile','new']);
                  }
                  else{
                    this.router.navigate(['c/',this.username,'manage_advs','published']);
                  }
                //  this.router.navigate(['c/',this.username]);
                }
          });
          //old code
          // this.authService.loginCompany(data).subscribe(rees => {
          //   localStorage.setItem('token', rees['access_token']);
          //   this.router.navigate(['company/company-wrapper']);
          // });
        }
        // if (res['error']) {
        //   this.errorMessage = res['error'];
        // }
      });
    }
  }

  toggleShowPassword() {
    this.show = !this.show;
    if (this.show) {
        this.type = 'text';
    } else {
        this.type = 'password';
    }
  }

}
