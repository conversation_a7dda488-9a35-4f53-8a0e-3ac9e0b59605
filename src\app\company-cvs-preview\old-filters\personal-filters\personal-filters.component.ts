import { Component, <PERSON><PERSON><PERSON><PERSON>, Input, OnChanges, OnInit, SimpleChanges, ViewChild, AfterViewInit } from '@angular/core';
import { Observable } from 'rxjs';
import { PeronsalFiltersDataModel } from './PeronsalFiltersDataModel';
import { KeyValueChanges, KeyValueDiffer, KeyValueDiffers } from '@angular/core';
import { GeneralService } from 'app/general/services/general.service';
declare var $: any;
@Component({
  selector: 'app-personal-filters',
  templateUrl: './personal-filters.component.html',
  styleUrls: ['./personal-filters.component.css']
})
export class PersonalFiltersComponent implements OnInit, AfterViewInit {
  public dataModel: PeronsalFiltersDataModel;
  @Input() filterData;
  public temp: any;
  private dataModelDiffer: KeyValueDiffer<string, any>;
  @ViewChild('googlelocationplaceLocation') public googlelocationplaceLocationRef: any;
  constructor(private differs: KeyValueDiffers, private generalService: GeneralService) {
    this.dataModel = new PeronsalFiltersDataModel();
  }


  ngOnInit(): void {
    //   this.dataModelDiffer = this.differs.find(this.dataModel).create();
    this.temp = {
      'cities': []
    };
    this.sendInitStateToWarapper();

    this.temp['cities'] = this.filterData['cities'];
  }

  //ngDoCheck(): void {
    //  const changes = this.dataModelDiffer.diff(this.dataModel);
    // if (changes) {
    //   this.dataModel.setFilters();
    //   //  *
    //   this.generalService.notify('filters-changes',
    //     'personal_info', 'filters-wrapper', this.dataModel.filters);
    // }
    /// * here  we set src of   this.generalService.notify equals to 'peronal_info' which it's not
    // the same of component name  "peronsal-filters" please see filters-wrapper component 
 // }

  filterArray(event, arr) {
    this.temp[arr] = this.filterData[arr].filter(e => e.name.toLowerCase().includes(event.query.toLowerCase()));
  }
/////////////////////////  -------
  private getLocationPlacefromLocationAutoComplete() {
    const autocomplete = new google.maps.places.Autocomplete(this.googlelocationplaceLocationRef.nativeElement,
      {
        types: ['geocode']  // 'establishment' / 'address' / 'geocode'
      });
    google.maps.event.addListener(autocomplete, 'place_changed', () => {
      const place = autocomplete.getPlace();
      this.invokeEvent(place);
    });
  }

  ngAfterViewInit() {
    this.getLocationPlacefromLocationAutoComplete();
  }


invokeEvent(place: Object) {
  //////// handle if user doesn't  select city, select only country

  let city  =  place['address_components'].filter(e => e['types'][0] === 'administrative_area_level_1');
   if (city.length  > 0) {
    this.dataModel.city = city;
   }
  this.dataModel.country = place['address_components'] .filter(e => e['types'][0] === 'country') ;
}
////////////////////////  ----------

sendFilters() {
   this.dataModel.setFilters();
  this.generalService.notify('filters-changes',
    'personal_info', 'filters-wrapper', this.dataModel.filters);
    $('#filtersModal').modal('hide');
}

sendInitStateToWarapper() {
  this.dataModel.setFilters();
  this.generalService.notify('init-filters',
    'personal_info', 'filters-wrapper', this.dataModel.filters);
}

hideModal() {
  $('#filtersModal').modal('hide');
}
}
