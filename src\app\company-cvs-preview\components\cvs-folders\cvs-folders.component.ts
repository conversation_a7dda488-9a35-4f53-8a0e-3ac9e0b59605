import { Component, OnInit } from '@angular/core';
import {TreeNode, MessageService} from 'primeng/api';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { CvsFoldersService } from '../../../company-cvs-folders/services/cvs-folders.service';
import { CvsTableService } from '../../../company-cvs-folders/services/cvs-table.service';
import { Cv } from '../cvs-table/DataModel';
import { Router, ActivatedRoute } from '@angular/router';
import { GeneralService } from '../../../general/services/general.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-cvs-folders',
  templateUrl: './cvs-folders.component.html',
  styleUrls: ['./cvs-folders.component.css'],
  providers: [MessageService]
})
export class CvsFoldersComponent implements OnInit {
  folders: TreeNode[];
  selectedFolder: TreeNode;
  loading = true;
  username='';
  foldersddData = [];
  private ngUnsubscribe: Subject<any> = new Subject();
  
  constructor(
      private cvsFoldersService: CvsFoldersService,
      //this service is for custom folders module
      private cvsTableService:CvsTableService,
      private router: Router,
      private route: ActivatedRoute,
      private generalService:GeneralService,
      private messageService: MessageService,
    ) { }

  ngOnInit() {
    this.route.params.subscribe(res => {
      this.username = res['username'];
    });

    this.cvsFoldersService.getFolders().subscribe( (folders: TreeNode[])  => {
        this.folders = folders['data'];
        this.loading=false;
    });

    this.cvsFoldersService.getFoldersData().subscribe(data => {
      this.foldersddData = data['data'];
      this.foldersddData.unshift("");
      this.generalService.notify('cvsFoldersDDReady' , 'cvs-folders' , 'folders-modals' , {'foldersddData':this.foldersddData});
    });

    this.generalService.internalMessage.takeUntil(this.ngUnsubscribe).subscribe( (data) => {
      // cvs-folders-cvs-preview-module
      if( (data['src'] === 'cv-previewer-toolbar' || data['src'] === 'cvs-table')  && data['dist']==='cvs-folders'){
        // case folders changed with move-cv-modal
        if (data['message'] === 'updateCVFoldersCount') {
          if(data['mData'].foldersType==='custom-folders'){
            for(let folder of data['mData'].oldFolders){
              this.editFolderCountWithKey('reduce',folder.key,this.folders);
            }
          }
          else if(data['mData'].foldersType==='original-folders'){
            this.generalService.notify('deleteCvFromFolder', 'cvs-folders', 'folders-list', { 'folder': data['mData'].oldFolders });
          }
          
          for(let folder of data['mData'].newFolders){
            this.editFolderCountWithKey('add',folder.key,this.folders);
          }
        }
      }

      //case folder changed with detach folder
      if (data['message'] === 'detachFolderFromResumeUpdateCount'){
        if(data['mData'].adv_title_folders){
          this.generalService.notify('addCvToFolder', 'cvs-folders', 'folders-list', { 'folders': data['mData'].adv_title_folders });
          this.editFolderCountWithKey('reduce',data['mData'].folder.key,this.folders);
        }
        else 
          this.editFolderCountWithKey('reduce',data['mData'].folder.key,this.folders);
      }
    });

  }

  dropCV(event: CdkDragDrop<Cv[]>) {
    const index = event.previousIndex;
    let cv = event.previousContainer.data[index];
  //  event.previousContainer.data.splice(index, 1);
    let id = +event.container.id.split('-')[1];
     let data = {"resume_id":cv.resume_id , "folders_ids":[id]};
     
     this.cvsTableService.moveCopyCVToFolder(data).subscribe( res => {
         this.generalService.notify('moveDraggedCvs', 'cvs-folders', 'cvs-table', { 'cv': cv });

         this.generalService.notify('deleteCvFromFolder', 'cvs-folders', 'folders-list', { 'folder': cv.folders });
         this.editFolderCountWithKey('add',res['data'][0].key,this.folders);
        
         let msg = '';
         msg = '"' + cv.name + '" CV moved successfully to "' +res['data'][0].label+'" folder';
        //  if(cv.count_resume_applications ===1)
        //     msg = cv.count_resume_applications + ' CV application for "' + cv.name + '" moved successfully to "' +res['data'][0].label+'" folder';
        //  else if(cv.count_resume_applications >1)
        //     msg = cv.count_resume_applications + ' CV applications for "' + cv.name + '" moved successfully to "' +res['data'][0].label+'" folder';

         this.messageService.add({
           severity: "success",
           detail:msg,
           life:7000
         });
     });
  }

  navigateToCvsFolders($event){
    let folder_id = $event['node'].key;
    this.router.navigate(['/c', 'cvs-folders', this.username], {state: {folder_id: folder_id, 'folder_label':$event['node'].label}});
  }

  getNodeWithKey(key: string, nodes: TreeNode[]): TreeNode | undefined {
    for (let node of nodes) {
      if (node.key === key) {
         return node;
      }
 
      if (node.children) {
        let matchedNode = this.getNodeWithKey(key, node.children);
        if (matchedNode) {
          return matchedNode;
        }
      }
    }
    return undefined;
 }

 cvsFoldersDDDataNotify(){
  this.generalService.notify('cvsFoldersDDReady' , 'cvs-folders' , 'folders-modals' , {'foldersddData':this.foldersddData});
 }

updateCountFoldersDD(key,newCount){
  for(let folder of this.foldersddData){
    if(folder.key===key)
      folder.cv_count=newCount;
  }
}

  editFolderCountWithKey(action,key: string, nodes: TreeNode[]){
    for (let node of nodes) {
      if (node.key === key) {
        if(action==='add')
          node.data.count++;
        else if(action==='reduce' && node.data.count>0)
          node.data.count--;
        this.updateCountFoldersDD(key,node.data.count);
        this.cvsFoldersDDDataNotify();
      }
 
      if (node.children) {
        let matchedNode = this.getNodeWithKey(key, node.children);
        if (matchedNode) {
          if(action==='add')
            matchedNode.data.count++;
          else if(action==='reduce' && matchedNode.data.count>0)
            matchedNode.data.count--;
          this.updateCountFoldersDD(key,matchedNode.data.count);
          this.cvsFoldersDDDataNotify();
        }
      }
    }
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
