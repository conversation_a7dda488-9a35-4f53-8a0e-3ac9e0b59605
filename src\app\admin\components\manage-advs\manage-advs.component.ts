import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute,Router } from '@angular/router';
import { ManageAdvsService } from 'app/admin/services/manage-advs.service';
import { ConfirmationService, Table } from 'primeng';
import { Subject } from 'rxjs';
import { ImageProcessingService } from 'shared/shared-services/image-processing.service';
declare var $: any;

@Component({
  selector: 'app-manage-advs',
  templateUrl: './manage-advs.component.html',
  styleUrls: ['./manage-advs.component.css'],
  providers: [ConfirmationService,],
})
export class ManageAdvsComponent implements OnInit {
  advs = [];

  end_job_adv_reasons = [];

  end_job_adv_reason_id: number;
  reason_comment: string = '';
  adv_end_id: number;
  @ViewChild('reason') reasonInput: ElementRef;

  status: string;
  loading = true;
  @ViewChild('dt') table: Table;
  private ngUnsubscribe: Subject<any> = new Subject();
  endAdvReasonIdErrorMessage: string;
  endAdvCommentErrorMessage: string;

  advEndedTitle: string = '';
  advEndedLog: [] = [];

  companyTypes = [
    {label: '', value: ''},
    {label: 'Same Company', value: 'my_company'},
    {label: 'Other Company', value: 'other_company'},
  ];

  constructor(private manageAdvsService: ManageAdvsService, private imageProcessingService: ImageProcessingService, private confirmationService: ConfirmationService, private route: ActivatedRoute,   private router: Router,) { }

  ngOnInit(): void {
    this.route.data.subscribe((item) => {
      this.status = item['status'];
    });
    if (this.status == 'ended-advs') {

      this.getEndedAdvs();
    } else {
      this.getActiveAdvs();
    }

  }

  getActiveAdvs() {
    this.manageAdvsService.getActiveAdvs().takeUntil(this.ngUnsubscribe).subscribe(res => {
      this.advs = res['data'];
      this.end_job_adv_reasons = res['end_job_adv_reasons'];
      this.loading = false;
    });

  }

  getEndedAdvs() {
    this.manageAdvsService.getEndedAdvs().takeUntil(this.ngUnsubscribe).subscribe(res => {
      this.advs = res['data'];
      this.loading = false;
    });

  }

  getImageLogo(adv) {
    // console.log('logo',adv.company_logo);
    if(adv.company_logo && adv.company_name && adv.opportunity_for === 'other_company')
      return this.imageProcessingService.getImagePath ('otherEmployerLogo','small_thumbnail',adv.company_logo);
    else if(adv.company_logo && adv.company_name && adv.opportunity_for === 'my_company')
      return this.imageProcessingService.getImagePath ('companyLogo','small_thumbnail',adv.company_logo);
    else if(adv.company_logo && adv.company_name && adv.opportunity_for===null)
      return './assets/images/no-image.png';
    else
      return './assets/images/no-image.png';


    // return (logo) ? this.imageProcessingService.getImagePath('companyLogo', 'small_thumbnail', logo) : './assets/images/no-image.png';
  }

  showEndedDialog(AdvId) {
    this.adv_end_id = AdvId;
  }


  showActiveDialog(AdvId) {

    const body = {
      'status': 'active',
      'id': AdvId
    };

    this.confirmationService.confirm({
      message: 'Are you sure that you want to active this job adv?',
      accept: () => {
         // Action to perform if user accepts
        this.manageAdvsService.updateStatusAdv(body).subscribe(res => {
          var adv = this.advs.find((x) => x.id == AdvId);
          // Update the data array (remove the deleted item)
          this.advs = this.advs.filter(currentItem => currentItem.id !== adv.id);
          alert(res['message']);
          if (res['error']) {
            alert(res['error']);
          }
        });
      },
      reject: () => {
        // Action to perform if user rejects or closes the dialog

        console.log('Rejected');
      }
    });
  }


  clearAll() {
    this.table.filter(null, 'type', 'startsWith');
    this.table.filter(null, 'company_name', 'contains');
    this.table.filter(null, 'job_adv_title', 'contains');
    this.table.filter(null, 'date', 'contains');
    this.table.filter(null, 'country', 'contains');
    this.table.filter(null, 'city', 'contains');

    $('span.ui-column-filter.ui-calendar input').val(null);
    $('.ui-table-globalfilter-container input').val(null);
    //console.log($('.ui-column-filter').val());
    $('.ui-column-filter').val(null);
  }

  endReasonChange(reason) {
    this.end_job_adv_reason_id = reason.value.id;
    this.endAdvReasonIdErrorMessage = null;
  }

  endJobAdv() {
    const body = {
      'status': 'ended',
      'id': this.adv_end_id,
      'reasons_end_job_adv_id': this.end_job_adv_reason_id,
      'reason': this.reason_comment
    };

    if (!this.end_job_adv_reason_id) this.endAdvReasonIdErrorMessage = "Advertisement End Category is Required";
    else if (!this.reason_comment) this.endAdvCommentErrorMessage = ' reason to stop Advertisement is required';
    else {

      this.manageAdvsService.updateStatusAdv(body).subscribe(res => {
        var adv = this.advs.find((x) => x.id == this.adv_end_id);
        // Update the data array (remove the deleted item)
        this.advs = this.advs.filter(currentItem => currentItem.id !== adv.id);
        alert(res['message']);
        if (res['error']) {
          alert(res['error']);
        }
        $('#showEndAdvModal').modal('hide');
        //this.reasonInput.nativeElement.value=undefined; bug
        this.reason_comment = '';
      });
    }

  }


  showAdvEndedLogDialog(AdvId) {

    this.manageAdvsService.getAdvEndedLog(AdvId).subscribe(res => {
      this.advEndedLog = res['log'];
      this.advEndedTitle = res['adv_title'];
      this.loading = false;
    });
  }

  showJobAdv(item) {
    console.log('onRowClick',item);

    //// job_title + '-in-' + job_company + '-' + job_country = slug : ie now slug composite on front-end
    const url = this.router.serializeUrl(this.router.createUrlTree(['/jobs',
      item.id , item.slug]));
    window.open(url, '_blank');
  }

  filterComapnyType(event: any){
console.log(event);
  }
}
