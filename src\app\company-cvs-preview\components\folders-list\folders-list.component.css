/* general styles  for all folder lists */
.folders-ul{
    margin-bottom: 0;
    padding:0;
}
.advs-title-folder .advs-title-li{
    margin-left:15px;
}
.folder-li {
    list-style: none;
    cursor: pointer;
    margin: 5px 0;
    /* padding: 0.3rem 0rem; */
}
/* .folder-li a:hover {
    background-color: #cccccc38;
} */
.folder-li a {
    color: #969895;
    text-decoration: none;
    display: block;
    padding: 6px 5px;
    display: flex;
    align-items: center;
    position: relative;
    font-family: Exo2-Regular,sans-serif;
    border-radius: 5px;
}
.folder-li a.active , .folder-li a:hover {
    background: #fff;
    color:#3d7bce;
}
.folder-icon{
    padding: 0 5px;
    font-size: 22px;
    color:#969895;;
}
.folder-li a.active .folder-icon , .folder-li a:hover .folder-icon{
    color:#3d7bce;
}
.folder-title{
    font-size: 1.2em;
}
.adv-folder .folder-title{
    font-size: 0.9rem;
    display:inline-block;
    margin: 0px 12px 0 7px;
}
/* .expand-folders .folder-title{
    display:inline !important;
} */
.adv-folder .adv-id , .adv-folder .cvs-count{
    display:inline-block;
    font-weight: bold;
}
.adv-id{
    color:#3d7bce;
}
.cvs-count{
    color: #3bb34b;
    position: absolute;
    right:8px;
}
.adv-ul{
    overflow: auto;
    /* height: calc(100% - 180px); */
    /* height: 150px; */
    height: calc(100vh - 360px);
    padding-left: 26px;
}

/* width */
::-webkit-scrollbar {
    width: 7px;
  }
  
  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #3D7BCE;
  }
@media screen and (max-width:1000px){
    .folder-title{
        display:none;
    }
    .folder-icon{
        font-size: 25px;
    }

    /* start styles moved to form-style.css */

    /* .expand-folders .folder-title{
        display:inline !important;
    }
    .expand-folders .folder-icon{
        font-size: 22px;
    }

    .collapse-folders .folder-title{
        display:none;
    }
    .collapse-folders .folder-icon{
        font-size: 25px;
    } */

    /* end styles moved to form-style.css */


    /* .folders-ul:hover .folder-title , .adv-ul:hover .folder-title{
        display:inline !important;
    }
    .folders-ul:hover .folder-icon , .adv-ul:hover .folder-icon{
        font-size: 22px;
    } */
}

@media screen and (max-width: 767px) and (orientation: landscape) {
    .adv-ul{
        height: 150px;
    }
}