<app-pre-loader  [show]="openedFromHelpTopic && mainCat.length === 0"></app-pre-loader>
<app-help-header></app-help-header>
<!-- display all main categories with its sub cats list -->
<div style="min-height:400px;">

<!-- row  content-row -->
  <div class="container-fluid" *ngIf="!displayOneMainCat">
    <div class=" main-group-list">
      <div class="row" *ngFor="let mainCat of mainCategories[currentLangId - 1]; let i = index">
          <!-- col-md-12 col-sm-12 col-md-offset-0 col-sm-offset-2 col-xs-12  cat-col -->
        <div class="col-xs-12 cat-col">
          <div class="card">
            <div class="badge badge-primary category-badge">
              <h2>{{ mainCat.name }}</h2>
            </div>
            <ul class="list-group sub-group-list">
              <div *ngFor="let subCat of mainCat.sub_cats; let j = index">
                <div *ngIf="j < 6">
                  <li class="list-group-item">
                      <!-- [routerLink]="['/i/' + '/help/' + mainCat.name + '/' + mainCat.id + '/s/' +  subCat.name + '/' + subCat.id]" -->
                    <h3>
                      <a
                        (click)="navigate(mainCat.name,mainCat.id,subCat.name,subCat.id)"
                        >
                        {{subCat.name }}
                     </a>
                    </h3>
                  </li> 
                </div>
                <div *ngIf="j >= 6 && display[i] && displayCats[i]" class="hidden-faq">
                  <li class="list-group-item">
                      <!-- [routerLink]="['/i/' + '/help/' + mainCat.name + '/' + mainCat.id + '/s/' +  subCat.name + '/' + subCat.id]" -->
                    <h3>
                      <a 
                        (click)="navigate(mainCat.name,mainCat.id,subCat.name,subCat.id)"
                        > {{ subCat.name }}
                      </a>
                    </h3>
                  </li>
                </div>
              </div>
            </ul>

            <div *ngIf="mainCat.sub_cats.length > 5 && !display[i]  && displayCats[i]"><a class="show text-center"
                (click)="showMore(i)" translate>help.labels.ShowMore</a></div>
            <div *ngIf="mainCat.sub_cats.length > 5 && display[i]  && displayCats[i]"><a class="show text-center"
                (click)="showLess(i)" translate>help.labels.ShowLess</a></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- display one main category with its sub cats list -->
  <div></div>
  <div [class.header]="openedFromHelpTopic" *ngIf="displayOneMainCat && mainCat.length !== 0">
    <!-- row  content-row -->
    <!-- header -->
    <!-- container-fluid  -->
    <div class="container-padding">
      <!-- <div class=" main-group-list"> -->
          <!-- class="col-md-10 col-sm-10 col-md-offset-1 col-sm-offset-1 col-xs-12 cat-col" -->
      <div class="cat-col">
        <div class="card">
          <div>
            <ol class="breadcrumb">
              <li *ngIf="!openedFromHelpTopic" class="active"><a (click)="displayHome()"><i class="fa fa-home"
                    title="home page of help"></i></a></li>
              <li *ngIf="openedFromHelpTopic" class="active"><a [routerLink]="['/i/' +  '/help' ]"><i class="fa fa-home"
                    title="home page of help"></i></a></li>
              <li class="active">{{ mainCat[currentLangId - 1].name }}</li>
            </ol>
          </div>

          <!-- <div class="badge badge-primary category-badge" >{{
            mainCat[currentLangId - 1].name }}</div> -->
            <h1 class="maincat-title">{{ mainCat[currentLangId - 1].name }}</h1>
          <div>
            <ul class="list-group sub-group-list">
              <div *ngFor="let subCat of mainCat[currentLangId - 1].sub_cats; let j = index">

                <!-- <li  class="list-group-item"><a
                      (click)="displaySubCatHelp(subCat.id, mainCat, 2)">{{ subCat.name }}</a> </li> -->
                <li class="list-group-item">
                    <!-- [routerLink]="['/i/' + '/help/' + mainCat[currentLangId -1].name + '/' + mainCat[currentLangId -1].id + '/s/' +  subCat.name + '/' + subCat.id]" -->
                  <h2>
                    <a
                    (click)="navigate(mainCat[currentLangId -1].name,mainCat[currentLangId -1].id,subCat.name,subCat.id)"
                    > {{subCat.name}}
                    </a>
                  </h2>
                 </li>
              </div>
            </ul>
            <div *ngIf="mainCat[currentLangId - 1].sub_cats.length > 5 && !display2  && displayMainCats"><a
                class="show text-center" (click)="display2 = true" translate>help.labels.ShowMore</a></div>
            <div *ngIf="mainCat[currentLangId - 1].sub_cats.length > 5 && display2  && displayMainCats"><a
                class="show text-center" (click)="display2 = false" translate>help.labels.ShowLess</a></div>
          </div>
        </div>
      </div>

      <!-- </div> -->
    </div>

  </div>

</div>

<!-- <p-progressBar *ngIf="displayOneMainCat && mainCat.length === 0" mode="indeterminate"></p-progressBar> -->
