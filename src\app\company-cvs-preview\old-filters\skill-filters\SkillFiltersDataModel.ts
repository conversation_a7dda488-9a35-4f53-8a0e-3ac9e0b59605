export class SkillFiltersDataModel {
    skills = []  ;
    allMandatory = false;
////////////////
    filters = {};
    firstSkill = {
         type_id  : '',
         level_id : '',
         mandatory : ''
    };

    setFilters() {
        let skillsValues = [] ;
        this.skills.forEach(ele => {
            if (ele['type_id']) {
                var  obj = {};
                obj['type_id'] = ele['type_id']['id'] ;
                obj['level_id'] = ele['level_id']['skill_level_id'] ;
                obj['mandatory'] = (ele['mandatory'] === true) ? 'and' : 'or' ;
                skillsValues.push(obj);
            }
        });
        this.filters = {/// *
            'all_mandatory' : this.allMandatory,
             'values' : skillsValues
        };
    }

    constructor() {
    }
}
