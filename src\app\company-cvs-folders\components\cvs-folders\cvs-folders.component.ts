import { Component, OnInit } from '@angular/core';
import {TreeNode} from 'primeng/api';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { CvsFoldersService } from '../../services/cvs-folders.service';
import { GeneralService } from '../../../general/services/general.service';
import { Cv } from '../../../company-cvs-preview/components/cvs-table/DataModel';
import { CvsTableService } from '../../services/cvs-table.service';
import { MessageService, Message, } from "primeng/api";
import { Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
declare var $: any;

@Component({
  selector: 'app-cvs-folders',
  templateUrl: './cvs-folders.component.html',
  styleUrls: ['./cvs-folders.component.css'],
  providers: [MessageService]
})
export class CvsFoldersComponent implements OnInit {
  folders: TreeNode[];
  selectedFolder: TreeNode;
  parentFolder: TreeNode;
  action = null;
  loading = true;
  activeFolder:string = null;
  foldersddData = [];
  foldersddDataLoaded=false;
  username = '';
  private ngUnsubscribe: Subject<any> = new Subject();
  
  constructor(
    private cvsFoldersService: CvsFoldersService,
    private cvsTableService:CvsTableService,
    private generalService:GeneralService,
    private messageService: MessageService,
    private route: ActivatedRoute
    ) { }

  ngOnInit() {
    this.route.params.subscribe(res => {
      this.username = res['username'];
    });

    this.generalService.internalMessage.takeUntil(this.ngUnsubscribe).subscribe( (data) => {
      if(data['src'] === 'cvs-table'){
        //when cvs table data ready, stop page loader
        if (data['message'] === 'folderChanged') {
          this.activeFolder = data['mData'].folder_id;
        }
        if (data['message'] === 'deleteCvUpdateCount'){
          for(let folder of data['mData'].cv_folders){
            this.editFolderCountWithKey('reduce',folder.key,this.folders);
          }
        }
      }
     
      // cvs-folders-cvs-folders-module
      if(  (data['src'] === 'cv-previewer-toolbar' || data['src'] === 'cvs-table') && data['dist']==='cvs-folders'){   
        // case folders changed with move-cv-modal
        if (data['message'] === 'updateCVFoldersCount') {
          for(let folder of data['mData'].oldFolders){
            this.editFolderCountWithKey('reduce',folder.key,this.folders);
          }
          for(let folder of data['mData'].newFolders){
            this.editFolderCountWithKey('add',folder.key,this.folders);
          }
        }

      }

      //case folder changed with detach folder
      if (data['message'] === 'detachFolderFromResumeUpdateCount'){
        this.editFolderCountWithKey('reduce',data['mData'].folder.key,this.folders);
      }
      
    });

    this.cvsFoldersService.getFolders().subscribe( (folders: TreeNode[])  => {
        this.folders = folders['data'];
        this.loading=false;
        // if(this.activeFolder===null)
        //   this.activeFolder = this.folders[0].key;
    });
  }

  ngAfterViewInit(){
    this.cvsFoldersService.getFoldersData().subscribe(data => {
      this.foldersddData = data['data'];
      this.foldersddData.unshift("");
      // this.foldersddDataLoaded=true;
      this.cvsFoldersDDDataNotify(false);
    });
  }


  addSubfolder(key){
    this.action='Add sub-folder';
    this.selectedFolder = this.getNodeWithKey(key, this.folders);
    if (this.selectedFolder) {
      this.parentFolder = this.selectedFolder.parent;
      //root case
      if(this.parentFolder===undefined){
        this.parentFolder=null;
        this.openAddEditFolderModal();
      }
      else{
        if(this.parentFolder.parent === undefined) 
          this.openAddEditFolderModal();
        else 
          this.messageService.add({
            severity: "error",
            detail:"You can create only  three  levels of folders",
            life:6000
          });
      }
    } 
 
  }

  editFolder(key){
      this.action='Edit';
      this.selectedFolder = this.getNodeWithKey(key, this.folders);
      if (this.selectedFolder) {
          this.parentFolder = this.selectedFolder.parent;
          if(this.parentFolder===undefined)
            this.parentFolder=null;
          this.openAddEditFolderModal();
      }    
  }

  childLoop(node){
    let childrenIds = [];
    let childrenLabels='';
    for(let child of node.children) {
      childrenLabels = childrenLabels + child.label + ' ';
      childrenIds.push(+child.key);
      if(child.children.length !==0){
        let child2Arr = child.children;
        childrenLabels = childrenLabels + '\n';
        childrenLabels = childrenLabels + '   ';
        for(let child2 of child2Arr){
          childrenLabels = childrenLabels + child2.label + '\n' + '   ';
          childrenIds.push(+child2.key);
        }
        childrenLabels = childrenLabels + '\n';
      }
    }

    let children = {'labels':childrenLabels, 'ids':childrenIds};

    return children;
  }

  // childLoop(node){
  //   let children='';
  //   for(let child of node.children) {
  //     children = children + child.label + ' ';
  //     if(child.children.length !==0){
  //       let child2Arr = child.children;
  //       children = children + '\n';
  //       children = children + '   ';
  //       for(let child2 of child2Arr){
  //         children = children + child2.label + '\n' + '   ';
  //       }
  //       children = children + '\n';
  //     }
  //   }
  //   return children;
  // }

  deleteFolder(key){    
    let children;
    this.selectedFolder = this.getNodeWithKey(key, this.folders);
    let folderPath = this.foldersddData.filter((folder) => folder.key === this.selectedFolder.key);
    if (this.selectedFolder) {
      let confirmMsg = '';
      if(this.selectedFolder.children.length === 0)
        confirmMsg = 'Delete the folder "' + folderPath[0].label +'" and move the CVs to original folder?';
      else{
        confirmMsg = 'Delete the folder "'+this.selectedFolder.label+'" with all sub-folders, and move the CVs to the original folder\n\nSub folder(s):\n';
        children = this.childLoop(this.selectedFolder);
        confirmMsg = confirmMsg + children.labels;
      }

      if(confirm(confirmMsg)){
        this.cvsFoldersService.deleteFolder(this.selectedFolder.key).subscribe(res=>{
          if(res['error'])
            this.messageService.add({
              severity: "error",
              detail:res['error'],
              life:6000
            });
          else{
            if(this.selectedFolder.parent){
              let index = this.selectedFolder.parent.children.indexOf(this.selectedFolder);
              this.selectedFolder.parent.children.splice(index,1);
            }
            else{
                let index = this.folders.indexOf(this.selectedFolder);
                this.folders.splice(index,1);
            }
            this.cvsFoldersDDDataNotify(true);

            //if the deleted folder is the same as active folder then display all folders instead of deleted folder
            if(this.activeFolder === this.selectedFolder.key){
              this.displayFolderCvs(null);
              // this.displayFolderCvs(this.folders[0].key);
            } 
            //if the deleted folder is parent of active folder then display all folders instead of deleted folder
            else if(children !== undefined && children.ids.includes(+this.activeFolder)){
              this.displayFolderCvs(null);
            }
              
          }
        });
      }
    } 
  }

  getNodeWithKey(key: string, nodes: TreeNode[]): TreeNode | undefined {
    for (let node of nodes) {
      if (node.key === key) {
         return node;
      }
 
      if (node.children) {
        let matchedNode = this.getNodeWithKey(key, node.children);
        if (matchedNode) {
          return matchedNode;
        }
      }
    }
    return undefined;
 }

 dropCV(event: CdkDragDrop<Cv[]>) {
    const index = event.previousIndex;
    let cv = event.previousContainer.data[index];
   
    let id = +event.container.id.split('-')[1];
    let data = {};
    // let data = {"resume_id":cv.resume_id , "folders_ids":[id]};

    let moveToSameFolder = false;
    for(let folder of cv.cv_folders){
      if(+folder.key === id){
        moveToSameFolder = true;
        break;
      }
    }
   
    if(moveToSameFolder === false){
      if(this.activeFolder !== null){
        data = {"resume_id":cv.resume_id , "folders_ids":[id], "copy":false};
        event.previousContainer.data.splice(index, 1);
        this.generalService.notify('reduceTotalFilteredCvs' , 'cvs-folders' , 'cvs-table' , {'reducecount' : 1 });
        
        this.cvsTableService.moveCopyCVToFolder(data).subscribe( res => {
          for(let folder of cv.cv_folders){
            this.editFolderCountWithKey('reduce',folder.key,this.folders);
          }
          this.editFolderCountWithKey('add',res['data'][0].key,this.folders);
          cv.cv_folders = res['data'];
          let msg = '"'+ cv.name +'" CV Moved Successfully to "'+ res['data'][0].label+'" folder';
          this.messageService.add({
            severity: "success",
            detail:msg,
            life:6000
          });
        });
      }
      else{
        data = {"resume_id":cv.resume_id , "folders_ids":[id], "copy":true};
        this.cvsTableService.moveCopyCVToFolder(data).subscribe( res => {
          cv.cv_folders = res['data'];
          this.editFolderCountWithKey('add',cv.cv_folders[cv.cv_folders.length-1].key,this.folders);
          let msg = '"'+ cv.name +'" CV Added Successfully to "'+ cv.cv_folders[cv.cv_folders.length-1].label+'" folder';
          this.messageService.add({
            severity: "success",
            detail:msg,
            life:6000
          });
        });
      }
    }

  }

  updateCountFoldersDD(key,newCount){
    for(let folder of this.foldersddData){
      if(folder.key===key)
        folder.cv_count=newCount;
    }
  }

  editFolderCountWithKey(action,key: string, nodes: TreeNode[]){
    for (let node of nodes) {
      if (node.key === key) {
        if(action==='add')
          node.data.count++;
        else if(action==='reduce' && node.data.count>0)
          node.data.count--;
        this.updateCountFoldersDD(key,node.data.count);
        this.cvsFoldersDDDataNotify(false);
      }
 
      if (node.children) {
        let matchedNode = this.getNodeWithKey(key, node.children);
        if (matchedNode) {
          if(action==='add')
            matchedNode.data.count++;
          else if(action==='reduce' && matchedNode.data.count>0)
            matchedNode.data.count--;
          this.updateCountFoldersDD(key,matchedNode.data.count);
          this.cvsFoldersDDDataNotify(false);
        }
      }
    }
  }

  openAddEditFolderModal(){
    $('#addEditFolderModal').appendTo("body").modal('show');
   // this.generalService.notify('modalOpened' , 'cvs-folders' , 'folders-modals' , {});
  }

  //handel add/edit folder
  handelPopup($event){
    let folder: TreeNode;
    folder = $event['folder'];
    let parent;
    if($event['parent_key']===null)
      parent = null;
    else
      parent = this.getNodeWithKey($event['parent_key'],this.folders);
   
    if(this.action==='Add' || this.action === 'Add sub-folder'){
      //root node case
      if(parent === null){
        this.folders.unshift(folder); 
      }
      else if(parent !== undefined){
        parent.children.unshift(folder); 
      }    
    }
    else if(this.action==='Edit'){
      let index_old;
      //root node case , (new parent)
     if(parent === null){
        //rename root folder case
        if(this.selectedFolder.parent ===  undefined ){
          index_old = this.folders.indexOf(this.selectedFolder);
          this.folders[index_old] = folder;
        }
        //move child folder to be a root folder
        else {
          index_old = this.selectedFolder.parent.children.indexOf(this.selectedFolder);
          this.selectedFolder.parent.children.splice(index_old,1);
          this.folders.unshift(folder);
        }
        
      }
      else{
        //old node is a root node
        if(this.selectedFolder.parent === undefined){
          index_old = this.folders.indexOf(this.selectedFolder);
          this.folders.splice(index_old,1);
          parent.children.unshift(folder);
        }
        else{
          index_old = this.selectedFolder.parent.children.indexOf(this.selectedFolder);
          if(parent !== undefined){
            this.selectedFolder.parent.children.splice(index_old,1);
            parent.children.unshift(folder); 
          }
          else if(this.selectedFolder.parent === parent){
            this.selectedFolder.parent.children[index_old] = folder;
          }
        }
      }
      
    }
    if(folder)
      this.cvsFoldersDDDataNotify(true);
  }

  displayFolderCvs(key){
    let folder_id;
    if(key !== null){
      this.selectedFolder = this.getNodeWithKey(key, this.folders);
      folder_id = +key;
      // this.activeFolder = key;
      this.generalService.notify('folderChanged' , 'cvs-folders' , 'cvs-table' , {'folder_id' : folder_id, 'folder_label':this.selectedFolder.label}) ;
    }
    else{
      folder_id = null;
      this.generalService.notify('folderChanged' , 'cvs-folders' , 'cvs-table' , {'folder_id' : folder_id, 'folder_label':''}) ;
    }
    this.activeFolder = key; 
  }

  //use this function when you want click event to happen on the hole node
  //but it is causing issues in event bubbling and event.stopPropagation not helping to solve all issues
  //but it is also causing another issues
  // displayFolderCvs($event){
  //   let folder_id = +$event['node'].key;
  //   this.activeFolder = $event['node'].key;
  //   this.generalService.notify('folderChanged' , 'cvs-folders' , 'cvs-table' , {'folder_id' : folder_id}) ;
  // }

  cvsFoldersDDDataNotify(getUpdates=false){
    if(getUpdates){
      this.cvsFoldersService.getFoldersData().subscribe(data => {
        this.foldersddData = data['data'];
        this.foldersddData.unshift("");
        this.generalService.notify('cvsFoldersDDReady' , 'cvs-folders' , 'folders-modals' , {'foldersddData':this.foldersddData});
      });
    }
    else
      this.generalService.notify('cvsFoldersDDReady' , 'cvs-folders' , 'folders-modals' , {'foldersddData':this.foldersddData});

  }

  closeAddEditFolderModal(){
    this.generalService.notify('addEditFolderModalClosed' , 'cvs-folders' , 'add-edit-folder-modal' , {});
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
