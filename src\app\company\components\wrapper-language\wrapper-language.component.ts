import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {AbstractControl, FormBuilder, FormControl, Validators} from '@angular/forms';
import { CompanyWrapperService } from '../../services/company-wrapper.service';
import { ActivatedRoute, Router } from '@angular/router';
declare var $: any;
@Component({
  // tslint:disable-next-line:component-selector
  selector: 'wrapper-language',
  templateUrl: './wrapper-language.component.html',
  styleUrls: ['./wrapper-language.component.css']
})
export class WrapperLanguageComponent implements OnInit {

  languageForm;

  @Output() closeModalPopup = new EventEmitter();
  translated_language_temp = [];
  companyId = Number (localStorage.getItem('company_id'));
  constructor(private  fb: FormBuilder,
    private companyWrapperService: CompanyWrapperService,
    private route: ActivatedRoute,
    private router: Router) {
    this.languageForm = this.fb.group({
      translated_languages_id : [ ''],
      main_language_id: ['']
    });
}

  ngOnInit() {
    this.companyWrapperService.getLanguages().subscribe(
      (res) => {
          this.translated_language_temp = res['data'];
          this.translated_language_temp.unshift({'name': '', 'id': ''});
      }
    );
  }

  submit() {
    if (this.languageForm.valid) {
      let sendData = this.languageForm.value;
      sendData.main_language_id = sendData.translated_languages_id.id;
    
    this.companyWrapperService.editLanguage(sendData,this.companyId).toPromise().then(
      () => {
      localStorage.setItem('current_company_language', sendData.main_language_id);
      $('#companyModal').modal('hide');
    }
  );
    }

  }

}
