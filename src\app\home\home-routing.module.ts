import { NgModule } from '@angular/core';
import { RouterModule , Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';

const routes: Routes = [
    { path: '', component: HomeComponent },
    { path: 'qatar', component: HomeComponent },
    { path: 'syria', component: HomeComponent },
    { path: 'egypt', component: HomeComponent },
    { path: 'india', component: HomeComponent },
   // { path: ':country', component: HomeComponent },
];


@NgModule({
    imports: [ RouterModule.forChild(routes) ],
    exports: [ RouterModule ]
})
export class HomeRoutingModule {

}
