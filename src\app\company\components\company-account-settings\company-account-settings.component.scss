.left-col-preview-alignright p,
.date-col,
.left-col-preview p {
    text-align: right;
    color: #276ea4;
}
.new{
  padding-left: 40px !important;
}

.CForm .info {
    color: #276ea4;
    font-size: 16px;
}

.CForm .preview {
    font-size: 16px;
}

.CForm .secondaryp {
    color: #276ea4;
}

.user-pro-pic,
.user-pro-pic:hover {
    text-decoration: none;
}

.user-pro-pic img {
    margin: auto;
    width: 180px;
    margin-top: -20px;
}

body {
    position: fixed;
}


/*add*/
body{
    //background-color: #eee !important;
  }
  #page-content-wrapper {
    transition: margin-left .5s ease;
  }
  
  #page-content-wrapper .page-content {
  font-family: 'Exo2-Regular', sans-serif !important;
  
    padding: 0px 15px 0px;
  }
  
  .table-page-content {
    position: relative;
  }
  .custom-checkbox input {
    width: 80%;
    height: 23px !important;
    border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));
  
  }
  
  
  .table-page-content .page-title {
    position: absolute;
    background: white;
    padding-right: 30px;
    z-index: 1;
  }
  
  .CForm {
  }
  
  .input-group[class*=col-] {
    float: left !important;
    padding-right: 5px;
    padding-left: 4px;
  }
  
  .strike2 {
    border-right: 1px solid gray
  }
  
  .pad-top {
    padding-top: 20px;
  }
  .pad-top6{
    padding-top:12px;
  }
  
  .has-error .error-message {
    color: #a94442;
    position: absolute;
    left: 70%;
    top: 10px;
    width: 60%;
  }
  .editing{
  color:red;
  background-color: red;
  }
  .glyphicon{
    left:80%;
  }
  .pad-top1{
    padding-top: 30px;
  }
  
  .pad-top2 {
    padding-top: 75px;
  }
  .pad-top3{
    padding-top: 35px;
  }
  .pad-top4{
    padding-top: 25px;
  }
  .custom-btn{
    width:100px; 
    background-color: #30457c;
    border-color: #30457c; 
    border-radius: 0;
  }

  #title {
    display: none;
  }
  
  #subtitle {
    display: none;
  }
  
  #warning{
    position: relative;
    
  }
  
  :host{
    //background-color: #eee
    
  }
  .container-fluid{
    background-size: 100% 100%;
    // padding-top:20px;
    }  
  .container-fluid::after{
    //background-color: #eee !important;
  }
  .add{
  background-color: white;
  position: absolute;
    width: 62%;
    left:410px;
  height: 78%;
   border-radius: 10px;
   padding-top: 30px;
   padding-left: 40px;
      font-family: 'Exo2-Regular', sans-serif !important;
     }
  .details{
    width: 840px;
    background-color: white;
    border-radius: 10px;
    padding-left: 0px;
    font-family: 'Exo2-Regular', sans-serif !important;
    margin-left: 50px;
  
  }
  // .lan{
  //   border-radius: 10px;
  //   padding-left: 40px;
  //   padding-top:60px;
  //   font-family: 'Exo2-Regular', sans-serif !important;
  //   text-align: center;
  // }
  .info1{
    // padding-left: 50px;
    // padding-top:30px;
    margin-bottom: 0;
  }
  .editStyle{
    margin-bottom: 20px;
    margin:auto;
  }

  .next{
   background-color: #3d7bce;
    border: #3d7bce solid 1px;
    margin-right: 4px;
  }
  .back{
   background-color: #959595;
   border:#959595 solid 1px;
   margin-right: 4px;
  }
  .finish{
   background-color: #30A03E;
    border: #30A03E solid 1px;
  }

  .labelAdd{
    font-size: 15px;
    color: #4f94df;
     text-align: right; 
  }
  .details .check{
   
  }
  .custom-checkbox4{
    margin-left: -30px;
  }
  .custom-checkbox3{
    margin-left: -18px;
  }
  .company-name{
  text-align: center; margin-top:70px; margin-left: -60px;
  }
  .company-industry{
  text-align: center; margin-left: -60px;
  }
  .extra-check{
    width: 20px; height: 20px; margin-top: 2px;
  }
  .check{
    border-color: -internal-light-dark-color(rgb(118, 118, 118), rgb(195, 195, 195));
  
  }
  .margin1{
    margin-left: -1px;
  }
  .margin2{
    margin-left: -2px;
  }
  .red{
    color:red !important;
  }
  .edit{
      font-size: 20px;
      color: #30457c;
      cursor: pointer;
display: none;  }
  .preview1:hover .edit{
display: inline !important;
}
:host ::ng-deep .ui-inplace .ui-inplace-display{
  background: none !important;
}
// 
.sidenav , .sidnav1 {
  height: 100%;
  width: 0;
  // position: fixed;
  position: absolute;
  z-index: 1;
  top: 126px;
  // left: 0;
  left:15px;
  overflow-x: hidden;
  transition: 0.5s;
  padding-top:0px;
  background-color: white;
}

.sidenav a , .sidnav1 a{
  padding: 8px 8px 8px 32px;
  text-decoration: none;
  font-size: 25px;
  color: #818181;
  display: inline-block;
  transition: 0.3s;

}

.sidenav a:hover{
  color: #f1f1f1;
}
 .sidnav1 a:hover{
  color: #f1f1f1;
}

.sidenav .closebtn ,  .sidnav1 .closebtn {
  position: absolute;
  top: 75px !important;
  right: 25px;
  font-size: 36px;
  margin-left: 110px;
}


.con{
  // width:60%;
  // padding:20px;

}
// .slide input[type=text], select, textarea {
//   width: 70%;
//   padding: 12px;
//   border: 1px solid #ccc;
//   border-radius: 4px;
//   box-sizing: border-box;
//   margin-top: 6px;
//   margin-bottom: 16px;
//   resize: vertical;
// }
// .con h3{
//   color: #cc0000;
// }
.section-title{
  color: #cc0000;
  font-size: 22px;
}
.slide button ,.slide .btn1{
  margin-top:15px;
  margin-bottom: 40px;
  background-color:  #3bb34b ;
}
.slide label{
  font-size: 18px;
  color:black;
  text-align: right;
  // margin-top:20px;
}
.slide .label1{
  margin-top:0px !important;

}

// .custom-form-control , .custom-form-control:focus{
//   padding-top: 0;
//   background: transparent;
//   box-shadow: none;
//   border-radius: 0px !important;
//   border: 0 !important;
//   border-bottom: 1px solid #ccc !important;
//   padding-bottom: 0;
// }
.resend-btn{
  color:#777;
  cursor: pointer;
}

.form-control{
  position: relative;
  width: 100%;
}
.error-message{
  margin-top:15px;
}
.error-message span{
  color: #a94442;
}
.password-eye-icon {
  position: absolute;
  bottom: 46%;
  right: 20px;
  z-index: 2;
  font-size: 1.1rem;
}
.modal-body p{
  font-size:16px;
}
.delete-btn , .delete-btn:hover , .delete-btn:active , .delete-btn:focus {
  background-color: #777;
}

.modal-body {
  min-height: 340px;
}

// resposive styles
@media screen and (max-width: 991px) {
  .modal-body p{
    font-size:14px;
  }
  #salary {
      right: 10%;
  }
}

@media only screen and (max-width: 767px) {
   table,
  thead,
  tbody,
  th,
  td,
  tr {
      display: block;
  } 
  
  .margin-bo-mo-10 {
      margin-bottom: 10px;
  }
  .custom-control-labels {
      padding-top: 8px;

  }
  .custom-btn{
      width: 70px;
  }
  
  .add{
    left:160px;
    width: 78%;

  }
  
  .details{
    // font-size: 10px;
    // width: 530px;
    width:100%;
    margin-left: 0px;
  }
  .details .labelAdd{
    font-size: 15px;
    padding-left:0px;
  }
  .details .custom-checkbox4{
    margin-left: -20px;
  }
  .details .custom-checkbox3{
    margin-left: -5px;
  }
  .details .check{
    width:15px !important;
    height: 15 !important;
    margin-left:7px;
  }
  .company-name{
    font-size: 15px;
    margin-left: -15px;
  }
  .company-industry{
    font-size: 13px;
    margin-left: -18px;
  }
  .extra-check{
    width: 15px;
    height: 15px;
  }

  .companyName{
    font-size: 14px;
    margin-top: 10px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 1022px) {
  #save {
      margin-left: 46px;
      margin-top: 10px;
  }
}

@media screen and (max-width:768px) {
  .CForm {
      // padding: 15px 15px 0px 30px;
  }
  .flex-order-sm-1 {
      order: 1;
  }
  .flex-order-sm-2 {
      order: 2;
  }

  .slide label{
    text-align: left;
  }
  .input-height{
    height:100%;
  }
}

@media screen and (max-height: 450px) {
  // .sidenav , .sidnav1{padding-top: 15px;}
  .sidenav a , .sidnav1 a{font-size: 18px;}
}