import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { ImageTransform, ImageCroppedEvent } from 'ngx-image-cropper';
import { GeneralService } from '../../../general/services/general.service';
declare var $: any;

@Component({
  selector: 'app-image-editor',
  templateUrl: './image-editor.component.html',
  styleUrls: ['./image-editor.component.css']
})
export class ImageEditorComponent implements OnInit {

  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  scale = 1;
  containWithinAspectRatio = false;
  transform: ImageTransform = {};

  @Output() closeModalPopup = new EventEmitter();
  imgError:string = null;
  image_code_to_send:{file:string, file_type:string, is_deleted:boolean};
  imageType = '';
  imageName = '';
  initialStateVisibility = false;
  constructor(private generalService: GeneralService) { }

  ngOnInit(): void {
      this.image_code_to_send = {file:'',file_type:'',is_deleted:false};

      this.generalService.internalMessage.subscribe((data) => {
        if (data['dist'] === 'image-editor') {
          if (data['message'] === 'image-deleted') {
            this.removeImage();
          }      
        }
      });
  }

  //Start image processing methods
  fileChangeEvent(event: any): void {
      this.initialStateVisibility = true;
      
      this.imageChangedEvent = event;
      let files = event.target.files;

      if(files[0].size > 1048576 || (files[0].type !== 'image/jpeg' && files[0].type !== 'image/png') ){
          if(files[0].size > 1048576){
              this.imgError = "validationMessages.imageSizeBig";
          }
          else if(files[0].type !== 'image/jpeg' && files[0].type !== 'image/png'){
              this.imgError = "validationMessages.invalidImageType";
          }
          this.removeImage();
      }
  
      else{
          this.imgError = null; 
          this.imageType = 'jpeg';
        //  this.imageType = files[0].name.slice((files[0].name.lastIndexOf(".") - 1 >>> 0) + 2);
          this.imageName = files[0].name;
      }
  }

  imageCropped(event: ImageCroppedEvent) {
      this.croppedImage = event.base64;
  }
  imageLoaded() {
      // show cropper
      if(this.imgError === null)
        this.showCropper = true;
      
      else this.showCropper = false;
  }

  zoomOut() {
      this.scale -= .1;
      this.transform = {
          ...this.transform,
          scale: this.scale
      };
  }

  zoomIn() {
      this.scale += .1;
      this.transform = {
          ...this.transform,
          scale: this.scale
      };
  }

  cropperReady() {
      // cropper ready
  }
  loadImageFailed() {
      // show message
  }
  
  //End image processing methods

  doneEditing(){
      if(this.croppedImage !==''){
        this.image_code_to_send.file = this.croppedImage;
        this.image_code_to_send.file_type = this.imageType;
        this.image_code_to_send.is_deleted = false;
      }
      this.closeModalPopup.emit({'editedImage': this.image_code_to_send});
      $('#imageEditorModal').modal('hide');
  }

  removeImage(){
    this.imageChangedEvent = null;
    this.croppedImage = '';
    this.showCropper = false;
    this.image_code_to_send = {file:'',file_type:'',is_deleted:false};
  }

  fileClickEvent($event){
      $event.target.value = '';
  }

  cancel(){
    this.removeImage();
    this.closeModalPopup.emit({'editedImage': this.image_code_to_send});
    $('#imageEditorModal').modal('hide');
  }

}
